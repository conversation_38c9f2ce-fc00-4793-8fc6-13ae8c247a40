## 视频播放用的库
[GSYVideoPlayer](https://github.com/CarGuo/GSYVideoPlayer)
全量引入太大，所以只引入了一部分，播放的参考可以看SimplePlayerActivity或者库的wiki

## UI适配
[一种非常好用的Android屏幕适配](https://www.jianshu.com/p/1302ad5a4b04)

如果需要添加尺寸，那么需要重新生成dimens文件(项目前期需要频繁添加尺寸，
可以在values文件夹下添加，最后打包的时候再生成尺寸)
生成dimens文件的方法比较麻烦，按照上面那个链接即可(需要安装ScreenMatch插件）


## Arouter注意事项
[kotlin databinding arouter结合使用无法产生Arouter类的原因](https://www.jianshu.com/p/fd7b9aa13062)

arouter使用withObject传值需要注意，Bean不要去实现 Serializable、Parcelable接口
如果已经实现了这两个接口的话使用 withSerializable()或者withParcelable()来传值

多组件下因为每个组件包名不一致,而databinding

```
        //pending binding itemModel
        holder.binding.setVariable(BR.schedule, item)
        //pending binding presenter
        holder.binding.setVariable(BR.presenter, itemPresenter)

```

设置绑定数据时根据BR来寻找的，包名不一致导致组件在寻找BR时找不到对应组件的BR.id
后面继续找办法，现在的解决方案是
BindingViewAdapter中的onBindViewHolder需要下沉到具体的组件当中去，即每个组件都需要
实现自己的BaseBindingViewAdapter

## 高斯模糊
[Android 图片高斯模糊解决方案](https://www.jianshu.com/p/02da487a2f43)

低版本是用fashBlur,高版本使用RenderScript

## 激励模块
1. 金币（ICoinHelper）

	使用方式:

		ICoinHelper coinHelper = IncentiveFactory.getCoinHelper();

	方法:

		void init(); //初始化方法（含初始化网络请求-初始化金币总数、上限值、当天已增加金币值、时间）
	
		void uploadUnuploadData(); //上报未上报数据（当获取或消耗金币之后，进行网络请求失败的情况下，会将本条流水存到本地，此方法会将本地的未上报数据调用接口上报）
	
		int getCoinCount(); //获取金币总数（接口返回的数据+本地未同步数据=准确金币值）
	
		boolean winCoin(@NonNull CoinRecordBean recordBean); //增加金币，其中金币数量一定要为 正数；当达到上限值会返回false（增加失败）
	
		boolean spendCoin(@NonNull CoinRecordBean recordBean); //消耗金币，其中金币数量一定要为 正数；当消耗金币超过金币总数会返回false（消耗金币失败）
	
		void clear(); //清空数据

1. 用户端私信unicode4位数字过滤
2. 数美对接私信、对接主播端头像审核。

### 生成APK MD5
```

[//]: # (certutil -hashfile .\anchor_official_release_1.0.1_250326.apk sha512)
Get-FileHash -Algorithm SHA512 .\anchor_official_release_1.0.1_250326.apk | ForEach-Object { $_.Hash.ToLower() }
```

