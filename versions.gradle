/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


/**
 * Shared file between builds so that they can all use the same dependencies and
 * maven repositories.
 **/

ext.deps = [:]
def versions = [:]
versions.android_gradle_plugin = '8.8.0'
versions.apache_commons = "2.5"
versions.arch_core = "latest.release"
versions.atsl_rules = "1.1.3"
versions.atsl_runner = "1.1.3"
versions.atsl_junit = "1.1.3"
versions.benchmark = "1.1.1"
versions.constraint_layout = "2.1.4"
versions.core_ktx = "1.7.0"
versions.dexmaker = "2.2.0"
versions.espresso = "3.1.1"
versions.fragment = "1.4.0"
versions.glide = "4.16.0"
versions.hamcrest = "1.3"
versions.junit = "4.12"
versions.kotlin = "2.1.0"
versions.lifecycle = "2.2.0"
versions.mockito = "2.25.0"
versions.mockito_all = "1.10.19"
versions.mockito_android = "2.25.0"
versions.lottie = "3.0.7"
versions.mockwebserver = "3.8.1"
versions.navigation = '2.4.1'
versions.okhttp_logging_interceptor = "3.9.0"
versions.paging = "2.1.0-rc01"
versions.retrofit = "2.3.0"
versions.robolectric = "4.2"
versions.room = "2.1.0-alpha06"
versions.rx_android = "2.0.1"
versions.rxjava2 = "2.1.3"
versions.retrofit2_converter = "2.4.0"
versions.support = "1.0.0"
versions.timber = "4.5.1"
versions.work = "2.1.0-alpha01"
versions.canarySdkVersion = "2.10"
versions.rxpermissions = "0.10.2"
versions.multilanguages = "8.0"
versions.permissionx = "1.7.1"
versions.rxlifecycle = "3.0.0"
versions.toast = "5.8"
versions.arrow_version = "0.7.3"
versions.arouter_register_version = "1.0.2"
versions.qrcodereaderview = "2.0.3"
versions.convenientbanner = "2.1.5"
versions.glide_transformations = "4.0.1"
versions.autodispose = "1.3.0"
versions.Android_PickerView = "4.1.8"
versions.ucrop = "2.2.3"
versions.SmartRefreshLayout = "1.1.0-andx-13"
versions.DSBridge = "3.0-SNAPSHOT"
versions.DSBridge_x5 = "x5-3.0-SNAPSHOT"
versions.immersionbar = "3.0.0-beta05"
versions.fastJson = "1.1.71.android"
versions.pageFlip = "1.0.2"
versions.GSYVideoPlayer = "v8.4.0-release-jitpack"
versions.aliyun = "1.1.3"
versions.disklrucache = "2.0.2"
versions.eventbus = '3.1.1'
versions.rtc = '4.3.2'
versions.core = '1.7.0'

ext.versions = versions

def deps = [:]
deps.benchmark = "androidx.benchmark:benchmark:$versions.benchmark"
deps.benchmark_gradle = "androidx.benchmark:benchmark-gradle-plugin:$versions.benchmark"

def support = [:]
support.annotations = "androidx.annotation:annotation:$versions.support"
support.app_compat = "androidx.appcompat:appcompat:1.7.0"
support.recyclerview = "androidx.recyclerview:recyclerview:$versions.support"
support.cardview = "androidx.cardview:cardview:$versions.support"
support.design = "com.google.android.material:material:1.4.0"
support.v4 = "androidx.legacy:legacy-support-v4:$versions.support"
support.core_utils = "androidx.legacy:legacy-support-core-utils:$versions.support"
support.test_core = "androidx.test:core:latest.release"
support.core_ktx = "androidx.core:core-ktx:$versions.core_ktx"
support.fragment_runtime = "androidx.fragment:fragment:${versions.fragment}"
support.fragment_runtime_ktx = "androidx.fragment:fragment-ktx:${versions.fragment}"
support.fragment_testing = "androidx.fragment:fragment-testing:${versions.fragment}"
support.activity_runtime = "androidx.activity:activity:${versions.fragment}"
support.activity_ktx = "androidx.activity:activity-ktx:${versions.fragment}"
support.core = "androidx.core:core:${versions.core}"
support.core_ktx = "androidx.core:core:${versions.core}"
deps.support = support

def room = [:]
room.runtime = "androidx.room:room-runtime:2.4.0"
room.compiler = "androidx.room:room-compiler:2.4.0"
room.rxjava2 = "androidx.room:room-rxjava2:$versions.room"
room.testing = "androidx.room:room-testing:$versions.room"
deps.room = room

def lifecycle = [:]
lifecycle.runtime = "androidx.lifecycle:lifecycle-runtime:$versions.lifecycle"
lifecycle.extensions = "androidx.lifecycle:lifecycle-extensions:$versions.lifecycle"
lifecycle.java8 = "androidx.lifecycle:lifecycle-common-java8:$versions.lifecycle"
lifecycle.compiler = "androidx.lifecycle:lifecycle-compiler:$versions.lifecycle"
lifecycle.viewmodel_ktx = "androidx.lifecycle:lifecycle-viewmodel-ktx:$versions.lifecycle"
deps.lifecycle = lifecycle

def arch_core = [:]
arch_core.runtime = "androidx.arch.core:core-runtime:$versions.arch_core"
arch_core.testing = "androidx.arch.core:core-testing:$versions.arch_core"
deps.arch_core = arch_core

def retrofit = [:]
retrofit.runtime = "com.squareup.retrofit2:retrofit:$versions.retrofit"
retrofit.gson = "com.squareup.retrofit2:converter-gson:$versions.retrofit"
retrofit.mock = "com.squareup.retrofit2:retrofit-mock:$versions.retrofit"
deps.retrofit = retrofit
deps.okhttp_logging_interceptor = "com.squareup.okhttp3:logging-interceptor:${versions.okhttp_logging_interceptor}"

def espresso = [:]
espresso.core = "androidx.lottie.espresso:espresso-core:$versions.espresso"
espresso.contrib = "androidx.lottie.espresso:espresso-contrib:$versions.espresso"
espresso.intents = "androidx.lottie.espresso:espresso-intents:$versions.espresso"
deps.espresso = espresso

def atsl = [:]
atsl.ext_junit = "androidx.lottie.ext:junit:$versions.atsl_junit"
atsl.runner = "androidx.lottie:runner:$versions.atsl_runner"
atsl.rules = "androidx.lottie:rules:$versions.atsl_rules"
deps.atsl = atsl

def mockito = [:]
mockito.core = "org.mockito:mockito-core:$versions.mockito"
mockito.all = "org.mockito:mockito-all:$versions.mockito_all"
mockito.android = "org.mockito:mockito-android:$versions.mockito_android"
deps.mockito = mockito

def airbnb = [:]
airbnb.lottie = "com.airbnb.android:lottie:$versions.lottie"
deps.airbnb = airbnb

def kotlin = [:]
kotlin.stdlib = "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$versions.kotlin"
kotlin.test = "org.jetbrains.kotlin:kotlin-lottie-junit:$versions.kotlin"
kotlin.plugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:$versions.kotlin"
kotlin.allopen = "org.jetbrains.kotlin:kotlin-allopen:$versions.kotlin"

deps.kotlin_android = "org.jetbrains.kotlin.android:1.8.0"

deps.kotlin = kotlin

deps.paging_ktx = "androidx.paging:paging-runtime-ktx:$versions.paging"

def glide = [:]
glide.runtime = "com.github.bumptech.glide:glide:$versions.glide"
glide.compiler = "com.github.bumptech.glide:compiler:$versions.glide"
glide.glide_transformations = "jp.wasabeef:glide-transformations:$versions.glide_transformations"
glide.annotations = "com.github.bumptech.glide:annotations:$versions.glide"
glide.integration = "com.github.bumptech.glide:okhttp3-integration:4.16.0"

deps.glide = glide
deps.constraint_layout = "androidx.constraintlayout:constraintlayout:$versions.constraint_layout"
deps.junit = "junit:junit:$versions.junit"
deps.mock_web_server = "com.squareup.okhttp3:mockwebserver:$versions.mockwebserver"
deps.rxjava2 = "io.reactivex.rxjava2:rxjava:$versions.rxjava2"
deps.rx_android = "io.reactivex.rxjava2:rxandroid:$versions.rx_android"
deps.android_gradle_plugin = "com.android.tools.build:gradle:$versions.android_gradle_plugin"
deps.retrofit2_converter = "com.squareup.retrofit2:converter-gson:$versions.retrofit2_converter"
deps.retrofit2_rxjava_converter = "com.squareup.retrofit2:adapter-rxjava2:$versions.retrofit2_converter"
//暂时改成以library引用
deps.immersionbar = "com.gyf.immersionbar:immersionbar:$versions.immersionbar"
deps.immersionbar_components = "com.gyf.immersionbar:immersionbar-components:$versions.immersionbar"
deps.immersionbar_ktx = "com.gyf.immersionbar:immersionbar-ktx:$versions.immersionbar"
// disklrucache
deps.disklrucache = "com.jakewharton:disklrucache:$versions.disklrucache"
deps.loading_dialog = "com.github.gittjy:LoadingDialog:1.0.2"
ext.deps = deps

def build_versions = [:]
build_versions.min_sdk = 26
build_versions.target_sdk = 34
build_versions.build_tools = "34.0.0"
build_versions.versionCode = getVersionCode()
build_versions.versionName = getVersionName()
build_versions.zileVersionName = getZileVersionName()
ext.build_versions = build_versions

def getVersionCode() {
//    Process process = "git rev-list --count HEAD".execute()
//    process.waitFor()
//    int versionCode = process.getText().toInteger()
//    return versionCode
    return 7
}

def getVersionName() {
    // 根据版本迭代返回正常的versionName
    return "1.0.6"
}

def getZileVersionName() {
    // zile内部维护的versionName，大版本 & 小版本 （手动修改）
    int app_major_version = 1
    int app_minor_version = 1
    int versionCode = getVersionCode()
//    return getVersionName() + ".$versionCode"
    return getVersionName()
}

def work = [:]
work.runtime = "androidx.work:work-runtime:$versions.work"
work.testing = "androidx.work:work-testing:$versions.work"
work.firebase = "androidx.work:work-firebase:$versions.work"
work.runtime_ktx = "androidx.work:work-runtime-ktx:$versions.work"
deps.work = work

def navigation = [:]
navigation.runtime = "androidx.navigation:navigation-runtime:$versions.navigation"
navigation.runtime_ktx = "androidx.navigation:navigation-runtime-ktx:$versions.navigation"
navigation.fragment = "androidx.navigation:navigation-fragment:$versions.navigation"
navigation.fragment_ktx = "androidx.navigation:navigation-fragment-ktx:$versions.navigation"
navigation.ui = "androidx.navigation:navigation-ui:$versions.navigation"
navigation.ui_ktx = "androidx.navigation:navigation-ui-ktx:$versions.navigation"
navigation.safe_args_plugin = "androidx.navigation:navigation-safe-args-gradle-plugin:$versions.navigation"
deps.navigation = navigation

def canary = [:]
canary.debug = "com.squareup.leakcanary:leakcanary-android:$versions.canarySdkVersion"
canary.release = "com.squareup.leakcanary:leakcanary-android:$versions.canarySdkVersion"
//canary.release = "com.squareup.leakcanary:leakcanary-android-no-op:$versions.canarySdkVersion"
deps.canary = canary

deps.multilanguages = "com.github.getActivity:MultiLanguages:$versions.multilanguages"

deps.rxpermissions = "com.github.tbruyelle:rxpermissions:$versions.rxpermissions"
deps.permissionx = "com.guolindev.permissionx:permissionx:$versions.permissionx"

deps.rxlifecycle = "com.trello.rxlifecycle3:rxlifecycle:$versions.rxlifecycle"
deps.rxlifecycle_android = "com.trello.rxlifecycle3:rxlifecycle-android:$versions.rxlifecycle"
deps.rxlifecycle_components = "com.trello.rxlifecycle3:rxlifecycle-components:$versions.rxlifecycle"
deps.rxlifecycle_components_preference = "com.trello.rxlifecycle3:rxlifecycle-components-preference:$versions.rxlifecycle"
deps.toast = "com.hjq:toast:$versions.toast"
deps.arrow = "io.arrow-kt:arrow-core:$versions.arrow_version"
deps.arouter_register = "com.alibaba:arouter-register:$versions.arouter_register_version"
deps.qrcodereaderview = "com.dlazaro66.qrcodereaderview:qrcodereaderview:$versions.qrcodereaderview"
deps.wechat_login = "com.tencent.mm.opensdk:wechat-sdk-android-with-mta:5.3.1"
deps.convenientbanner = "com.bigkoo:convenientbanner:$versions.convenientbanner"
deps.Android_PickerView = "com.contrarywind:Android-PickerView:$versions.Android_PickerView"
deps.ucrop = "com.github.yalantis:ucrop:$versions.ucrop"
//deps.sensoranalytics = "com.sensorsdata.analytics.android:SensorsAnalyticsSDK:3.2.3"

def SmartRefreshLayout = [:]
SmartRefreshLayout.content = "com.scwang.smartrefresh:SmartRefreshLayout:$versions.SmartRefreshLayout"
SmartRefreshLayout.header = "com.scwang.smartrefresh:SmartRefreshHeader:$versions.SmartRefreshLayout"
deps.SmartRefreshLayout = SmartRefreshLayout

def autodispose = [:]
autodispose.autodispose = "com.uber.autodispose:autodispose:$versions.autodispose"
autodispose.autodispose_android = "com.uber.autodispose:autodispose-android-archcomponents:$versions.autodispose"
autodispose.autodispose_lifecycle = "com.uber.autodispose:autodispose-lifecycle:$versions.autodispose"
deps.autodispose = autodispose

//def wendu.dsbridge = [:]
////wendu.dsbridge.android = "com.github.wendux:DSBridge-Android:$versions.DSBridge"
////wendu.dsbridge.android_x5 = "com.github.wendux:DSBridge-Android:$versions.DSBridge_x5"
////deps.wendu.dsbridge = wendu.dsbridge

deps.fastJson = "com.alibaba:fastjson:$versions.fastJson"
deps.pageFlip = "com.github.eschao:android-PageFlip:$versions.pageFlip"

def GSYVideoPlayer = [:]
GSYVideoPlayer.GSYVideoPlayer_java = "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer-java:$versions.GSYVideoPlayer"
GSYVideoPlayer.GSYVideoPlayer_exo2 = "com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer-exo2:$versions.GSYVideoPlayer"
GSYVideoPlayer.GSYVideoPlayer_armv7a = "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer-armv7a:$versions.GSYVideoPlayer"
GSYVideoPlayer.GSYVideoPlayer_arm64 = "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer-arm64:$versions.GSYVideoPlayer"
deps.GSYVideoPlayer = GSYVideoPlayer

def aliyun = [:]
aliyun.alicloud_android_httpdns = "com.aliyun.ams:alicloud-android-httpdns:$versions.aliyun"
deps.aliyun = aliyun

def bugly = [:]
bugly.crashreport = "com.tencent.bugly:crashreport:latest.release"
bugly.nativecrashreport = "com.tencent.bugly:nativecrashreport:latest.release"
deps.bugly = bugly

def greenrobot = [:]
greenrobot.eventbus = "org.greenrobot:eventbus:$versions.eventbus"
deps.greenrobot = greenrobot

def agora = [:]
agora.rtc = "io.agora.rtc:full-sdk:$versions.rtc"
deps.agora = agora
//deps.agora = "io.agora.rtc:full-sdk:4.0.0-rc.1"
//ext.agora = agora

deps.badgeview = "q.rorbin:badgeview:1.1.3"
deps.switchButton = "com.kyleduo.switchbutton:library:2.0.0"
deps.tinypinyin = "com.github.promeg:tinypinyin:2.0.3"
ext.deps = deps

def addRepos(RepositoryHandler handler) {
    handler.jcenter()
    handler.google()
    handler.mavenLocal()
    handler.mavenCentral()

    handler.maven() {
        allowInsecureProtocol = true
        url 'https://maven.aliyun.com/repository/google'
    }
    handler.maven {
        allowInsecureProtocol = true
        url "https://jitpack.io"
    }
    handler.maven {
        allowInsecureProtocol = true
        url 'https://oss.sonatype.org/content/repositories/snapshots' }
    handler.maven {
        allowInsecureProtocol = true
        url 'https://dl.bintray.com/kodein-framework/Kodein-DI/' }

    handler.maven {
        allowInsecureProtocol = true
        url 'https://maven.aliyun.com/repository/public/' }
    handler.maven {
        allowInsecureProtocol = true
        url 'https://maven.aliyun.com/repository/google' }
    handler.maven {
        allowInsecureProtocol = true
        url 'https://maven.aliyun.com/repository/jcenter' }
    handler.maven {
        allowInsecureProtocol = true
        url 'http://maven.aliyun.com/nexus/content/groups/public' }
    handler.maven {
        allowInsecureProtocol = true
        url 'https://developer.huawei.com/repo/' }
    handler.maven {
        //融云 maven 仓库地址
        allowInsecureProtocol = true
        url 'https://maven.rongcloud.cn/repository/maven-releases/' }
    handler.maven {
        // 声网美颜
        url 'http://maven.faceunity.com/repository/maven-public/'
        allowInsecureProtocol = true
    }

    handler.flatDir {
        dirs project(':base').file('libs')
    }
}

ext.addRepos = this.&addRepos
