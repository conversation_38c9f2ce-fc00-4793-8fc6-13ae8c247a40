//def componentName = project.getName().startsWith("module_") ? project.getName().substring(0, "module_".length()) :

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
android {
    compileSdkVersion build_versions.target_sdk
    buildToolsVersion build_versions.build_tools

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        versionCode build_versions.versionCode
        versionName build_versions.versionName
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        develop.initWith(debug)
        develop {
            matchingFallbacks = ['develop', 'debug', 'uat', 'release']
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        uat.initWith(release)
        uat {
            matchingFallbacks = ['release']
        }
    }

    lintOptions {
        disable 'InvalidPackage'
        disable "ResourceType"
        abortOnError false
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    dataBinding {
        enabled = true
    }

    viewBinding {
        enabled = true
    }
}

dependencies {
    compileOnly deps.canary.debug
    compileOnly deps.canary.release
    kapt "com.android.databinding:compiler:${versions.android_gradle_plugin}"
}
