<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/rc_destruct_click"
        android:layout_width="125dp"
        android:layout_height="120dp"
        android:layout_marginStart="4.5dp"
        android:layout_marginTop="4.5dp"
        android:layout_marginEnd="4.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/fl_receiver_fire"
        app:layout_constraintStart_toStartOf="@id/fl_send_fire"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="0dp"
        app:layout_goneMarginStart="0dp">

        <TextView
            android:id="@+id/rc_destruct_click_hint"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@drawable/rc_ic_destruct_video_play_selector"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:text="@string/rc_click_to_play"
            android:textColor="@color/rc_text_main_color"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/rc_sight_duration"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:textColor="@color/rc_text_main_color"
            android:textSize="12dp" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_send_fire"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_send_fire"
            android:layout_width="@dimen/rc_margin_size_15"
            android:layout_height="@dimen/rc_margin_size_15"
            android:layout_gravity="center"
            android:src="@drawable/rc_fire" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_receiver_fire"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_receiver_fire"
            android:layout_width="@dimen/rc_margin_size_15"
            android:layout_height="@dimen/rc_margin_size_15"
            android:layout_gravity="center"
            android:src="@drawable/rc_fire" />

        <TextView
            android:id="@+id/tv_receiver_fire"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/rc_image_msg_count_down"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/rc_white_color"
            android:textSize="11dp"
            android:visibility="gone" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>