<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="14dp"
    android:paddingEnd="14dp"
    android:minHeight="52dp">
    <TextView
        android:id="@+id/rc_cs_tv_group_name"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:textColor="#353535"
        android:layout_weight="1"
        android:textSize="16sp" />

    <CheckBox
        android:id="@+id/rc_cs_group_checkBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|end"
        android:layout_weight="0"
        android:focusable="false"
        android:clickable="false"/>
</LinearLayout>
