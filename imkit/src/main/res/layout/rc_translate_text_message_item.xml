<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:background="@color/background">

    <ImageView
        android:id="@+id/tr01"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginEnd="@dimen/dp_8"
        android:src="@drawable/rc_translate_text_message_item_bg_01" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/rc_text"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_223"
            android:padding="@dimen/dp_5"
            android:textColor="@color/white"
            android:textColorLink="@color/rc_main_theme"
            android:textSize="@dimen/rc_font_secondary_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="今天天气好好" />

        <!--    -->
        <TextView
            android:id="@+id/rc_translated_text"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_223"
            android:padding="@dimen/dp_5"
            android:textColor="@color/color_666666"
            android:textColorLink="@color/rc_main_theme"
            android:textSize="@dimen/rc_font_secondary_size"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/rc_text"
            app:layout_constraintTop_toBottomOf="@id/rc_text"
            tools:text="今天天气好好"
            tools:visibility="gone" />

        <ProgressBar
            android:id="@+id/rc_pb_translating"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_6"
            android:maxWidth="@dimen/dp_25"
            android:maxHeight="@dimen/dp_25"
            android:minWidth="@dimen/dp_25"
            android:minHeight="@dimen/dp_25"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/rc_text"
            app:layout_constraintStart_toStartOf="@id/rc_text"
            app:layout_constraintTop_toBottomOf="@id/rc_translated_text"
            tools:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/tr02"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/rc_translate_text_message_item_bg_01" />
</LinearLayout>
