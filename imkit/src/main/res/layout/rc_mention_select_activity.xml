<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#0195ff" >

        <TextView
            android:id="@+id/rc_btn_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/rc_cancel"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="14dp"
            android:clickable="true"
            android:textColor="@android:color/white"
            android:textSize="18sp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TextStyle.Alignment"
            android:text="@string/rc_mention_title_choose_members"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:textColor="@android:color/white"
            android:textSize="20sp"/>

    </RelativeLayout>

    <LinearLayout
        android:focusable="true" android:focusableInTouchMode="true"
        android:layout_width="0px" android:layout_height="0px"/>

    <EditText
        android:id="@+id/rc_edit_text"
        style="@style/EditTextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/rc_search"
        android:singleLine="true"
        android:textSize="14sp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ListView
            android:id="@id/rc_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:divider="@null">
        </ListView>

        <TextView
            android:id="@+id/rc_popup_bg"
            style="@style/TextStyle.Alignment"
            android:layout_width="80.0dip"
            android:layout_height="80.0dip"
            android:layout_gravity="center"
            android:gravity="center"
            android:background="@drawable/rc_sidebar_letter_popup_bg"
            android:textColor="#ffffffff"
            android:textSize="30.0dip"
            android:visibility="invisible" />

        <io.rong.imkit.widget.SideBar
            android:id="@+id/rc_sidebar"
            android:layout_width="20.0dip"
            android:layout_height="fill_parent"
            android:layout_gravity="end|center" />
    </FrameLayout>
</LinearLayout>