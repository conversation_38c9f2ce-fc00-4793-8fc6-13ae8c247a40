<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rc_conversation_item"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_76"
    tools:background="@color/colorTheme">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rc_conversation_portrait_rl"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/rc_margin_size_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <anchor.app.base.view.RoundImageView
                android:id="@+id/rc_conversation_portrait"
                android:layout_width="@dimen/dp_44"
                android:layout_height="@dimen/dp_44"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_pic_default_oval"
                app:is_circle="true" />
        </RelativeLayout>

        <View
            android:id="@+id/top_label_view"
            android:layout_width="@dimen/dp_3"
            android:layout_height="@dimen/dp_44"
            android:background="#9F2AF8"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_portrait_rl" />

        <TextView
            android:id="@+id/rc_conversation_title"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_150"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/rc_text_main_color"
            android:textSize="@dimen/sp_16"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_portrait_rl"
            android:text="张三张三张三张三张三张三张三张三张三" />

        <anchor.app.base.view.LevelLabelView
            android:id="@+id/level_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginStart="@dimen/dp_10"
            app:layout_constraintStart_toEndOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_title" />

        <TextView
            android:id="@+id/rc_conversation_content"
            style="@style/TextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/rc_secondary_color"
            android:textSize="@dimen/sp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/rc_conversation_unread"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            app:layout_constraintTop_toBottomOf="@+id/rc_conversation_title"
            tools:text="你好，朋友！" />

        <TextView
            android:id="@+id/rc_conversation_date"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/rc_auxiliary_color"
            android:textSize="@dimen/sp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_portrait_rl"
            tools:text="3 月 22 日" />

        <ImageView
            android:id="@+id/rc_conversation_no_disturb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:src="@drawable/rc_no_disturb"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/rc_conversation_read_receipt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_2"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:src="@drawable/rc_read_receipt"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/rc_conversation_no_disturb"
            app:layout_goneMarginEnd="@dimen/rc_margin_size_12" />

        <RelativeLayout
            android:id="@+id/rc_conversation_unread"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            app:layout_constraintBottom_toBottomOf="@id/rc_conversation_portrait_rl"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/rc_conversation_unread_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/rc_unread_count_bg_normal" />

            <TextView
                android:id="@+id/rc_conversation_unread_count"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/rc_white_color"
                android:textSize="@dimen/sp_10"
                tools:text="15" />
        </RelativeLayout>

        <View
            android:id="@+id/divider"
            android:layout_width="wrap_content"
            android:layout_height="0.5dp"
            android:layout_marginTop="71.5dp"
            android:background="@color/rc_divider_color"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>