<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="76dp"
    android:layout_gravity="center_vertical"
    android:descendantFocusability="blocksDescendants"
    android:paddingStart="19dp"
    android:paddingEnd="20dp"
    android:orientation="horizontal">

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:paddingTop="15dp">

        <TextView
            android:id="@id/rc_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="2"
            android:textColor="#262626"
            android:textSize="18dp" />

        <View
            android:id="@+id/rc_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_gravity="bottom"
            android:background="#e5e5e5" />

    </FrameLayout>

    <ImageView
        android:id="@id/rc_img"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_alignParentEnd="true"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="18dp"
        android:scaleType="centerCrop" />

</LinearLayout>