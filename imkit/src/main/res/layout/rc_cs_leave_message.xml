<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f0f0f6"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout_head"
        android:layout_width="match_parent"
        android:layout_height="48dip"
        android:background="#0195ff"
        android:orientation="horizontal">

        <Button
            android:id="@+id/rc_btn_cancel"
            android:layout_width="48dp"
            android:layout_height="match_parent"
            android:background="@drawable/rc_cs_back_selector"
            android:drawableStart="@drawable/rc_cs_back_icon"
            android:gravity="center_vertical"
            android:onClick="onHeadLeftButtonClick"
            android:textColor="@android:color/white" />

        <View
            android:layout_width="1dp"
            android:layout_height="24dp"
            android:layout_marginBottom="12dp"
            android:layout_marginTop="12dp"
            android:background="#0083e0" />


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:gravity="center_vertical|start"
            android:maxLength="20"
            android:maxLines="1"
            android:text="@string/rc_cs_leave_message"
            android:textColor="@android:color/white"
            android:textSize="18sp" />
    </LinearLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="14dp"
                android:gravity="center_vertical"
                android:text="@string/rc_cs_please_leave_message"
                android:textColor="#999999"
                android:textSize="14sp" />

            <LinearLayout
                android:id="@+id/rc_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
        </LinearLayout>
            <TextView
                android:id="@+id/rc_submit_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/rc_cs_leave_message_btn"
                android:textColor="@android:color/white"
                android:text="@string/rc_cs_submit_message"
                android:gravity="center"/>
    </LinearLayout>
    </ScrollView>


</LinearLayout>
