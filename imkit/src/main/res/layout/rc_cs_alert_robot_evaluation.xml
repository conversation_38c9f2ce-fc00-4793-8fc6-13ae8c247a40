<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/white"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:paddingTop="22dp"
        android:text="@string/rc_cs_evaluate_robot"
        android:textColor="@android:color/black"
        android:textSize="17sp" />

    <LinearLayout
        android:id="@+id/rc_cs_yes_no"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="22dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/rc_cs_yes_selector" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:src="@drawable/rc_cs_no_selector" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/rc_divider_color"
        android:layout_marginTop="20dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@id/rc_btn_ok"
            style="@style/TextStyle.Alignment"
            android:layout_width="135dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/rc_cs_submit"
            android:textColor="#0099ff"
            android:textSize="17sp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/rc_divider_color" />

        <TextView
            android:id="@id/rc_btn_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="135dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/rc_cs_cancel"
            android:textColor="#0099ff"
            android:textSize="17sp" />
    </LinearLayout>
</LinearLayout>