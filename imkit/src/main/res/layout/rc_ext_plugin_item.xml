<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/rc_ext_plugin_icon"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_gravity="center_horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3"
            tools:background="@color/app_color_black"
            tools:layout_width="100dp" />

        <TextView
            android:id="@+id/rc_ext_plugin_title"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/rc_margin_size_8"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="75dp"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rc_ext_plugin_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>