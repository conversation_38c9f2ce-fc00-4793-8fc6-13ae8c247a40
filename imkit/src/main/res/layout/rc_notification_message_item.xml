<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/rc_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/rc_margin_size_12"
        android:textColor="@color/rc_secondary_color"
        android:textSize="@dimen/rc_font_describe_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/rc_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/rc_margin_size_12"
        android:layout_marginTop="@dimen/rc_margin_size_12"
        android:layout_marginEnd="@dimen/rc_margin_size_12"
        android:layout_marginBottom="@dimen/rc_margin_size_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rc_time"
        app:layout_goneMarginTop="@dimen/rc_margin_size_12" />
</androidx.constraintlayout.widget.ConstraintLayout>