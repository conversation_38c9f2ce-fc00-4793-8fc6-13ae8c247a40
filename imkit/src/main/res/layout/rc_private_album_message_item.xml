<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/CLAlbum"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@drawable/shape_private_albm_bg"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"
            android:layout_marginTop="@dimen/dp_10"
            android:src="@mipmap/ic_pic_default_oval"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:layout_width="@dimen/dp_10"
            android:layout_height="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_2"
            android:layout_marginBottom="@dimen/dp_2"
            android:background="@drawable/shape_online_dot"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintEnd_toEndOf="@id/iv_avatar">

            <View
                android:id="@+id/online_dot"
                android:layout_width="@dimen/dp_8"
                android:layout_height="@dimen/dp_8"
                android:layout_gravity="center"
                android:background="@drawable/shape_online_dot" />
        </FrameLayout>


        <TextView
            android:id="@+id/tv_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_avatar"
            tools:text="李生" />


        <TextView
            android:id="@+id/tv_age"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_age_bg"
            android:paddingHorizontal="@dimen/dp_4"
            android:paddingVertical="@dimen/dp_2"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_11"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintStart_toStartOf="@id/tv_nickname"
            tools:text="26" />

        <TextView
            android:id="@+id/tv_country"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:background="@drawable/shape_country_bg"
            android:paddingHorizontal="@dimen/dp_4"
            android:paddingVertical="@dimen/dp_2"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_11"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintStart_toEndOf="@id/tv_age"
            tools:text="26" />

        <TextView
            android:id="@+id/anchor_signature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_avatar"
            app:layout_goneMarginTop="@dimen/dp_8"
            tools:text="fadfjladfal"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_follow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_vertical|end"
            android:background="@drawable/shape_follow_btn_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_4"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_avatar"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_follow"
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_7"
                android:src="@mipmap/ic_follow" />

            <TextView
                android:id="@+id/tv_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:includeFontPadding="false"
                android:text="@string/btn_follow"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/LLAlbum"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/anchor_signature">

            <anchor.app.base.view.RoundImageView
                android:id="@+id/album00"
                android:layout_width="@dimen/dp_57"
                android:layout_height="@dimen/dp_72"
                android:background="@drawable/rc_conversation_fragment_bg_03"
                android:scaleType="centerCrop"
                app:corner_radius="@dimen/dp_4" />


            <anchor.app.base.view.RoundImageView
                android:id="@+id/album01"
                android:layout_width="@dimen/dp_57"
                android:layout_height="@dimen/dp_72"
                android:layout_marginStart="@dimen/dp_6"
                android:background="@drawable/rc_conversation_fragment_bg_03"
                android:scaleType="centerCrop"
                app:corner_radius="@dimen/dp_4" />

            <anchor.app.base.view.RoundImageView
                android:id="@+id/album02"
                android:layout_width="@dimen/dp_57"
                android:layout_height="@dimen/dp_72"
                android:layout_marginStart="@dimen/dp_6"
                android:background="@drawable/rc_conversation_fragment_bg_03"
                android:scaleType="centerCrop"
                app:corner_radius="@dimen/dp_4" />

            <anchor.app.base.view.RoundImageView
                android:id="@+id/album03"
                android:layout_width="@dimen/dp_57"
                android:layout_height="@dimen/dp_72"
                android:layout_marginStart="@dimen/dp_6"
                android:background="@drawable/rc_conversation_fragment_bg_03"
                android:scaleType="centerCrop"
                app:corner_radius="@dimen/dp_4" />

            <anchor.app.base.view.RoundImageView
                android:id="@+id/album04"
                android:layout_width="@dimen/dp_57"
                android:layout_height="@dimen/dp_72"
                android:layout_marginStart="@dimen/dp_6"
                android:background="@drawable/rc_conversation_fragment_bg_03"
                android:scaleType="centerCrop"
                app:corner_radius="@dimen/dp_4" />

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_19"
                android:layout_gravity="center_vertical"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_19"
                    android:layout_gravity="end"
                    android:src="@drawable/rc_conversation_fragment_bg_02" />
            </FrameLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
