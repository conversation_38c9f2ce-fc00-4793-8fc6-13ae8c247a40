<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rc_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/rc_voice_bg"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:adjustViewBounds="true"
        android:gravity="center_vertical"
        android:maxWidth="230dp"
        android:minWidth="52dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/rc_voice"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/rc_voice_receive_play3"
            tools:visibility="visible"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/rc_duration"
            style="@style/TextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/rc_voice_send"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/rc_voice_send_play3"
            tools:visibility="visible"
            app:tint="@color/colorWhite"
            android:tint="@color/colorWhite"
            android:visibility="gone"
            tools:ignore="UseAppTint" />
    </LinearLayout>

    <ImageView
        android:id="@+id/rc_voice_unread"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/rc_voice_unread" />

    <ImageView
        android:id="@+id/rc_voice_download_error"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/rc_voice_hq_message_download_error"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/rc_download_progress"
        style="?android:attr/progressBarStyle"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:indeterminateDrawable="@drawable/rc_hq_voice_message_downloading_style"
        android:visibility="gone" />
</LinearLayout>