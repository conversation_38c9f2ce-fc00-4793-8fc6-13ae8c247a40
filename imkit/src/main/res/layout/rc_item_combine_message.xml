<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rc_message"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        style="@style/TextStyle.Alignment"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="7dp"
        android:layout_marginBottom="2dp"
        android:text="title"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/rc_text_main_color"
        android:textSize="17dp" />

    <TextView
        android:id="@+id/summary"
        style="@style/TextStyle.Alignment"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="9dp"
        android:ellipsize="end"
        android:maxLines="4"
        android:text="summary"
        android:textColor="@color/rc_secondary_color"
        android:textSize="12dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="2dp"
        android:background="@color/rc_dialog_bottom_selection_separate_color" />

    <TextView
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="9dp"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp"
        android:text="@string/rc_combine_chat_history"
        android:textColor="@color/rc_secondary_color"
        android:textSize="12dp" />

</LinearLayout>