<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:background="@drawable/rc_popup_dialog_corner_style"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_popup_dialog_prompt_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="48dp">


        <TextView
            android:id="@+id/popup_dialog_message"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/rc_secondary_color"
            android:paddingStart="19dp"
            android:includeFontPadding="false"
            android:textSize="17sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/popup_dialog_prompt_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:paddingTop="49dp"
        android:paddingBottom="24dp"
        android:paddingEnd="19dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/popup_dialog_button_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:includeFontPadding="false"
            android:text="@string/rc_cancel"
            android:textColor="@color/rc_popup_dialog_prompt_cancel_color"/>

        <TextView
            android:id="@+id/popup_dialog_button_ok"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="19dp"
            android:textSize="16sp"
            android:includeFontPadding="false"
            android:text="@string/rc_clear"
            android:textColor="@color/rc_popup_dialog_prompt_clear_color" />

    </LinearLayout>


</LinearLayout>