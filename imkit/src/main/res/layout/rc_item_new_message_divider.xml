<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rc_msg"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:paddingStart="39dp"
    android:paddingEnd="39dp">

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:background="@color/rc_notification_bg"
        android:gravity="center_vertical" />

    <TextView
        android:id="@+id/tv_divider_message"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="9dp"
        android:layout_marginEnd="9dp"
        android:textColor="@color/rc_new_message_divider_text_color"
        android:textSize="12sp" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:background="@color/rc_notification_bg"
        android:gravity="center_vertical" />

</LinearLayout>

