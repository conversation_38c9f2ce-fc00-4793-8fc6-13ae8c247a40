<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="64dp"
    android:background="@android:color/transparent"
    android:gravity="center_vertical"
    android:minHeight="45dp"
    android:orientation="horizontal"
    android:padding="10dp">


    <ImageView
        android:id="@+id/portrait"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_gravity="center_vertical"
        android:scaleType="fitXY"
        android:src="@drawable/rc_default_portrait" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            style="@style/RCTheme.Message.TextView"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp" />

        <TextView
            android:id="@+id/introduction"
            style="@style/RCTheme.Message.RichContent.TextView"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="3dp" />

    </LinearLayout>
</LinearLayout>
