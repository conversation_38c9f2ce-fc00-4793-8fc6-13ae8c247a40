<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/rc_view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:overScrollMode="never"
        android:scrollbarSize="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/rc_indicator"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/rc_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rc_view_pager" />
</androidx.constraintlayout.widget.ConstraintLayout>
