<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rc_popup_dialog_corner_style"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_popup_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp">

        <TextView
            android:id="@+id/popup_dialog_title"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:paddingStart="19dp"
            android:singleLine="true"
            android:textColor="@color/rc_popup_dialog_text_color"
            android:textSize="20sp"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_popup_dialog_prompt_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp">

        <TextView
            android:id="@+id/popup_dialog_message"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:paddingStart="19dp"
            android:paddingEnd="19dp"
            android:textColor="@color/rc_popup_dialog_text_color"
            android:textSize="17sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/popup_dialog_prompt_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal"
        android:paddingBottom="12dp"
        android:paddingEnd="5dp"
        android:paddingTop="12dp">

        <TextView
            android:id="@+id/popup_dialog_button_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:padding="12dp"
            android:text="@string/rc_cancel"
            android:textColor="@color/rc_popup_dialog_prompt_cancel_color"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/popup_dialog_button_ok"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:padding="12dp"
            android:text="@string/rc_confirm"
            android:textColor="@color/rc_popup_dialog_prompt_ok_color"
            android:textSize="16sp" />

    </LinearLayout>


</LinearLayout>