<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/rc_corner_style"
    android:paddingStart="4dp"
    android:paddingTop="4dp"
    android:paddingEnd="4dp"
    android:paddingBottom="4dp">

    <TextView
        android:layout_gravity="center"
        android:id="@+id/rc_msg"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/rc_font_text_third_size"
        android:textColor="@color/rc_text_color_primary_inverse"/>
</FrameLayout>

