<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/white"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/rc_cs_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:paddingTop="22dp"
        android:textColor="@android:color/black"
        android:textSize="17sp" />

    <View
        android:layout_height="0.5dp"
        android:layout_width="match_parent"
        android:background="@color/rc_divider_color"
        android:layout_marginTop="20dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@id/rc_btn_ok"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/rc_dialog_ok"
            android:textColor="#0099ff"
            android:textSize="17sp" />
    </LinearLayout>
</LinearLayout>