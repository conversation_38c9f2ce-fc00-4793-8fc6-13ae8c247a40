<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_window_focused="false">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white" />
            <corners android:topLeftRadius="6dp" android:topRightRadius="6dp" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_list_item_hover" />
            <corners android:topLeftRadius="6dp" android:topRightRadius="6dp" />
        </shape>
    </item>

</selector>