package io.rong.imkit;

import android.app.Application;
import android.content.Context;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;

import java.util.ArrayList;

import anchor.app.base.BuildConfig;
import anchor.app.base.IComponentApplication;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.parsemessage.MikChatAskGiftMessage;
import io.rong.imkit.parsemessage.MikChatGiftMessage;
import io.rong.imkit.parsemessage.MikChatMessage;
import io.rong.imkit.parsemessage.MikChatPrivateAlbumMessage;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.InitOption;
import io.rong.imlib.model.MessageContent;
import io.rong.push.RongPushClient;
import io.rong.push.pushconfig.PushConfig;

public class ImkitApplicationImpl implements IComponentApplication {
    @Override
    public void initBefore(Application application) {

//        google  fcm 推送
        PushConfig config = new PushConfig.Builder().enableFCM(true).build();
        RongPushClient.setPushConfig(config);

        RongIMClient.setServerInfo("navsg01.cn.ronghub.com", null);

        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
        myMessages.add(MikChatMessage.class);
        myMessages.add(MikChatGiftMessage.class);
        myMessages.add(MikChatAskGiftMessage.class);
        myMessages.add(MikChatPrivateAlbumMessage.class);
        RongIMClient.registerMessageType(myMessages);

        //融云聊天
        String appKey;
//        String appKey = "vnroth0kvg2qo"; // 测试环境
//        String appKey = "25wehl3u2viqw"; // 正式环境
        if (BuildConfig.DEBUG) {
            appKey = "sfci50a7sy39i"; // 测试环境
//             appKey = "sfci50a7sy39i"; // 正式环境
        } else {
            appKey = "qd46yzrfqa5mf"; // 正式环境
//            appKey = "sfci50a7sy39i"; // 测试环境
        }

        //融云推送
//        RongCoreClient.init(application, appKey);
        InitOption.AreaCode areaCode = InitOption.AreaCode.SG;
        InitOption initOption = new InitOption.Builder().setAreaCode(areaCode).build();
        IMCenter.init(application, appKey, initOption);

        RongConfigCenter.featureConfig().setKitImageEngine(new GlideKitImageEngine() {
            @Override
            public void loadConversationPortrait(@NonNull Context context, @NonNull String url, @NonNull ImageView imageView, io.rong.imlib.model.Message message) {
                if (url != null && url.trim().length() > 0) {
                    Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(new CircleCrop())).into(imageView);
                }
            }
        });
    }

    @Override
    public void initAfter(Application application) {
    }
}
