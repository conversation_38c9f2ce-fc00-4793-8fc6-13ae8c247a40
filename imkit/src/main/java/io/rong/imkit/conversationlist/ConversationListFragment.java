package io.rong.imkit.conversationlist;

import static com.uber.autodispose.AutoDispose.autoDisposable;
import static anchor.app.base.core.Constants.HTTP_URL.follow_flag;
import static anchor.app.base.core.Constants.HTTP_URL.rongcloud_token;
import static anchor.app.base.core.Constants.HTTP_URL.rongcloud_token_translate;
import static anchor.app.base.core.Constants.HTTP_URL.user_simple;

import android.annotation.SuppressLint;
import android.app.NotificationManager;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.tbruyelle.rxpermissions2.RxPermissions;
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider;

import java.util.ArrayList;
import java.util.List;

import anchor.app.base.bean.LoginBean;
import anchor.app.base.dialog.SelectTipDialog;
import anchor.app.base.ext.rxweaver.RxErrorUtil;
import anchor.app.base.manager.UserInfoManager;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultMap;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.GsonUtil;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.SharePreUtil;
import io.reactivex.Flowable;
import io.rong.common.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.R;
import io.rong.imkit.RongIM;
import io.rong.imkit.bean.UserSimpleBean;
import io.rong.imkit.config.ConversationListBehaviorListener;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.conversationlist.model.BaseUiConversation;
import io.rong.imkit.conversationlist.model.GatheredConversation;
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel;
import io.rong.imkit.event.Event;
import io.rong.imkit.model.NoticeContent;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imkit.widget.FixedLinearLayoutManager;
import io.rong.imkit.widget.adapter.BaseAdapter;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imkit.widget.dialog.OptionsPopupDialog;
import io.rong.imkit.widget.refresh.SmartRefreshLayout;
import io.rong.imkit.widget.refresh.api.RefreshLayout;
import io.rong.imkit.widget.refresh.constant.RefreshState;
import io.rong.imkit.widget.refresh.listener.OnLoadMoreListener;
import io.rong.imkit.widget.refresh.listener.OnRefreshListener;
import io.rong.imkit.widget.refresh.wrapper.RongRefreshHeader;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.UserInfo;
import io.rong.imlib.translation.TranslationClient;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.dialog.SelectTipDialog;
import anchor.app.base.ext.rxweaver.RxErrorUtil;
import anchor.app.base.manager.UserInfoManager;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultMap;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.GsonUtil;
import anchor.app.base.utils.Logger;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 信息列表
 */
public class ConversationListFragment extends Fragment implements BaseAdapter.OnItemClickListener {
    /*
     * 连接通知状态延迟显示时间。
     * 为了防止连接闪断，不会在断开连接时立即显示连接通知状态，而是在延迟一定时间后显示。
     */
    protected final long NOTICE_SHOW_DELAY_MILLIS = 4000L;
    private final String TAG = ConversationListFragment.class.getSimpleName();
    protected ConversationListAdapter mAdapter;
    protected RecyclerView mList;
    protected View mNoticeContainerView;
    protected TextView mNoticeContentTv;
    protected ImageView mNoticeIconIv;
    protected ConversationListViewModel mConversationListViewModel;
    protected SmartRefreshLayout mRefreshLayout;
    protected Handler mHandler = new Handler(Looper.getMainLooper());
    protected int mNewState = RecyclerView.SCROLL_STATE_IDLE;
    protected boolean delayRefresh = false;
    protected RxPermissions rxPermissions;


    {
        mAdapter = onResolveAdapter();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.rc_conversationlist_fragment, null, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (!IMCenter.getInstance().isInitialized()) {
            RLog.e(TAG, "Please init SDK first!");
            return;
        }
//        rxPermissions = new RxPermissions(this);
//
//        rxPermissions.request(Manifest.permission.POST_NOTIFICATIONS)
//                .as(autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
//                .subscribe(granted -> {
//                    if (!granted) {
//                        SelectTipDialog selectTipDialog = SelectTipDialog.showTips(ConversationListFragment.this.getContext(), this.getString(R.string.b24), getResources().getString(R.string.Cancel),
//                                "\"SoulPair\" wants notification access",
//                                "If do not , you cannot receive messages in a timely manner", new SelectTipDialog.ISimpleTipsClickListener() {
//
//                                    @Override
//                                    public void confirmClick() {
//                                        Intent intent = new Intent();
//                                        intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
//                                        intent.putExtra("android.provider.extra.APP_PACKAGE", ConversationListFragment.this.getContext().getPackageName());
//                                        startActivity(intent);
//
//                                    }
//
//                                    @Override
//                                    public void concelClick() {
//
//                                    }
//                                });
//                        selectTipDialog.setCanceledOnTouchOutside(false);
//                    } else {}
//                });

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            boolean isEnabled = getActivity().getSystemService(NotificationManager.class).areNotificationsEnabled();
            if (!isEnabled) {
                SelectTipDialog selectTipDialog = SelectTipDialog.showTips(ConversationListFragment.this.getContext(), this.getString(R.string.b24), getResources().getString(R.string.Cancel), "\"MindMate\" wants notification", "If do not , you cannot receive messages in a timely manner", new SelectTipDialog.ISimpleTipsClickListener() {

                    @Override
                    public void confirmClick() {
                        Intent intent = new Intent();
                        intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
                        intent.putExtra("android.provider.extra.APP_PACKAGE", ConversationListFragment.this.getContext().getPackageName());
                        startActivity(intent);
                    }

                    @Override
                    public void concelClick() {
                        getActivity().finish();
                    }
                });
                selectTipDialog.setCanceledOnTouchOutside(false);
            }
        }


        TranslationClient.getInstance().addTranslationResultListener((code, result) -> {
            if (code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_INVALID_AUTH_TOKEN.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_AUTH_FAILED.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_SERVER_AUTH_FAILED.code) {
                if (getView() != null) {
                    MutableLiveData<String> tokenTranslate = getTokenTranslate(getActivity());
                    tokenTranslate.observe(getViewLifecycleOwner(), token -> TranslationClient.getInstance().updateAuthToken(token));
                }
            }
        });


        MutableLiveData<String> tokenTranslate = getTokenTranslate(getActivity());
        tokenTranslate.observe(getViewLifecycleOwner(), token -> TranslationClient.getInstance().updateAuthToken(token));

        MutableLiveData<String> token = getToken(getActivity());
        token.observe(getViewLifecycleOwner(), token1 -> RongIM.connect(token1, new RongIMClient.ConnectCallback() {
            //                RongIM.connect("ws9/e02xdsBnshtVtIZyNQ3Fs61CDDV1EjOAz0am8n/<EMAIL>;srtp.sg.rongcfg.com", new RongIMClient.ConnectCallback() {
            @Override
            public void onSuccess(String t) {
                SharePreUtil.setRongToken(token1);
                System.out.println("ConnectCallback onSuccess");
                LoginBean loginBean = UserInfoManager.Companion.user();
                UserInfo userInfo = new UserInfo(loginBean.getId().toString(), loginBean.getNickName(), Uri.parse(loginBean.getHeadFileName()));
                userInfo.setExtra(GsonUtil.GsonString(loginBean));
                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo);
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_AUTOMATIC_ONLINE, 0);
            }

            @Override
            public void onError(RongIMClient.ConnectionErrorCode e) {
                System.out.println("ConnectCallback onError" + e.name() + "  : " + e.getValue());
            }

            @Override
            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
                System.out.println("ConnectCallback onDatabaseOpened");
            }
        }));


        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE03, Integer.class).observeOn(RxSchedulers.INSTANCE.getUi()).as(autoDisposable(AndroidLifecycleScopeProvider.from((FragmentActivity) getContext(), Lifecycle.Event.ON_DESTROY))).subscribe(hashMapStr -> {
            LoginBean loginBean = UserInfoManager.Companion.user();
            if (loginBean != null) {
                UserInfo userInfo = new UserInfo(loginBean.getId().toString(), loginBean.getNickName(), Uri.parse(loginBean.getHeadFileName()));
                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo);
            }
        }, throwable -> Logger.e(throwable.getMessage()));

        initData(view);
    }

    private void initData(View view) {
//        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, Long::class.java)
//                .observeOn(RxSchedulers.ui)
//                .`as`(autoDisposable(scopeProvider))
//                .subscribe { id ->
//                RouteUtils.routeToConversationActivity(this, Conversation.ConversationType.PRIVATE, id.toString(), false);
//        }

        mList = view.findViewById(R.id.rc_conversation_list);
        mRefreshLayout = view.findViewById(R.id.rc_refresh);

        mAdapter.setItemClickListener(this);
        LinearLayoutManager layoutManager = new FixedLinearLayoutManager(getActivity());
        mList.setLayoutManager(layoutManager);
        mList.setAdapter(mAdapter);
        mList.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                mNewState = newState;
                if (mNewState == RecyclerView.SCROLL_STATE_IDLE && delayRefresh && mAdapter != null && mConversationListViewModel != null) { // 滚动停止
                    delayRefresh = false;
                    mAdapter.setDataCollection(mConversationListViewModel.getConversationListLiveData().getValue());
                }
            }
        });
        mNoticeContainerView = view.findViewById(R.id.rc_conversationlist_notice_container);
        mNoticeContentTv = view.findViewById(R.id.rc_conversationlist_notice_tv);
        mNoticeIconIv = view.findViewById(R.id.rc_conversationlist_notice_icon_iv);

        initRefreshView();
        subscribeUi();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mConversationListViewModel != null) {
            mConversationListViewModel.clearAllNotification();
        }
    }

    /**
     * 初始化刷新模块
     */
    protected void initRefreshView() {
        if (mRefreshLayout == null) {
            RLog.d(TAG, "initRefreshView null");
            return;
        }
        mRefreshLayout.setNestedScrollingEnabled(false);
        mRefreshLayout.setRefreshHeader(new RongRefreshHeader(getContext()));
        mRefreshLayout.setRefreshFooter(new RongRefreshHeader(getContext()));
        mRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                onConversationListRefresh(refreshLayout);
            }
        });
        mRefreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                onConversationListLoadMore();
            }
        });
    }

    public interface IIMmitApi {
        @POST(rongcloud_token)
        Flowable<BaseResult<String>> getToken();

        @POST(rongcloud_token_translate)
        Flowable<BaseResult<String>> getTokenTranslate();

        @GET(user_simple)
        Flowable<BaseResult<UserSimpleBean>> getUserSimple(@Query("id") String targetId);

        @POST(follow_flag)
        Flowable<BaseResult<String>> getFollowFlag(@Query("id") String targetId);

    }


    public AndroidLifecycleScopeProvider scopeProvider = AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY);


    @SuppressLint("AutoDispose")
    public MutableLiveData<String> getToken(FragmentActivity activity) {
        MutableLiveData<String> bindStatus = new MutableLiveData<>();
        BaseApiClient.getInstance().create(IIMmitApi.class).getToken().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi()).compose(RxErrorUtil.handleGlobalError(activity)).map(new ResultMap<>()).as(autoDisposable(scopeProvider)).subscribe(bindStatus::setValue, throwable -> bindStatus.setValue(null));
        return bindStatus;
    }

    @SuppressLint("AutoDispose")
    public MutableLiveData<String> getTokenTranslate(FragmentActivity activity) {
        MutableLiveData<String> bindStatus = new MutableLiveData<>();
        BaseApiClient.getInstance().create(IIMmitApi.class).getTokenTranslate().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi()).compose(RxErrorUtil.handleGlobalError(activity)).map(new ResultMap<>()).as(autoDisposable(scopeProvider)).subscribe(bindStatus::setValue, throwable -> bindStatus.setValue(null));
        return bindStatus;
    }

    /**
     * 观察 view model 各数据以便进行页面刷新操作。
     */
    protected void subscribeUi() {
        // 会话列表数据监听
        mConversationListViewModel = new ViewModelProvider(this).get(ConversationListViewModel.class);
        mConversationListViewModel.getConversationList(false, false, 0);
        mConversationListViewModel.getConversationListLiveData().observe(getViewLifecycleOwner(), new Observer<List<BaseUiConversation>>() {
            @Override
            public void onChanged(List<BaseUiConversation> uiConversations) {
                RLog.d(TAG, "conversation list onChanged.");
                if (mNewState == RecyclerView.SCROLL_STATE_IDLE) {
                    mAdapter.setDataCollection(uiConversations);
                } else {
                    delayRefresh = true;
                }
            }
        });
        // 连接状态监听
        mConversationListViewModel.getNoticeContentLiveData().observe(getViewLifecycleOwner(), new Observer<NoticeContent>() {
            @Override
            public void onChanged(NoticeContent noticeContent) {
                // 当连接通知没有显示时，延迟进行显示，防止连接闪断造成画面闪跳。
                if (mNoticeContainerView.getVisibility() == View.GONE) {
                    mHandler.postDelayed(() -> {
                        // 刷新时使用最新的通知内容
                        updateNoticeContent(mConversationListViewModel.getNoticeContentLiveData().getValue());
                    }, NOTICE_SHOW_DELAY_MILLIS);
                } else {
                    updateNoticeContent(noticeContent);
                }
            }
        });
        // 刷新事件监听
        mConversationListViewModel.getRefreshEventLiveData().observe(getViewLifecycleOwner(), new Observer<Event.RefreshEvent>() {
            @Override
            public void onChanged(Event.RefreshEvent refreshEvent) {
                if (refreshEvent.state.equals(RefreshState.LoadFinish)) {
                    if (mRefreshLayout != null) {
                        mRefreshLayout.finishLoadMore();
                    } else {
                        RLog.d(TAG, "onChanged finishLoadMore error");
                    }
                } else if (refreshEvent.state.equals(RefreshState.RefreshFinish)) {
                    if (mRefreshLayout != null) {
                        mRefreshLayout.finishRefresh();
                    } else {
                        RLog.d(TAG, "onChanged finishRefresh error");
                    }
                }
            }
        });
    }

    protected void onConversationListRefresh(RefreshLayout refreshLayout) {
        if (mConversationListViewModel != null) {
            mConversationListViewModel.getConversationList(false, true, 0);
        }
    }

    protected void onConversationListLoadMore() {
        if (mConversationListViewModel != null) {
            mConversationListViewModel.getConversationList(true, true, 0);
        }
    }

    /**
     * 更新连接状态通知栏
     *
     * @param content
     */
    protected void updateNoticeContent(NoticeContent content) {
        if (content == null) return;

        if (content.isShowNotice()) {
            mNoticeContainerView.setVisibility(View.VISIBLE);
            mNoticeContentTv.setText(content.getContent());
            if (content.getIconResId() != 0) {
                mNoticeIconIv.setImageResource(content.getIconResId());
            }
        } else {
            mNoticeContainerView.setVisibility(View.GONE);
        }
    }

    /**
     * 获取 adapter. 可复写此方法实现自定义 adapter.
     *
     * @return 会话列表 adapter
     */
    protected ConversationListAdapter onResolveAdapter() {
        mAdapter = new ConversationListAdapter();
        mAdapter.setEmptyView(R.layout.rc_conversationlist_empty_view);
        return mAdapter;
    }

    /**
     * 会话列表点击事件回调
     *
     * @param view     点击 view
     * @param holder   {@link ViewHolder}
     * @param position 点击位置
     */
    @Override
    public void onItemClick(View view, ViewHolder holder, int position) {
        if (position < 0) {
            return;
        }
        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0);
        BaseUiConversation baseUiConversation = mAdapter.getItem(position);
        ConversationListBehaviorListener listBehaviorListener = RongConfigCenter.conversationListConfig().getListener();
        if (listBehaviorListener != null && listBehaviorListener.onConversationClick(view.getContext(), view, baseUiConversation)) {
            RLog.d(TAG, "ConversationList item click event has been intercepted by App.");
            return;
        }
        if (baseUiConversation != null && baseUiConversation.mCore != null) {
            if (baseUiConversation instanceof GatheredConversation) {
                RouteUtils.routeToSubConversationListActivity(view.getContext(), ((GatheredConversation) baseUiConversation).mGatheredType, baseUiConversation.mCore.getConversationTitle());
            } else {
                RouteUtils.routeToConversationActivity(view.getContext(), baseUiConversation.getConversationIdentifier());
            }
        } else {
            RLog.e(TAG, "invalid conversation.");
        }
    }

    /**
     * 会话列表长按事件回调
     *
     * @param view     点击 view
     * @param holder   {@link ViewHolder}
     * @param position 点击位置
     * @return 事件是否被消费
     */
    @Override
    public boolean onItemLongClick(final View view, ViewHolder holder, int position) {
        if (position < 0) {
            return false;
        }
        final BaseUiConversation baseUiConversation = mAdapter.getItem(position);
        ConversationListBehaviorListener listBehaviorListener = RongConfigCenter.conversationListConfig().getListener();
        if (listBehaviorListener != null && listBehaviorListener.onConversationLongClick(view.getContext(), view, baseUiConversation)) {
            RLog.d(TAG, "ConversationList item click event has been intercepted by App.");
            return true;
        }
        final ArrayList<String> items = new ArrayList<>();
        final String removeItem = view.getContext().getResources().getString(R.string.rc_conversation_list_dialog_remove);
        final String setTopItem = view.getContext().getResources().getString(R.string.rc_conversation_list_dialog_set_top);
        final String cancelTopItem = view.getContext().getResources().getString(R.string.rc_conversation_list_dialog_cancel_top);

        if (!(baseUiConversation instanceof GatheredConversation)) {
            if (baseUiConversation.mCore.isTop()) {
                items.add(cancelTopItem);
            } else {
                items.add(setTopItem);
            }
        }
        items.add(removeItem);
        int size = items.size();
        OptionsPopupDialog.newInstance(view.getContext(), items.toArray(new String[size])).setOptionsPopupDialogListener(new OptionsPopupDialog.OnOptionsItemClickedListener() {
            @Override
            public void onOptionsItemClicked(final int which) {
                if (items.get(which).equals(setTopItem) || items.get(which).equals(cancelTopItem)) {
                    IMCenter.getInstance().setConversationToTop(baseUiConversation.getConversationIdentifier(), !baseUiConversation.mCore.isTop(), false, new RongIMClient.ResultCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean value) {
                            Toast.makeText(view.getContext(), items.get(which), Toast.LENGTH_SHORT).show();
                        }

                        @Override
                        public void onError(RongIMClient.ErrorCode errorCode) {
                            // do nothing
                        }
                    });
                } else if (items.get(which).equals(removeItem)) {
                    IMCenter.getInstance().removeConversation(baseUiConversation.mCore.getConversationType(), baseUiConversation.mCore.getTargetId(), null);
                }
            }
        }).show();
        return true;
    }

    /**
     * @param view 自定义列表 header view
     */
    public void addHeaderView(View view) {
        mAdapter.addHeaderView(view);
    }

    /**
     * @param view 自定义列表 footer view
     */
    public void addFooterView(View view) {
        mAdapter.addFootView(view);
    }

    /**
     * @param view 自定义列表 空数据 view
     */
    public void setEmptyView(View view) {
        mAdapter.setEmptyView(view);
    }

    /**
     * @param emptyId 自定义列表 空数据的 LayoutId
     */
    public void setEmptyView(@LayoutRes int emptyId) {
        mAdapter.setEmptyView(emptyId);
    }
}
