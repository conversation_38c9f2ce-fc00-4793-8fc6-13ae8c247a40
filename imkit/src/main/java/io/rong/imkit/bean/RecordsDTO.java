package io.rong.imkit.bean;

import java.io.Serializable;

public class RecordsDTO implements Serializable {
    private String onlineStatus;
    private String groundFileName;
    private Long id;
    private String language;
    private String country;
    private String nickName;
    private String roomTitle;
    private String followFlag;
    private String userCode;
    private String headFileName;
    private String duration;
    private String channelID;
    private Integer videoPrice;
    private String level;
    private String role;

    public String getChannelID() {
        return channelID;
    }

    public void setChannelID(String channelID) {
        this.channelID = channelID;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getHeadFileName() {
        return headFileName;
    }

    public void setHeadFileName(String headFileName) {
        this.headFileName = headFileName;
    }

    public String getFollowFlag() {
        return followFlag;
    }

    public void setFollowFlag(String followFlag) {
        this.followFlag = followFlag;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }


    public String getGroundFileName() {
        return groundFileName;
    }

    public void setGroundFileName(String groundFileName) {
        this.groundFileName = groundFileName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }


    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getRoomTitle() {
        return roomTitle;
    }

    public void setRoomTitle(String roomTitle) {
        this.roomTitle = roomTitle;
    }

    public String getUserId() {
        return userCode;
    }

    public void setUserId(String userId) {
        this.userCode = userId;
    }

    public Integer getVideoPrice() {
        return videoPrice;
    }

    public void setVideoPrice(Integer videoPrice) {
        this.videoPrice = videoPrice;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
}
