package io.rong.imkit.view.chatTip;

import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.LifecycleObserver;


import java.util.HashMap;

import io.reactivex.functions.Consumer;
import anchor.app.base.bean.ChatTipBean;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.ActivityUtils;
import anchor.app.base.utils.GsonUtil;
import anchor.app.base.utils.RxViewUtils;

/**
 * 私聊顶部提醒管理
 */
public class ChatTipManager implements LifecycleObserver {

    private static ChatTipManager instance = new ChatTipManager();

    private ChatTipView chatTipView;
    private ChatTipBean bean;

    private WindowManager windowManager;

    private ChatTipManager() {
    }

    public static ChatTipManager getInstance() {
        return instance;
    }


    public void show(AppCompatActivity activity, ChatTipView chatTipView, ChatTipBean bean) {
        if (ActivityUtils.isDestroy(activity) || chatTipView == null) {
            return;
        }
        if (windowManager != null) {
            try {
                windowManager.removeViewImmediate(this.chatTipView);
            } catch (Exception e) {

            } finally {
                this.chatTipView = null;
            }
        }
        this.chatTipView = chatTipView;
        this.bean = bean;
        windowManager = activity.getWindowManager();
        activity.getLifecycle().addObserver(this);

        try {
            windowManager.addView(chatTipView, initLayoutParams());
        } catch (Exception e) {
            this.chatTipView = null;
        }

        //设置延迟关闭
        chatTipView.postDelayed(() -> hideAndDestroy(false), ChatTipView.SHOW_TIME);

        RxViewUtils.setOnClickListeners(chatTipView.mFlContainer, new Consumer<View>() {
            @Override
            public void accept(View view) throws Exception {
                hideAndDestroy(false);
//                TalkActivity.router(view.getContext(), chatTipView.getUserId());
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("headFileName",bean.getHeadFileName());
                hashMap.put("nikeName",bean.getNickName());
                hashMap.put("id",bean.getId());
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(hashMap));
            }
        });
    }


    private WindowManager.LayoutParams initLayoutParams() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                0, 0,
                PixelFormat.TRANSPARENT
        );
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.TOP;

        layoutParams.flags =
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | // 不获取焦点，以便于在弹出的时候 下层界面仍然可以进行操作
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                        WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR; // 确保你的内容不会被装饰物(如状态栏)掩盖.
        // popWindow的层级为 TYPE_APPLICATION_PANEL
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL;
        return layoutParams;
    }


    /**
     * 隐藏并销毁
     */
    public void hideAndDestroy(boolean removeNow) {
        if (chatTipView != null) {
            chatTipView.hide(removeNow);
        }
        chatTipView = null;
        windowManager = null;
    }
}
