package io.rong.imkit.view.chatTip;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AnticipateOvershootInterpolator;
import android.view.animation.OvershootInterpolator;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import io.rong.imkit.R;
import anchor.app.base.utils.ImageUtil;
import anchor.app.base.view.RoundImageView;


/**
 * 私聊顶部提醒
 */
public class ChatTipView extends FrameLayout implements SwipeDismissTouchListener.DismissCallbacks {
    private static final long ANIMATION_DURATION = 500;
    //展示时间
    public static final long SHOW_TIME = 3000;

    RoundImageView mIvPic;
    TextView mTvTitle;
    TextView mTvContent;
    FrameLayout mFlContainer;
    LinearLayout mLlCallBack;

    private String userId;

    private int type;

    public ChatTipView(@NonNull Context context) {
        super(context);

        initView(context);
    }

    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.rc_dialog_layout_chat_top_tip, this, false);
        addView(view);

        mIvPic =  view.findViewById(R.id.iv_head_id);
        mTvTitle= view.findViewById(R.id.tv_title_id);
        mTvContent= view.findViewById(R.id.tv_content_id);
        mFlContainer= view.findViewById(R.id.id_fl_container_id);
        mLlCallBack = view.findViewById(R.id.ll_view_callback_id);

        mFlContainer.setOnTouchListener(new SwipeDismissTouchListener(mFlContainer, this));
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        //出现动画
        ObjectAnimator animEnter = ObjectAnimator.ofFloat(mFlContainer, "translationY", -getMeasuredHeight(), 0);
        animEnter.setInterpolator(new OvershootInterpolator());
        animEnter.setDuration(ANIMATION_DURATION);
        animEnter.start();
    }

    public void hide(boolean removeNow) {
        if (!isAttachedToWindow()) {
            return;
        }
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        if (removeNow) {
            if (isAttachedToWindow()) {
                windowManager.removeViewImmediate(this);
            }
            return;
        }
        mFlContainer.setClickable(false);
        ObjectAnimator anim = ObjectAnimator.ofFloat(mFlContainer, "translationY", 0, -getMeasuredHeight());
        anim.setInterpolator(new AnticipateOvershootInterpolator());
        anim.setDuration(ANIMATION_DURATION);
        anim.start();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isAttachedToWindow()) {
                    windowManager.removeViewImmediate(ChatTipView.this);
                }
            }
        }, ANIMATION_DURATION);
    }

    public void setTitle(String title) {
        mTvTitle.setText(title);
    }

    public void setContent(String content) {
        mTvContent.setText(content);
    }

    public void setPic(String url) {
        ImageUtil.displayStaticImage(getContext(), mIvPic, url, R.mipmap.ic_pic_default_oval);
    }

    public void setType(int type) {
        this.type = type;
        switch (type) {
            case 0://房间邀请
                mLlCallBack.setVisibility(View.GONE);
                break;
            default:
                mLlCallBack.setVisibility(View.VISIBLE);
                break;
        }
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    /**
     * Called to determine whether the view can be dismissed.
     *
     * @return boolean The view can dismiss.
     */
    @Override
    public boolean canDismiss() {
        return true;
    }

    /**
     * Called when the user has indicated they she would like to dismiss the view.
     *
     * @param view The originating [View]
     */
    @Override
    public void onDismiss(View view) {
        hide(true);
    }

    /**
     * Called when the user touches the view or release the view.
     *
     * @param view  The originating [View]
     * @param touch The view is being touched.
     */
    @Override
    public void onTouch(View view, Boolean touch) {

    }
}
