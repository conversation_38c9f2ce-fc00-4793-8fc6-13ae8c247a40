package io.rong.imkit.picture.broadcast;

public class BroadcastAction {
    /** 预览界面选中数据Action */
    public static final String ACTION_SELECTED_DATA = "com.luck.picture.lib.action.selected.data";

    /** 关闭预览界面Action */
    public static final String ACTION_CLOSE_PREVIEW = "com.luck.picture.lib.action.close.preview";

    /** 预览界面Action */
    public static final String ACTION_PREVIEW_COMPRESSION =
            "com.luck.picture.lib.action.preview.compression";

    /** 外部预览界面删除图片Action */
    public static final String ACTION_DELETE_PREVIEW_POSITION =
            "com.luck.picture.lib.action.delete_preview_position";
}
