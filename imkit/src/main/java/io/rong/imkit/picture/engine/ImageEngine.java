package io.rong.imkit.picture.engine;

import android.content.Context;
import android.widget.ImageView;
import androidx.annotation.NonNull;

/** 图库加载接口 */
public interface ImageEngine {
    /**
     * 加载图片
     *
     * @param context
     * @param url
     * @param imageView
     */
    void loadImage(@NonNull Context context, @NonNull String url, @NonNull ImageView imageView);

    /**
     * 加载相册目录图片
     *
     * @param context 上下文
     * @param url 图片路径
     * @param imageView 承载图片ImageView
     */
    void loadFolderImage(
            @NonNull Context context, @NonNull String url, @NonNull ImageView imageView);

    /**
     * 加载gif图片
     *
     * @param context 上下文
     * @param url 图片路径
     * @param imageView 承载图片ImageView
     */
    void loadAsGifImage(
            @NonNull Context context, @NonNull String url, @NonNull ImageView imageView);

    /**
     * 加载图片列表图片
     *
     * @param context 上下文
     * @param url 图片路径
     * @param imageView 承载图片ImageView
     */
    void loadGridImage(@NonNull Context context, @NonNull String url, @NonNull ImageView imageView);
}
