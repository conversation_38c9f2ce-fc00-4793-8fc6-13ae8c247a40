package io.rong.imkit.parsemessage;

import android.os.Parcel;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import io.rong.common.ParcelUtils;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

@MessageTag(value = "mikchat:videoCall", flag = MessageTag.ISCOUNTED)
public class MikChatVideoCallMessage extends MessageContent {
    private static final String TAG = "MikChatMessage";
    // 自定义消息变量，可以有多个
    public String type;
    public String userId;
    public String userRole;
    public String callTime;

    private MikChatVideoCallMessage() {
    }

    /**
     * 设置文字消息的内容。
     *{"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     * @param content 文字消息的内容。
     */
//    public void setContent(String content) {
//        this.content = content;
//    }

    /**
     * 构造函数。
     *
     * @param in 初始化传入的 Parcel。
     */
    public MikChatVideoCallMessage(Parcel in) {
        setExtra(ParcelUtils.readFromParcel(in));
//        setContent(ParcelUtils.readFromParcel(in));
        type = ParcelUtils.readFromParcel(in);
        userId = ParcelUtils.readFromParcel(in);
        userRole = ParcelUtils.readFromParcel(in);
        callTime = ParcelUtils.readFromParcel(in);
    }

    // 快速构建消息对象方法
    public static MikChatVideoCallMessage obtain(String userId, String userRole, String callTime) {
        MikChatVideoCallMessage msg = new MikChatVideoCallMessage();
        msg.userId = userId;
        msg.userRole = userRole;
        msg.callTime = callTime;
        return msg;
    }

    /**
     * 创建 MikChatMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容.
     */
    public MikChatVideoCallMessage(byte[] data) {
        if (data == null) {
            return;
        }
        String jsonStr = null;
        try {
            jsonStr = new String(data, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        if (jsonStr == null) {
            Log.e(TAG, "jsonStr is null ");
            return;
        }

        try {
            JSONObject jsonObj = new JSONObject(jsonStr);
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                setUserInfo(parseJsonToUserInfo(jsonObj.getJSONObject("user")));
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                setMentionedInfo(parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo")));
            }


            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("type")) {
                type = jsonObj.optString("type");
            }
            if (jsonObj.has("userId")) {
                userId = jsonObj.optString("userId");
            }

            if (jsonObj.has("userRole")) {
                userRole = jsonObj.optString("userRole");
            }
            if (jsonObj.has("callTime")) {
                callTime = jsonObj.optString("callTime");
            }

            if (jsonObj.has("extra")) setExtra(jsonObj.optString("extra"));


        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     * <p>
     * {"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (getJSONUserInfo() != null) {
                jsonObj.putOpt("user", getJSONUserInfo());
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (getJsonMentionInfo() != null) {
                jsonObj.putOpt("mentionedInfo", getJsonMentionInfo());
            }
            if (!TextUtils.isEmpty(this.getExtra())) {
                jsonObj.put("extra", this.getExtra());
            }
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("type", this.type);
            jsonObj.put("userId", this.userId);
            jsonObj.put("userRole", this.userRole);
            jsonObj.put("callTime", this.callTime);
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        try {
            return jsonObj.toString().getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int i) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, getExtra());
        ParcelUtils.writeToParcel(dest, type);
        ParcelUtils.writeToParcel(dest, userId);
        ParcelUtils.writeToParcel(dest, userRole);
        ParcelUtils.writeToParcel(dest, callTime);
    }

    public static final Creator<MikChatVideoCallMessage> CREATOR =
            new Creator<MikChatVideoCallMessage>() {
                public MikChatVideoCallMessage createFromParcel(Parcel source) {
                    return new MikChatVideoCallMessage(source);
                }

                public MikChatVideoCallMessage[] newArray(int size) {
                    return new MikChatVideoCallMessage[size];
                }
            };
}