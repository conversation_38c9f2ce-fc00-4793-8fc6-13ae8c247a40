package io.rong.imkit.parsemessage;

import android.os.Parcel;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import io.rong.common.ParcelUtils;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

@MessageTag(value = "mikchat:askgift", flag = MessageTag.ISCOUNTED)
public class MikChatAskGiftMessage extends MessageContent {
    private static final String TAG = "MikChatAskGiftMessage";
    // 自定义消息变量，可以有多个
    public String type;
    public String channelId;
    public String giftCode;
    public String userId;
    public String userCategory;
    public String headFileName;
    public String giveNum;
    public String nickName;
    public String userRole;
    public String giftUrl;
    public String giftName;
    public String giftPrice;
    public String giftId;
//    public String giftDirection;//0发送者,1接受者

    private MikChatAskGiftMessage() {}

    /**
     * 设置文字消息的内容。
     *{"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     * @param content 文字消息的内容。
     */
//    public void setContent(String content) {
//        this.content = content;
//    }

    /**
     * 构造函数。
     *
     * @param in 初始化传入的 Parcel。
     */
    public MikChatAskGiftMessage(Parcel in) {
        setExtra(ParcelUtils.readFromParcel(in));
//        setContent(ParcelUtils.readFromParcel(in));
        type = ParcelUtils.readFromParcel(in);
        channelId = ParcelUtils.readFromParcel(in);
        userId = ParcelUtils.readFromParcel(in);
        userCategory = ParcelUtils.readFromParcel(in);
        giftCode = ParcelUtils.readFromParcel(in);
        headFileName = ParcelUtils.readFromParcel(in);
        giveNum = ParcelUtils.readFromParcel(in);
        nickName = ParcelUtils.readFromParcel(in);
        userRole = ParcelUtils.readFromParcel(in);
        giftUrl = ParcelUtils.readFromParcel(in);
        giftName = ParcelUtils.readFromParcel(in);
        giftPrice = ParcelUtils.readFromParcel(in);
        giftId= ParcelUtils.readFromParcel(in);
    }

    // 快速构建消息对象方法
    public static MikChatAskGiftMessage obtain(String type, String channelId, String userId, String userCategory, String giftCode, String headFileName, String giveNum, String nickName, String userRole, String giftUrl, String giftName, String giftPrice, String giftId) {
        MikChatAskGiftMessage msg = new MikChatAskGiftMessage();
        msg.type = type;
        msg.channelId = channelId;
        msg.userId = userId;
        msg.userCategory = userCategory;
        msg.giftCode = giftCode;
        msg.headFileName = headFileName;
        msg.giveNum = giveNum;
        msg.nickName = nickName;
        msg.userRole = userRole;
        msg.giftUrl = giftUrl;
        msg.giftName = giftName;
        msg.giftPrice = giftPrice;
        msg.giftId = giftId;
        return msg;
    }
    // 快速构建消息对象方法
    public static MikChatAskGiftMessage obtain(String nickName,String giftCode,String giveNum,  String giftUrl,  String giftName,  String giftPrice, String giftId) {
        MikChatAskGiftMessage msg = new MikChatAskGiftMessage();
        msg.giftCode = giftCode;
        msg.giveNum = giveNum;
        msg.nickName = nickName;
        msg.giftUrl = giftUrl;
        msg.giftName = giftName;
        msg.giftPrice = giftPrice;
        msg.giftId = giftId;
        return msg;
    }

    /** 创建 MikChatMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容. */
    public MikChatAskGiftMessage(byte[] data) {
        if (data == null) {
            return;
        }
        String jsonStr = null;
        try {
            jsonStr = new String(data, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        if (jsonStr == null) {
            Log.e(TAG, "jsonStr is null ");
            return;
        }

        try {
            JSONObject jsonObj = new JSONObject(jsonStr);
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                setUserInfo(parseJsonToUserInfo(jsonObj.getJSONObject("user")));
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                setMentionedInfo(parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo")));
            }


            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("type")) {
                type = jsonObj.optString("type");
            }
            if (jsonObj.has("channelId")) {
                channelId = jsonObj.optString("channelId");
            }
            if (jsonObj.has("userId")) {
                userId = jsonObj.optString("userId");
            }
            if (jsonObj.has("userCategory")) {
                userCategory = jsonObj.optString("userCategory");
            }
            if (jsonObj.has("giftCode")) {
                giftCode = jsonObj.optString("giftCode");
            }

            if (jsonObj.has("headFileName")) {
                headFileName = jsonObj.optString("headFileName");
            }
            if (jsonObj.has("giveNum")) {
                giveNum = jsonObj.optString("giveNum");
            }
            if (jsonObj.has("nickName")) {
                nickName = jsonObj.optString("nickName");
            }
            if (jsonObj.has("userRole")) {
                userRole = jsonObj.optString("userRole");
            }
            if (jsonObj.has("giftUrl")) {
                giftUrl = jsonObj.optString("giftUrl");
            }
            if (jsonObj.has("giftName")) {
                giftName = jsonObj.optString("giftName");
            }
            if (jsonObj.has("giftPrice")) {
                giftPrice = jsonObj.optString("giftPrice");
            }
            if (jsonObj.has("giftId")) {
                giftId = jsonObj.optString("giftId");
            }

            if (jsonObj.has("extra")) setExtra(jsonObj.optString("extra"));


        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }
    }

    public String getContent() {
        return channelId;
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     *
     * {"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (getJSONUserInfo() != null) {
                jsonObj.putOpt("user", getJSONUserInfo());
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (getJsonMentionInfo() != null) {
                jsonObj.putOpt("mentionedInfo", getJsonMentionInfo());
            }

            if (!TextUtils.isEmpty(this.getExtra())) {
                jsonObj.put("extra", this.getExtra());
            }

            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("type", this.type);
            jsonObj.put("channelId", this.channelId);
            jsonObj.put("userId", this.userId);
            jsonObj.put("userCategory", this.userCategory);
            jsonObj.put("giftCode", this.giftCode);
            jsonObj.put("headFileName", this.headFileName);
            jsonObj.put("giveNum",      this.giveNum);
            jsonObj.put("nickName",     this.nickName);
            jsonObj.put("userRole",     this.userRole);
            jsonObj.put("giftUrl",     this.giftUrl);
            jsonObj.put("giftName",     this.giftName);
            jsonObj.put("giftPrice",     this.giftPrice);
            jsonObj.put("giftId",     this.giftId);
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        try {
            return jsonObj.toString().getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int i) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, getExtra());
        ParcelUtils.writeToParcel(dest, type);
        ParcelUtils.writeToParcel(dest, channelId);
        ParcelUtils.writeToParcel(dest, userId);
        ParcelUtils.writeToParcel(dest, userCategory);
        ParcelUtils.writeToParcel(dest, giftCode);
        ParcelUtils.writeToParcel(dest, headFileName);
        ParcelUtils.writeToParcel(dest, giveNum);
        ParcelUtils.writeToParcel(dest, nickName);
        ParcelUtils.writeToParcel(dest, userRole);
        ParcelUtils.writeToParcel(dest, giftUrl);
        ParcelUtils.writeToParcel(dest, giftName);
        ParcelUtils.writeToParcel(dest, giftPrice);
        ParcelUtils.writeToParcel(dest, giftId);
    }

    public static final Creator<MikChatAskGiftMessage> CREATOR =
            new Creator<MikChatAskGiftMessage>() {
                public MikChatAskGiftMessage createFromParcel(Parcel source) {
                    return new MikChatAskGiftMessage(source);
                }

                public MikChatAskGiftMessage[] newArray(int size) {
                    return new MikChatAskGiftMessage[size];
                }
            };
}