package io.rong.imkit.conversation;

import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import java.util.Locale;

import io.rong.imkit.R;
import io.rong.imkit.activity.RongBaseActivity;
import io.rong.imkit.model.TypingInfo;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imkit.userinfo.model.GroupUserInfo;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imkit.widget.TitleBar;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Group;
import io.rong.imlib.model.UserInfo;
import anchor.app.base.bean.PersonalCenterBean;
import anchor.app.base.manager.OtherManager;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.GsonUtil;


public class RongConversationActivity extends RongBaseActivity {
    protected String mTargetId;
    protected Conversation.ConversationType mConversationType;
    protected ConversationFragment mConversationFragment;
    private ConversationViewModel conversationViewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            mTargetId = getIntent().getStringExtra(RouteUtils.TARGET_ID);
            String type = getIntent().getStringExtra(RouteUtils.CONVERSATION_TYPE);
            if (!TextUtils.isEmpty(type)) {
                mConversationType =
                        Conversation.ConversationType.valueOf(type.toUpperCase(Locale.US));
            } else {
                return;
            }
        }
        setContentView(R.layout.rc_conversation_activity);
        setTitle();
        mConversationFragment =
                (ConversationFragment)
                        getSupportFragmentManager().findFragmentById(R.id.conversation);
        mTitleBar.setOnBackClickListener(
                () -> {
                    if (mConversationFragment != null
                            && !mConversationFragment.onBackPressed()) {
                        finish();
                    }
                });
        mTitleBar.getRightView().setVisibility(View.GONE);
        initViewModel();
        observeUserInfoChange();

        if (mConversationType.getName().equals("group")) {
            mTitleBar.getRightView().setVisibility(View.VISIBLE);
            mTitleBar.setOnRightIconClickListener(new TitleBar.OnRightIconClickListener() {
                @Override
                public void onRightIconClick(View v) {
                    if (mConversationFragment != null) {
                        ComponentName componentName = new ComponentName("com.mobile.anchor.app", "com.mobile.anchor.app.user.grouplist.GroupListActivity");
                        Intent intent = new Intent();
                        intent.setComponent(componentName);
                        intent.putExtra("unionId", Integer.parseInt(mTargetId));
                        startActivity(intent);
//                        ARouter.getInstance().build(ArouterPath.PATH_USER_GROUPLIST)
//                                .withInt("unionId", Integer.parseInt(mTargetId))
//                                .navigation();
                    }
                }
            });


        }

//        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_CONVERSATIONACTIVITY, Integer.class)
//                .observeOn(RxSchedulers.INSTANCE.getUi())
//                .as(autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
//                .subscribe(integer -> {
//                    finish();
//                });
    }

    private void observeUserInfoChange() {
        if (!TextUtils.isEmpty(mTargetId)) {
            RongUserInfoManager.getInstance().addUserDataObserver(mUserDataObserver);
        }
    }

    private final RongUserInfoManager.UserDataObserver mUserDataObserver =
            new RongUserInfoManager.UserDataObserver() {
                @Override
                public void onUserUpdate(UserInfo info) {
                    if (TextUtils.equals(mTargetId, info.getUserId())) {
                        runOnUiThread(
                                new Runnable() {
                                    @Override
                                    public void run() {
                                        setTitle();
                                    }
                                });
                    }
                }

                @Override
                public void onGroupUpdate(Group group) {
                    if (TextUtils.equals(mTargetId, group.getId())) {
                        runOnUiThread(
                                new Runnable() {
                                    @Override
                                    public void run() {
                                        setTitle();
                                    }
                                });
                    }
                }

                @Override
                public void onGroupUserInfoUpdate(GroupUserInfo groupUserInfo) {
                    // do nothing
                }
            };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (!TextUtils.isEmpty(mTargetId)) {
            RongUserInfoManager.getInstance().removeUserDataObserver(mUserDataObserver);
        }
    }

    private void setTitle() {
        if (!TextUtils.isEmpty(mTargetId)
                && mConversationType.equals(Conversation.ConversationType.GROUP)) {
            Group group = RongUserInfoManager.getInstance().getGroupInfo(mTargetId);
            mTitleBar.setTitle(group == null ? mTargetId : group.getName());
        } else {
            UserInfo userInfo = RongUserInfoManager.getInstance().getUserInfo(mTargetId);
            mTitleBar.setTitle(userInfo == null ? mTargetId : userInfo.getName());
        }
        if (mConversationType.equals(Conversation.ConversationType.CUSTOMER_SERVICE)
                || mConversationType.equals(Conversation.ConversationType.CHATROOM)) {
            mTitleBar.setRightVisible(false);
        }
    }

    private void initViewModel() {
        conversationViewModel = new ViewModelProvider(this).get(ConversationViewModel.class);
        conversationViewModel
                .getTypingStatusInfo()
                .observe(
                        this,
                        new Observer<TypingInfo>() {
                            @Override
                            public void onChanged(TypingInfo typingInfo) {
                                if (typingInfo == null) {
                                    return;
                                }
                                if (typingInfo.conversationType == mConversationType
                                        && mTargetId.equals(typingInfo.targetId)) {
                                    if (typingInfo.typingList == null) {
                                        mTitleBar.getMiddleView().setVisibility(View.VISIBLE);
                                        mTitleBar.getTypingView().setVisibility(View.GONE);
                                    } else {
                                        mTitleBar.getMiddleView().setVisibility(View.GONE);
                                        mTitleBar.getTypingView().setVisibility(View.VISIBLE);
                                        TypingInfo.TypingUserInfo typing =
                                                typingInfo.typingList.get(
                                                        typingInfo.typingList.size() - 1);
                                        if (typing.type == TypingInfo.TypingUserInfo.Type.text) {
                                            mTitleBar.setTyping(
                                                    R.string.rc_conversation_remote_side_is_typing);
                                        } else if (typing.type
                                                == TypingInfo.TypingUserInfo.Type.voice) {
                                            mTitleBar.setTyping(
                                                    R.string.rc_conversation_remote_side_speaking);
                                        }
                                    }
                                }
                            }
                        });

        OtherManager.Companion.manager(this).getPersonalCenter(this, Long.parseLong(mTargetId), "2").observe(this, new Observer<PersonalCenterBean>() {
            @Override
            public void onChanged(PersonalCenterBean personalCenterBean) {
                UserInfo userInfo = new UserInfo(personalCenterBean.getId().toString(), personalCenterBean.getNickName(), Uri.parse(personalCenterBean.getHeadFileName()));
                PersonalCenterBean simpleBean = new PersonalCenterBean();
                simpleBean.setNickName(personalCenterBean.getNickName());
                simpleBean.setHeadFileName(personalCenterBean.getHeadFileName());
                simpleBean.setId(personalCenterBean.getId());
                simpleBean.setLevel(personalCenterBean.getLevel());
                simpleBean.setUserCode(personalCenterBean.getUserCode());
                simpleBean.setUsername(personalCenterBean.getUsername());
                userInfo.setExtra(GsonUtil.GsonString(simpleBean));
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_RONGYUN_CACHE, userInfo);
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (KeyEvent.KEYCODE_BACK == event.getKeyCode()) {
            if (mConversationFragment != null && !mConversationFragment.onBackPressed()) {
                finish();
            }
        }
        return false;
    }
}
