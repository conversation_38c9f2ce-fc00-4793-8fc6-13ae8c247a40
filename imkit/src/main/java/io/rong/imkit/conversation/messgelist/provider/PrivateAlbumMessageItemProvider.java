package io.rong.imkit.conversation.messgelist.provider;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;

import com.bumptech.glide.Glide;

import java.util.ArrayList;
import java.util.List;

import io.rong.imkit.R;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.parsemessage.MikChatPrivateAlbumMessage;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.MessageContent;
import anchor.app.base.bean.PersonalCenterBean;

public class PrivateAlbumMessageItemProvider extends BaseNotificationMessageItemProvider<MikChatPrivateAlbumMessage> {

//    public PrivateAlbumMessageItemProvider() {
//        mConfig.showReadState = true;
//    }

    @Override
    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view =
                LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.rc_private_album_message_item, parent, false);
        return new ViewHolder(parent.getContext(), view);
    }

    @Override
    protected void bindMessageContentViewHolder(
            final ViewHolder holder,
            ViewHolder parentHolder,
            MikChatPrivateAlbumMessage message,
            final UiMessage uiMessage,
            int position,
            List<UiMessage> list,
            IViewProviderListener<UiMessage> listener) {

//        final TextView nickName = holder.getView(R.id.nickName);
//        final ImageView gift = holder.getView(R.id.gift);
//
//        nickName.setText(message.nickName);

        final LinearLayout LLAlbum = holder.getView(R.id.LLAlbum);
        final ImageView album00 = holder.getView(R.id.album00);
        final ImageView album01 = holder.getView(R.id.album01);
        final ImageView album02 = holder.getView(R.id.album02);
        final ImageView album03 = holder.getView(R.id.album03);
        final ConstraintLayout CLAlbum = holder.getView(R.id.CLAlbum);
        final TextView nickName = holder.getView(R.id.tv_nickname);
        final ImageView avatar = holder.getView(R.id.iv_avatar);
        final TextView age = holder.getView(R.id.tv_age);
        final TextView country = holder.getView(R.id.tv_country);

        FragmentActivity context1 = (FragmentActivity) holder.getContext();

        ArrayList<String> list01 = new ArrayList<>();


//        if (context1 != null) {
//            OtherManager.Companion.manager(context1).getPersonalCenter(context1, Long.parseLong(message.anchorId), "1", "0").observe(context1, new Observer<PersonalCenterBean>() {
//                @Override
//                public void onChanged(PersonalCenterBean personalCenterBean) {
//                    nickName.setText(personalCenterBean.getNickName());
//                    age.setText(personalCenterBean.getAge());
//                    country.setText(personalCenterBean.getCountry());
//                    if (personalCenterBean.getAnchorFileList() == null || personalCenterBean.getAnchorFileList().size() == 0) {
//                        CLAlbum.setVisibility(View.GONE);
//                    } else {
//                        CLAlbum.setVisibility(View.VISIBLE);
//
//                        for (int i = 0; i < personalCenterBean.getAnchorFileList().size(); i++) {
//                            list01.add(personalCenterBean.getAnchorFileList().get(i).getFileUrl());
//                            switch (i) {
//                                case 0:
//                                    setAlbumItem(album00, personalCenterBean.getAnchorFileList().get(i), context1);
//                                    break;
//                                case 1:
//                                    setAlbumItem(album01, personalCenterBean.getAnchorFileList().get(i), context1);
//                                    break;
//                                case 2:
//                                    setAlbumItem(album02, personalCenterBean.getAnchorFileList().get(i), context1);
//                                    break;
//                                case 3:
//                                    setAlbumItem(album03, personalCenterBean.getAnchorFileList().get(i), context1);
//                                    break;
//                            }
//                        }
//                    }
//
//
//                    RxViewUtils.setOnClickListeners(CLAlbum, view -> {
////                        AlbumBigImagePreviewActivity.router(context1, CLAlbum, list , 0,personalCenterBean.getAnchorFileList());
//                        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_CENTER)
//                                .withLong("id", Long.parseLong(message.anchorId))
//                                .withString("userRole", "1")
//                                .withString("isCover", "1")
//                                .withString("viewpagerCurrentItem", "1")
//                                .navigation();
//                    });
//
//
//                }
//            });
//        }
    }


    private void setAlbumItem(ImageView albumView, PersonalCenterBean.AnchorFileListDTO bean, FragmentActivity context1) {

//        if (bean.getPurchaseFlag().equals( "0") ) {
//            //设置图片控件的高斯模糊效果
//            Glide.with(context1)
//                    .load(bean.getFileUrl())
//                    .apply(RequestOptions.bitmapTransform(new BlurTransformation(context1, 8, 8)))
//                    .into(albumView);
//        }else{
        Glide.with(context1).load(bean.getFileUrl()).placeholder(context1.getResources().getDrawable(R.drawable.rc_conversation_fragment_bg_03)).into(albumView);    //
//        }
    }

    private void setDirection(View view, boolean isSender) {
        ConstraintLayout.LayoutParams lp = ((ConstraintLayout.LayoutParams) view.getLayoutParams());
        if (isSender) {
            lp.startToStart = ConstraintLayout.LayoutParams.UNSET;
            lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
        } else {
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            lp.endToEnd = ConstraintLayout.LayoutParams.UNSET;
        }
        view.setLayoutParams(lp);
    }


    @Override
    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof MikChatPrivateAlbumMessage;
    }


    @Override
    public Spannable getSummarySpannable(Context context, MikChatPrivateAlbumMessage mikChatPrivateAlbumMessage) {
        return new SpannableString("[PrivateAlbum]");
    }

}
