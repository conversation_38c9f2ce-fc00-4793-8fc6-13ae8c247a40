package io.rong.imkit.conversation.extension.component.inputpanel;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static io.rong.imkit.utils.PermissionCheckUtil.REQUEST_CODE_ASK_PERMISSIONS;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import java.lang.ref.WeakReference;
import java.util.List;

import io.reactivex.functions.Consumer;
import io.rong.imkit.IMCenter;
import io.rong.imkit.R;
import io.rong.imkit.bean.RecordsDTO;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.conversation.extension.InputMode;
import io.rong.imkit.conversation.extension.RongExtension;
import io.rong.imkit.conversation.extension.RongExtensionCacheHelper;
import io.rong.imkit.conversation.extension.RongExtensionViewModel;
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule;
import io.rong.imkit.conversation.extension.component.plugin.ImagePlugin;
import io.rong.imkit.feature.reference.ReferenceManager;
import io.rong.imkit.manager.AudioPlayManager;
import io.rong.imkit.manager.AudioRecordManager;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imkit.utils.PermissionCheckUtil;
import io.rong.imkit.utils.RongOperationPermissionUtils;
import io.rong.imkit.widget.RongEditText;
import io.rong.imlib.ChannelClient;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.model.UserInfo;
import anchor.app.base.dialog.LoadingDialog;
import anchor.app.base.ext.AppKtKt;
import anchor.app.base.manager.DiamondManager;
import anchor.app.base.manager.OtherManager;
import anchor.app.base.manager.UserInfoManager;
import anchor.app.base.utils.GsonUtil;
import anchor.app.base.utils.RxViewUtils;
import anchor.app.base.utils.ToastUtil;

public class InputPanel {
    private final String TAG = this.getClass().getSimpleName();
    private Context mContext;
    private ConversationIdentifier mConversationIdentifier;
    private InputStyle mInputStyle;
    private Fragment mFragment;
    private View mInputPanel;
    private boolean mIsVoiceInputMode; // 语音和键盘切换按钮的当前显示状态是否为键盘模式
    private ImageView mVoiceToggleBtn;
    private EditText mEditText;
    private TextView mVoiceInputBtn;
    private ImageView mEmojiToggleBtn;
    private Button mSendBtn;
    //    private ImageView mSendGiftBtn;
    private ImageView chatVideo;
    //    private ImageView mSendImgBtn;
    private LinearLayout llSendImg, llSendGift;
    private ImageView mAddBtn;
    private LinearLayout LLSubItem;
    private ViewGroup mAddOrSendBtn;
    private RongExtensionViewModel mExtensionViewModel;
    private String mInitialDraft = "";

    public InputPanel(Fragment fragment, ViewGroup parent, InputStyle inputStyle, ConversationIdentifier conversationIdentifier) {
        mFragment = fragment;
        mInputStyle = inputStyle;
        mConversationIdentifier = conversationIdentifier;
        initView(fragment.getContext(), parent);

        mExtensionViewModel = new ViewModelProvider(fragment).get(RongExtensionViewModel.class);
        mExtensionViewModel.getInputModeLiveData().observe(fragment.getViewLifecycleOwner(), new Observer<InputMode>() {
            @Override
            public void onChanged(InputMode inputMode) {
                updateViewByInputMode(inputMode);
            }
        });
        if (fragment.getContext() != null) {
            mIsVoiceInputMode = RongExtensionCacheHelper.isVoiceInputMode(fragment.getContext(), conversationIdentifier.getType(), conversationIdentifier.getTargetId());
        }
        if (mIsVoiceInputMode) {
            mExtensionViewModel.getInputModeLiveData().setValue(InputMode.VoiceInput);
        } else {
            getDraft();
            mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initView(final Context context, ViewGroup parent) {
        mContext = context;
        mInputPanel = LayoutInflater.from(context).inflate(R.layout.rc_extension_input_panel, parent, false);
        mVoiceToggleBtn = mInputPanel.findViewById(R.id.input_panel_voice_toggle);
        mEditText = mInputPanel.findViewById(R.id.edit_btn);
        mVoiceInputBtn = mInputPanel.findViewById(R.id.press_to_speech_btn);
        mEmojiToggleBtn = mInputPanel.findViewById(R.id.input_panel_emoji_btn);
        mAddOrSendBtn = mInputPanel.findViewById(R.id.input_panel_add_or_send);
        mSendBtn = mInputPanel.findViewById(R.id.input_panel_send_btn);
        llSendGift = mInputPanel.findViewById(R.id.ll_gift);
        chatVideo = mInputPanel.findViewById(R.id.chatVideo);
        llSendImg = mInputPanel.findViewById(R.id.ll_photo);
        mAddBtn = mInputPanel.findViewById(R.id.input_panel_add_btn);
        LLSubItem = mInputPanel.findViewById(R.id.LLSubItem);

        RxViewUtils.setOnClickListeners(chatVideo, new Consumer<View>() {
            @Override
            public void accept(View view) throws Exception {
                LoadingDialog.getInstance(mContext).show();
                OtherManager.Companion.manager((FragmentActivity) mContext).getUserSimple(mConversationIdentifier.getTargetId()).observeForever(bean -> OtherManager.Companion.manager((FragmentActivity) mContext).getFollowFlag(mConversationIdentifier.getTargetId()).observe((FragmentActivity) mContext, followFlag -> {
                    UserInfo userInfo = new UserInfo(bean.getId() + "", bean.getNickName(), Uri.parse(bean.getHeadFileName()));
                    userInfo.setExtra(GsonUtil.GsonString(bean));
                    RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo);

                    LoadingDialog.getInstance(mContext).dismiss();

                    if (bean.getRole().equals("1")) {

                        if (DiamondManager.Companion.diamond() < bean.getVideoPrice() && UserInfoManager.Companion.user().getFreeVideoCall() <= 0) {
                            ToastUtil.show(mContext.getString(R.string.Your_current_balance_is_insufficient_please_recharge));
                            return;
                        }
                    } else {
                        if (DiamondManager.Companion.diamond() < bean.getVideoPrice()) {
                            ToastUtil.show(mContext.getString(R.string.Your_current_balance_is_insufficient_please_recharge));
                            return;
                        }
                    }

                    RecordsDTO recordsDTO = new RecordsDTO();
                    recordsDTO.setHeadFileName(bean.getHeadFileName());
                    recordsDTO.setNickName(bean.getNickName());
                    recordsDTO.setId(bean.getId());
                    recordsDTO.setOnlineStatus(bean.getOnlineStatus());
                    recordsDTO.setVideoPrice(bean.getVideoPrice());
                    recordsDTO.setFollowFlag(followFlag);
                    recordsDTO.setGroundFileName(bean.getHeadFileName());
                    recordsDTO.setCountry(bean.getCountry());
                    recordsDTO.setLevel(bean.getLevel());
                    recordsDTO.setRole(bean.getRole());

                    if (UserInfoManager.Companion.user().getDiamond() < bean.getVideoPrice()) {
                        ToastUtil.show(mContext.getString(R.string.Your_current_balance_is_insufficient_please_recharge));
                    } else if (!bean.getOnlineStatus().equals("1")) {
                        ToastUtil.show(mContext.getString(R.string.The_anchor_is_busy_please_dial_again_later) + "...");
                    } else {
                        ComponentName componentName = new ComponentName("com.mobile.anchor.app", "com.mobile.anchor.app.discover.main.chat.videochat.CallVideoChatActivity");
                        Intent intent = new Intent();
                        intent.setComponent(componentName);
                        intent.putExtra("id", Long.parseLong(mConversationIdentifier.getTargetId()));
                        intent.putExtra("userRole", bean.getRole());
                        intent.putExtra("sourceType", "1");
                        intent.putExtra("type", 1);
                        intent.putExtra("beanstr", GsonUtil.GsonString(recordsDTO));
                        AppKtKt.jump(mContext, intent);
//                        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).withLong("id", Long.parseLong(mConversationIdentifier.getTargetId())).withString("userRole", bean.getRole()).withString("sourceType", "1").withInt("type", 1).withString("beanstr", GsonUtil.GsonString(recordsDTO)).navigation();
                    }
//                            ARouter.getInstance().build(ArouterPath.PATH_LOGIN_TARGET).navigation();
                }));
            }
        });
        mSendBtn.setOnClickListener(mOnSendBtnClick);
        llSendGift.setOnClickListener(mOnSendGitfBtnClick);
        llSendImg.setOnClickListener(mImageIconClickListener);
        mEditText.setOnFocusChangeListener(mOnEditTextFocusChangeListener);
        mEditText.addTextChangedListener(mEditTextWatcher);


        mVoiceToggleBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsVoiceInputMode) {
                    mIsVoiceInputMode = false;
                    mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
                    // 切换到文本输入模式后需要弹出软键盘
                    mEditText.requestFocus();
                    if (TextUtils.isEmpty(mInitialDraft)) {
                        getDraft();
                    }
                } else {
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.VoiceInput);
                    mIsVoiceInputMode = true;
                }
                RongExtensionCacheHelper.saveVoiceInputMode(context, mConversationIdentifier.getType(), mConversationIdentifier.getTargetId(), mIsVoiceInputMode);
            }
        });
        mEmojiToggleBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mExtensionViewModel == null) {
                    return;
                }
                if (mExtensionViewModel.getInputModeLiveData().getValue() != null && mExtensionViewModel.getInputModeLiveData().getValue().equals(InputMode.EmoticonMode)) {
                    mEditText.requestFocus();
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
                } else {
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.EmoticonMode);
                }
            }
        });
        mAddBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mExtensionViewModel.getInputModeLiveData().getValue() != null && mExtensionViewModel.getInputModeLiveData().getValue().equals(InputMode.PluginMode)) {
                    mEditText.requestFocus();
                    mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
                } else {
                    mExtensionViewModel.getInputModeLiveData().setValue(InputMode.PluginMode);
                    ReferenceManager.getInstance().hideReferenceView();
                }
                if (TextUtils.isEmpty(mInitialDraft)) {
                    getDraft();
                }
            }
        });
        mVoiceInputBtn.setOnTouchListener(mOnVoiceBtnTouchListener);
        setInputPanelStyle(mInputStyle);
    }


//    public AndroidLifecycleScopeProvider scopeProvider = AndroidLifecycleScopeProvider.from((FragmentActivity)activityFromView, Lifecycle.Event.ON_DESTROY);


    private void updateViewByInputMode(InputMode inputMode) {
        if (inputMode.equals(InputMode.TextInput) || inputMode.equals(InputMode.PluginMode)) {
            if (inputMode.equals(InputMode.TextInput)) {
                mIsVoiceInputMode = false;
            }
            mVoiceToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_voice_btn));
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_input_panel_emoji));
            mEditText.setVisibility(VISIBLE);
            mVoiceInputBtn.setVisibility(GONE);
            resetInputView();
        } else if (inputMode.equals(InputMode.VoiceInput)) {
            mVoiceToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_keyboard_btn));
            mVoiceInputBtn.setVisibility(GONE);
            mEditText.setVisibility(GONE);
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_input_panel_emoji));
        } else if (inputMode.equals(InputMode.EmoticonMode)) {
            mVoiceToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_voice_btn));
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_keyboard_btn));
            mEditText.setVisibility(VISIBLE);
            mVoiceInputBtn.setVisibility(GONE);
        } else if (inputMode.equals(InputMode.QuickReplyMode)) {
            mIsVoiceInputMode = false;
            mVoiceToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_voice_btn));
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_input_panel_emoji));
            mEditText.setVisibility(VISIBLE);
            mVoiceInputBtn.setVisibility(GONE);
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_input_panel_emoji));
        } else if (inputMode.equals(InputMode.NormalMode)) {
            mIsVoiceInputMode = false;
            mVoiceToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_toggle_voice_btn));
            mEmojiToggleBtn.setImageDrawable(mContext.getResources().getDrawable(R.drawable.rc_ext_input_panel_emoji));
            mEditText.setVisibility(VISIBLE);
            mVoiceInputBtn.setVisibility(GONE);
            resetInputView();
        }
    }

    private void resetInputView() {
        Editable text = mEditText.getText();
        if (text == null || text.length() == 0) {
            if (mInputStyle.equals(InputStyle.STYLE_CONTAINER_EXTENSION) || mInputStyle.equals(InputStyle.STYLE_SWITCH_CONTAINER_EXTENSION)) {
                mAddOrSendBtn.setVisibility(VISIBLE);
//                mAddBtn.setVisibility(VISIBLE);
                mSendBtn.setVisibility(GONE);
            } else {
                mAddOrSendBtn.setVisibility(GONE);
            }
        } else {
            mAddOrSendBtn.setVisibility(VISIBLE);
            mSendBtn.setVisibility(VISIBLE);
            mAddBtn.setVisibility(GONE);
        }
    }

    public EditText getEditText() {
        return mEditText;
    }

    public void setSendGiftListener(SendGiftListener sendGift) {
        this.sendGift = sendGift;
    }

    public View getRootView() {
        return mInputPanel;
    }

    public void setVisible(int viewId, boolean visible) {
        mInputPanel.findViewById(viewId).setVisibility(visible ? VISIBLE : GONE);
    }

    /**
     * 设置 InputPanel 样式.
     *
     * @param style 目前支持 5 种样式，参照: {@link InputStyle}
     */
    public void setInputPanelStyle(InputStyle style) {
        switch (style) {
            case STYLE_SWITCH_CONTAINER_EXTENSION:
                setSCE();
                break;
            case STYLE_CONTAINER:
                setC();
                break;
            case STYLE_CONTAINER_EXTENSION:
                setCE();
                break;
            case STYLE_SWITCH_CONTAINER:
                setSC();
                break;
            default:
                setSCE();
                break;
        }
        mInputStyle = style;
    }

    private void setSCE() {
        if (mInputPanel != null) {
            mVoiceToggleBtn.setVisibility(GONE);
            mEmojiToggleBtn.setVisibility(shouldShowEmojiButton() ? VISIBLE : GONE);
//            mAddBtn.setVisibility(VISIBLE);
        }
    }

    private void setC() {
        if (mInputPanel != null) {
            mVoiceToggleBtn.setVisibility(GONE);
            mAddOrSendBtn.setVisibility(GONE);
            mEmojiToggleBtn.setVisibility(GONE);
            mAddBtn.setVisibility(GONE);
            mSendBtn.setVisibility(GONE);
        }
    }

    private void setCE() {
        if (mInputPanel != null) {
            mVoiceToggleBtn.setVisibility(GONE);
            mAddOrSendBtn.setVisibility(VISIBLE);
            mEmojiToggleBtn.setVisibility(shouldShowEmojiButton() ? VISIBLE : GONE);
//            mAddBtn.setVisibility(VISIBLE);
        }
    }

    private void setSC() {
        if (mInputPanel != null) {
            mVoiceToggleBtn.setVisibility(VISIBLE);
            mAddOrSendBtn.setVisibility(GONE);
            mAddBtn.setVisibility(GONE);
        }
    }

    private boolean shouldShowEmojiButton() {
        return !RongConfigCenter.featureConfig().isHideEmojiButton();
    }

    public void getDraft() {
        ChannelClient.getInstance().getTextMessageDraft(mConversationIdentifier.getType(), mConversationIdentifier.getTargetId(), mConversationIdentifier.getChannelId(), new IRongCoreCallback.ResultCallback<String>() {
            @Override
            public void onSuccess(final String s) {
                if (!TextUtils.isEmpty(s)) {
                    mEditText.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mInitialDraft = s;
                            if (mEditText instanceof RongEditText) {
                                ((RongEditText) mEditText).setText(s, false);
                            } else {
                                mEditText.setText(s);
                            }
                            mEditText.setSelection(s.length());
                            mEditText.requestFocus();
                            resetInputView();
                        }
                    }, 50);
                }
            }

            @Override
            public void onError(IRongCoreEnum.CoreErrorCode errorCode) {
                // do nothing
            }
        });
    }

    private float mLastTouchY;
    private boolean mUpDirection;
    private View.OnTouchListener mOnVoiceBtnTouchListener = new View.OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            float mOffsetLimit = 70 * v.getContext().getResources().getDisplayMetrics().density;
            String[] permissions = {Manifest.permission.RECORD_AUDIO};

            if (!PermissionCheckUtil.checkPermissions(v.getContext(), permissions) && event.getAction() == MotionEvent.ACTION_DOWN) {
                PermissionCheckUtil.requestPermissions(mFragment, permissions, REQUEST_CODE_ASK_PERMISSIONS);
                return true;
            }

            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                if (AudioPlayManager.getInstance().isPlaying()) {
                    AudioPlayManager.getInstance().stopPlay();
                }
                // 判断正在视频通话和语音通话中不能进行语音消息发送
                if (RongOperationPermissionUtils.isOnRequestHardwareResource()) {
                    Toast.makeText(v.getContext(), v.getContext().getResources().getString(R.string.rc_voip_occupying), Toast.LENGTH_SHORT).show();
                    return true;
                }
                AudioRecordManager.getInstance().startRecord(v.getRootView(), mConversationIdentifier);
                mLastTouchY = event.getY();
                mUpDirection = false;
                ((TextView) v).setText(R.string.rc_voice_release_to_send);
                ((TextView) v).setBackground(v.getContext().getResources().getDrawable(R.drawable.rc_ext_voice_touched_button));
            } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                if (mLastTouchY - event.getY() > mOffsetLimit && !mUpDirection) {
                    AudioRecordManager.getInstance().willCancelRecord();
                    mUpDirection = true;
                    ((TextView) v).setText(R.string.rc_voice_press_to_input);
                    ((TextView) v).setBackground(v.getContext().getResources().getDrawable(R.drawable.rc_ext_voice_idle_button));
                } else if (event.getY() - mLastTouchY > -mOffsetLimit && mUpDirection) {
                    AudioRecordManager.getInstance().continueRecord();
                    mUpDirection = false;
                    ((TextView) v).setBackground(v.getContext().getResources().getDrawable(R.drawable.rc_ext_voice_touched_button));
                    ((TextView) v).setText(R.string.rc_voice_release_to_send);
                }
            } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                AudioRecordManager.getInstance().stopRecord();
                ((TextView) v).setText(R.string.rc_voice_press_to_input);
                ((TextView) v).setBackground(v.getContext().getResources().getDrawable(R.drawable.rc_ext_voice_idle_button));
            }
            if (mConversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
                RongIMClient.getInstance().sendTypingStatus(mConversationIdentifier.getType(), mConversationIdentifier.getTargetId(), "RC:VcMsg");
            }
            return true;
        }
    };

    private View.OnClickListener mOnSendBtnClick = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            mExtensionViewModel.onSendClick();
        }
    };

    Activity activityFromView;

    public void setActivity(Activity activityFromView) {
        this.activityFromView = activityFromView;
    }

    public interface SendGiftListener {
        void sendGift();
    }

    SendGiftListener sendGift;
    private View.OnClickListener mOnSendGitfBtnClick = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
//                    mExtensionViewModel
//                            .getInputModeLiveData()
//                            .postValue(InputMode.VoiceInput);
//                    mIsVoiceInputMode = true;

            InputMethodManager inputMethodManager = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null) {
                inputMethodManager.hideSoftInputFromWindow(v.getWindowToken(), 0);
            }

            if (sendGift != null) {
                sendGift.sendGift();
            }
        }
    };

    private View.OnClickListener mImageIconClickListener = new View.OnClickListener() {
        private IPluginModule sightPlugin;
        private IPluginModule imagePlugin;
        private int sightIndex;
        private int imageIndex = 0;

        @Override
        public void onClick(View v) {
//                    mRongExtension.get()
//                    new ImagePlugin().onClick(mFragment, ReferenceManager.getInstance().mRongExtension.get(), imageIndex);

            WeakReference<RongExtension> mRongExtension = ReferenceManager.getInstance().mRongExtension;

            if (mRongExtension == null) {
                return;
            }
            final RongExtension extension = mRongExtension.get();
            if (extension == null) {
                return;
            }
            if (imagePlugin == null && sightPlugin == null) {
                List<IPluginModule> pluginModules = extension.getPluginBoard().getPluginModules();
                for (int i = 0; i < pluginModules.size(); i++) {
                    if (pluginModules.get(i) instanceof ImagePlugin) {
                        imageIndex = i;
                        imagePlugin = pluginModules.get(i);
                    } else if (pluginModules.get(i).getClass().getName().equals("io.rong.sight.SightPlugin")) {
                        sightIndex = i;
                        sightPlugin = pluginModules.get(i);
                    }
                    if (imagePlugin != null && sightPlugin != null) {
                        break;
                    }
                }
            }
            imagePlugin.onClick(mFragment, extension, imageIndex);

//                    DestructImageDialog imageClickDialog = new DestructImageDialog();
//                    imageClickDialog.setHasImage(imagePlugin != null);
//                    imageClickDialog.setHasSight(sightPlugin != null);
//                    imageClickDialog.setImageVideoDialogListener(
//                            new DestructImageDialog.ImageVideoDialogListener() {
//                                @Override
//                                public void onSightClick(View v) {
//                                    if (sightPlugin != null)
//                                        sightPlugin.onClick(mFragment, extension, sightIndex);
//                                }
//
//                                @Override
//                                public void onImageClick(View v) {
//                                    if (imagePlugin != null)
//                                        imagePlugin.onClick(mFragment, extension, imageIndex);
//                                }
//                            });
//                    if (mFragment.isAdded()) {
//                        imageClickDialog.show(mFragment.getParentFragmentManager());
//                    }
        }
    };


    private View.OnFocusChangeListener mOnEditTextFocusChangeListener = new View.OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (mExtensionViewModel != null && mExtensionViewModel.getInputModeLiveData() != null) {
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
                }
                if (!TextUtils.isEmpty(mEditText.getText())) {
                    mSendBtn.setVisibility(VISIBLE);
                    mAddBtn.setVisibility(GONE);
                }
            } else {
                if (mExtensionViewModel != null) {
                    EditText editText = mExtensionViewModel.getEditTextWidget();
                    if (editText.getText() != null && editText.getText().length() == 0) {
                        mSendBtn.setVisibility(GONE);
//                                mAddBtn.setVisibility(VISIBLE);
                    }
                }
            }
        }
    };

    private TextWatcher mEditTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // do nothing
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s == null || s.length() == 0) {
                IMCenter.getInstance().saveTextMessageDraft(mConversationIdentifier, mEditText.getText().toString(), null);
                if (mInputStyle.equals(InputStyle.STYLE_CONTAINER_EXTENSION) || mInputStyle.equals(InputStyle.STYLE_SWITCH_CONTAINER_EXTENSION)) {
                    mAddOrSendBtn.setVisibility(VISIBLE);
//                            mAddBtn.setVisibility(VISIBLE);
                    mSendBtn.setVisibility(GONE);
                } else {
                    mAddOrSendBtn.setVisibility(GONE);
                }
            } else {
                mAddOrSendBtn.setVisibility(VISIBLE);
                mSendBtn.setVisibility(VISIBLE);
                mAddBtn.setVisibility(GONE);
            }

            int cursor, offset;
            if (count == 0) {
                cursor = start + before;
                offset = -before;
            } else {
                cursor = start;
                offset = count;
            }
            if ((Conversation.ConversationType.PRIVATE).equals(mConversationIdentifier.getType()) && offset != 0) {
                RongIMClient.getInstance().sendTypingStatus(mConversationIdentifier.getType(), mConversationIdentifier.getTargetId(), "RC:TxtMsg");
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            // do nothing
        }
    };

    public void onDestroy() {

        mFragment = null;
        mExtensionViewModel = null;
        if (mEditText != null && mEditText.getText() != null && !mInitialDraft.equals(mEditText.getText().toString())) {
            IMCenter.getInstance().saveTextMessageDraft(mConversationIdentifier, mEditText.getText().toString(), null);
        }
    }

    public enum InputStyle {
        /**
         * 录音切换-输入框-扩展
         */
        STYLE_SWITCH_CONTAINER_EXTENSION(0x123),
        /**
         * 录音切换-输入框
         */
        STYLE_SWITCH_CONTAINER(0x120),
        /**
         * 输入框-扩展
         */
        STYLE_CONTAINER_EXTENSION(0x023),
        /**
         * 仅有输入框
         */
        STYLE_CONTAINER(0x020);

        int v;

        InputStyle(int v) {
            this.v = v;
        }

        public static InputStyle getStyle(int v) {
            InputStyle result = null;
            for (InputStyle style : values()) {
                if (style.v == v) {
                    result = style;
                    break;
                }
            }
            return result;
        }
    }
}
