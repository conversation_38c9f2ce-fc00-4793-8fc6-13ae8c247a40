package io.rong.imkit

import androidx.fragment.app.FragmentActivity
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ScopeProvider
import io.reactivex.Flowable
import io.rong.imlib.RongIMClient
import anchor.app.base.core.Constants
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.retrofit.BaseApiClient
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.utils.ToastUtil
import retrofit2.http.POST

//public class InitRongData {
//
//    fun initrong(activity : FragmentActivity, scopeProvider: ScopeProvider) {
//        BaseApiClient.getInstance().create(IRongApi::class.java).init()
//            .subscribeOn(RxSchedulers.io)
//            .observeOn(RxSchedulers.ui)
//            .map(ResultMap())
//            .compose(RxErrorUtil.handleGlobalError(activity))
//            .`as`(AutoDispose.autoDisposable(scopeProvider))
//            .subscribe({ token: String ->
//                RongIM.connect(token, object : RongIMClient.ConnectCallback() {
//                    override fun onSuccess(t: String) {
//                        RongIM.connect(token, object : RongIMClient.ConnectCallback() {
//                            override fun onSuccess(userId: String) {
//                                // 登录成功，跳转到默认会话列表页。
////            RouteUtils.routeToConversationListActivity(this@MainActivity, "")
//                            }
//
//                            override fun onError(connectionErrorCode: RongIMClient.ConnectionErrorCode) {}
//                            override fun onDatabaseOpened(databaseOpenStatus: RongIMClient.DatabaseOpenStatus) {}
//                        })
//                    }
//                    override fun onError(e: RongIMClient.ConnectionErrorCode) {}
//                    override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus) {}
//                })
//            }) { throwable: Throwable ->
//                ToastUtil.show("...." + throwable.message)
//            }
//
//
//    }
//}
//
//
//interface IRongApi {
//    @POST(Constants.HTTP_URL.get_rongim_token)
//    fun init(): Flowable<BaseResult<String>>
//}