<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<navigation
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/permissions_fragment">

    <fragment
        android:id="@+id/permissions_fragment"
        android:name="anchor.app.base.view.video.fragments.PermissionsFragment"
        android:label="Permissions" >

        <action
            android:id="@+id/action_permissions_to_capture"
            app:destination="@id/capture_fragment"
            app:popUpTo="@id/permissions_fragment"
            app:popUpToInclusive="true"/>

    </fragment>

    <fragment
        android:id="@+id/capture_fragment"
        android:name="anchor.app.base.view.video.fragments.CaptureFragment"
        android:label="Capture" >

        <action
            android:id="@+id/action_capture_to_permissions"
            app:destination="@id/permissions_fragment" />
        <action
            android:id="@+id/action_capture_to_video_viewer"
            app:destination="@id/video_viewer" />
    </fragment>

    <fragment
        android:id="@+id/video_viewer"
        android:name="anchor.app.base.view.video.fragments.VideoViewerFragment"
        android:label="fragment_video_viewer"
        tools:layout="@layout/fragment_video_viewer" >
        <argument
            android:name="uri"
            app:argType="android.net.Uri" />

        <argument
            android:name="second"
            app:argType="string" />

        <action
            android:id="@+id/action_video_viewer_to_capture"
            app:destination="@id/capture_fragment" />
    </fragment>

</navigation>
