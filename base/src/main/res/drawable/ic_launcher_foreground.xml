<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="261.75"
    android:viewportHeight="261.75"
    android:autoMirrored="true">

    <group android:translateX="78"
        android:translateY="72">
    <path
        android:pathData="M103.59,33.15h0c-2.69,-6.37 -6.61,-12.24 -11.51,-17.14l-0.73,-0.73C81.31,5.72 67.84,0.33 53.88,0.33S25.96,5.97 15.67,16.01v0.24C5.63,26.54 0,40.01 0,54.21c0,5.88 0.98,11.27 2.69,16.65 1.7,4.85 3.88,9.47 6.78,13.84 0.05,0.08 0.11,0.15 0.18,0.22 0.25,0.25 0.39,0.59 0.39,0.94l0.18,18.51c0,1.81 1.48,3.29 3.29,3.29H54.06c7.35,0 14.2,-1.47 21.06,-4.16 6.37,-2.69 12.07,-6.43 16.96,-11.33l0.73,-0.73c9.8,-10.04 15.18,-23.27 15.18,-37.47 -0.24,-7.1 -1.71,-13.96 -4.41,-20.82Z"
        android:fillColor="#fff"/>
    <path
        android:pathData="M33.16,31.6L59.62,31.6A9.17,9.17 0,0 1,68.79 40.77L68.79,67.23A9.17,9.17 0,0 1,59.62 76.4L33.16,76.4A9.17,9.17 0,0 1,23.99 67.23L23.99,40.77A9.17,9.17 0,0 1,33.16 31.6z"
        android:fillColor="#ffb909"/>
    <path
        android:pathData="M82.04,36.64h-0.75c-1.27,0 -2.45,0.4 -3.42,1.08l-3,1.85 -2.54,1.54 -22.4,12.88 22.4,12.88 2.54,1.54 3,1.85c0.97,0.68 2.14,1.08 3.42,1.08h0.75c3.3,0 5.97,-2.67 5.97,-5.97v-22.77c0,-3.3 -2.67,-5.97 -5.97,-5.97Z"
        android:fillColor="#ffb909"/>
    <path
        android:pathData="M38.81,49.81m-4.43,0a4.43,4.43 0,1 1,8.86 0a4.43,4.43 0,1 1,-8.86 0"
        android:fillColor="#fff"/>
    <path
        android:pathData="M56.7,49.81m-3.06,0a3.06,3.06 0,1 1,6.12 0a3.06,3.06 0,1 1,-6.12 0"
        android:fillColor="#fff"/>
    <path
        android:pathData="M48.44,56.61m-3.06,0a3.06,3.06 0,1 1,6.12 0a3.06,3.06 0,1 1,-6.12 0"
        android:fillColor="#fff"/>
    </group>

</vector>

<!--<vector xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    android:width="108dp"-->
<!--    android:height="108dp"-->
<!--    android:viewportWidth="348.9796"-->
<!--    android:viewportHeight="348.97958"-->
<!--    android:autoMirrored="true">-->
<!--  <group android:translateX="104.4898"-->
<!--      android:translateY="88.9898">-->
<!--      <group>-->
<!--          <clip-path android:pathData="M48.011,-0.019l41.409,0l0,25.517l-41.409,0z M 0,0" />-->
<!--          <path-->
<!--              android:fillColor="#00FF00"-->
<!--              android:fillType="evenOdd"-->
<!--              android:pathData="M83.706,5.074C83.313,5.074 82.926,5.114 82.555,5.189C82.549,5.189 82.546,5.192 82.543,5.192C82.253,5.267 81.966,5.351 81.682,5.444C76.551,7.083 72.511,11.151 70.912,16.295C69.572,9.46 65.058,3.759 58.966,0.79C58.935,0.778 58.906,0.762 58.876,0.747C58.751,0.688 58.626,0.628 58.501,0.572C58.498,0.569 58.492,0.569 58.489,0.569C57.588,0.189 56.6,-0.02 55.561,-0.02C51.391,-0.02 48.011,3.357 48.011,7.525C48.011,11.69 51.391,15.07 55.561,15.07C56.591,15.07 57.572,14.865 58.467,14.491C58.629,14.407 58.798,14.329 58.969,14.258C59.776,13.921 60.662,13.734 61.591,13.734C65.353,13.734 68.405,16.784 68.405,20.547C68.405,22.497 67.585,24.257 66.27,25.498L70.725,25.498L70.762,25.498L73.243,25.498C72.701,24.538 72.395,23.429 72.395,22.248C72.395,18.569 75.379,15.588 79.06,15.588C79.887,15.588 80.679,15.737 81.408,16.014C81.521,16.055 81.633,16.102 81.742,16.152C81.745,16.152 81.748,16.155 81.751,16.155C82.362,16.376 83.02,16.497 83.706,16.497C86.864,16.497 89.42,13.94 89.42,10.784C89.42,7.631 86.864,5.074 83.706,5.074"-->
<!--              android:strokeWidth="1"-->
<!--              android:strokeColor="#00000000" />-->
<!--      </group>-->
<!--      <group>-->
<!--          <clip-path android:pathData="M0.063,22.416l139.679,0l0,148.074l-139.679,0z M 0,0" />-->
<!--          <path-->
<!--              android:fillColor="#FFEB00"-->
<!--              android:fillType="evenOdd"-->
<!--              android:pathData="M139.741,83.164C139.741,81.459 139.698,79.768 139.614,78.085C139.47,75.195 139.205,72.338 138.819,69.519C135.053,47.625 118.337,30.157 96.834,25.27C96.135,25.117 95.434,24.974 94.729,24.837C86.708,23.248 78.413,22.416 69.924,22.416C61.429,22.416 53.127,23.248 45.099,24.84C44.405,24.974 43.716,25.117 43.027,25.267C21.021,30.266 4.028,48.438 0.789,71.042C0.574,72.806 0.409,74.587 0.29,76.382C0.137,78.622 0.063,80.883 0.063,83.164C0.063,99.26 3.857,114.469 10.599,127.952C7.772,131.534 6.088,136.057 6.088,140.974C6.088,149.506 11.17,156.855 18.471,160.164C18.477,160.166 18.483,160.166 18.487,160.169C18.695,160.263 18.907,160.353 19.119,160.441C28.3,164.338 38.036,167.185 48.177,168.833C54.87,169.924 61.741,170.49 68.746,170.49C76.882,170.49 84.837,169.727 92.547,168.266C101.525,166.565 110.17,163.924 118.371,160.441C118.583,160.353 118.795,160.263 119.004,160.169C119.008,160.166 119.014,160.166 119.02,160.164C126.321,156.855 131.402,149.506 131.402,140.974C131.402,136.892 130.242,133.079 128.229,129.855C135.58,115.908 139.741,100.02 139.741,83.164"-->
<!--              android:strokeWidth="1"-->
<!--              android:strokeColor="#00000000" />-->
<!--      </group>-->
<!--      <path-->
<!--          android:fillColor="#2F2119"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M91.545,74.893C91.283,74.893 91.017,74.826 90.774,74.685C90.042,74.258 89.795,73.32 90.222,72.589C90.286,72.479 90.361,72.357 90.437,72.236C90.495,72.142 90.554,72.048 90.604,71.962C90.62,71.934 90.637,71.906 90.656,71.879C91.046,71.302 91.493,70.784 92.064,70.249C93.793,68.716 95.858,67.94 98.232,67.912C100.596,67.885 102.695,68.593 104.44,70.019C105.383,70.83 106.003,71.595 106.422,72.451C106.794,73.212 106.478,74.129 105.717,74.501C104.956,74.872 104.037,74.557 103.666,73.796C103.437,73.326 103.056,72.871 102.469,72.365C101.327,71.432 99.911,70.951 98.268,70.976C96.652,70.995 95.259,71.512 94.13,72.512C93.769,72.852 93.479,73.184 93.222,73.558C93.167,73.651 93.105,73.75 93.043,73.85C92.983,73.948 92.922,74.045 92.871,74.133C92.586,74.621 92.073,74.893 91.545,74.893M32.386,75.207C32.151,75.207 31.912,75.153 31.689,75.039C30.935,74.654 30.636,73.731 31.022,72.977C31.474,72.095 32.114,71.276 32.926,70.543C34.635,69.03 36.686,68.253 39.034,68.225C41.392,68.16 43.505,68.866 45.261,70.348C46.139,71.104 46.796,71.927 47.224,72.804C47.596,73.564 47.281,74.482 46.52,74.853C45.76,75.223 44.841,74.91 44.469,74.149C44.224,73.647 43.821,73.153 43.27,72.679C42.106,71.696 40.732,71.245 39.099,71.289C37.458,71.309 36.117,71.811 34.97,72.826C34.448,73.299 34.034,73.823 33.752,74.373C33.481,74.903 32.943,75.207 32.386,75.207"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#FF9784"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M70.632,153.402C74.58,153.402 78.327,154.264 81.695,155.808C82.184,156.033 82.666,156.271 83.14,156.527C81.931,157.518 80.608,158.377 79.194,159.081C76.382,160.481 73.211,161.267 69.855,161.267C66.546,161.267 63.419,160.501 60.636,159.138C59.427,158.548 58.28,157.842 57.216,157.037C57.604,156.807 58,156.587 58.404,156.38C62.062,154.475 66.22,153.402 70.632,153.402"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#A50F00"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M69.884,142.618C73.744,142.618 77.327,143.811 80.282,145.85C80.439,145.969 80.597,146.082 80.76,146.191C81.535,146.724 82.35,147.195 83.202,147.604C84.945,148.44 86.837,149.006 88.83,149.255C88.18,150.628 87.386,151.922 86.464,153.112C85.488,154.372 84.372,155.518 83.14,156.527C82.666,156.271 82.185,156.033 81.695,155.808C78.327,154.264 74.58,153.402 70.632,153.402C66.22,153.402 62.062,154.476 58.404,156.38C58,156.587 57.604,156.807 57.216,157.037C55.74,155.922 54.414,154.618 53.283,153.159C52.348,151.958 51.54,150.654 50.885,149.268C52.894,149.03 54.808,148.468 56.563,147.632C57.255,147.304 57.922,146.934 58.564,146.525L58.588,146.509C59.074,146.199 59.546,145.862 60.001,145.506C60.009,145.5 60.017,145.495 60.025,145.49C62.873,143.671 66.257,142.618 69.884,142.618"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#FF7800"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M45.29,141.661C45.508,141.734 45.728,141.801 45.948,141.86C46.927,142.137 47.942,142.331 48.983,142.437C49.567,142.494 50.163,142.525 50.766,142.525C54.772,142.525 58.469,141.195 61.439,138.952C61.576,138.851 61.71,138.745 61.845,138.636C61.85,138.634 61.858,138.629 61.863,138.623C63.157,137.658 64.633,136.921 66.23,136.471C67.393,136.145 68.618,135.969 69.884,135.969C71.173,135.969 72.419,136.15 73.599,136.489C75.147,136.934 76.584,137.648 77.848,138.582C78.039,138.738 78.234,138.888 78.43,139.035C78.433,139.035 78.433,139.037 78.436,139.04C81.384,141.229 85.035,142.525 88.993,142.525C89.579,142.525 90.156,142.497 90.726,142.442C91.878,142.331 92.997,142.109 94.071,141.785C94.153,141.762 94.234,141.739 94.314,141.71C94.772,141.583 95.254,141.514 95.754,141.511L95.782,141.511C98.596,141.511 100.911,143.645 101.196,146.383C101.216,146.572 101.227,146.76 101.227,146.955C101.227,148.936 100.168,150.672 98.583,151.625L98.581,151.627C98.265,151.772 97.949,151.919 97.638,152.075C94.239,153.749 91.114,155.899 88.354,158.437C88.349,158.442 88.344,158.445 88.341,158.45C88.253,158.53 88.165,158.61 88.08,158.693C87.847,158.91 87.616,159.13 87.389,159.353L87.187,159.555L87.184,159.557C82.599,163.676 76.533,166.18 69.884,166.18C63.481,166.18 57.62,163.857 53.101,160.01C52.949,159.883 52.801,159.751 52.654,159.619L52.649,159.614C52.333,159.298 52.014,158.991 51.691,158.685C51.574,158.577 51.455,158.465 51.336,158.359C51.333,158.357 51.333,158.354 51.331,158.354C48.581,155.839 45.477,153.71 42.098,152.049L42.096,152.049C41.821,151.911 41.547,151.78 41.27,151.653C41.267,151.65 41.264,151.65 41.262,151.648C39.654,150.701 38.577,148.954 38.577,146.955C38.577,146.755 38.588,146.559 38.608,146.367C38.901,143.638 41.21,141.514 44.017,141.511L44.022,141.511C44.459,141.511 44.884,141.563 45.29,141.661M53.283,153.159C54.414,154.618 55.74,155.922 57.216,157.037C58.28,157.842 59.427,158.548 60.636,159.138C63.419,160.501 66.547,161.267 69.855,161.267C73.211,161.267 76.383,160.481 79.194,159.081C80.608,158.377 81.931,157.518 83.14,156.527C84.372,155.518 85.488,154.372 86.464,153.112C87.386,151.922 88.181,150.628 88.831,149.255C86.837,149.006 84.944,148.44 83.202,147.604C82.35,147.195 81.534,146.724 80.76,146.191C80.602,146.075 80.445,145.961 80.281,145.85C77.327,143.811 73.744,142.618 69.884,142.618C66.257,142.618 62.873,143.671 60.025,145.49C60.017,145.495 60.009,145.5 60.001,145.506C59.514,145.816 59.043,146.153 58.588,146.51L58.565,146.525C57.922,146.934 57.254,147.304 56.563,147.633C54.808,148.468 52.895,149.029 50.885,149.267C51.54,150.654 52.348,151.958 53.283,153.159"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#2F2119"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M97.32,104.286C93.015,104.286 89.527,107.772 89.527,112.075L89.527,126.106C89.527,126.302 89.533,126.495 89.552,126.688C89.845,130.719 93.212,133.897 97.32,133.897C101.429,133.897 104.796,130.719 105.092,126.688C105.111,126.495 105.117,126.302 105.117,126.106L105.117,112.075C105.117,107.772 101.626,104.286 97.32,104.286"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#2F2119"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M42.96,104.286C38.656,104.286 35.167,107.772 35.167,112.075L35.167,126.106C35.167,126.302 35.173,126.495 35.192,126.688C35.485,130.719 38.852,133.897 42.96,133.897C47.069,133.897 50.436,130.719 50.732,126.688C50.751,126.495 50.757,126.302 50.757,126.106L50.757,112.075C50.757,107.772 47.266,104.286 42.96,104.286"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#FEFEFE"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M48.287,110.3C48.287,112.222 46.727,113.781 44.803,113.781C42.88,113.781 41.32,112.222 41.32,110.3C41.32,108.377 42.88,106.818 44.803,106.818C46.727,106.818 48.287,108.377 48.287,110.3"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--      <path-->
<!--          android:fillColor="#FEFEFE"-->
<!--          android:fillType="evenOdd"-->
<!--          android:pathData="M102.509,110.3C102.509,112.222 100.949,113.781 99.025,113.781C97.101,113.781 95.542,112.222 95.542,110.3C95.542,108.377 97.101,106.818 99.025,106.818C100.949,106.818 102.509,108.377 102.509,110.3"-->
<!--          android:strokeWidth="1"-->
<!--          android:strokeColor="#00000000" />-->
<!--  </group>-->
<!--</vector>-->
