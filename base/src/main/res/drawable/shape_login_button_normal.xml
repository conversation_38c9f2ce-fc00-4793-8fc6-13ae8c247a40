<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle"
    >
    <!-- 填充的颜色 -->
    <solid android:color="#FF2500" />
    <!--设置渐变-->
    <gradient android:startColor="#FF2500"
        android:endColor="#FF5700"
        android:angle="180"/>
    <!--angle控制渐变的方向-->
    <!-- 设置按钮的四个角为弧形 -->
    <!-- android:radius 弧形的半径 -->
    <corners android:radius="@dimen/dp_33" />

</shape>