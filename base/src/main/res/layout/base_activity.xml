<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!--加载失败-->
            <ViewStub
                android:id="@+id/vs_error_refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout="@layout/layout_loading_error" />

            <!--加载中..-->
            <ViewStub
                android:id="@+id/vs_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:inflatedId="@+id/panel_import"
                android:layout="@layout/layout_loading_view" />

        </RelativeLayout>
    </LinearLayout>

</layout>