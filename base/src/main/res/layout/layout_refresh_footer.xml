<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <com.wang.avi.AVLoadingIndicatorView
        android:layout_margin="@dimen/dp_8"
        android:id="@+id/view_anim_refresh_id"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center_vertical"
        android:background="#00FFFFFF"
        android:visibility="visible"
        app:indicator="LineSpinFadeLoader"
        app:indicator_color="@color/colorPrimaryDark" />
    <TextView
        android:gravity="center"
        android:id="@+id/tv_view_state_desc_id"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_60"
        android:textColor="#AFBECF"
        android:textSize="14dp"
        android:text="No more data ~" />

</LinearLayout>