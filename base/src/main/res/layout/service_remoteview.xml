<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="right"
    android:layout_marginEnd="@dimen/dp_20"
    android:id="@+id/line1"
    android:orientation="vertical">
<!--    android:alpha="0"-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/CL01"
        android:background="@mipmap/service_remoteview_active"
        android:orientation="vertical"
        android:gravity="right"
        android:layout_gravity="right"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60">

<!--        <anchor.app.base.view.RoundImageView-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"-->
<!--            app:is_circle="true"-->
<!--            android:layout_gravity="center"-->
<!--            android:src="@mipmap/ic_launcher"-->
<!--            android:layout_width="@dimen/dp_60"-->
<!--            android:layout_height="@dimen/dp_60" />-->

        <TextView
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/badge"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_2"
            android:background="@drawable/base_shape_oval_primary"
            android:gravity="center"
            android:includeFontPadding="false"
            tools:text="99"
            android:textSize="@dimen/sp_10"
            android:textColor="@color/colorWhite" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:visibility="gone"
        android:layout_margin="10dp"
        android:textColor="#ffffff"
        android:text="MindMate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <Chronometer
        android:visibility="gone"
        android:id="@+id/chronometer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>