<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/toast_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/base_bg_click_toast"
    android:orientation="horizontal"
    tools:ignore="UseCompoundDrawables">

    <TextView
        android:id="@+id/tv_content_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="今日金币已达上限"
        android:textColor="@android:color/white"
        android:textSize="@dimen/sp_14" />

    <TextView
        android:id="@+id/tv_option_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="【不再提示】"
        android:textColor="#22e463"
        android:textSize="@dimen/sp_14" />

</LinearLayout>