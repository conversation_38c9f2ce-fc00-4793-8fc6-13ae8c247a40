<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="@dimen/dp_310"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/base_shape_round_background"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_40"
            android:paddingBottom="@dimen/dp_40">

            <ImageView
                android:id="@+id/imageViewTips1"
                android:layout_width="@dimen/dp_55"
                android:layout_height="@dimen/dp_55"
                android:layout_marginStart="@dimen/dp_55"
                android:src="@mipmap/base_ic_wechat_timeline" />

            <TextView
                android:id="@+id/textViewTips1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imageViewTips1"
                android:layout_marginStart="@dimen/dp_45"
                android:layout_marginTop="@dimen/dp_11"
                android:text="分享到朋友圈"
                android:textColor="#666C72"
                android:textSize="@dimen/sp_13" />

            <ImageView
                android:id="@+id/imageViewTips2"
                android:layout_width="@dimen/dp_55"
                android:layout_height="@dimen/dp_55"
                android:layout_alignParentRight="true"
                android:layout_marginEnd="@dimen/dp_55"
                android:src="@mipmap/base_ic_wechat" />

            <TextView
                android:id="@+id/textViewTips2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imageViewTips2"
                android:layout_alignParentRight="true"
                android:layout_marginTop="@dimen/dp_11"
                android:layout_marginEnd="@dimen/dp_35"
                android:text="分享到微信好友"
                android:textColor="#666C72"
                android:textSize="@dimen/sp_13" />

        </RelativeLayout>
    </FrameLayout>
</layout>