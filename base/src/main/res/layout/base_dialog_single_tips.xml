<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <RelativeLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/linearContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginStart="@dimen/dp_44"
            android:layout_marginTop="@dimen/dp_67"
            android:layout_marginEnd="@dimen/dp_44"
            android:background="@drawable/base_shape_round_background"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dp_22"
                android:text="@string/Operation_Tips"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_18" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/base_color_divider" />

            <TextView
                android:id="@+id/textViewContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dp_27"
                android:text="Are you sure to remove that userfrom your blacklist?"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_14" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/base_color_divider" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btnConfirm"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_60"
                    android:layout_marginStart="@dimen/dp_2"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/Done"
                    android:textColor="#FFF84139"
                    android:textSize="@dimen/sp_16" />
            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/imageViewBack"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="@dimen/dp_44"
            android:layout_marginEnd="@dimen/dp_44"
            android:src="@mipmap/base_ic_tips_complete_info"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/imageViewClose"
            android:layout_width="@dimen/dp_26"
            android:layout_height="@dimen/dp_26"
            android:layout_below="@+id/linearContent"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_20"
            android:src="@mipmap/base_ic_circle_close"
            android:visibility="gone" />
    </RelativeLayout>
</layout>