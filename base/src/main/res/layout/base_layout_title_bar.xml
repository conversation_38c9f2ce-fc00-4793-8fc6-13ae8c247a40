<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <RelativeLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60"
        android:orientation="horizontal"
        tools:background="@color/colorTheme">

        <FrameLayout
            android:id="@+id/relativeBack"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:paddingStart="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_20">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="center"
                android:scaleType="fitXY"
                android:src="@mipmap/base_ic_back" />

        </FrameLayout>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:singleLine="true"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_18"
            tools:text="Title" />

        <TextView
            android:id="@+id/tv_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp_16"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_15"
            tools:text="action" />
    </RelativeLayout>
</layout>