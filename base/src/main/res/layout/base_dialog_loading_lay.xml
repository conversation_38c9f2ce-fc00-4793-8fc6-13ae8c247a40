<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="#000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


<!--    <com.airbnb.lottie.LottieAnimationView-->
<!--        android:id="@+id/lottite_animation_id"-->
<!--        android:layout_width="64dp"-->
<!--        android:layout_height="64dp"-->
<!--        android:layout_gravity="center"-->
<!--        android:layout_marginTop="26dp"-->
<!--        app:lottie_autoPlay="true"-->
<!--        app:lottie_fileName="lottie_loading.json"-->
<!--        app:lottie_imageAssetsFolder="images"-->
<!--        app:lottie_loop="true" />-->

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/lottite_animation_id"
        android:layout_width="44dp"
        android:layout_height="34dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center_vertical"
        android:background="#00FFFFFF"
        android:visibility="visible"
        app:indicator="LineScale"
        app:indicator_color="#FFF" />

<!--    <LinearLayout-->
<!--        android:layout_centerInParent="true"-->

<!--        android:orientation="horizontal"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content">-->


<!--        <ImageView-->
<!--            android:id="@+id/imgProgress"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="50dp"-->
<!--            android:src="@drawable/base_loading" />-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginStart="10dp"-->
<!--            android:layout_marginTop="46dp"-->
<!--            android:layout_toRightOf="@+id/img_progress"-->
<!--            android:text="Loading..."-->
<!--            android:textColor="@color/colorPrimary"-->
<!--            android:textSize="14sp" />-->

<!--    </LinearLayout>-->
<!--    @color/colorAccent-->


</RelativeLayout>