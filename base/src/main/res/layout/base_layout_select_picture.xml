<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="ResourceName">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/base_bottom_dialog_background"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textViewTakePhoto"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_54"
            android:gravity="center"
            android:text="@string/a0017"
            android:textColor="@color/user_select_text_color"
            android:textSize="@dimen/sp_16" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_color_divider" />


        <TextView
            android:id="@+id/textViewSelectAlbum"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_54"
            android:gravity="center"
            android:text="@string/a0016"
            android:textColor="@color/user_select_text_color"
            android:textSize="@dimen/sp_16" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_10"
            android:background="#FFF6F7F9" />

        <TextView
            android:id="@+id/tvCancel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:text="@string/Cancel"
            android:textColor="#FF999999"
            android:textSize="@dimen/sp_16" />


    </LinearLayout>
</FrameLayout>