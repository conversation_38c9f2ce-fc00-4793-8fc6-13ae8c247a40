<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">




<!--    <com.coolpi.mutter.ui.present.view.fall.EmojiRainPreLayout-->
<!--        android:id="@+id/emoji_view_falling_view_id"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent" />-->
    <!--跑道1-->

    <LinearLayout
        android:id="@+id/LLContinuation"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:orientation="horizontal"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/giftIcon"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="10dp"
            tools:src="@mipmap/ic_pic_default_oval" />

        <TextView
            android:layout_toRightOf="@+id/giftIcon"
            android:id="@+id/giftNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="20dp"
            tools:text="x520"
            android:textColor="#ffcc45"
            android:textSize="30sp"
            android:textStyle="bold"/>

    </LinearLayout>

    <!--中级特效动画控件-->
    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/svg_runway_anim_middle_id"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:scaleType="centerCrop"
        app:loopCount="1" />


    <!--高级特效全屏动画-->
    <FrameLayout
        android:id="@+id/fl_view_anim_high_id"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/svg_me_anim_hi_id"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:loopCount="1" />
    </FrameLayout>

</RelativeLayout>