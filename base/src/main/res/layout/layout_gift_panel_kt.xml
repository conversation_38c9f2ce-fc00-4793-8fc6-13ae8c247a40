<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="HardcodedText,SpUsage">

        <TextView
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_5"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/base_shape_popup_indicator" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vpTabPanel"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_height="@dimen/dp_260" />

        <LinearLayout
            android:id="@+id/llIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_20"
            android:orientation="horizontal" />
    </LinearLayout>
</layout>