<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/c_level"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_16"
        android:layout_marginStart="@dimen/dp_9"
        android:layout_marginTop="@dimen/dp_2"
        android:background="@drawable/base_shape_gradient_vip"
        android:gravity="center"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_6"
        android:text="VIP"
        android:textColor="#584325"
        android:textSize="@dimen/sp_10"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/c_level_bg"
        app:layout_constraintTop_toTopOf="@id/c_level_bg" />

    <ImageView
        android:id="@+id/c_level_bg"
        android:layout_width="@dimen/dp_22"
        android:layout_height="@dimen/dp_22"
        android:src="@mipmap/ic_vip_person"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>