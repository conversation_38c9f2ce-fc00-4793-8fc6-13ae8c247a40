<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_60"
    android:orientation="horizontal">

    <FrameLayout
        android:id="@+id/relativeBack"
        android:layout_width="wrap_content"
        android:paddingLeft="@dimen/dp_27"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@mipmap/base_ic_back" />

    </FrameLayout>

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:singleLine="true"
        android:textColor="@color/public_text_color"
        android:textSize="@dimen/sp_24" />
</RelativeLayout>