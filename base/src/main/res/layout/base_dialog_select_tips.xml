<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
           android:layout_marginHorizontal="@dimen/dp_40"
            android:background="@drawable/base_shape_round_background"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_30"
                android:gravity="center"
                android:paddingTop="@dimen/dp_27"
                android:text="title"
                android:textColor="@color/user_select_text_color"
                android:textSize="@dimen/sp_16"
                android:visibility="visible" />

            <TextView
                android:id="@+id/textViewContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_30"
                android:ellipsize="none"
                android:gravity="center"
                android:textColor="#666C72"
                android:textSize="@dimen/sp_13" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginTop="@dimen/dp_32"
                android:background="@color/base_tips_divider_color" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/textViewConcle"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_45"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/base_not_allow"
                    android:textAllCaps="false"
                    android:textColor="@color/user_select_text_color"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />

                <View
                    android:layout_width="@dimen/dp_1"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dp_7"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:background="@color/base_tips_divider_color" />

                <TextView
                    android:id="@+id/textViewConfirm"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_45"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/base_allow"
                    android:textAllCaps="false"
                    android:textColor="@color/colorAccent"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>