<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none"
    tools:background="@color/background">

    <RelativeLayout
        android:id="@+id/ll_progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/viewStatusbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/status_bar_height" />
        <!--        android:background="@color/colorWhite"-->

        <RelativeLayout
            android:id="@+id/relativeNetworkError"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:layout_below="@+id/viewStatusbar"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/baseBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="无网络"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_18"
                android:visibility="gone" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/img_err"
            android:layout_width="@dimen/dp_270"
            android:layout_height="@dimen/dp_270"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_134"
            android:src="@mipmap/base_ic_net_error" />

        <TextView
            android:id="@+id/tvNetErrorTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/img_err"
            android:layout_centerHorizontal="true"
            android:layout_margin="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_32"
            android:text="@string/b59"
            android:textColor="#B3B9BF"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvError"
            android:layout_width="@dimen/dp_160"
            android:layout_height="@dimen/dp_40"
            android:layout_below="@+id/tvNetErrorTips"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@color/colorPrimaryDark"
            android:gravity="center"
            android:text="@string/b60"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvAddSchedule"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_below="@+id/tvNetErrorTips"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/base_bg_yellow_button"
            android:gravity="center"
            android:text="添加新日程"
            android:textColor="@color/device_unbind_text_color"
            android:textSize="@dimen/sp_16"
            android:visibility="gone" />
    </RelativeLayout>

</androidx.core.widget.NestedScrollView>
