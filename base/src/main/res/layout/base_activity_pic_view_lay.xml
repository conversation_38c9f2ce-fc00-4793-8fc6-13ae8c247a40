<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000000"
        >


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <ImageView

                android:id="@+id/mBackground"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:alpha="0"
                />


            <FrameLayout
                android:layout_centerInParent="true"
                android:id="@+id/mLoading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#40000000">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@mipmap/base_ic_keyboard_message_sending" />
            </FrameLayout>

            <anchor.app.base.view.MyBigImgView
                android:id="@+id/mOnePic"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <FrameLayout
                android:id="@+id/mFlViewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <anchor.app.base.view.CustomSideViewPager
                    android:id="@+id/mViewPager"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <LinearLayout
                    android:id="@+id/mPicPoint"
                    android:layout_width="wrap_content"
                    android:layout_height="6dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal" />

            </FrameLayout>

        </RelativeLayout>

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="42dp"
            android:layout_height="44dp"
            android:layout_marginStart="6dp"
            android:paddingStart="10dp"
            android:paddingTop="10dp"
            android:tint="@color/colorWhite"
            android:paddingEnd="8dp"
            android:paddingBottom="10dp"
            android:src="@mipmap/base_ic_back_black" />
        <!--        android:background="?android:attr/selectableItemBackground"-->

    </RelativeLayout>
</layout>