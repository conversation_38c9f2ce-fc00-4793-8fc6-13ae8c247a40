<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_20"
        android:background="@drawable/base_shape_round_background"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_26"
            android:gravity="center"
            android:text="Login is invalid. Do you want to log in again?"
            android:textColor="@color/user_select_text_color"
            android:textSize="@dimen/sp_16" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_color_divider" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textViewConcle"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_50"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/Cancel"
                android:textColor="@color/user_select_text_color"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/textViewConfirm"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_50"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/Done"
                android:textColor="@color/colorPrimaryDark"
                android:textSize="@dimen/sp_16" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>