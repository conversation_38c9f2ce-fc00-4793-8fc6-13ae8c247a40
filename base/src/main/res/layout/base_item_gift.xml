<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/dp_80"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/base_selector_gift"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_10"
        tools:background="#80000000"
        tools:ignore="SpUsage,ContentDescription">

        <ImageView
            android:id="@+id/ivGiftImg"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:layout_marginTop="@dimen/dp_4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvGiftName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivGiftImg"
            tools:text="鲲" />

        <ImageView
            android:id="@+id/ivGiftPrice"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_10"
            android:src="@mipmap/ic_diamond_purple"
            app:layout_constraintBottom_toBottomOf="@id/tvGiftPrice"
            app:layout_constraintEnd_toStartOf="@id/tvGiftPrice"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvGiftPrice" />

        <TextView
            android:id="@+id/tvGiftPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:textColor="#F691FF"
            android:textSize="@dimen/sp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivGiftPrice"
            app:layout_constraintTop_toBottomOf="@id/tvGiftName"
            tools:text="20" />

        <TextView
            android:id="@+id/tvGiftTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#9092a5"
            android:textSize="@dimen/sp_11"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGiftName" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>