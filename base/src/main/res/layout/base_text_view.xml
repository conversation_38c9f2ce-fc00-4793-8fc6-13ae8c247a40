<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_left"
            style="@style/textTitle1_16"
            android:text="活动地址" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="right|center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_right"
                style="@style/textTitle1_16"
                android:layout_width="match_parent"
                android:ellipsize="end"
                android:gravity="right|center_vertical"
                android:maxLines="1"
                tools:hint="请选择所在省市区" />

            <EditText
                android:id="@+id/tv_right_ed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:ellipsize="end"
                android:gravity="right"
                android:maxLines="1"
                android:textSize="@dimen/sp_16"
                android:visibility="gone"
                tools:hint="请选择所在省市区" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            app:srcCompat="@mipmap/base_ic_right_gray" />
    </LinearLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_divider"
        android:visibility="gone" />
</LinearLayout>