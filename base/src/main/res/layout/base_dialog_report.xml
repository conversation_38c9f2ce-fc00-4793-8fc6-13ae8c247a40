<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="dialog"
            type="anchor.app.base.dialog.ReportDialogFragment" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_102"
        android:background="@drawable/base_dialog_bg_01"
        android:gravity="center_horizontal"
        android:minHeight="@dimen/dp_200"
        android:orientation="vertical"
        android:paddingLeft="@dimen/dp_14"
        android:paddingRight="@dimen/dp_14">

        <TextView
            android:id="@+id/blacklist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="@string/Blacklist"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_16"
            app:bind_view_onClick="@{ () -> dialog.select(0) }" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_tips_divider_color" />

        <TextView
            android:id="@+id/report"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="@string/Report"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_16"
            app:bind_view_onClick="@{ () -> dialog.select(1) }" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_5"
            android:background="@color/base_tips_divider_color" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="@string/Cancel"
            android:textColor="@color/color_F84139"
            android:textSize="@dimen/sp_14"
            app:bind_view_onClick="@{ () -> dialog.dismiss() }" />
    </LinearLayout>
</layout>