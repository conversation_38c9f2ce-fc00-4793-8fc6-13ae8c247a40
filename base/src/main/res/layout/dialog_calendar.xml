<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_12"
        android:background="@drawable/shape_gradient_ffe0e0_white_8">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_later_year"
                android:layout_width="@dimen/dp_22"
                android:layout_height="match_parent"
                android:paddingVertical="@dimen/dp_4"
                android:paddingLeft="@dimen/dp_4"
                android:paddingRight="@dimen/dp_2"
                android:src="@mipmap/ic_year_up"/>
            <ImageView
                android:id="@+id/iv_last_month"
                android:layout_width="@dimen/dp_20"
                android:layout_height="match_parent"
                android:layout_marginRight="9.5dp"
                android:paddingHorizontal="@dimen/dp_2"
                android:paddingVertical="@dimen/dp_15"
                android:src="@mipmap/ic_month_up"/>

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2021年5月"
                android:textColor="#ff333333"
                android:textSize="16sp"
                android:textStyle="bold"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                />

            <ImageView
                android:id="@+id/iv_later_month"
                android:layout_width="@dimen/dp_20"
                android:layout_height="match_parent"
                android:layout_marginLeft="9.5dp"
                android:paddingHorizontal="@dimen/dp_2"
                android:paddingVertical="@dimen/dp_15"
                android:src="@mipmap/ic_month_down"/>
            <ImageView
                android:id="@+id/iv_last_year"
                android:layout_width="@dimen/dp_20"
                android:layout_height="match_parent"
                android:paddingVertical="@dimen/dp_4"
                android:paddingHorizontal="@dimen/dp_2"
                android:src="@mipmap/ic_year_down"/>

        </LinearLayout>

        <com.haibin.calendarview.CalendarView
            android:id="@+id/calendarView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:calendar_height="46dp"
            app:calendar_padding="10dp"
            app:current_month_lunar_text_color="#CFCFCF"
            app:current_month_text_color="#4f4f4f"
            app:day_text_size="18sp"
            app:max_select_range="-1"
            app:min_select_range="-1"
            app:min_year="2015"
            app:month_view="anchor.app.base.view.CustomRangeMonthView"
            app:month_view_show_mode="mode_fix"
            app:other_month_text_color="@color/color_999999"
            app:select_mode="range_mode"
            app:selected_text_color="#fff"
            app:selected_theme_color="@color/color_FA5144"
            app:week_background="@color/transparent"
            app:week_text_color="#333333"
            app:current_day_text_color="#333333"
            app:week_text_size="@dimen/sp_14"
            app:month_view_scrollable="true"
            app:year_view_scrollable="true"
            app:max_year="2024"
            app:year_view="anchor.app.base.view.CustomRangeMonthView"
            app:week_view="anchor.app.base.view.CustomRangeMonthView" />



    </LinearLayout>


</LinearLayout>