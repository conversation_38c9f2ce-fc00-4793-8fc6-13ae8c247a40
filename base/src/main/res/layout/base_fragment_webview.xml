<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="anchor.app.base.web.BaseWebViewFragment" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:visibility="visible"
            android:background="@color/colorWhite">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginBottom="@dimen/dp_7"
                android:layout_weight="1"
                tools:text="SoulPair爱英语"
                android:textColor="@color/public_text_color"
                android:textSize="@dimen/sp_23"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/imageViewScan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_10"
                android:visibility="invisible"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginEnd="@dimen/dp_10"
                android:src="@mipmap/discover_ic_search"/>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/framelayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <anchor.app.base.web.bridge.DWebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <anchor.app.base.view.WebProgressBarView
                android:id="@+id/webprogress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorWhite"
                app:webviewprogress="100"
                app:webviewprogressHeight="5" />
        </FrameLayout>
    </LinearLayout>
</layout>