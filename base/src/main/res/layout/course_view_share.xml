<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llShareContainer"
    android:layout_width="750px"
    android:layout_height="1334px"
    android:orientation="vertical">

    <!--顶部图片-->
    <ImageView
        android:id="@+id/iv_top_share"
        android:layout_width="750px"
        android:layout_height="903px"
        android:scaleType="fitXY" />

    <!--中间数据-->
    <LinearLayout
        android:id="@+id/ll_content_share"
        android:layout_width="750px"
        android:layout_height="193px"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/account_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5"
                android:textColor="#FF6619"
                android:textSize="56px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="累计上课"
                android:textColor="#666C72"
                android:textSize="24px" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/account_opening"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="34"
                android:textColor="#FF6619"
                android:textSize="56px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="累计开口"
                android:textColor="#666C72"
                android:textSize="24px" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_third_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="6"
                android:textColor="#FF6619"
                android:textSize="56px" />

            <TextView
                android:id="@+id/tv_third_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="本课开口"
                android:textColor="#666C72"
                android:textSize="24px" />

        </LinearLayout>

    </LinearLayout>

    <!--底部二维码-->
    <LinearLayout
        android:id="@+id/ll_bottom_share"
        android:layout_width="750px"
        android:layout_height="238px"
        android:background="#FAFAFA"
        android:orientation="horizontal"
        android:paddingLeft="40px"
        android:paddingRight="40px">

        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="110px"
            android:layout_height="110px"
            android:layout_gravity="center_vertical"
            android:scaleType="fitXY"
            android:src="@mipmap/ic_launcher" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingLeft="5dp"
            android:paddingRight="5dp">

            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:singleLine="true"
                android:text="我家Wilsion讲英语故事"
                android:textColor="#475460"
                android:textSize="24px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14px"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_star_1"
                    android:layout_width="38px"
                    android:layout_height="38px"
                    android:src="@drawable/icon_star_share" />

                <ImageView
                    android:id="@+id/iv_star_2"
                    android:layout_width="38px"
                    android:layout_height="38px"
                    android:layout_marginStart="10px"
                    android:src="@drawable/icon_star_share" />

                <ImageView
                    android:id="@+id/iv_star_3"
                    android:layout_width="38px"
                    android:layout_height="38px"
                    android:layout_marginStart="10px"
                    android:src="@drawable/icon_star_share" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_qcode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="invisible">

            <ImageView
                android:id="@+id/iv_qcode"
                android:layout_width="139px"
                android:layout_height="139px"
                android:scaleType="fitXY" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="扫码收听作品"
                android:textColor="#B3B9BF"
                android:textSize="24px" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>