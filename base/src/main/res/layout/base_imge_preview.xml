<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView
        android:id="@+id/longImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/ivLong"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@mipmap/base_ic_main_default"
        android:visibility="gone"/>

    <anchor.app.base.view.MyBigImgView
        android:id="@+id/preview_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

</FrameLayout>