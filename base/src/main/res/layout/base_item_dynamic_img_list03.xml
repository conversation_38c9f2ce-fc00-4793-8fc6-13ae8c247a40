<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="ContentDescription">

    <anchor.app.base.view.RoundImageView
        app:corner_radius="@dimen/dp_12"
        android:id="@+id/iv_head_id"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="#50000000"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tvInReview"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="In Review"
        android:textColor="#ffffb909"
        android:textSize="@dimen/sp_14"
        android:layout_centerHorizontal="true"
        />




</androidx.constraintlayout.widget.ConstraintLayout>