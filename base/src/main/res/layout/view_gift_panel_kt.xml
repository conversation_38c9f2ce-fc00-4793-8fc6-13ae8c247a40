<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/CLBG"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:ignore="HardcodedText,SpUsage">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/base_shape_gift_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:ignore="ContentDescription,HardcodedText,SpUsage,RtlSymmetry">

            <anchor.app.base.view.tablayout.MTabLayout
                android:id="@+id/tabLayout"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="8dp"
                android:layout_marginTop="@dimen/dp_9"
                android:layout_marginEnd="5dp"
                app:ddAnimatedIndicator="dachshund"
                app:ddIndicatorColor="#7E3BFC"
                app:ddIndicatorHeight="2dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabMaxWidth="200dp"
                app:tabMinWidth="20dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="8dp"
                app:tabPaddingStart="8dp"
                app:tabSelectedTextColor="#7E3BFC"
                app:tabTextAppearance="@style/TabLayoutTextStyle_15_bold"
                app:tabTextColor="#9092a5" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/vpGiftCategory"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_310"
                android:layout_marginTop="@dimen/dp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/vBottomPanel"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vpGiftCategory" />

            <LinearLayout
                android:id="@+id/llMyBalance"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:layout_marginStart="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/vBottomPanel"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/vBottomPanel">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@mipmap/diamond" />

                <TextView
                    android:id="@+id/diamondCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_4"
                    android:textColor="#ffffff"
                    android:textSize="15sp"
                    tools:text="1200" />

                <TextView
                    android:id="@+id/tvBalanceOpr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="19dp"
                    android:text=""
                    android:textColor="#f16aa0"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:layout_marginEnd="8dp"
                android:background="@drawable/base_rectangle_9f2af8_r16"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/vBottomPanel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/vBottomPanel">

                <LinearLayout
                    android:id="@+id/llCountInput"
                    android:layout_width="@dimen/dp_45"
                    android:layout_height="@dimen/dp_28"
                    android:layout_marginStart="1dp"
                    android:background="@drawable/base_rectangle_161823_r19"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvCountInput"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:minWidth="@dimen/dp_50"
                        android:text="1"
                        android:textColor="#ffffff"
                        android:textSize="@dimen/sp_14"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="@dimen/sp_20"
                        android:layout_height="@dimen/sp_20"
                        android:src="@mipmap/base_ic_arrow_right_grey_small" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvSend"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_7"
                    android:paddingEnd="@dimen/dp_10"
                    android:text="Ask for gift"
                    android:textColor="#f7f7f7"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>