<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName">

    <RelativeLayout
        android:id="@+id/RL"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <!--    android:layout_width="@dimen/dp_90"-->
        <!--    android:layout_height="@dimen/dp_90"-->
        <anchor.app.base.view.RoundImageView
            android:layout_centerInParent="true"
            android:id="@+id/userPic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop"
            android:background="@mipmap/ic_pic_default_oval"
            app:is_circle="true" />

        <!--头套区域-->
        <FrameLayout
            android:layout_centerInParent="true"
            android:id="@+id/headgearContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <!--        android:layout_width="wrap_content"-->
            <!--        android:layout_height="wrap_content"-->
            <!--        android:src="@mipmap/base_ic_headgear_01"-->
            <!--        android:layout_width="@dimen/dp_120"-->
            <!--        android:layout_height="@dimen/dp_120"-->
            <!--静态头套-->
            <anchor.app.base.view.RoundImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/headgearStatic"
                android:scaleType="centerCrop" />

            <!--动态头套-->
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/headgearDynamic"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>

    </RelativeLayout>
</layout>