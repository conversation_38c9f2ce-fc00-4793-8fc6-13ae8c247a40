<!DOCTYPE html>
<html>

<head lang="zh-cmn-Hans">
    <meta charset="UTF-8">
    <title>无网络</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=0.5,user-scalable=no" />
    <script src="js/dsbridge.js"> </script>
    <style>
        body{
            text-align: center;
            padding-top:4.14rem;
        }
        .btn {
            width: 6.4rem;
            height: 1.6rem;
            background:url('images/button.png');
            background-size:100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 25px;
            border-radius: 10px;
            color:#fff;
            position: fixed;
            bottom:6rem;
            left:50%;
            transform: translateX(-50%)
        }

        img {
            width: 4.8rem;
            height: 4.66rem;
        }
        p{
            color:#B3B9BF;
            font-size: 26px;
            margin-top:1.32rem;
        }
    </style>
</head>

<body>
    <img src="images/netError.png" alt="">
    <p>网络连接失败，请检查您的网络设置</p>
    <div class="btn" onclick="reload()">重新加载</div>

    <script>

        function reload() {
            dsBridge.call("refresh")
        }
        function setsize() {
            var winW = document.documentElement.clientWidth,
                winH = document.documentElement.clientHeight,
                baseFontSize = 50,
                baseWidth = 750,
                winWidthSize = Math.min(winW, winH);
            if (winWidthSize > 560) {
                winWidthSize = 560;
            }
            if (winWidthSize < 270) {
                winWidthSize = 270;
            }
            var _html = document.getElementsByTagName('html')[0];
            _html.style.fontSize = winWidthSize / baseWidth * baseFontSize + 'px';
            window.onload = function () {
                var div = document.createElement("div");
                document.body.appendChild(div);
                div.style.width = "10rem";
                div.style.height = "0px";
                if (div.clientWidth !== (winWidthSize / baseWidth * baseFontSize) * 10) {
                    var basePercent = div.clientWidth / (winWidthSize / baseWidth * baseFontSize) / 10;
                    _html.style.fontSize = (winWidthSize / baseWidth * baseFontSize) / basePercent + 'px';
                }
            }
        }
        setsize();


    </script>
</body>

</html>