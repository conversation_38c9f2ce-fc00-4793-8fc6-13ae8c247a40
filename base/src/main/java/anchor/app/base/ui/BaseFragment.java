package anchor.app.base.ui;

import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProviders;


import anchor.app.base.R;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.ui.layoutstate.FragmentLayoutStateManager;
import anchor.app.base.utils.ClassUtil;
import anchor.app.base.utils.PerfectClickListener;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.viewmodel.BaseViewModel;


/**
 * ViewModel、ViewDataBinding都需要的基类
 */

public abstract class BaseFragment<VM extends BaseViewModel, DB extends ViewDataBinding>
        extends BaseNoModelFragment<DB> {

    public static final int REQUEST_LOGIN = 100;
    protected VM viewModel;
    // 加载中
    protected View loadingView;
    // 加载失败
    protected View errorView;
    // 动画
    protected AnimationDrawable mAnimationDrawable;
    // fragment是否显示了
    protected boolean mIsVisible = false;
    protected RelativeLayout mContainer;
    private FragmentLayoutStateManager layoutStateManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        /**Description:加载业务布局; <AUTHOR>
        bindingView = DataBindingUtil.inflate(getActivity().getLayoutInflater(), getLayoutId(), null, false);
        /**  <AUTHOR>  Description : 布局状态管理器,管理展示加载状态,错误状态等*/
        layoutStateManager = new FragmentLayoutStateManager(this,inflater,bindingView);
        return layoutStateManager.addStateLayout();//将loading布局进行返回
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        layoutStateManager.setStateLayout();
        /**Description:进行各类初始化; <AUTHOR>
        initViewModel();//viewmodel的初始化
        initView();//view的初始化
        loadData();//加载服务器数据
    }




    /**  <AUTHOR>  Description : 布局状态*/
    protected void showLoading() { layoutStateManager.showLoading(); }
    protected void showContentView() { layoutStateManager.showContentView(); }
    protected void showNetWorkError() { layoutStateManager.showNetWorkError(); }
    protected void showError(String message) { layoutStateManager.showError(message); }
    protected void showError(String message, int errorImageResourceId) { layoutStateManager.showError(message,errorImageResourceId); }
    protected void showEmpty(String message) { layoutStateManager.showError(message,-1,false); }

    /**
     * 显示时加载数据,需要这样的使用
     * 注意声明 isPrepared，先初始化
     * 生命周期会先执行 setUserVisibleHint 再执行onActivityCreated
     * 在 onActivityCreated 之后第一次显示加载数据，只加载一次
     */
    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mActivity = getActivity();
    }
    /**
     * 初始化ViewModel
     */
    private void initViewModel() {
        /**Description:获取当前类中的viewmodel的class类对象; <AUTHOR>
        Class<VM> viewModelClass =  ClassUtil.<VM>getViewModel(this);

        /**Description:通过反射加载viewmodel的类对象实例; <AUTHOR>
        if (viewModelClass != null) {
            this.viewModel = ViewModelProviders.of(this).get(viewModelClass);
        }

        viewModel.initFliterAttribute(mActivity);

        /**Description:设置viewmodel的error的数据监听; <AUTHOR>
        viewModel.error.observe(mActivity, o -> {
            if (o instanceof ResultException) {
                if (((ResultException) o).getCode() == BaseResult.SUCCESS) {
//                    ...
                } else if (((ResultException) o).getCode() == BaseResult.NETWORK_ERROR) {
                    showNetWorkError();
                } else if (((ResultException) o).getCode() == BaseResult.NOT_DATA) {
                    showEmpty(((ResultException) o).getMessage());
                }
                else {
                    showError(((ResultException) o).getMessage());
                }
            }else {
                ToastUtil.show(((Throwable) o).getMessage());
                showError(((Throwable) o).getMessage());
            }
        });
    }

    /**
     * 加载失败后点击后的操作
     */
    public void onRefresh() {
        loadData();
    }

    public void loadData() {
    }

    /**
     * 初始化视图
     */
    protected abstract void initView();


    protected void showScheduleEmpty(String message, View.OnClickListener listener) {
        if (loadingView != null && loadingView.getVisibility() != View.GONE) {
            loadingView.setVisibility(View.GONE);
        }
        // 停止动画
        if (mAnimationDrawable.isRunning()) {
            mAnimationDrawable.stop();
        }
        if (errorView == null) {
            ViewStub viewStub = getView(R.id.vs_error_refresh);
            errorView = viewStub.inflate();
            TextView textView = errorView.findViewById(R.id.tvNetErrorTips);
            textView.setText(message);
            errorView.findViewById(R.id.tvError).setVisibility(View.GONE);
            errorView.findViewById(R.id.relativeNetworkError).setVisibility(View.GONE);
            // 点击加载失败布局
            errorView.findViewById(R.id.tvError).setOnClickListener(new PerfectClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    showLoading();
                    onRefresh();
                }
            });
            errorView.findViewById(R.id.tvAddSchedule).setVisibility(View.VISIBLE);
            errorView.findViewById(R.id.tvAddSchedule).setOnClickListener(listener);
        } else {
            errorView.setVisibility(View.VISIBLE);
        }
        if (bindingView.getRoot().getVisibility() != View.GONE) {
            bindingView.getRoot().setVisibility(View.GONE);
        }
    }

}
