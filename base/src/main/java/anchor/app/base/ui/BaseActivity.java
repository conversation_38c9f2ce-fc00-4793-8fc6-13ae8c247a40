package anchor.app.base.ui;

import static com.uber.autodispose.AutoDispose.autoDisposable;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProviders;

import com.hjq.language.MultiLanguages;
import com.tbruyelle.rxpermissions2.RxPermissions;

import anchor.app.base.R;
import anchor.app.base.dialog.TokenInvailidDialog;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.ui.layoutstate.ActivityLayoutStateManager;
import anchor.app.base.utils.ActivityUtil;
import anchor.app.base.utils.ClassUtil;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.utils.immersionbar.standard.ImmersionBar;
import anchor.app.base.viewmodel.BaseViewModel;


/**
 * ViewModel、ViewDataBinding都需要的基类
 */

public abstract class BaseActivity<VM extends BaseViewModel, DB extends ViewDataBinding> extends AutoDisposeActivity {

    public static final int REQUEST_CODE_SELECT_AREA = 100;
    public static final int RESULT_CODE_SELECT_AREA = 101;

    public VM viewModel;

    // 布局view
    protected DB bindingView;
    protected Activity mContext = this;
    protected RxPermissions rxPermissions;
    protected LinearLayout mLinearRoot;
    protected ActivityLayoutStateManager layoutStateManager;
    private TokenInvailidDialog mTokenInvailidDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityUtil.getInstance().addActivity(this);
        int layoutId = getLayoutId();
        setContentView(layoutId);
    }

    protected abstract int getLayoutId();

    @Override
    public void setContentView(@LayoutRes int layoutResID) {
        rxPermissions = new RxPermissions(this);

        bindingView = DataBindingUtil.inflate(getLayoutInflater(), layoutResID, null, false);

        /**  <AUTHOR>  Description : 布局状态管理器,管理展示加载状态,错误状态等*/
        layoutStateManager = new ActivityLayoutStateManager(this, bindingView).setStateLayout();
        mLinearRoot = layoutStateManager.getStateLayoutRootView();
        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_TOKEN_INVILADATED, Boolean.class).as(autoDisposable(getScopeProvider())).subscribe(result -> {
            if (result) {
                if (mTokenInvailidDialog == null) mTokenInvailidDialog = new TokenInvailidDialog(mContext);
                if (!mTokenInvailidDialog.isShowing()) mTokenInvailidDialog.show();
            }
        });

        View backView = bindingView.getRoot().findViewById(R.id.relativeBack);
        if (backView != null) {
            backView.setOnClickListener(v -> finish());
        }

        initViewModel();
        initView();
        loadData();
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        // 绑定语种
        super.attachBaseContext(MultiLanguages.attach(newBase));
    }

    protected void setTitleText(@StringRes int titleTextRes) {
        setTitleText(getString(titleTextRes));
    }

    protected void setTitleText(CharSequence titleText) {
        TextView titleView = bindingView.getRoot().findViewById(R.id.tvTitle);
        if (titleView != null) {
            titleView.setText(titleText);
        }
    }

    protected void setActionImage(int resId) {
        TextView titleView = bindingView.getRoot().findViewById(R.id.tv_action);
        if (titleView != null) {
            titleView.setBackgroundResource(resId);
        }
    }

    protected void setActionText(@StringRes int titleTextRes) {
        setTitleText(getString(titleTextRes));
    }

    protected void setActionText(CharSequence titleText) {
        TextView titleView = bindingView.getRoot().findViewById(R.id.tv_action);
        if (titleView != null) {
            titleView.setText(titleText);
        }
    }

    protected void setOnActionListener(View.OnClickListener onClickListener) {
        TextView titleView = bindingView.getRoot().findViewById(R.id.tv_action);
        if (titleView != null) {
            titleView.setOnClickListener(onClickListener);
        }
    }


    /**
     * <AUTHOR>  Description : 布局状态
     */
    protected void showLoading() {
        layoutStateManager.showLoading();
    }

    protected void showContentView() {
        layoutStateManager.showContentView();
    }

    protected void showNetWorkError() {
        layoutStateManager.showNetWorkError();
    }

    protected void showError(String message) {
        layoutStateManager.showError(message);
    }

    protected void showError(String message, int errorImageResourceId) {
        layoutStateManager.showError(message, errorImageResourceId);
    }

    protected void showEmpty(String message) {
        layoutStateManager.showError(message, -1, false);
    }

    @Override
    public void onResume() {
        super.onResume();
//        MobclickAgent.onResume(this);
    }

    @Override
    public void onPause() {
        super.onPause();
//        MobclickAgent.onPause(this);
    }

    /**
     * 初始化ViewModel
     */
    private void initViewModel() {
        Class<VM> viewModelClass = ClassUtil.getViewModel(this);
        if (viewModelClass != null) {
            this.viewModel = ViewModelProviders.of(this).get(viewModelClass);
        }
        viewModel.initFliterAttribute(this);

        viewModel.error.observe(this, o -> {
            if (o instanceof ResultException) {
                if (((ResultException) o).getCode() == BaseResult.SUCCESS) {
//                    ...
                } else if (((ResultException) o).getCode() == BaseResult.NETWORK_ERROR) {
                    showNetWorkError();
                } else if (((ResultException) o).getCode() == BaseResult.NOT_DATA) {
                    showEmpty(((ResultException) o).getMessage());
                } else {
                    showError(((ResultException) o).getMessage());
                }
            } else {
                ToastUtil.show(((Throwable) o).getMessage());
                showError(((Throwable) o).getMessage());
            }
        });
    }

    protected boolean isDarkMode() {
        return false;
    }

    protected boolean fitsSystemWindows() {
        return true;
    }

    protected boolean keyboardEnable() {
        return false;
    }

    protected void initImmersionBar() {
        ImmersionBar.with(this).fitsSystemWindows(fitsSystemWindows()).keyboardEnable(keyboardEnable()).navigationBarColor(R.color.colorTheme).statusBarDarkFont(isDarkMode(), 0.5f).init();

    }

    /**
     * 初始化视图
     */
    protected abstract void initView();

    protected void loadData() {
    }


    /**
     * 失败后点击刷新
     */
    public void onRefresh() {
        loadData();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (bindingView != null) {
            bindingView.unbind();
        }
        mContext = null;
        ActivityUtil.getInstance().removeActivity(this);
    }
}
