package anchor.app.base.ui.layoutstate;

import android.graphics.drawable.AnimationDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.databinding.ViewDataBinding;

import anchor.app.base.R;
import anchor.app.base.ui.BaseFragment;
import anchor.app.base.utils.PerfectClickListener;

public class FragmentLayoutStateManager extends BaseLayoutState {

    private BaseFragment baseFragment;
    protected View stateLayout ;

    public FragmentLayoutStateManager(BaseFragment baseFragment, LayoutInflater inflater, ViewDataBinding viewRoot){
        this.baseFragment = baseFragment;
        this.rootView = viewRoot;
        /**Description:加载器加载带有loading的布局base_fragment; <AUTHOR>
        stateLayout = inflater.inflate(R.layout.base_fragment, null);
    }

    /**  <AUTHOR>  Description : 将业务视图布局,添加到状态布局中*/
    public View addStateLayout (){
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        rootView.getRoot().setLayoutParams(params);
        RelativeLayout mContainer = stateLayout.findViewById(R.id.container);
        mContainer.addView(rootView.getRoot());

        return stateLayout;
    }

    public FragmentLayoutStateManager setStateLayout (){

        /**Description:获取loading布局中的loadng  <AUTHOR>  */
        loadingView = ((ViewStub) baseFragment.getView(R.id.vs_loading)).inflate();//ViewStub加载的时候不占用布局位置，只有当调用的时候才占用，有利于节省内存空间
        //当前ViewStub一旦被调用就直接展示出来了
        /**Description:获取loading布局中的loading对象，开启动画; <AUTHOR>
        ImageView img = loadingView.findViewById(R.id.img_progress);
        mAnimationDrawable = (AnimationDrawable) img.getDrawable();// 加载动画
        if (!mAnimationDrawable.isRunning()) {// 默认进入页面就开启动画
            mAnimationDrawable.start();
        }

        /**Description:默认设置业务布局在初始化未完成不可见,展示加载状态布局
         *             默认界面业务布局加载完成都隐藏，展示上面的lading布局，等后面获取到服务器数据后就展示业务布局; <AUTHOR>
        rootView.getRoot().setVisibility(View.GONE);

        return this;
    }


    /**
     * @return
     * <AUTHOR>  Description : ErrorView只在需要的时候进行inflate填充,并不默认初始化
     */
    protected View initErrorView(String message, int errorImageResourceId){
        errorView = ((ViewStub) baseFragment.getView(R.id.vs_error_refresh)).inflate();
        errorView.findViewById(R.id.relativeNetworkError).setVisibility(View.GONE);
        errorView.findViewById(R.id.baseBack).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                baseFragment.getFragmentManager().popBackStack();
            }
        });
        if (message!=null&&message.length() > 0) {//当未进行自定义设置message,默认展示网络错误布局
            TextView textView = errorView.findViewById(R.id.tvNetErrorTips);
            textView.setText(message);
        }
        if (errorImageResourceId != -1) {
            ImageView errorImageView = errorView.findViewById(R.id.img_err);
            errorImageView.setImageResource(errorImageResourceId);
        }
//        errorView.findViewById(R.id.tvError).setVisibility(View.GONE);
        // 点击加载失败布局
        errorView.findViewById(R.id.tvError).setOnClickListener(new PerfectClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                showLoading();
                baseFragment.onRefresh();
            }
        });

        return errorView;
    }



}
