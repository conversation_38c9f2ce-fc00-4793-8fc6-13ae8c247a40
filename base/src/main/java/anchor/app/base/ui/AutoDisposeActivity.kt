package anchor.app.base.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import anchor.app.base.R
import anchor.app.base.utils.immersionbar.standard.ImmersionBar

/**
 * 用于防止内存泄漏
 */
open abstract class AutoDisposeActivity : AppCompatActivity() {

    protected val scopeProvider: AndroidLifecycleScopeProvider by lazy {
        AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initImmersionBar()
    }

    /**
     * 设置状态栏颜色
     */
    open protected fun initImmersionBar() {
        ImmersionBar.with(this)
            .fitsSystemWindows(true)
            .statusBarDarkFont(false, 0.2f)
            .navigationBarColor(R.color.colorTheme)
            .init()
    }
}