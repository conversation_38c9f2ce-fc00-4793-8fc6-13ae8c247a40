package anchor.app.base.ui

import com.angcyo.dsladapter.DslAdapterItem
import anchor.app.base.R
import anchor.app.base.databinding.FragmentBaseRecyclerviewBinding
import anchor.app.base.viewmodel.BaseViewModel

open class BaseRecyclerViewFragment<VM : BaseViewModel<*>> :
    BaseFragment<VM, FragmentBaseRecyclerviewBinding>() {
    override fun initView() {
    }

    override fun getLayoutId(): Int = R.layout.fragment_base_recyclerview

    protected inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false, items: List<Any>
    ) {
        bindingView.refreshLayout.append<T>(refresh, items) { data ->
            itemData = data
        }
    }

    protected inline fun <reified T : DslAdapterItem> append(
        refresh: Boolean = false,
        items: List<Any>,
        crossinline initItem: T.(data: Any?) -> Unit = {}
    ) {
        bindingView.refreshLayout.append<T>(refresh, items, initItem)
    }
}