package anchor.app.base.ui.layoutstate;

import android.graphics.drawable.AnimationDrawable;
import android.view.View;

import androidx.databinding.ViewDataBinding;

import anchor.app.base.R;

public abstract class BaseLayoutState {

    protected AnimationDrawable mAnimationDrawable;
    protected View loadingView;
    protected View errorView;
    protected ViewDataBinding rootView;

    /**  <AUTHOR>  Description : 展示加载布局*/
    public void showLoading() {
        if (loadingView != null && loadingView.getVisibility() != View.VISIBLE) {
            loadingView.setVisibility(View.VISIBLE);
        }
        // 开始动画
        if (!mAnimationDrawable.isRunning()) {
            mAnimationDrawable.start();
        }
        if (rootView.getRoot().getVisibility() != View.GONE) {
            rootView.getRoot().setVisibility(View.GONE);
        }
        if (errorView != null) {
            errorView.setVisibility(View.GONE);
        }
    }

    /**  <AUTHOR>  Description : 展示业务布局*/
    public void showContentView() {
        if (loadingView != null && loadingView.getVisibility() != View.GONE) {
            loadingView.setVisibility(View.GONE);
        }
        // 停止动画
        if (mAnimationDrawable.isRunning()) {
            mAnimationDrawable.stop();
        }
        if (errorView != null) {
            errorView.setVisibility(View.GONE);
        }
        if (rootView.getRoot().getVisibility() != View.VISIBLE) {
            rootView.getRoot().setVisibility(View.VISIBLE);
        }
    }

    /**  <AUTHOR>  Description : 展示网络错误布局*/
    public void showNetWorkError() {
        showError("",-1,true);
    }

    /**  <AUTHOR>  Description : 展示自定义错误布局*/
    public void showError(String message) {
        showError(message,-1);
    }
    public void showError(String errorMessage, int errorImageResourceId) {
        showError(errorMessage,errorImageResourceId,true);
    }

    /**  <AUTHOR>  Description : 展示自定义错误布局*/
    public void showError(String errorMessage, int errorImageResourceId,boolean isShowReload) {
        if (loadingView != null && loadingView.getVisibility() != View.GONE) {
            loadingView.setVisibility(View.GONE);
        }
        // 停止动画
        if (mAnimationDrawable.isRunning()) {
            mAnimationDrawable.stop();
        }
        if (errorView == null) {
            View view = initErrorView(errorMessage, errorImageResourceId);
            if (isShowReload) {
                view.findViewById(R.id.tvError).setVisibility(View.VISIBLE);
            }else {
                view.findViewById(R.id.tvError).setVisibility(View.GONE);
            }
        } else {
            errorView.setVisibility(View.VISIBLE);
        }
        if (rootView.getRoot().getVisibility() != View.GONE) {
            rootView.getRoot().setVisibility(View.GONE);
        }
    }



     protected abstract View initErrorView(String message, int errorImageResourceId);



}
