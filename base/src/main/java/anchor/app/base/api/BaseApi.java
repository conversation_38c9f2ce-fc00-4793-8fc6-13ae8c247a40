package anchor.app.base.api;

import java.io.File;
import java.util.HashMap;
import java.util.List;

import anchor.app.base.bean.GiftBean;
import anchor.app.base.bean.PersonalCenterBean;
import anchor.app.base.bean.UserSimpleBean;
import anchor.app.base.retrofit.AliApiClient;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.utils.FileUtil;
import anchor.app.base.utils.OkHttpUtil;
import io.reactivex.Flowable;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;

public class BaseApi {

    /**
     * 上传Mp3
     *
     * @param filePath
     * @return
     */
    public static void uploadMp3(String url, String filePath, Callback callBack) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("audio/mp3"), FileUtil.getFileBytes(filePath));
        Request request = new Request.Builder()
                .url(OkHttpUtil.assembleUrlWithParams(url, null))
                .put(requestBody)
                .build();
        AliApiClient.getInstance().getOkhttpClient().newCall(request).enqueue(callBack);
    }

    /**
     * 上传图片
     *
     * @param file
     * @return
     */
    public static Flowable<BaseResult> uploadPicture(File file) {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);

        RequestBody imageBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
        builder.addFormDataPart("file", file.getName(), imageBody);//imgfile 后台接收图片流的参数名

        List<MultipartBody.Part> parts = builder.build().parts();
        return BaseApiClient.getInstance().create(IBaseApi.class).uploadPicture(parts)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult> uploadPicture(byte[] content, String fileName) {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);

        RequestBody imageBody = RequestBody.create(MediaType.parse("multipart/form-data"), content);
        builder.addFormDataPart("file", fileName, imageBody);//imgfile 后台接收图片流的参数名

        List<MultipartBody.Part> parts = builder.build().parts();
        return BaseApiClient.getInstance().create(IBaseApi.class).uploadPicture(parts)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<Object> getAD() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getAD()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }



    public static Flowable<BaseResult<List<GiftBean.DataDTO>>> getGiftList() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getGiftList()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> sendGift(long anchorId, int giveNum, String id, String source, String userRole) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("anchorId", anchorId);
        request.put("giveNum", giveNum);
        request.put("id", id);
        request.put("source", source);
        request.put("userRole", userRole);
        return BaseApiClient.getInstance().create(IBaseApi.class).sendGift(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> askGift(long userId, int giveNum, String id) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("userId", userId);
        request.put("giveNum", giveNum);
        request.put("giftCode", id);
        return BaseApiClient.getInstance().create(IBaseApi.class).askGift(userId, id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getHeartBeat() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getHeartBeat()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<UserSimpleBean>> getUserSimple(String targetId) {
        return BaseApiClient.getInstance().create(IBaseApi.class).getUserSimple(targetId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getMomentAudit(String targetId) {
        return BaseApiClient.getInstance().create(IBaseApi.class).getMomentAudit(targetId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<String>> getFollowFlag(String targetId) {


        return BaseApiClient.getInstance().create(IBaseApi.class).getFollowFlag(targetId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getPackageRenew() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getPackageRenew()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<String>> getRongcloundAppId() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getRongcloundAppId()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getSysParam() {
        return BaseApiClient.getInstance().create(IBaseApi.class).getSysParam()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getVideoWarn(String channelId, String warnUrl) {
        return BaseApiClient.getInstance().create(IBaseApi.class).getVideoWarn(channelId, warnUrl)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<PersonalCenterBean>> getPersonalCenter(long id, String userRole) {
        if (userRole.equals("1")) {
            return BaseApiClient.getInstance().create(IBaseApi.class).getPersonalCenter(id)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
        }else return BaseApiClient.getInstance().create(IBaseApi.class).queryUserInfo(id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 主播端收到礼物之后获取自定义场景文本
     * @param userId
     * @return
     */
    public static Flowable<BaseResult<Object>> scenarioScript(String userId) {
        return BaseApiClient.getInstance().create(IBaseApi.class).scenarioScript(userId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }
}
