package anchor.app.base.api;

import static anchor.app.base.core.Constants.HTTP_URL.follow_flag;
import static anchor.app.base.core.Constants.HTTP_URL.moment_audit;
import static anchor.app.base.core.Constants.HTTP_URL.package_renew;
import static anchor.app.base.core.Constants.HTTP_URL.rongclound_appId;
import static anchor.app.base.core.Constants.HTTP_URL.sys_param;
import static anchor.app.base.core.Constants.HTTP_URL.user_simple;
import static anchor.app.base.core.Constants.HTTP_URL.video_warn;


import java.util.List;
import java.util.Map;

import anchor.app.base.bean.GiftBean;
import anchor.app.base.bean.PersonalCenterBean;
import anchor.app.base.bean.UserSimpleBean;
import anchor.app.base.core.Constants;
import anchor.app.base.retrofit.BaseResult;
import io.reactivex.Flowable;
import okhttp3.MultipartBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;

public interface IBaseApi {

    /**
     * 上传图片
     */
    @Multipart
    @POST(Constants.HTTP_URL.upload_picture)
    Flowable<BaseResult> uploadPicture(@Part List<MultipartBody.Part> partList);

    //https://www.ip.cn/ip/**************.html
//    @GET("https://searchplugin.csdn.net/api/v1/ip/get")
//    @GET("https://searchplugin.csdn.net/api/v1/ip/get") // {address=中国 广东 深圳 电信, ip=**************}
    @GET("https://ip.useragentinfo.com/json?ip=[]")
    // {"country":"中国","short_name":"CN","province":"广东省","city":"深圳市","area":"南山区","isp":"电信","net":"城域网","ip":"**************","code":200,"desc":"success"}
    Flowable<Object> getAD();

    /**
     * 解绑设备
     */
    @GET(Constants.HTTP_URL.scenario_script)
    Flowable<BaseResult<Object>> scenarioScript(@Query("userId") String userId);


    @POST(Constants.HTTP_URL.get_gift_list)
    Flowable<BaseResult<List<GiftBean.DataDTO>>> getGiftList();


    @POST(Constants.HTTP_URL.send_gift)
//    Flowable<BaseResult<Object>> sendGift(@Query("anchorId") long anchorId,@Query("giveNum") int giveNum,@Query("id") String id,@Query("source") String source);
    Flowable<BaseResult<Object>> sendGift(@Body Map<String, Object> map);

    @POST(Constants.HTTP_URL.ask_gift)
//    Flowable<BaseResult<Object>> askGift(@Body Map<String, Object> map);
    Flowable<BaseResult<Object>> askGift(@Query("userId") long userId, @Query("giftCode") String giftCode);


    @POST(Constants.HTTP_URL.heartbeat)
    Flowable<BaseResult<Object>> getHeartBeat();
//    @POST(Constants.HTTP_URL.getGiftList)
//    Flowable<BaseResult<GiftBean>> getGiftList();

//    @POST(Constants.HTTP_URL.getGiftList)
//    Flowable<BaseResult<GiftBean>> getGiftList();


    @GET(user_simple)
    Flowable<BaseResult<UserSimpleBean>> getUserSimple(@Query("id") String targetId);

    @POST(moment_audit)
    Flowable<BaseResult<Object>> getMomentAudit(@Query("id") String targetId);

    @GET(package_renew)
    Flowable<BaseResult<Object>> getPackageRenew();

    @POST(follow_flag)
    Flowable<BaseResult<String>> getFollowFlag(@Query("id") String targetId);

    @GET(rongclound_appId)
    Flowable<BaseResult<String>> getRongcloundAppId();

    @GET(sys_param)
    Flowable<BaseResult<Object>> getSysParam();

    @POST(video_warn)
    Flowable<BaseResult<Object>> getVideoWarn(@Query("channelId") String channelId, @Query("warnUrl") String warnUrl);


    @GET(Constants.HTTP_URL.get_personal_center)
    Flowable<BaseResult<PersonalCenterBean>> getPersonalCenter(@Query("id") long id);

    @GET(Constants.HTTP_URL.query_user_detail_consumer)
    Flowable<BaseResult<PersonalCenterBean>> queryUserInfo(@Query("id") Long id);

}
