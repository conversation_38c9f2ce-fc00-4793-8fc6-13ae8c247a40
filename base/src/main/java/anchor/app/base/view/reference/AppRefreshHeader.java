package anchor.app.base.view.reference;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshKernel;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.constant.SpinnerStyle;
import com.wang.avi.AVLoadingIndicatorView;

import anchor.app.base.R;
import pl.droidsonroids.gif.GifImageView;

/**
 * 下拉刷新样式
 */
public class AppRefreshHeader extends FrameLayout implements RefreshHeader {

    //刷新icon
    private AVLoadingIndicatorView ivRefreshIcon;
    //下拉刷新状态描述
    private TextView tvStateDesc;

    public AppRefreshHeader(@NonNull Context context) {
        super(context);
        initView(context);
    }

    public AppRefreshHeader(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);

    }

    public AppRefreshHeader(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);

    }

    @SuppressLint("MissingInflatedId")
    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.layout_refresh_header, this, false);
        addView(view);
        ivRefreshIcon = view.findViewById(R.id.view_anim_refresh_id);
        tvStateDesc = view.findViewById(R.id.tv_view_state_desc_id);

    }
    public AppRefreshHeader setTextColor(int color) {
//        tvStateDesc.setTextColor(AppUtils.getColor(color));
        return this;
    }

    public AppRefreshHeader setStyle(int style) {
        switch (style) {
            case AppRefreshFooter.STYLE_BLACK:
//                tvStateDesc.setTextColor(AppUtils.getColor(R.color.color_323643));
                break;
            case AppRefreshFooter.STYLE_WHITE:
            default:
//                tvStateDesc.setTextColor(AppUtils.getColor(R.color.color_ffffff));
                break;
        }

        return this;
    }

    /**
     * 获取实体视图
     *
     * @return 实体视图
     */
    @NonNull
    @Override
    public View getView() {
        return this;
    }

    /**
     * 获取变换方式 {@link SpinnerStyle} 必须返回 非空
     *
     * @return 变换方式
     */
    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    /**
     * 设置主题颜色
     *
     * @param colors 对应Xml中配置的 srlPrimaryColor srlAccentColor
     */
    @Override
    public void setPrimaryColors(int... colors) {

    }

    /**
     * 尺寸定义完成 （如果高度不改变（代码修改：setHeader），只调用一次, 在RefreshLayout#onMeasure中调用）
     *
     * @param kernel        RefreshKernel
     * @param height        HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {

    }

    /**
     * 手指拖动下拉（会连续多次调用，添加isDragging并取代之前的onPulling、onReleasing）
     *
     * @param isDragging    true 手指正在拖动 false 回弹动画
     * @param percent       下拉的百分比 值 = offset/footerHeight (0 - percent - (footerHeight+maxDragHeight) / footerHeight )
     * @param offset        下拉的像素偏移量  0 - offset - (footerHeight+maxDragHeight)
     * @param height        高度 HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    /**
     * 释放时刻（调用一次，将会触发加载）
     *
     * @param refreshLayout RefreshLayout
     * @param height        高度 HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    /**
     * 开始动画
     *
     * @param refreshLayout RefreshLayout
     * @param height        HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
//        ivRefreshIcon.playAnimation();
    }

    /**
     * 动画结束
     *
     * @param refreshLayout RefreshLayout
     * @param success       数据是否成功刷新或加载
     * @return 完成动画所需时间 如果返回 Integer.MAX_VALUE 将取消本次完成事件，继续保持原有状态
     */
    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
//        ivRefreshIcon.cancelAnimation();

        //延迟300毫秒之后再弹回
        return 300;
    }

    /**
     * 水平方向的拖动
     *
     * @param percentX  下拉时，手指水平坐标对屏幕的占比（0 - percentX - 1）
     * @param offsetX   下拉时，手指水平坐标对屏幕的偏移（0 - offsetX - LayoutWidth）
     * @param offsetMax 最大的偏移量
     */
    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    /**
     * 是否支持水平方向的拖动（将会影响到onHorizontalDrag的调用）
     *
     * @return 水平拖动需要消耗更多的时间和资源，所以如果不支持请返回false
     */
    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    /**
     * 状态改变事件 {@link RefreshState}
     *
     * @param refreshLayout RefreshLayout
     * @param oldState      改变之前的状态
     * @param newState      改变之后的状态
     */
    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullDownToRefresh://下拉中
                tvStateDesc.setVisibility(View.VISIBLE);
                ivRefreshIcon.setVisibility(View.GONE);
                tvStateDesc.setText(" Pull down to refresh");
                break;
            case Refreshing://刷新中
                tvStateDesc.setVisibility(View.GONE);
                ivRefreshIcon.setVisibility(View.VISIBLE);
                break;
            case ReleaseToRefresh://松手即可刷新
//                tvStateDesc.setText("最好的爱是手放开");
                break;
        }
    }
}
