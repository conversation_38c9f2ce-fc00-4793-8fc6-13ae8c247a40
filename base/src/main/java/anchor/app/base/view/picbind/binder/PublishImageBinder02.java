package anchor.app.base.view.picbind.binder;

import static com.bumptech.glide.request.RequestOptions.bitmapTransform;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.convenientbanner.utils.ScreenUtil;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestOptions;

import jp.wasabeef.glide.transformations.RoundedCornersTransformation;
import me.drakeet.multitype.ItemViewBinder;
import anchor.app.base.R;
import anchor.app.base.utils.ImageUtil;

public class PublishImageBinder02 extends ItemViewBinder<String, PublishImageBinder02.ImageHolder> {
    private OnItemChildClickListener onItemChildClickListener;

    @NonNull
    @Override
    protected ImageHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent) {
        View root = inflater.inflate(R.layout.base_item_dynamic_img_list02, parent, false);
        return new ImageHolder(root);
    }

    @Override
    protected void onBindViewHolder(@NonNull ImageHolder holder, @NonNull String item) {
        ImageUtil.displayStaticImage(holder.img.getContext(), holder.img, item, R.mipmap.base_ic_main_default);
        Glide.with(holder.img.getContext())
                .setDefaultRequestOptions(new RequestOptions().centerCrop())
                .asBitmap()
                .load(item)
                .apply(bitmapTransform(new MultiTransformation<>(new CenterCrop(), new RoundedCornersTransformation(ScreenUtil.dip2px(holder.img.getContext(),5), 0, RoundedCornersTransformation.CornerType.ALL))))
                .into(holder.img);
        addOnClickListener(holder.img, holder.getLayoutPosition());
        addOnClickListener(holder.imgDelete, holder.getLayoutPosition());
    }

    static class ImageHolder extends RecyclerView.ViewHolder {

        private ImageView img;
        private ImageView imgDelete;

        public ImageHolder(@NonNull View itemView) {
            super(itemView);
            this.img = itemView.findViewById(R.id.iv_head_id);
            this.imgDelete = itemView.findViewById(R.id.ivDelete);
        }
    }


    public void addOnClickListener(View view, int p) {
        if (view != null) {
            view.setOnClickListener(v -> {
                if (onItemChildClickListener != null) {
                    onItemChildClickListener.onItemChildClick(v, p);
                }
            });
        }
    }


    public OnItemChildClickListener getOnItemChildClickListener() {
        return onItemChildClickListener;
    }

    public void setOnItemChildClickListener(OnItemChildClickListener onItemChildClickListener) {
        this.onItemChildClickListener = onItemChildClickListener;
    }
}
