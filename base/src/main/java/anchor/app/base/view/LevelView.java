package anchor.app.base.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;

import anchor.app.base.R;

/**
 * Created by Android Studio.
 * User: yj
 * Date: 2023/11/22
 * Time: 16:49
 * 项目名称：基础框架
 * 类名称：LevelView
 */
public class LevelView extends LinearLayout {
    ImageView bg;
    TextView level;

    public void setTextSize(float size) {
        level.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
    }

    public void setLevelInfo(String levelLogo, String level) {
        if (!TextUtils.isEmpty(levelLogo))
            Glide.with(this).load(levelLogo).error(R.mipmap.base_ic_vip_person).into(bg);
        else bg.setImageResource(R.mipmap.base_ic_vip_person);
        this.level.setText("VIP " + level);
    }

    public void setLevelInfo(String level) {
        this.level.setText("VIP " + level);
    }

    public LevelView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public LevelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    public LevelView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }

    private void initView(Context context) {
        View inflate = LayoutInflater.from(context).inflate(R.layout.base_level_view, this, true);
        bg = inflate.findViewById(R.id.c_level_bg);
        level = inflate.findViewById(R.id.c_level);
    }
}
