package anchor.app.base.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentActivity;

import com.bigkoo.convenientbanner.utils.ScreenUtil;
import com.bumptech.glide.Glide;

import anchor.app.base.R;
import anchor.app.base.databinding.BaseLayoutUserBanBinding;
import anchor.app.base.utils.Logger;

/**
 * 用户头像控件
 * 1、支持头像显示
 * 2、支持封禁状态
 * 3、支持头套
 */
public class AvatarView extends FrameLayout {

    //头像控件与头套控件的大小比例，头套大
    private static final float SCALE_PIC = 0.666666667F;



    public AvatarView(@NonNull Context context) {
        super(context);

        initView(context, null);
    }

    public AvatarView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context, attrs);

    }

    public AvatarView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        initView(context, attrs);
    }

    private BaseLayoutUserBanBinding bindingView;
    private void initView(Context context, @Nullable AttributeSet attrs) {

//        View view = LayoutInflater.from(context).inflate(R.layout.base_layout_user_ban, this, true);
//        bindingView = DataBindingUtil.inflate(((FragmentActivity)context).getLayoutInflater(), R.layout.base_layout_user_ban, null, false);
        bindingView = DataBindingUtil.inflate(((FragmentActivity)context).getLayoutInflater(), R.layout.base_layout_user_ban, this, true);
        if (attrs != null) {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.AvatarView, 0, 0);
            for (int i = 0; i < ta.getIndexCount(); i++) {
                int attr = ta.getIndex(i);
                //根据控件大小，等比例设置头像大小
                if (attr == R.styleable.AvatarView_view_size) {
                    float dimension = ta.getDimension(attr, 0);
                    LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    layoutParams.width = (int) (dimension * SCALE_PIC);
                    layoutParams.height = (int) (dimension * SCALE_PIC);
                    layoutParams.gravity = Gravity.CENTER;
                    bindingView.userPic.setLayoutParams(layoutParams);

                } else if (attr == R.styleable.AvatarView_border_width) {
                    bindingView.userPic.setBorderWidth(ta.getDimensionPixelSize(attr, 0));
                } else if (attr == R.styleable.AvatarView_border_color) {
                    bindingView.userPic.setBorderColor(ta.getColor(attr, Color.WHITE));
                }
            }
            ta.recycle();
        }
    }


    public void setPic(int resource) {
        //清除头套
        bindingView. headgearDynamic.stopAnimation(true);
        bindingView.headgearContainer.setVisibility(View.GONE);
        //加载本地资源

        try {
//            ImageUtil.displayResource(getContext(), bindingView.userPic, resource);
        } catch (IllegalArgumentException e) {
            Logger.e("页面已经被销毁");
        }
    }

    /**
     * 仅仅设置头像，会隐藏所有其他控件
     *
     * @param url 头像url
     */
    public void setPic(String url) {
        //清除头套
        bindingView.headgearDynamic.stopAnimation(true);
        bindingView.headgearContainer.setVisibility(View.GONE);
        //加载本地资源
        Glide.with(getContext()).load(url).into(bindingView.userPic);
    }


    /**
     * 设置头像 + 动态头套
     *
     * @param picUrl     头像url
     * @param banState   用户封禁状态
     * @param headgearId 头套id
     */
    public void setPicAndDynamicHeadgear(String picUrl, int banState, int headgearId) {
        setPicAndDynamicHeadgear(picUrl, banState, headgearId, 0);
    }


    /**
     * 设置头像 + 动态头套
     *
     * @param picUrl     头像url
     * @param banState   用户封禁状态
     * @param headgearId 头套id
     * @param userSex    用户性别
     * @param def        展位图
     */
    public void setPicAndDynamicHeadgear(String picUrl, int banState, int headgearId, int userSex, int def) {
        setDynamicHeadgear(headgearId, userSex);

        try {
            Glide.with(getContext()).load(picUrl).into(bindingView.userPic);
        } catch (IllegalArgumentException e) {
            Logger.e("页面已经被销毁");
        }
    }


    /**
     * 设置头像 + 动态头套
     *
     * @param picUrl     头像url
     * @param banState   用户封禁状态
     * @param headgearId 头套id
     * @param userSex    用户性别
     */
    public void setPicAndDynamicHeadgear(String picUrl, int banState, int headgearId, int userSex) {
        setPicAndDynamicHeadgear(picUrl, banState, headgearId, userSex, R.mipmap.ic_pic_default_oval);
    }

    /**
     * 设置头像 + 静态头套
     *
     * @param headgearId 头套id
     */
    public void setPicAndStaticHeadgear(String picUrl,String headgearId) {
        setPicAndStaticHeadgear(picUrl, headgearId, 0);
    }

    /**
     * 设置头像 + 静态头套
     *
     * @param headgearId 头套id
     * @param userSex    用户性别
     */
    public void setPicAndStaticHeadgear(String picUrl, String headgearId, int userSex) {
        if (headgearId != null && headgearId.trim().length()>0) {
            setStaticHeadgear(headgearId, userSex);
        }
        try {
            Glide.with(getContext()).load(picUrl).into(bindingView.userPic);

        } catch (IllegalArgumentException e) {
            Logger.e("页面已经被销毁");
        }
    }

    public void setPicAndStaticHeadgear(String picUrl, String headgearId, int userSex, int headScreen) {
        if (headgearId != null && headgearId.trim().length()>0) {
            setStaticHeadgear(headgearId, userSex);
        }
        try {
            bindingView.userPic.getLayoutParams().height = ScreenUtil.dip2px(getContext(),headScreen);;
            bindingView.userPic.getLayoutParams().width = ScreenUtil.dip2px(getContext(),headScreen);;
            if (picUrl != null && picUrl.trim().length()>0) {
                Glide.with(getContext()).load(picUrl).into(bindingView.userPic);
            }
        } catch (IllegalArgumentException e) {
            Logger.e("页面已经被销毁");
        }
    }


    /**
     * 设置动态头套
     */
    private void setDynamicHeadgear(int headgearId, int userSex) {
//        //先清除上次的动画
//        bindingView.headgearDynamic.stopAnimation(true);
//        if (headgearId == 0) {
//            bindingView.headgearContainer.setVisibility(View.GONE);
//            setSexBorder(userSex);
//            return;
//        }
//        setSexBorder(0);
//        GoodsItemBean goodData = StaticResourceManager.getInstance().getGoodsDataById(String.valueOf(headgearId));
//
//        setDynamicHeadgear(goodData);
    }


    /**
     * 设置动态头套
     */
    public void setDynamicHeadgear(Object goodData) {
        //先清除上次的动画
//        bindingView.headgearDynamic.stopAnimation(true);
//
//        if (goodData == null) {
//            bindingView.headgearContainer.setVisibility(View.GONE);
//            return;
//        }
//        bindingView.headgearContainer.setVisibility(View.VISIBLE);
//        if (!TextUtils.isEmpty(goodData.resource) && goodData.resource.endsWith(".gif")) {
//            bindingView.headgearDynamic.setVisibility(View.GONE);
//            bindingView.headgearStatic.setVisibility(View.VISIBLE);
//            ImageUtil.displayGif(getContext(), bindingView.headgearStatic, UrlManager.getRealHeadPath(goodData.resource));
//        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !TextUtils.isEmpty(goodData.animation)) {
//            bindingView.headgearDynamic.setVisibility(View.VISIBLE);
//            bindingView.headgearStatic.setVisibility(View.GONE);
//            SVGAUtils.displayGoodAnim(bindingView.headgearDynamic, goodData.id);
//        } else {
//            bindingView.headgearDynamic.setVisibility(View.GONE);
//            bindingView.headgearStatic.setVisibility(View.VISIBLE);
//            ImageUtil.displayStaticImage(getContext(), bindingView.headgearStatic, UrlManager.getRealHeadPath(goodData.icon), 0);
//        }
    }

    /**
     * 设置静态头套
     */
    public void setStaticHeadgear(String headgearId, int userSex) {
//        //先清除上次的动画
        bindingView.headgearDynamic.stopAnimation(true);
        setSexBorder(0);
        if (!headgearId.equals("-1")) {
            bindingView.headgearContainer.setVisibility(View.VISIBLE);
            bindingView.headgearDynamic.setVisibility(View.GONE);
            bindingView.headgearStatic.setVisibility(View.VISIBLE);
            bindingView.headgearStatic.setImageResource(getContext().getResources().getIdentifier("base_ic_headgear_"+headgearId,"mipmap", "mikchat.app"));
        } else {
            bindingView.headgearContainer.setVisibility(View.GONE);
        }
    }

    /**
     * 设置性别描边
     */
    private void setSexBorder(int userSex) {
        bindingView.userPic.setBorderWidth(0);
    }

    public void setBorderWidth(int borderWidth) {
        bindingView.userPic.setBorderWidth(borderWidth);
    }


    /**
     * 设置头像 + 动态头套
     *
     * @param picUrl     头像url
     * @param headgearId 头套id
     */
    public void setPicAndDynamicHeadgearList(String picUrl, int headgearId) {
        setPicAndDynamicHeadgearList(picUrl, 0, headgearId, 0);
    }

    /**
     * 设置头像 + 动态头套
     *
     * @param picUrl     头像url
     * @param banState   用户封禁状态
     * @param headgearId 头套id
     * @param userSex    用户性别
     */
    public void setPicAndDynamicHeadgearList(String picUrl, int banState, int headgearId, int userSex) {
        //先清除上次的动画
//        bindingView.headgearDynamic.stopAnimation(true);
//        if (headgearId == 0) {
//            bindingView.headgearContainer.setVisibility(View.GONE);
//            setSexBorder(userSex);
//        } else {
//            setSexBorder(0);
//            GoodsItemBean goodData = StaticResourceManager.getInstance().getGoodsDataById(String.valueOf(headgearId));
//            //先清除上次的动画
////            bindingView.headgearDynamic.stopAnimation(true);
//            if (goodData == null) {
//                bindingView.headgearContainer.setVisibility(View.GONE);
//            } else {
//                bindingView.headgearContainer.setVisibility(View.VISIBLE);
//
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O || "dev".equals(ChannelUtils.getChannel())) {
//
//                    if (!TextUtils.isEmpty(goodData.resource) && goodData.resource.endsWith(".gif")) {
//                        bindingView.headgearDynamic.setVisibility(View.GONE);
//                        bindingView.headgearStatic.setVisibility(View.VISIBLE);
//                        ImageUtil.displayGif(getContext(), bindingView.headgearStatic, UrlManager.getRealHeadPath(goodData.resource));
//                    } else if (!TextUtils.isEmpty(goodData.animation)) {
//                        bindingView.headgearDynamic.setVisibility(View.VISIBLE);
//                        bindingView.headgearStatic.setVisibility(View.GONE);
//                        SVGAUtils.displayGoodAnim(bindingView.headgearDynamic, goodData.id);
//                    } else {
//                        bindingView.headgearDynamic.setVisibility(View.GONE);
//                        bindingView.headgearStatic.setVisibility(View.VISIBLE);
//                        ImageUtil.displayStaticImage(getContext(), bindingView.headgearStatic, UrlManager.getRealHeadPath(goodData.icon), 0);
//                    }
//
//                } else {
//                    bindingView.headgearDynamic.setVisibility(View.GONE);
//                    bindingView.headgearStatic.setVisibility(View.VISIBLE);
//                    ImageUtil.displayStaticImage(getContext(), bindingView.headgearStatic, UrlManager.getRealHeadPath(goodData.icon), 0);
//                }
//
//            }
//
//        }
//
//        try {
//            Glide.with(getContext()).load(picUrl).into(bindingView.userPic);
//        } catch (IllegalArgumentException e) {
//            Logger.e("页面已经被销毁");
//        }
    }


}
