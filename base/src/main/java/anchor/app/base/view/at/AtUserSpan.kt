package anchor.app.base.view.at

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import com.iyao.eastat.span.DataBindingSpan
import com.iyao.eastat.span.DirtySpan

data class AtUserSpan(val id: Int, var name: String) : DataBindingSpan,
    DirtySpan {

    fun getSpannedName(): Spannable {
        return SpannableString("@$name").apply {
            setSpan(ForegroundColorSpan(Color.rgb(0x7a, 0x2c, 0xf6)), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    override fun isDirty(text: Spannable): <PERSON><PERSON>an {
        val spanStart = text.getSpanStart(this)
        val spanEnd = text.getSpanEnd(this)
        return spanStart >= 0 && spanEnd >= 0 && text.substring(spanStart, spanEnd) != "@$name"
    }
}