package anchor.app.base.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.viewpager.widget.ViewPager;

/**
 * 监听左右边界划动的ViewPager
 */
public class CustomSideViewPager extends ViewPager {
    /**
     * 开始点击的位置
     */
    private int startX;
    /**
     * 临界值
     */
    private int criticalValue = 200;

    /**
     * 边界滑动回调
     */
    public interface onSideListener {
        /**
         * 左边界回调
         */
        void onLeftSide();

        /**
         * 右边界回调
         */
        void onRightSide();
    }

    /**
     * 回调
     */
    private onSideListener mOnSideListener;

    /**
     * 设置回调
     */
    public void setOnSideListener(onSideListener listener) {
        this.mOnSideListener = listener;
    }

    /**
     * 设置临界值
     */
    public void setCriticalValue(int criticalValue) {
        this.criticalValue = criticalValue;
    }

    public CustomSideViewPager(Context context) {
        this(context, null);
    }

    public CustomSideViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            startX = (int) event.getX();
        }
        return super.dispatchTouchEvent(event);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_MOVE) {
            if (startX - event.getX() > criticalValue && (getAdapter() != null && getCurrentItem() == getAdapter().getCount() - 1)) {
                if (mOnSideListener != null) {
                    mOnSideListener.onRightSide();
                }
            }
            if ((event.getX() - startX) > criticalValue && (getCurrentItem() == 0)) {
                if (mOnSideListener != null) {
                    mOnSideListener.onLeftSide();
                }
            }
        }
        return super.onTouchEvent(event);
    }
}