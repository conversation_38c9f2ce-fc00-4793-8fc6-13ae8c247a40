package anchor.app.base.view.gift;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import io.reactivex.functions.Consumer;
import anchor.app.base.R;
import anchor.app.base.bean.SoftKeyboardDownEvent;
import anchor.app.base.utils.ActivityUtils;
import anchor.app.base.utils.EventBusUtils;
import anchor.app.base.utils.KeyboardUtils;
import anchor.app.base.utils.RxViewUtils;

/**
 * -----------
 * 送礼数量输入弹窗
 */
public class GiftNumInputPopupWindow extends PopupWindow implements Consumer<View> {

    EditText content;
    ImageView ivSend;

    private Context context;
    private int mSoftInputMode;
    private View contentView;
    private Callback callback;

    public GiftNumInputPopupWindow(Context context) {
        super(context);

        setAnimationStyle(R.style.PopupWindow_Animation);
        initView(context);
    }

    private void initView(Context context) {
        this.context = context;
        mSoftInputMode = getWindow().getAttributes().softInputMode;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        contentView = inflater.inflate(R.layout.base_pop_gift_count_edit, null, false);

        content = contentView.findViewById(R.id.et_des_content_id);
        ivSend = contentView.findViewById(R.id.iv_send_id);
//        contentView.findViewById(R.id.);
        setContentView(contentView);
        setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        setHeight(WindowManager.LayoutParams.WRAP_CONTENT);

        setFocusable(true);
        setTouchable(true);
        setOutsideTouchable(true);

        //透明背景
        setBackgroundDrawable(new BitmapDrawable());

        RxViewUtils.setOnClickListeners(ivSend, this);

        setWidth(WindowManager.LayoutParams.MATCH_PARENT);
        //键盘不会挡住控件
        setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        //回退按钮监听
        contentView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    dismiss();
                    return true;
                }
                return false;
            }
        });


        //输入框快捷按钮点击事件
        content.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if ( actionId == EditorInfo.IME_ACTION_DONE ||  (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    confirm();
                    return true;
                }
                return false;
            }
        });
    }

    private Window getWindow() {
        return ((Activity) context).getWindow();
    }

    /**
     * 展示弹窗
     */
    public void show(View view) {
        EventBusUtils.register(this);
        contentView.setAlpha(1);
        if (mSoftInputMode != WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN) {
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        }
        showAtLocation(view, Gravity.BOTTOM, 0, 0);

        content.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!ActivityUtils.isDestroy(context) && content != null){
                    KeyboardUtils.showSoftInput(content);
                }
            }
        }, 300);

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(SoftKeyboardDownEvent event) {
        if (context.equals(event.context)) {
            contentView.animate().alphaBy(1).alpha(0).setDuration(200).start();
            contentView.postDelayed(() -> GiftNumInputPopupWindow.super.dismiss(), 200);
            if (mSoftInputMode != WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN) {
                getWindow().setSoftInputMode(mSoftInputMode);
            }
            EventBusUtils.unregister(this);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        KeyboardUtils.hideSoftInput(content);
    }

    @Override
    public void accept(View view) throws Exception {
        if (view.getId() == R.id.iv_send_id) {
            confirm();
        }
    }

    private void confirm() {

        if (callback != null && content != null && content.getText() != null && content.getText().toString().trim().length()>0) {
            callback.onSend(content.getText().toString().trim());
        }
        content.setText("");
        dismiss();
    }

    public interface Callback {
        void onSend(String num);
    }

    public void setCallback(String hideStr) {
        content.setHint(hideStr);
    }
    public void setCallback(Callback callback) {
        this.callback = callback;
    }
}
