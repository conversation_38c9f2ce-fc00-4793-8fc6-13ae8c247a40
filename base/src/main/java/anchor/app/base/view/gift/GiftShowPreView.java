package anchor.app.base.view.gift;

import static com.uber.autodispose.AutoDispose.autoDisposable;

import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;

import com.bumptech.glide.Glide;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGAImageView;
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import io.reactivex.disposables.Disposable;
import anchor.app.base.R;
import anchor.app.base.bean.GiftBean;
import anchor.app.base.bean.SendGiftEvent;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.EventBusUtils;
import anchor.app.base.manager.LoadGiftManager;
import anchor.app.base.utils.GsonUtil;

public class GiftShowPreView extends FrameLayout {
    private static final String TAG = "GifShowView__";

    private final static int GIFT_SHOW_HOLD_TIME = 2 * 1000;
    //最小的礼物组数量，大于这个数字时，即可触发礼物雨
    private static final int MIN_GIFT_GROUP_NUM = 10;

//    @BindView(R.id.emoji_view_falling_view_id)
//    EmojiRainPreLayout emojiRainPreLayout;


    SVGAImageView animMiddle;
    SVGAImageView animHigh;
    FrameLayout   animHigh1;

    LinearLayout   LLContinuation;
    ImageView giftIcon;
    TextView giftNum;

    //高级特效队列，高级队列 与 中级队列，同时进行，无播放冲突
    private List<GiftBean.DataDTO.GiftslistDTO> highAnimList = new ArrayList<>();
    //高级特效是否准备就绪，true代表可以播放动画，false代表正在播放
    private boolean highAnimIsReady = true;

    //中级特效队列
    private List<GiftBean.DataDTO.GiftslistDTO> middleAnimList = new ArrayList<>();
    //中级特效是否准备就绪，true代表可以播放动画，false代表正在播放
    private boolean middleAnimIsReady = true;

    //礼物雨特效队列
//    private List<GiftRainBean> giftRainList = new ArrayList<>();
    //礼物雨是否准备就绪，true代表可以播放动画，false代表正在播放
    private boolean giftRainIsReady = true;
    private Context context;


    private Animation highAnimationShow, highAnimationDismiss, contractAnimationShow, contractAnimationDismiss;
    private Disposable disposable;


    public GiftShowPreView(@NonNull Context context) {
        this(context, null);
    }

    public GiftShowPreView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GiftShowPreView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        View root = View.inflate(context, R.layout.base_layout_gif_show, null);

        animMiddle = root.findViewById(R.id.svg_runway_anim_middle_id);
        animHigh =   root.findViewById(R.id.svg_me_anim_hi_id);
        animHigh1 =  root.findViewById(R.id.fl_view_anim_high_id);

        LLContinuation =  root.findViewById(R.id.LLContinuation);
        giftIcon =  root.findViewById(R.id.giftIcon);
        giftNum =  root.findViewById(R.id.giftNum);

        root.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        addView(root);
        initData();
        EventBusUtils.register(this);

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_GET_GIFT, String.class)
                .observeOn(RxSchedulers.INSTANCE.getUi())
                .as(autoDisposable(AndroidLifecycleScopeProvider.from((FragmentActivity)context, Lifecycle.Event.ON_DESTROY)))
                .subscribe(hashMapStr ->{
                            HashMap<String, String>    hashMap = GsonUtil.GsonToBean(hashMapStr, HashMap.class);
                            String giftCode = hashMap.get("giftCode");
                            String giveNum = hashMap.get("giveNum");
                            GiftBean.DataDTO.GiftslistDTO giftslistDTO = new GiftBean.DataDTO.GiftslistDTO();
                            giftslistDTO.setGiftCode(giftCode);
                            giftslistDTO.setNum(Integer.parseInt(giveNum));
                            giftslistDTO.setGiftGrade(2);
                            for (GiftBean.DataDTO.GiftslistDTO dto : LoadGiftManager.Companion.getMgiftManager().getData().getValue().getData().get(0).getGiftslist()) {
                                if (dto.getGiftCode().equals(giftCode)) {
                                    giftslistDTO.setGiftSvgaUrl(dto.getGiftSvgaUrl());
                                    giftslistDTO.setGiftIcon(dto.getGiftIcon());
                                    break;
                                }
                            }
                            showGift(giftslistDTO);
                }
                );
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(SendGiftEvent event) {
        showGift(event.gift);
    }

    private void initData() {
        highAnimationShow = AnimationUtils.loadAnimation(getContext(), R.anim.high_gift_show);
        highAnimationDismiss = AnimationUtils.loadAnimation(getContext(), R.anim.high_gift_hide);
//        contractAnimationShow = AnimationUtils.loadAnimation(getContext(), R.anim.contract_desc_show);
//        contractAnimationDismiss = AnimationUtils.loadAnimation(getContext(), R.anim.contract_desc_dismiss);
        //高级特效播放回调
        animHigh.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {
                highAnimIsReady = true;
                nextHighAnim();
            }

            @Override
            public void onRepeat() {

            }

            @Override
            public void onStep(int i, double v) {

            }
        });
        //中级特效播放回调
        animMiddle.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {
                middleAnimIsReady = true;
                nextMiddleAnim();
            }

            @Override
            public void onRepeat() {

            }

            @Override
            public void onStep(int i, double v) {

            }
        });
        //礼物雨播放回调
//        emojiRainPreLayout.setFinishCallback(() -> {
//            giftRainIsReady = true;
//            nextGiftRain();
//        });
    }


    public void onDestroy() {
        highAnimList.clear();
        middleAnimList.clear();
        animHigh.stopAnimation(true);
        animMiddle.stopAnimation(true);
        EventBusUtils.unregister(this);
    }


//    public void showGiftRain(int giftId, int giftNum) {
//
//        boolean isSuccess = emojiRainPreLayout.setGiftInfo(giftId, giftNum);
//        if (!isSuccess) {
//            giftRainIsReady = true;
//            nextGiftRain();
//        }
//    }

//    public void showGift(UserInfo sender, UserInfo receiver, PresentInfo gift, int giftNum, long comboId, boolean isAllMic, boolean needTrack) {}


    String currentGiftID = "";
    int  currentNum = 0;


    public  final int MIN_CLICK_DELAY_TIME = 10000;
    public  long lastClickTime = 0;

    public void showGift(GiftBean.DataDTO.GiftslistDTO gift) {
        if (System.currentTimeMillis() - lastClickTime - MIN_CLICK_DELAY_TIME < 0) {
            if (currentGiftID.equals(gift.getGiftCode())) {
                currentNum = gift.getNum()+currentNum;
            }else {
                currentNum = gift.getNum();
            }
        }else {
            currentNum = gift.getNum();
        }

        LLContinuation.setVisibility(VISIBLE);
        Glide.with(context).load(gift.getGiftIcon()).into(giftIcon);
        giftNum.setText(" X "+currentNum);
        currentGiftID = gift.getGiftCode();
        lastClickTime = System.currentTimeMillis();
         new Handler().postDelayed(new Runnable() {
             @Override
             public void run() {
                 if (System.currentTimeMillis() -  lastClickTime-3000 > 0) {
                     LLContinuation.setVisibility(GONE);
                 }
             }
         },4000);
        lastClickTime = System.currentTimeMillis();







        //如果是高级特效，走对应的高级动画
        if (gift.getGiftGrade() == 3) {
            //送几个高级特效， 向队列中插入几条数据
            for (int i = 0; i < gift.getNum(); i++) {
//                LogUtils.debug(TAG, "高级特效队列添加");
                highAnimList.add(gift);
                nextHighAnim();
            }

        } else {
            //判断是否有中级特效
            if (gift.getGiftGrade() == 2) {
//                LogUtils.iTag(TAG, "中级特效队列添加");
                middleAnimList.add(gift);
                nextMiddleAnim();
            }
            //不是连送 & 送礼数量 > 礼物雨最低数量，播放礼物雨
//            else if (comboId == 0) {
//                addGiftRainToList(gift.getGoodsId(), giftNum, giftGrade);
//            }
        }
    }

    /**
     * 播放下一个高级动画
     */
    private void nextHighAnim() {
        //礼物列表为空时，清空动画
        if (highAnimList.size() == 0) {
            highAnimIsReady = true;
            animHigh.stopAnimation(true);
            return;
        }

        if (highAnimIsReady) {
            highAnimIsReady = false;
            GiftBean.DataDTO.GiftslistDTO gift = highAnimList.remove(0);
            //播放高级特效文本
            playHighAnim(gift);
        }
    }

    /**
     * 播放高级特效
     */
    private void playHighAnim(GiftBean.DataDTO.GiftslistDTO gift) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            double size = 20;
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                size = 40;
            }
            //应用程序最大可用内存
            double maxMemory = ((double) Runtime.getRuntime().maxMemory()) / 1024 / 1024;
            //应用程序已获得内存
            double totalMemory = ((double) Runtime.getRuntime().totalMemory()) / 1024 / 1024;
            //应用程序已获得内存中未使用内存
            double freeMemory = ((double) Runtime.getRuntime().freeMemory()) / 1024 / 1024;

            double mMemory = maxMemory - (totalMemory - freeMemory);
            if (mMemory < size) {
                highAnimIsReady = true;
                highAnimList.clear();
                animHigh.stopAnimation(true);
            } else {
                LoadGiftManager.Companion.giftManager((FragmentActivity) context).displayGoodAnim(animHigh, gift);
            }
        }else {
            LoadGiftManager.Companion.giftManager((FragmentActivity) context).displayGoodAnim(animHigh, gift);
        }
    }

    /**
     * 播放下一个礼物雨动画
     */
//    private void nextGiftRain() {
//        //礼物列表为空时，清空动画
//        if (giftRainList.size() == 0) {
//            giftRainIsReady = true;
//            return;
//        }
//        if (giftRainIsReady) {
//            giftRainIsReady = false;
//            GiftRainBean remove = giftRainList.remove(0);
////            showGiftRain(remove.giftId, remove.giftNum);
//        }
//    }

    /**
     * 播放下一个中级动画
     */
    private void nextMiddleAnim() {
        //礼物列表为空时，清空动画
        if (middleAnimList.size() == 0) {
            middleAnimIsReady = true;
            animMiddle.stopAnimation(true);
            return;
        }

        if (middleAnimIsReady) {
            middleAnimIsReady = false;
            GiftBean.DataDTO.GiftslistDTO gift = middleAnimList.remove(0);
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                double size = 20;
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                    size = 40;
                }
                //应用程序最大可用内存
                double maxMemory = ((double) Runtime.getRuntime().maxMemory()) / 1024 / 1024;
                //应用程序已获得内存
                double totalMemory = ((double) Runtime.getRuntime().totalMemory()) / 1024 / 1024;
                //应用程序已获得内存中未使用内存
                double freeMemory = ((double) Runtime.getRuntime().freeMemory()) / 1024 / 1024;
                double mMemory = maxMemory - (totalMemory - freeMemory);
                if (mMemory < size) {
                    middleAnimList.clear();
                    middleAnimIsReady = true;
                    animMiddle.stopAnimation(true);
                } else {
                    LoadGiftManager.Companion.giftManager((FragmentActivity) context).displayGoodAnim(animMiddle,gift);
                }
            }else {
                LoadGiftManager.Companion.giftManager((FragmentActivity) context).displayGoodAnim(animMiddle, gift);
            }
        }
    }
}
