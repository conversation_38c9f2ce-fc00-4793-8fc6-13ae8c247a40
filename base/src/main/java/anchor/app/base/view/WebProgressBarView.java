package anchor.app.base.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import anchor.app.base.R;

/**
 * Project:WebViewProgressBar
 * Author:dyping
 * Date:2017/7/24 13:58
 */

public class WebProgressBarView extends View {

    private Context mContext;
    private int mCurProgress;
    private int mWidth;
    private int mHeight;
    private Paint mPaint;
    private int mColor;
    private EventEndListener endListener;


    public WebProgressBarView(Context context) {
        super(context);
    }

    public WebProgressBarView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);

        mContext = context;
        TypedArray array = context.obtainStyledAttributes(attrs,R.styleable.appProgress);
        mCurProgress = array.getInt(R.styleable.appProgress_webviewprogress, 0);
        mHeight = array.getInt(R.styleable.appProgress_webviewprogressHeight, 200);
        mColor = array.getColor(R.styleable.appProgress_webviewprogressColor, Color.parseColor("#FF6619"));
        array.recycle();

        mPaint = new Paint();
        mPaint.setColor(mColor);
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = MeasureSpec.getSize(widthMeasureSpec);
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float result = mWidth * ((float) mCurProgress / (float) 100);
        canvas.drawRect(0, 0, result, mHeight, mPaint);
    }

    public void setCurProgress(long time, final EventEndListener endListener){
        ValueAnimator animator = ValueAnimator.ofInt(mCurProgress,100);
        animator.setDuration(time);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                setNormalProgress((Integer) animation.getAnimatedValue());
            }
        });

        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                 if(endListener != null){
                     endListener.onEndEvent();
                 }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        animator.start();

    }

    public void setNormalProgress(int mCurProgress){
        this.mCurProgress = mCurProgress;
        postInvalidate();
    }


    public interface EventEndListener{
        void onEndEvent();
    }

}
