package anchor.app.base.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import anchor.app.base.bean.WordEntity

@Dao
interface WordDao {
    @Insert
    fun insertWords(words: List<WordEntity>)

    @Query("SELECT * FROM words")
    fun getAllWords(): List<WordEntity>

    @Query("SELECT * FROM words WHERE type = :type")
    fun getWordsByType(type: Int): List<WordEntity>

    @Query("SELECT COUNT(*) FROM words")
    fun getWordCount(): Int
}