package anchor.app.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.os.UserManager;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.WebSettings;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.multidex.MultiDex;

import com.google.firebase.FirebaseApp;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.gxx.activitylifelibrary.ActivityLifeCallbackSdk;
import com.gxx.activitylifelibrary.inter.OnLifeCallBackListener;
import com.gxx.activitylifelibrary.inter.OnLifeServiceLifeListener;
import com.hjq.language.MultiLanguages;
import com.hjq.toast.ToastUtils;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.ActivityUtil;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.Utils;
import anchor.app.base.view.reference.AppRefreshFooter;
import anchor.app.base.view.reference.AppRefreshHeader;
import anchor.app.base.web.BaseJsAPI;
import anchor.app.base.web.bridge.DWebView;

public class BaseApp extends Application {

    private static final String TAG = BaseApp.class.getSimpleName();

    private static BaseApp mInstance;

    private static final String[] MODULE_APPLICATION_PATH = new String[]{"com.mobile.anchor.app.main.AppApplicationImpl", "io.rong.imkit.ImkitApplicationImpl", "com.mobile.anchor.app.monitor.MonitorApplicationImpl"};

    public static BaseApp getAppContext() {
        return mInstance;
    }

    public static String targetLanguage = "en";

    private FirebaseAnalytics mFirebaseAnalytics;

    /**
     * static 代码段可以防止内存泄露
     * 指定全局的下拉刷新、上拉加载更多自定义样式
     */
    static {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator((context, layout) -> new AppRefreshHeader(context));
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator((context, layout) -> new AppRefreshFooter(context));
    }

    private DWebView mGlobalWebView;

    @Override
    protected void attachBaseContext(Context base) {
        // 绑定语种
        super.attachBaseContext(MultiLanguages.attach(base));
        MultiDex.install(this);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mInstance = this;

        int processId = android.os.Process.myPid();
        String processName = Utils.getProcessName(processId);
        Logger.d(TAG, "application onCreate in process:" + processId + " name:" + processName);

        // 初始化语种切换框架
        MultiLanguages.init(this);
        MultiLanguages.setAppLanguage(this, MultiLanguages.getAppLanguage());

        getAppLanguage();

        ToastUtils.init(this);

        moduleApplicationInitBefore();

        moduleApplicationInitAfter();

        initGlobalWebview(processName);

        boolean userUnlocked = getSystemService(UserManager.class).isUserUnlocked();

        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);// Add any additional Firebase Analytics configuration code here

        FirebaseApp.initializeApp(this);

        initActivityLifecycleCallbacks();
    }

    private static int runningActivities = 0;

    public static boolean isForeground() {
        return runningActivities > 0 ? true : false;
    }

    private void initActivityLifecycleCallbacks() {
        //初始化
        ActivityLifeCallbackSdk.INSTANCE.init(BuildConfig.DEBUG, this, new OnLifeCallBackListener() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                runningActivities++;
                System.out.println(" ActivityLifeCallbackSdk onActivityResumed CallWindowServices" + runningActivities);
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, runningActivities > 0 ? "1" : "0");
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, "1");
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                runningActivities--;
                System.out.println(" ActivityLifeCallbackSdk onActivityStopped CallWindowServices" + runningActivities);

                for (Activity activity1 : ActivityUtil.getStack()) {
                    System.out.println("ActivityLifeCallbackSdk CallWindowServices ClassName: " + activity1.getComponentName().getClassName());
                }

                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, runningActivities > 0 ? "1" : "0");
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, "0");
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
            }

            @Override
            public void onProcessForeground(boolean b, @NonNull String s, boolean b1) {
//                System.out.println(                " ActivityLifeCallbackSdk onProcessForeground = " + b);
            }

            @Override
            public void onAppForeground(boolean b) {
//                System.out.println(                " ActivityLifeCallbackSdk 整个APP是否在前台 = " + b);
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, b? "1":"0");
            }
        });

        //绑定service
        ActivityLifeCallbackSdk.INSTANCE.bindService(this, new OnLifeServiceLifeListener() {
            @Override
            public void onBindLifeServiceSuccess() {
            }

            @Override
            public void onBindLifeServiceDisConnect() {
            }
        });
    }

    public FirebaseAnalytics getFirebaseAnalytics() {
        return mFirebaseAnalytics;
    }

    public static String getAppLanguage() {
        return switch (MultiLanguages.getAppLanguage().getLanguage()) {
            case "zh" -> targetLanguage = "zh_CN";
            case "en" -> targetLanguage = "en";
            case "ar" -> targetLanguage = "iw";
            case "es" -> targetLanguage = "gl";
            case "fr" -> targetLanguage = "fr";
            case "hi" -> targetLanguage = "hi";
            case "in" -> targetLanguage = "id";
            case "tr" ->//土耳其共和国
                    targetLanguage = "tr";
            case "pt" ->//葡萄牙共和国
                    targetLanguage = "pt";
            case "ur" ->//巴基斯坦伊斯兰共和国
                    targetLanguage = "ur";
            case "vi" ->//越南
                    targetLanguage = "vi";
            default -> null;
        };
    }

    public DWebView getGlobalWebView() {
        return mGlobalWebView;
    }

    private void moduleApplicationInitBefore() {
        for (String moduleImpl : MODULE_APPLICATION_PATH) {
            try {
                Class<?> clz = Class.forName(moduleImpl);
                Object obj = clz.newInstance();
                if (obj instanceof IComponentApplication) {
                    ((IComponentApplication) obj).initBefore(this);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void moduleApplicationInitAfter() {
        for (String moduleImpl : MODULE_APPLICATION_PATH) {
            try {
                Class<?> clz = Class.forName(moduleImpl);
                Object obj = clz.newInstance();
                if (obj instanceof IComponentApplication) {
                    ((IComponentApplication) obj).initAfter(this);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 初始化全局webview
     */
    private void initGlobalWebview(String processName) {

        // 判断进程
//        if (LeakCanary.isInAnalyzerProcess(this)) {
//            return;
//        }
//        LeakCanary.install(this);
//        refWatcher = LeakCanary.install(this);
//        AppUtil.setRefWatcherProvider(this);

        mInstance = this;
        // 进程为空 或者进程非主进程，不初始化全局webview
        if (TextUtils.isEmpty(processName) || (!processName.equals(this.getPackageName()))) {
            Logger.d(TAG, "current process is not main process , init GlobalWebview : false");
            return;
        }
        Logger.d(TAG, "current process is main process , init GlobalWebview : true");

        mGlobalWebView = createGlobalWebView();
    }

    public DWebView getNewGlobalWebView() {
        destroyGlobalWebView();
        return createGlobalWebView();
    }

    private DWebView createGlobalWebView() {
        mGlobalWebView = new DWebView(BaseApp.getAppContext().getApplicationContext());
        mGlobalWebView.getSettings().setUseWideViewPort(true);
        mGlobalWebView.getSettings().setLoadWithOverviewMode(true);
        mGlobalWebView.getSettings().setAllowFileAccess(true);
        mGlobalWebView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
//        mGlobalWebView.getSettings().setAppCacheEnabled(true);
        mGlobalWebView.getSettings().setDomStorageEnabled(true);
        mGlobalWebView.getSettings().setDatabaseEnabled(true);
        mGlobalWebView.getSettings().setJavaScriptEnabled(true);
        mGlobalWebView.setHorizontalScrollBarEnabled(false);
        mGlobalWebView.setVerticalScrollBarEnabled(false);
        // 初始化JSAPI
        mGlobalWebView.addJavascriptObject(new BaseJsAPI(this), null);
        return mGlobalWebView;
    }

    private void destroyGlobalWebView() {
        if (null == mGlobalWebView) {
            return;
        }

        if (mGlobalWebView.getParent() != null) {
            ((ViewGroup) mGlobalWebView.getParent()).removeView(mGlobalWebView);
        }
        mGlobalWebView.setWebChromeClient(null);
        mGlobalWebView.setWebViewClient(null);
        mGlobalWebView.stopLoading();
        mGlobalWebView.destroy();
    }
}
