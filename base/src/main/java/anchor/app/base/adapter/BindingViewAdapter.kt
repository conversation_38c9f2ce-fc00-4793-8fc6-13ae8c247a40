package anchor.app.base.adapter

import anchor.app.base.BR
import android.content.Context
import android.view.LayoutInflater
import androidx.databinding.ObservableList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView

/**
 * 和databinding结合的Reyclerview的adapter
 */
abstract class BindingViewAdapter<T>(context: Context, protected val list: ObservableList<T>)
    : RecyclerView.Adapter<BindingViewHolder<ViewDataBinding>>() {

    protected val mLayoutInflater: LayoutInflater = LayoutInflater.from(context)

    var itemPresenter: ItemClickPresenter<T>? = null
    var itemPresenter3: ItemClickPresenter3<T>? = null

    var itemDecorator: ItemDecorator? = null

    override fun getItemCount(): Int = list.size

    fun getItem(position: Int): T? = list[position]

}