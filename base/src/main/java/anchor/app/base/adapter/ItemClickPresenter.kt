package anchor.app.base.adapter

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding

/**
 * 定义RecyclerView item点击事件
 */
interface ItemClickPresenter<in Any> {
    fun onItemClick(v: View, item: Any)
}


interface ItemClickPresenter2<in Any,in Any2:ViewDataBinding> {
    fun onItemClick(v: View, item: Any ,binding:Any2)
}


interface ItemClickPresenter3<in Any> {
    fun onItemClick(v: View, item: Any,index:Int)
}

/**
 * 定义RecyclerView item decorator
 */
interface ItemDecorator {
    fun decorator(holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int)
}

fun ObservableArrayList<*>.rangeRemove(startIndex: Int, offset: Int) {
    (startIndex + offset - 1 downTo startIndex).forEach {
        removeAt(it)
    }
}