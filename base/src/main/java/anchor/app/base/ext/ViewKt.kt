package anchor.app.base.ext

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.text.InputFilter
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.webkit.WebView
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.Lifecycle
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import anchor.app.base.utils.Logger

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: He<PERSON>hao
 * Date: 2021/6/8 11:33
 * Description: View扩展类
 */
var mLastClickTime: Long = 0
fun <T : View> T.click(SKIP_DURATION: Int = 350, block: (T) -> Unit) = setOnClickListener {
    //milliseconds
    if (System.currentTimeMillis() - mLastClickTime > SKIP_DURATION) {
        @Suppress("UNCHECKED_CAST") block(it as T)
        mLastClickTime = System.currentTimeMillis()
    } else {
        Logger.i("OnThrottleClickListener: 重复点击")
    }
}


fun View.makeVisible() {
    visibility = View.VISIBLE
}

fun View.makeVisibleAnimation() {
    visibility = View.VISIBLE
//    animation =
//        android.view.animation.AnimationUtils.loadAnimation(context, R.anim.fade_in)
}

fun View.makeVisible(boolean: Boolean) {
    visibility = if (boolean) View.VISIBLE else View.GONE
}

fun View.makeInVisible() {
    visibility = View.INVISIBLE
}

fun View.makeGone() {
    visibility = View.GONE
}

fun View.makeGoneAnimation() {
    visibility = View.GONE
//    animation =
//        android.view.animation.AnimationUtils.loadAnimation(context, R.anim.fade_out)
}

fun View.setHeight(height: Int) {
    val params = layoutParams
    params.height = height
    layoutParams = params
}

fun View.setWidth(width: Int) {
    val params = layoutParams
    params.width = width
    layoutParams = params
}

fun TextView.setDrawableTop(resId: Int, drawablePadding: Int = 0) {
    val drawable = ContextCompat.getDrawable(this.context, resId)
    setCompoundDrawablesWithIntrinsicBounds(
        null, drawable, null, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun TextView.setDrawableLeft(resId: Int, drawablePadding: Int = 0) {
    if (resId <= 0) setCompoundDrawablesWithIntrinsicBounds(
        null, null, null, null
    )
    return
    val drawable = ContextCompat.getDrawable(this.context, resId)
    setCompoundDrawablesWithIntrinsicBounds(
        drawable, null, null, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun TextView.setDrawableRight(resId: Int, drawablePadding: Int = 0) {
    val drawable = ContextCompat.getDrawable(this.context, resId)
    setCompoundDrawablesWithIntrinsicBounds(
        null, null, drawable, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun ImageView.tintDrawable(res: Int, color: Int) {
    setImageResource(res)
    imageTintList = if (color <= 0) {
        null
    } else {
        ColorStateList.valueOf(ContextCompat.getColor(context, color))
    }
}

fun View.backgroundTint(color: Int) {
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, color))
}

fun TextView.setDrawableHorizontal(left: Int, right: Int, drawablePadding: Int = 0) {
    val drawableLeft = ContextCompat.getDrawable(this.context, left)
    val drawableRight = ContextCompat.getDrawable(this.context, right)
    setCompoundDrawablesWithIntrinsicBounds(
        drawableLeft, null, drawableRight, null
    )
    compoundDrawablePadding = drawablePadding.toFloat().dp2px()
}

fun TextView.clearDrawable() {
    setCompoundDrawablesWithIntrinsicBounds(
        null, null, null, null
    )
}

/**
 * 禁止EditText输入空格和换行符
 */
fun EditText.setEditTextInputSpace() {
    val filter = InputFilter { source, _, _, _, _, _ ->
        if (source == " " || source.toString().contentEquals("\n")) {
            ""
        } else {
            null
        }
    }
    filters = arrayOf(filter)
}


fun ViewPager.initial(
    manager: FragmentManager, fragments: ArrayList<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStatePagerAdapter(
        manager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
    ) {
        override fun getCount() = fragments.size
        override fun getItem(position: Int) = fragments[position]
    }

    addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int, positionOffset: Float, positionOffsetPixels: Int
        ) {
        }

        override fun onPageSelected(position: Int) {
            listener.invoke(position)
        }

        override fun onPageScrollStateChanged(state: Int) {
        }
    })
}

fun ViewPager2.initial(
    fragment: Fragment, fragments: List<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStateAdapter(
        fragment
    ) {
        override fun getItemCount() = fragments.size
        override fun createFragment(position: Int) = fragments[position]
    }
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            listener.invoke(position)
        }
    })
}

fun ViewPager2.initial(
    activity: AppCompatActivity, fragments: List<Fragment>, listener: (Int) -> Unit = {}
) {
    adapter = object : FragmentStateAdapter(
        activity
    ) {
        override fun getItemCount() = fragments.size
        override fun createFragment(position: Int) = fragments[position]
    }
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            listener.invoke(position)
        }
    })
}


fun WebView.loadData(content: String?, margin: Int = 5) {
    content?.let {
        val css =
            "<style> img{max-width:100%;width:100% !important;height:auto !important;min-height:10px;border-radius:5px;} p{margin-top:0 !important;margin-bottom:0 !important;}</style>"
        val fontCss = "<style>p{font-size :16px;color:#333333; !important;line-height:25px !important}</style>"
        val style = "<html><header>$css$fontCss</header><body style='margin:${margin}px;padding:0'>${
            it.let {
                if (it.contains("<p>")) it else "<p>$it</p>"
            }
        }</body></html>"

        loadDataWithBaseURL(null, style, "text/html", "utf-8", null)
    }
}

fun View.showSoftKeyboard() {
    val imm = this.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.showSoftInput(this, InputMethodManager.SHOW_FORCED)
}

fun View.hideSoftKeyboard() {
    val imm = this.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(this.windowToken, 0)
}

fun TextView.setTextCompatColor(color: Int) {
    setTextColor(ContextCompat.getColor(context, color))
}

fun TextView.setHintTextCompatColor(color: Int) {
    setHintTextColor(ContextCompat.getColor(context, color))
}

fun View.setBackgroundCompatColor(color: Int) {
    setBackgroundColor(ContextCompat.getColor(context, color))
}

fun View.setBackgroundCompatResource(res: Int) {
    background = ContextCompat.getDrawable(
        this.context, res
    )
}

fun TextView.adapterWidth(): String {
    val rawText: String = getText().toString() //原始文本
    val tvPaint: Paint = getPaint() //paint，包含字体等信息
    val tvWidth: Float = getWidth() - getPaddingLeft() - getPaddingRight().toFloat() //控件可用宽度

    //将原始文本按行拆分
    val rawTextLines = rawText.replace("\r".toRegex(), "").split("\n".toRegex()).toTypedArray()
    val sbNewText = StringBuilder()
    for (rawTextLine in rawTextLines) {
        if (tvPaint.measureText(rawTextLine) <= tvWidth) {
            //如果整行宽度在控件可用宽度之内，就不处理了
            sbNewText.append(rawTextLine)
        } else {
            //如果整行宽度超过控件可用宽度，则按字符测量，在超过可用宽度的前一个字符处手动换行
            var lineWidth = 0f
            var cnt = 0
            while (cnt != rawTextLine.length) {
                val ch = rawTextLine[cnt]
                lineWidth += tvPaint.measureText(ch.toString())
                if (lineWidth <= tvWidth) {
                    sbNewText.append(ch)
                } else {
                    sbNewText.append("\n")
                    lineWidth = 0f
                    --cnt
                }
                ++cnt
            }
        }
        sbNewText.append("\n")
    }

    //把结尾多余的\n去掉

    //把结尾多余的\n去掉
    if (!rawText.endsWith("\n")) {
        sbNewText.deleteCharAt(sbNewText.length - 1)
    }

    return sbNewText.toString()
}

fun TextView.gradientColor(@ColorRes startColor: Int, @ColorRes endColor: Int) {
    val endX = paint.textSize * text.length
    val gradient = LinearGradient(
        0F,
        0F,
        endX,
        0F,
        ContextCompat.getColor(context, startColor),
        ContextCompat.getColor(context, endColor),
        Shader.TileMode.CLAMP
    )
    paint.shader = gradient
    invalidate()
}

fun ImageView.setBackgroundTint(color: Int) {
    backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, color))
}

fun ViewPager2.attach(
    manager: FragmentManager, lifeCycle: Lifecycle, fragments: List<Fragment>, isInputEnabled: Boolean = true, //默认可滑动
    isLimitAll: Boolean = false,//默认不加载全部
    onPageSelected: (position: Int) -> Unit
) {
    isUserInputEnabled = isInputEnabled
    if (isLimitAll) {
        offscreenPageLimit = fragments.size
    }
    adapter = object : FragmentStateAdapter(manager, lifeCycle) {
        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            onPageSelected(position)
        }
    })
}

// ViewPager2扩展函数，设置当前选中的索引
fun ViewPager2.checkSelected(position: Int) {
    if (currentItem != position) {
        setCurrentItem(position, false)
    }
}