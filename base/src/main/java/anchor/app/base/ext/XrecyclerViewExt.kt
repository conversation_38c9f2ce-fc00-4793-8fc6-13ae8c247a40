package anchor.app.base.ext

import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_ERROR
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_LOADING
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NORMAL
import com.angcyo.dsladapter.DslLoadMoreItem.Companion.LOAD_MORE_NO_MORE
import com.angcyo.dsladapter.DslLoadMoreItem.Companion._LOAD_MORE_RETRY
import anchor.app.base.R
import anchor.app.base.view.refreshlayout.XRecyclerView


inline fun XRecyclerView.openLoadMore(crossinline loadMore: () -> Unit) {
    enableLoadMore(true)
    dslAdapter.apply {
        dslLoadMoreItem.apply {
            itemStateLayoutMap[LOAD_MORE_NORMAL] = R.layout.list_item_loading_layout
            itemStateLayoutMap[LOAD_MORE_LOADING] = R.layout.list_item_loading_layout
            itemStateLayoutMap[LOAD_MORE_NO_MORE] = R.layout.list_item_no_more_layout
            itemStateLayoutMap[LOAD_MORE_ERROR] = R.layout.list_item_error_layout
            itemStateLayoutMap[_LOAD_MORE_RETRY] = R.layout.list_item_error_layout
            onLoadMore = {
                it.postDelay(300L) {
                    render {
                        loadMore.invoke()
                        setLoadMore(LOAD_MORE_LOADING)
                    }
                }
            }
        }
    }
}