package anchor.app.base.ext

import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import anchor.app.base.R

fun ImageView.loadAvatar(context: Context, url: String) {
    Glide.with(context)
        .load(url) // 要加载的图片 URL
        .placeholder(R.mipmap.ic_pic_default_oval) // 加载中的占位图
        .error(R.mipmap.ic_pic_default_oval) // 加载失败时的图片
        .apply(RequestOptions.bitmapTransform(CircleCrop()))
        .into(this) // 目标 ImageView
}

fun ImageView.loadImage(context: Context, url:String){
    Glide.with(context)
        .load(url) // 要加载的图片 URL
        .placeholder(R.mipmap.ic_default_image) // 加载中的占位图
        .error(R.mipmap.ic_default_image) // 加载失败时的图片
        .into(this) // 目标 ImageView
}
