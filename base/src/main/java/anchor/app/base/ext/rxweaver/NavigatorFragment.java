package anchor.app.base.ext.rxweaver;

import static android.app.Activity.RESULT_OK;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import io.reactivex.Single;
import io.reactivex.subjects.PublishSubject;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.SharePreUtil;

public class NavigatorFragment extends Fragment {

    private static final String TAG = "NavigatorFragment";

    private PublishSubject<Boolean> resultSubject;
    private PublishSubject<Boolean> cancelSubject;
    private PublishSubject<Boolean> attachSubject = PublishSubject.create();

    public Single<Boolean> startLoginForResult(FragmentActivity activity) {
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        Fragment fragment = fragmentManager.findFragmentByTag(TAG);
        if (fragment == null) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.add(this, TAG).commitAllowingStateLoss();
            return startLoginSingle(activity);
        } else {
            return ((NavigatorFragment) fragment).startLoginSingle(activity);
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        attachSubject.onNext(true);
        attachSubject.onComplete();
    }

    public Single<Boolean> startLoginSingle(FragmentActivity activity) {
        resultSubject = PublishSubject.create();
        cancelSubject = PublishSubject.create();
        startLogin(activity);
        return resultSubject
                .takeUntil(cancelSubject)
                .single(false);
    }

    @SuppressLint({"CheckResult", "AutoDispose"})
    public void startLogin(FragmentActivity activity) {
        if (!isAdded()) {
            attachSubject.subscribe(__ -> startSplashForResult(activity));
        } else {
            startSplashForResult(activity);
        }
    }

    private void startSplashForResult(FragmentActivity activity) {
        if (SharePreUtil.isLogin()) {
            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TOKEN_INVILADATED, true);
        } else {
            cancelSubject.onNext(true);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            boolean isLogin = SharePreUtil.isLogin();
            resultSubject.onNext(isLogin);
            resultSubject.onComplete();
        } else {
            cancelSubject.onNext(true);
        }
    }
}
