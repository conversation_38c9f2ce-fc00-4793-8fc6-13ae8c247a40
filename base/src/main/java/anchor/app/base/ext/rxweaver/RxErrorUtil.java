package anchor.app.base.ext.rxweaver;

import androidx.fragment.app.FragmentActivity;

import org.json.JSONException;

import java.net.ConnectException;
import java.net.UnknownHostException;

import anchor.app.base.BaseApp;
import anchor.app.base.R;
import anchor.app.base.ext.rxweaver.core.GlobalErrorTransformer;
import anchor.app.base.ext.rxweaver.retry.RetryConfig;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.retrofit.TokenInvalideException;
import anchor.app.base.utils.NetUtil;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.SingleSource;
import io.reactivex.functions.Function;

public class RxErrorUtil {
    public static <T> GlobalErrorTransformer<T> handleGlobalError(FragmentActivity activity) {
        return new GlobalErrorTransformer<T>(

                // 通过onNext流中数据的状态进行操作
                it -> Observable.just(it),

                // 通过onError中Throwable状态进行操作
                error -> {
                    //网络错误
                    if (!NetUtil.isNetworkConnected(BaseApp.getAppContext()) || error instanceof UnknownHostException
                            || error instanceof ConnectException) {
                        return Observable.error(new ResultException(BaseResult.NETWORK_ERROR, BaseApp.getAppContext().getString(R.string.b59)));
                    }
                    return Observable.error(error);
                },
                //重试机制
                error -> {
                    if (error instanceof TokenInvalideException) {
                        return new RetryConfig(1, 1000,
                                () -> new NavigatorFragment()
                                        .startLoginForResult(activity)
                                        .flatMap((Function<Boolean, SingleSource<Boolean>>) retry -> Single.just(retry)));
                    }
                    return new RetryConfig();
                },

                throwable -> {
                    if (throwable instanceof JSONException) {
                        //全局异常捕获-Json解析异常！
                    }
                }
        );
    }
}