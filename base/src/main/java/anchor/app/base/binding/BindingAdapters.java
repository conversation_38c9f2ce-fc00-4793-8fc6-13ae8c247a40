package anchor.app.base.binding;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.databinding.BindingAdapter;
import androidx.databinding.InverseBindingAdapter;
import androidx.databinding.InverseBindingListener;

import com.bumptech.glide.Glide;

import anchor.app.base.utils.PerfectClickListener;

/**
 * Data Binding adapters specific to the app.
 */
public class BindingAdapters {
    @BindingAdapter("visibleGone")
    public static void showHide(View view, boolean show) {
        view.setVisibility(show ? View.VISIBLE : View.GONE);
    }


    @BindingAdapter("bind_view_onClick")
    public static void viewClick(View view, final ActionConsumer consumer) {
        view.setOnClickListener(new PerfectClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                try {
                    consumer.accept(v);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }


    @BindingAdapter("bind_view_clickable")
    public static void clickable(View view, boolean value) {
        if (value != view.isClickable()) {
            view.setClickable(value);
        }
    }

    @BindingAdapter("bind_view_clickable")
    public static void clickable(ViewGroup view, boolean value) {
        if (value != view.isClickable()) {
            view.setClickable(value);
        }
    }


    @InverseBindingAdapter(
            attribute = "bind_view_clickable",
            event = "bind_view_clickableAttrChanged"
    )
    public static boolean isClickable(View view) {
        return view.isClickable();
    }

    @BindingAdapter("bind_view_clickableAttrChanged")
    public static void setClickableValue(View view, InverseBindingListener bindingListener) {
        if (bindingListener != null) {
            view.setOnClickListener(v -> {
                bindingListener.onChange();
            });
        }
    }

    /**
     * 绑定Adapter的ImageView
     *
     * @param imageView
     * @param url       图片地址
     */
    @BindingAdapter({"bind_imageView_url"})
    public static void loadImage(ImageView imageView, String url) {
        if (url!= null && url.trim().length()>0&& !TextUtils.isEmpty(url))
            Glide.with(imageView.getContext()).load(url).into(imageView);
    }
}
