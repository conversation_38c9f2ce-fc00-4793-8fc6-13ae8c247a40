package anchor.app.base.binding;

import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.databinding.BindingAdapter;

import com.google.android.material.bottomnavigation.BottomNavigationView;

public class BottomNavigationViewBinding {

    @BindingAdapter("bind_onNavigationBottomSelectedChanged")
    public static void setOnSelectedChangeListener(BottomNavigationView view, final SelectedChangeConsumer consumer) {
        view.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem menuItem) {
                try {
                    consumer.accept(menuItem);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return true;
            }
        });
    }
}

