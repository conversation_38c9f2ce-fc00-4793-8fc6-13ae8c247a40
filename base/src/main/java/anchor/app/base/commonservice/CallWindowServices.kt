package anchor.app.base.commonservice

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.graphics.PixelFormat
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Vibrator
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Chronometer
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleService
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ObservableSubscribeProxy
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import anchor.app.base.R
import anchor.app.base.ext.jump
import anchor.app.base.manager.OtherManager
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.utils.ActivityUtil
import anchor.app.base.utils.NotificationUtil
import anchor.app.base.utils.RxTimerUtil

class CallWindowServices : LifecycleService() {
    private var winManager: WindowManager? = null
    private var wmParams: WindowManager.LayoutParams? = null
    private var inflater: LayoutInflater? = null
    private val MainActivityClassName = "com.mobile.anchor.app.main.MainActivity"
    private val CallVideoChatActivityClassName =
        "com.mobile.anchor.app.module.discover.main.chat.videochat.CallVideoChatActivity"

    //浮动布局
    private var mFloatingLayout: View? = null
    private var linearLayout: LinearLayout? = null
    private val chronometer: Chronometer? = null
    private val rangeTime: Long = 0

    var vibrator: Vibrator? = null
    var observer = androidx.lifecycle.Observer<String?> {
        var badge: TextView = mFloatingLayout!!.findViewById(R.id.badge)
        if (it == null || it == "0") {
            badge.visibility = View.GONE
        } else {
            badge.visibility = View.VISIBLE
            badge.text = OtherManager.manager?.totalUnreadCount?.value
        }
    }

    var observer02 = androidx.lifecycle.Observer<String?> {
        var CL01: ConstraintLayout = mFloatingLayout!!.findViewById(R.id.CL01)
        if (it == "1") {
            CL01.background = getDrawable(R.mipmap.service_remoteview_active)
        } else {
            CL01.background = getDrawable(R.mipmap.service_remoteview_disabled)
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        initWindow()
        //悬浮框点击事件的处理
        initFloating()
        return null
//        return MyBinder()
    }


    inner class MyBinder : Binder() {
        val service: CallWindowServices
            get() = this@CallWindowServices
    }

    override fun onCreate() {
        super.onCreate()
//        initWindow()
//        initFloating()

        println("CallWindowServices onCreate AppStartListenerService Firebase")

//        Handler().postDelayed({
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//        }   , 10000)

    }


    var count = 0

    /**
     * 悬浮窗点击事件
     */
    @SuppressLint("CheckResult")
    private fun initFloating() {
//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//
//                Intent intent = new Intent(CallWindowServices.this, Main2Activity.class);
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                startActivity(intent);
//            }
//        },6000);
        linearLayout = mFloatingLayout!!.findViewById(R.id.line1)
        //悬浮框触摸事件，设置悬浮框可拖动
        linearLayout?.setOnTouchListener(FloatingListener())


        var CL01: ConstraintLayout = mFloatingLayout!!.findViewById(R.id.CL01)
        if (OtherManager.manager?.onlineStatus?.value == "0") {
            CL01.background = getDrawable(R.mipmap.service_remoteview_disabled)
        } else {
            CL01.background = getDrawable(R.mipmap.service_remoteview_active)
        }


        var badge: TextView = mFloatingLayout!!.findViewById(R.id.badge)
        if (OtherManager.manager?.totalUnreadCount == null || OtherManager.manager?.totalUnreadCount?.value == "0") {
            badge.visibility = View.GONE
        } else {
            badge.visibility = View.VISIBLE
            badge.text = OtherManager.manager?.totalUnreadCount?.value
        }

        OtherManager.manager?.totalUnreadCount?.observeForever(observer)
        OtherManager.manager?.onlineStatus?.observeForever(observer02)


        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe { totalUnreadCount ->
//                    println("JUMP_TYPE_TO_REFRESH_UNREAD_COUNT")
                if (totalUnreadCount == "0") {
                    badge.visibility = View.GONE
                } else {
                    badge.visibility = View.VISIBLE
                    badge.text = totalUnreadCount
                }
            }

        linearLayout?.setOnClickListener(View.OnClickListener { //                Intent intent = new Intent(CallWindowServices.this, MainActivity.class);
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                startActivity(intent);
            println("CallWindowServices componentName.className : linearLayout")
            val stack = ActivityUtil.getStack()
            for (i in stack.indices) {
                println("CallWindowServices componentName.className : " + ActivityUtil.getStack()[i].componentName.className)
                if (ActivityUtil.getStack()[i].componentName.className == CallVideoChatActivityClassName) {
//                    linearLayout?.visibility = View.GONE
                    val componentName = ComponentName(
                        "com.mobile.anchor.app", CallVideoChatActivityClassName
                    )
                    val intent = Intent()
                    intent.setComponent(componentName)
                    linearLayout?.context?.jump(intent)
                    return@OnClickListener
                }
            }
            println("CallWindowServices componentName.className : PATH_APP_MAIN")
            Handler().postDelayed({
//                ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
                val componentName = ComponentName("com.mobile.anchor.app", MainActivityClassName)
                val intent = Intent()
                intent.flags = FLAG_ACTIVITY_NEW_TASK
                intent.setComponent(componentName)
                linearLayout?.context?.jump(intent)
            }, 200)
        })

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, String::class.java)
            .observeOn(RxSchedulers.ui).`as`<ObservableSubscribeProxy<String>>(
                AutoDispose.autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe { boo: String ->
                if (this@CallWindowServices == null) {
                    return@subscribe
                }
                isResumeActivity = boo
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_CANCEL, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe({ hashMapStr: String? ->
                isContainCallVideo = true
            }) { throwable: Throwable? -> }


        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_START_VIDEO, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe { hashMapStr: String? ->

                Handler().postDelayed({
//                    ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
                    val componentName =
                        ComponentName("com.mobile.anchor.app", MainActivityClassName)
                    val intent = Intent()
                    intent.flags = FLAG_ACTIVITY_NEW_TASK
                    intent.setComponent(componentName)
                    linearLayout?.context?.jump(intent)
                }, 200)

                RxTimerUtil.interval(this@CallWindowServices, 1) {
                    val stack = ActivityUtil.getStack()
                    for (i in stack.indices) {
                        if (ActivityUtil.getStack()[i].componentName.className == CallVideoChatActivityClassName) {
                            isContainCallVideo = true
                        }
                    }

//                        当在视频聊天的时候,直接阻断
                    if (isContainCallVideo || count >= 35) {
                        isContainCallVideo = false
                        RxTimerUtil.cancel(this@CallWindowServices)
                        count = 0
                        vibrator?.cancel()
                        println("CallWindowServices 2  ${count}")
                        (<EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager).cancelAll()
                        NotificationUtil.getInstance()
                            .showNotification(this, "Call", "Call is hung up...", null, 132321)
                        return@interval
                    } else {
                        if (isResumeActivity == "0") {
                            val componentName =
                                ComponentName("com.mobile.anchor.app", MainActivityClassName)
                            val intent = Intent()
                            intent.flags = FLAG_ACTIVITY_NEW_TASK
                            intent.setComponent(componentName)
                            linearLayout?.context?.jump(intent)
                        }
                    }
                    count++
                    println("CallWindowServices 1 ${count}")
                    vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
                    vibrator?.vibrate(longArrayOf(0, 1100), 0)
                }
            }
    }

    var isContainCallVideo = false
    private var isResumeActivity = "0"

    //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
    private var mTouchStartX = 0
    private var mTouchStartY = 0
    private var mTouchCurrentX = 0
    private var mTouchCurrentY = 0

    //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
    private var mStartX = 0
    private var mStartY = 0
    private var mStopX = 0
    private var mStopY = 0

    //判断悬浮窗口是否移动，这里做个标记，防止移动后松手触发了点击事件
    private var isMove = false

    private inner class FloatingListener : View.OnTouchListener {
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            val action = event.action
            when (action) {
                MotionEvent.ACTION_DOWN -> {
                    isMove = false
                    mTouchStartX = event.rawX.toInt()
                    mTouchStartY = event.rawY.toInt()
                    mStartX = event.x.toInt()
                    mStartY = event.y.toInt()
                }

                MotionEvent.ACTION_MOVE -> {
                    mTouchCurrentX = event.rawX.toInt()
                    mTouchCurrentY = event.rawY.toInt()
                    wmParams!!.x += mTouchCurrentX - mTouchStartX
                    wmParams!!.y += mTouchCurrentY - mTouchStartY
                    winManager!!.updateViewLayout(mFloatingLayout, wmParams)
                    mTouchStartX = mTouchCurrentX
                    mTouchStartY = mTouchCurrentY
                }

                MotionEvent.ACTION_UP -> {
                    mStopX = event.x.toInt()
                    mStopY = event.y.toInt()
                    if (Math.abs(mStartX - mStopX) >= 1 || Math.abs(mStartY - mStopY) >= 1) {
                        isMove = true
                    }
                }

                else -> {}
            }

            //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
            return isMove
        }
    }

    /**
     * 初始化窗口
     */
    private fun initWindow() {
        winManager = application.getSystemService(WINDOW_SERVICE) as WindowManager
        //设置好悬浮窗的参数
        wmParams = params02
        // 悬浮窗默认显示以左上角为起始坐标
        wmParams!!.gravity = Gravity.LEFT or Gravity.TOP
//        wmParams!!.alpha = 0.0f
        wmParams!!.format = PixelFormat.RGBA_8888
        //悬浮窗的开始位置，因为设置的是从左上角开始，所以屏幕左上角是x=0;y=0
        wmParams!!.x = winManager!!.defaultDisplay.width
        wmParams!!.y = 210
        //得到容器，通过这个inflater来获得悬浮窗控件
        inflater = LayoutInflater.from(baseContext)
        // 获取浮动窗口视图所在布局
        mFloatingLayout = inflater?.inflate(R.layout.service_remoteview, null)
        // 添加悬浮窗的视图
        winManager!!.addView(mFloatingLayout, wmParams)
    }
    //设置window type 下面变量2002是在屏幕区域显示，2003则可以显示在状态栏之上
    //设置可以显示在状态栏上

    //设置悬浮窗口长宽数据
    val params02: WindowManager.LayoutParams
        get() {
            wmParams = WindowManager.LayoutParams()
            //设置window type 下面变量2002是在屏幕区域显示，2003则可以显示在状态栏之上
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                wmParams!!.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                wmParams!!.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT // 系统提示window
            }
            //设置可以显示在状态栏上
            wmParams!!.flags =
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH

            //设置悬浮窗口长宽数据
            wmParams!!.width = WindowManager.LayoutParams.WRAP_CONTENT
            wmParams!!.height = WindowManager.LayoutParams.WRAP_CONTENT
            return wmParams as WindowManager.LayoutParams
        }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        RxTimerUtil.cancel(this@CallWindowServices)
        vibrator?.cancel()
        winManager!!.removeView(mFloatingLayout)
        OtherManager.manager?.totalUnreadCount?.removeObserver(observer)
        OtherManager.manager?.onlineStatus?.removeObserver(observer02)
        println("CallWindowServices  onDestroy")
    }
}