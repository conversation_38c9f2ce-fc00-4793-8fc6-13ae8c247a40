package anchor.app.base.commonservice;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.os.SystemClock;
import android.util.Log;

import androidx.annotation.Nullable;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AppStartListenerService extends Service {
    private String temp = null;
    private List<String> mList = new ArrayList<>();
    private static final String TAG = "AppStartListenerService";
    private static boolean flag = true;//服务Destroy结束线程
    private ActivityManager am;
 
    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "：onCreate");


        //监视任务栈最顶端应用
        am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        new Thread() {
            @Override
            public void run() {
                super.run();
                while (flag) {
                    synchronized (AppStartListenerService.class) {
//                        getRunningTasks();
//                         getCurrentActivity();

                        SystemClock.sleep(1000);
                    }
                }
            }
        }.start();
    }

    private void getRunningTasks() {
        List<ActivityManager.RunningTaskInfo> runningTasks = am.getRunningTasks(1);
        ActivityManager.RunningTaskInfo runningTaskInfo = runningTasks.get(0);
        ComponentName topActivity = runningTaskInfo.topActivity;
        String packageName = topActivity.getPackageName();
        Log.i(TAG, "：getPackageName() : "+getPackageName());
        Log.i(TAG, "：topActivity packageName : "+packageName);
        Log.i(TAG, "：temp packageName : "+temp);


        if (!getPackageName().equals(packageName)  && !getPackageName().equals(temp) && temp != null) {
            //进入第三方应用，第二种情况
            if (isThirdPartApp(packageName) && !isThirdPartApp(temp)) {
                if (mList.contains(packageName)) {
                    Log.i(TAG, packageName + "：返回第三方应用");
                } else if (!mList.contains(packageName)) {
                    mList.add(packageName);
                    Log.i(TAG, packageName + "：首次进入第三方应用");
                }
                //退出第三方应用，第四种情况
            } else if (!isThirdPartApp(packageName) && isThirdPartApp(temp)) {
                Log.i(TAG, packageName + "：退出第三方应用");
            }
        }
        temp = packageName;
    }

    public static Activity getCurrentActivity () {
        try {
            Class activityThreadClass = Class.forName("android.app.ActivityThread");
            Object activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(
                    null);
            Field activitiesField = activityThreadClass.getDeclaredField("mActivities");
            activitiesField.setAccessible(true);
            Map activities = (Map) activitiesField.get(activityThread);
            for (Object activityRecord : activities.values()) {
                Class activityRecordClass = activityRecord.getClass();
                Field pausedField = activityRecordClass.getDeclaredField("paused");
                pausedField.setAccessible(true);
                if (!pausedField.getBoolean(activityRecord)) {
                    Field activityField = activityRecordClass.getDeclaredField("activity");
                    activityField.setAccessible(true);
                    Activity activity = (Activity) activityField.get(activityRecord);


                    String packageName = activity.getComponentName().getPackageName();
                    String className = activity.getComponentName().getClassName();

                    Log.i(TAG, "getCurrentActivity ：getPackageName() : "+packageName);
                    Log.i(TAG, "getCurrentActivity ：topActivity className : "+className);

                    return activity;
                }
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        Log.i(TAG, "getCurrentActivity ：null ");
        return null;
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "：onDestroy");

        flag = false;
        mList.clear();
        mList = null;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }


     //判断是否为第三方应用
    public boolean isThirdPartApp(String packageName) {
        boolean b = false;
        PackageInfo mPackageInfo = null;
        try {
            mPackageInfo = getApplication().getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        Log.i(TAG, packageName + "：当前的包名");

        //判断是否为系统预装的应用
            if ((mPackageInfo.applicationInfo.flags & mPackageInfo.applicationInfo.FLAG_SYSTEM) <= 0 ) {
                //第三方应用
                b = true;
            } else
            {
                //系统应用
            }
        return  b;
    }
}