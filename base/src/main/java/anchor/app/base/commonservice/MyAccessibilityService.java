package anchor.app.base.commonservice;

import android.accessibilityservice.AccessibilityService;
import android.view.accessibility.AccessibilityEvent;

public class MyAccessibilityService extends AccessibilityService {
    private static final String TAG = "MyAccessibilityService";

    public static MyAccessibilityService mService;

    /**
     * 辅助功能是否启动
     */
    public static boolean isStart() {
        return mService != null;
    }


    //初始化
    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        mService = this;
    }


    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {

//        String packageName = event.getPackageName().toString();
//        String className = event.getClassName().toString();
//
//        Log.i(TAG, "：getPackageName() : "+packageName);
//        Log.i(TAG, "：className  : "+className);

        if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            // 应用程序启动/切换事件处理逻辑
//            String packageName = event.getPackageName().toString();
//            String className = event.getClassName().toString();
//
//            Log.i(TAG, "：getPackageName() : "+packageName);
//            Log.i(TAG, "：className  : "+className);

            // 在这里检查packageName和className是否是目标应用程序的启动/切换事件
            // ...
        }
    }

    @Override
    public void onInterrupt() {

    }

    // 其他方法和逻辑...
}