package anchor.app.base.lottie;

import android.animation.Animator;
import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import anchor.app.base.utils.FileUtils;
import anchor.app.base.utils.Logger;

/**
 * lottie 动画工具类
 * 用于管理动画播放
 * 目标：
 * 第一阶段：需要实例化之后使用，在构造中设置动画路径( 当前 )
 * 第二阶段：实现生命周期监听，pause自动暂停，resume继续
 */
public class LottieAnimationHelper {

    private static final String TAG = LottieAnimationHelper.class.getSimpleName();

    private Context mContext;

    // 类型
    private HashMap<String, String> lottieType;

    private HashMap<String, LottieAnimationView> lottieViewMap;
    private HashMap<String, String> lottieDataJsonPath;
    private HashMap<String, String> lottieViewStatus;
    private HashMap<String, String> lottieImageAssetsPath;
    private HashMap<String, LottieComposition> lottieCompositions;
    private HashMap<String, Integer> lottieRepeat;
    private HashMap<String, Animator.AnimatorListener> lottieListener;
    // 拦截配置
    private HashMap<String, Map<String, String>> lottieInterceptFilter;

    public LottieAnimationHelper() {
        lottieType = new HashMap<>();
        lottieViewMap = new HashMap<>();
        lottieViewStatus = new HashMap<>();
        lottieDataJsonPath = new HashMap<>();
        lottieImageAssetsPath = new HashMap<>();
        lottieCompositions = new HashMap<>();
        lottieRepeat = new HashMap<>();
        lottieListener = new HashMap<>();
        lottieInterceptFilter = new HashMap<>();
    }

    public void registerExtralStorageAnimationView(@NonNull Context context, @NonNull String tag,
                                                   @NonNull LottieAnimationView lottieAnimationView,
                                                   @NonNull String dataJson, String imagesPath,
                                                   int repeat, Animator.AnimatorListener listener, boolean async) {
        this.mContext = context;

        if (async) {
            LottieCompositionFactory.fromJsonString(FileUtils.readFile2String(dataJson), null)
                    .addListener(result -> {
                        lottieCompositions.put(tag, result);
                        Logger.d(TAG, "async load lottie:" + tag);
                    });
        }
        lottieType.put(tag, LottieType.EXTRAL_STORAGE);

        lottieViewMap.put(tag, lottieAnimationView);
        lottieDataJsonPath.put(tag, dataJson);
        if (null != imagesPath) lottieImageAssetsPath.put(tag, imagesPath);
        lottieRepeat.put(tag, repeat);
        if (null != listener) lottieListener.put(tag, listener);

    }

    /**
     * 注册assets动画
     *
     * @param async 是否异步处理
     */
    public void registerAssetsAnimationView(@NonNull Context context, @NonNull String tag,
                                            @NonNull LottieAnimationView lottieAnimationView,
                                            @NonNull String dataJson, String imageAssets,
                                            int repeat, Animator.AnimatorListener listener, boolean async) {
        this.mContext = context;

        if (async) {
            LottieCompositionFactory.fromAsset(context, dataJson)
                    .addListener(result -> {
                        lottieCompositions.put(tag, result);
                        Logger.d(TAG, "async load lottie:" + tag);
                    });
        }
        lottieType.put(tag, LottieType.ASSETS);

        lottieViewMap.put(tag, lottieAnimationView);
        lottieDataJsonPath.put(tag, dataJson);
        if (null != imageAssets) lottieImageAssetsPath.put(tag, imageAssets);
        lottieRepeat.put(tag, repeat);
        if (null != listener) lottieListener.put(tag, listener);

    }

    /**
     * 注册assets动画（有拦截器）
     *
     * @param interruptFilter 拦截器，暂时实现的方式为加载的assets的目录，但是其中拦截替换sd卡上的资源，若资源不存在则使用默认assets资源
     */
    public void registerAssetsAnimationView(@NonNull Context context, @NonNull String tag,
                                            @NonNull LottieAnimationView lottieAnimationView,
                                            @NonNull String dataJson, String imageAssets,
                                            int repeat, Animator.AnimatorListener listener, boolean async,
                                            Map<String, String> interruptFilter) {
        this.registerAssetsAnimationView(context, tag, lottieAnimationView, dataJson, imageAssets, repeat, listener, async);

        if (null != interruptFilter) lottieInterceptFilter.put(tag, interruptFilter);
    }

    /**
     * 播放动画
     */
    public void playAnim(@NonNull String tag) {
        Logger.d(TAG, "playAnim  ： " + tag);
        if (!lottieType.containsKey(tag)) {
            Logger.e(tag + " hasn't been registered yet.");
            return;
        }
        // 不为空
        String type = lottieType.get(tag);
        // 不为空
        LottieAnimationView lottieAnimationView = lottieViewMap.get(tag);
        // 不为空，可能为assets路径或者sd卡绝对路径
        String dataJsonPath = lottieDataJsonPath.get(tag);
        // 可能为空
        String imageAssetsPath = null;
        if (lottieImageAssetsPath.containsKey(tag)) {
            imageAssetsPath = lottieImageAssetsPath.get(tag);
        }
        // 不为空
        int repeatCount = lottieRepeat.get(tag);
        // 可能为空
        Animator.AnimatorListener animatorListener = null;
        if (lottieListener.containsKey(tag)) {
            animatorListener = lottieListener.get(tag);
        }

        LottieComposition composition = null;
        if (lottieCompositions.containsKey(tag)) {
            composition = lottieCompositions.get(tag);
        }

        // 若之前有播放lottie动画，停止播放
        stopAnim(lottieAnimationView);

        if (LottieType.ASSETS.equals(type)) {
            Logger.d(TAG, "play assets lottie :" + tag);
            Map<String, String> filter = null;
            if (lottieInterceptFilter.containsKey(tag)) filter = lottieInterceptFilter.get(tag);
            if (null != filter && filter.size() > 0) {
                // 有拦截器情况下，需要使用混合加载方式设置拦截
                playMixtureAnim(lottieAnimationView, dataJsonPath, composition, imageAssetsPath, repeatCount, filter, animatorListener);
            } else {
                // 无拦截器的情况下无需使用复杂方式自己加载
                playAssetaAnim(lottieAnimationView, dataJsonPath, composition, imageAssetsPath, repeatCount, animatorListener);
            }
        } else if (LottieType.EXTRAL_STORAGE.equals(type)) {
            playExtralStorageAnim(lottieAnimationView, dataJsonPath, composition, imageAssetsPath, repeatCount, animatorListener);
        }
    }

    /**
     * pause动画
     */
    public void pauseAnim(@NonNull String tag) {
        if (!lottieType.containsKey(tag)) {
            throw new RuntimeException(tag + " hasn't been registered yet.");
        }
        String status = lottieViewStatus.get(tag);

        pauseAnim(lottieViewMap.get(tag));
    }

    /**
     * resume动画
     */
    public void resumeAnim(@NonNull String tag) {
        if (!lottieType.containsKey(tag)) {
            throw new RuntimeException(tag + " hasn't been registered yet.");
        }

        resumeAnim(lottieViewMap.get(tag));
    }

    /**
     * 停止所有的动画
     */
    public void stopAnim(@NonNull String tag) {
        if (!lottieType.containsKey(tag)) {
            throw new RuntimeException(tag + " hasn't been registered yet.");
        }
        LottieAnimationView animationView = lottieViewMap.get(tag);
        stopAnim(animationView);
    }

    /**
     * 停止所有的动画
     */
    public void stopAllAnim() {
        for (String tag : lottieViewMap.keySet()) {
            LottieAnimationView animationView = lottieViewMap.get(tag);
            stopAnim(animationView);
        }
    }

    public void destroy() {
        lottieType.clear();
        lottieViewMap.clear();
        lottieViewStatus.clear();
        lottieDataJsonPath.clear();
        lottieImageAssetsPath.clear();
        lottieCompositions.clear();
        lottieRepeat.clear();
        lottieListener.clear();
        lottieInterceptFilter.clear();
    }

    /**
     * 播放外部存储动画
     */
    private void playExtralStorageAnim(LottieAnimationView animView, String dataJsonPath, LottieComposition composition, String imageAbsolutePath,
                                       int repeatCount, Animator.AnimatorListener animatorListener) {
        Logger.d(TAG, "play from file");

        String dataString = null;

        if (composition == null) {
            // 当没有异步加载的数据时，需要解析文件的json
            dataString = FileUtils.readFile2String(dataJsonPath);
            if (TextUtils.isEmpty(dataString)) return;
        }

        stopAnim(animView);
        if (animView.getVisibility() != View.VISIBLE) {
            animView.setVisibility(View.VISIBLE);
        }
        animView.removeAllAnimatorListeners();
        if (null != animatorListener) {
            animView.addAnimatorListener(animatorListener);
        }

        if (null != composition) {
            animView.setComposition(composition);
        } else {
            animView.setAnimationFromJson(dataString, null);
        }

        if (null != imageAbsolutePath) {
            animView.setImageAssetDelegate(asset -> {
                Bitmap bitmap = null;
                try {
                    FileInputStream fileInputStream = new FileInputStream(imageAbsolutePath + File.separator + asset.getFileName());
                    bitmap = BitmapFactory.decodeStream(fileInputStream);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (bitmap == null) {
                        bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8);
                    }
                }
                return bitmap;
            });
        }

        animView.setRepeatCount(repeatCount);
        animView.playAnimation();
    }

    /**
     * 播放混合lottie
     */
    private void playMixtureAnim(LottieAnimationView animView, String assetsJsonPath, LottieComposition composition,
                                 String assetsImagePath, int repeatCount, Map<String, String> interceptMap,
                                 Animator.AnimatorListener animatorListener) {
        Logger.d(TAG, "play mixture lottie . ");

        if (animView == null) return;
        if (animView.isAnimating()) {
            animView.cancelAnimation();
        }
        animView.removeAllAnimatorListeners();

        if (animView.getVisibility() != View.VISIBLE) {
            animView.setVisibility(View.VISIBLE);
        }
        animView.removeAllAnimatorListeners();
        if (null != animatorListener) {
            animView.addAnimatorListener(animatorListener);
        }

        if (null == composition) {
            animView.setAnimation(assetsJsonPath);
        } else {
            animView.setComposition(composition);
        }

        animView.setImageAssetDelegate(asset -> {

            String fileName = asset.getFileName();
            Logger.d(TAG, "playMixtureAnim() ------ fileName = " + asset.getFileName());

            String picPath;
            // 拦截内容不为空 并且 文件名在需要拦截的map中
            if ((null != interceptMap) && interceptMap.containsKey(fileName)) {
                String p = interceptMap.get(fileName);
                // 路径不为空 并且 文件存在
                if ((!TextUtils.isEmpty(p)) && FileUtils.isFileExist(p)) {
                    picPath = p;
                } else picPath = null;
            } else picPath = null;

            if (null != picPath) {
                Bitmap bitmap = null;
                try {
                    FileInputStream fileInputStream = new FileInputStream(picPath);
                    bitmap = BitmapFactory.decodeStream(fileInputStream);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (bitmap == null) {
                        bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8);
                    }
                }
                if (null != bitmap) return bitmap;
            }

            String pathSuffix = "";
            if (!assetsImagePath.endsWith("/")) pathSuffix += "/";

            Bitmap image = null;
            AssetManager am = animView.getContext().getResources().getAssets();
            try {
                InputStream is = am.open(assetsImagePath + pathSuffix + fileName);
                image = BitmapFactory.decodeStream(is);
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            return image;
        });

        animView.setRepeatCount(repeatCount);
        animView.playAnimation();
    }

    /**
     * 播放assets动画
     *
     * @param dataJson         lottie json文件路径
     * @param imageAsset       lottie动画对应的image文件夹路径
     * @param repeatCount      动画重复次数，0只播一次
     * @param animatorListener 动画回调函数
     */
    private void playAssetaAnim(LottieAnimationView animView, String dataJson, LottieComposition lottieComposition,
                                String imageAsset, int repeatCount, Animator.AnimatorListener animatorListener) {
        Logger.d(TAG, "play from file");
        stopAnim(animView);
        if (animView.getVisibility() != View.VISIBLE) {
            animView.setVisibility(View.VISIBLE);
        }
        animView.removeAllAnimatorListeners();
        if (null != animatorListener) {
            animView.addAnimatorListener(animatorListener);
        }
        if (null != imageAsset) {
            animView.setImageAssetsFolder(imageAsset);
        }
        if (null == lottieComposition) {
            animView.setAnimation(dataJson);
        } else {
            animView.setComposition(lottieComposition);
        }
        animView.setRepeatCount(repeatCount);
        animView.playAnimation();
    }

    private void resumeAnim(LottieAnimationView animView) {
        if (animView == null) return;
//        if (animView.isAnimating()) return;
        animView.resumeAnimation();
    }

    private void pauseAnim(LottieAnimationView animView) {
        if (animView == null) return;

        if (animView.isAnimating()) {
            animView.pauseAnimation();
        }
    }

    private void stopAnim(LottieAnimationView animView) {
        if (animView == null) return;

        if (animView.isAnimating()) {
            animView.cancelAnimation();
        }
        animView.removeAllAnimatorListeners();
    }

    /**
     * 动画的播放类型
     * 比如：播放的是assets路径下的动画
     */
    private interface LottieType {
        // assets 路径下的动画
        String ASSETS = "assets";

        // 外部存储动画
        String EXTRAL_STORAGE = "extral_storage";

    }

}
