package anchor.app.base.tip;

import android.content.Context;
import android.media.AudioManager;

import java.util.HashMap;
import java.util.Map;

import anchor.app.base.BaseApp;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.ZiLeSoundPlayer;

import static android.content.Context.AUDIO_SERVICE;

public class TipsManager implements ITip {

    private static final String TAG = TipsManager.class.getSimpleName();

    private long mTipId;
    private long mTipIdGenerator; // 用于生成唯一的播放id
    private boolean isPlaying;
    private TipChannel mTipChannel;
    private int mTipPriority;
    private Map<Long, ITipCallback> mCallbackMaps;
    private AudioManager mAudioManager;
    private AudioManager.OnAudioFocusChangeListener mAudioFocusChangeListener = focusChange -> {
        Logger.d(TAG, "onAudioFocusChange state:" + focusChange);
        switch (focusChange) {
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                Logger.d(TAG, "AUDIOFOCUS_LOSS_TRANSIENT");
                break;
            case AudioManager.AUDIOFOCUS_GAIN:
                Logger.d(TAG, "AUDIOFOCUS_GAIN");
                break;
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                Logger.d(TAG, "AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
                break;
            case AudioManager.AUDIOFOCUS_LOSS:
                Logger.d(TAG, "AUDIOFOCUS_LOSS");
                break;
        }
    };

    private ZiLeSoundPlayer.OnSoundPlayListener mSoundPlayerListener = new ZiLeSoundPlayer.OnSoundPlayListener() {
        @Override
        public void onSoundPlayComplete() {
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackEnd();
        }

        @Override
        public void onSoundPlayError() {
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(mTipId, null);
        }
    };

    private static class LazyHolder {
        private static TipsManager mInstance = new TipsManager();
    }

    private TipsManager() {
        isPlaying = false;
        mTipId = 0L;
        mTipIdGenerator = 0L;
        mTipPriority = TipPriority.NONE.ordinal();
        Context appContext = BaseApp.getAppContext().getApplicationContext();
        mAudioManager = (AudioManager) appContext.getSystemService(AUDIO_SERVICE);
        mCallbackMaps = new HashMap<>();
    }


    public static TipsManager getInstance() {
        return LazyHolder.mInstance;
    }


    @Override
    public long requestTipId() {
        if (mTipIdGenerator + 1 > Long.MAX_VALUE) {
            mTipIdGenerator = 0L;
        }
        return ++mTipIdGenerator;
    }

    @Override
    public void unRegisterTipCallback(long tipId) {
        Logger.d(TAG, "unRegisterTipCallback tipId=" + tipId);
        if (mCallbackMaps.containsKey(tipId)) {
            mCallbackMaps.remove(tipId);
            Logger.d(TAG, "unRegisterTipCallback ok");
        } else {
            Logger.d(TAG, "unRegisterTipCallback: no such tip callback");
        }
    }

    @Override
    public void playAssetFile(long tipId, String assetFile, ITipCallback tipCallback) {
        Logger.d(TAG, "实际tipId是：" + tipId);
        playAssetFile(tipId, assetFile, TipPriority.NORMAL.ordinal(), tipCallback);
    }

    @Override
    public void playAssetFile(long tipId, String assetFile, int priority, ITipCallback tipCallback) {
        Logger.d(TAG, "playAssetFile assetFile=" + assetFile + " priority=" + priority + " tipCallback=" + tipCallback);
        mCallbackMaps.put(tipId, tipCallback);
        if (tipId == mTipId) {
            Logger.d(TAG, "playTTS tip id is the same as current tip id.");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "playTTS tip id is the same as current tip id.");
            return;
        }

        if (!requestAudioFocus()) {
            Logger.w(TAG, "Not grant music stream audio focus !");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "Not grant music stream audio focus !");
            return;
        }

        if (priority >= mTipPriority) {
            Logger.d(TAG, "priority ok, ready to play");
            forceStopPlayTip();
            mTipId = tipId;
            mTipPriority = priority;
            mTipChannel = TipChannel.MEDIA_PLAYER;
            isPlaying = true;
            notifyTipCallbackBegin();
            ZiLeSoundPlayer.getInstance().playAsset(assetFile, mSoundPlayerListener);
        } else {
            Logger.d(TAG, "priority failed");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "priority is lower than current");
        }
    }

    @Override
    public void playExtralStorageFile(long tipId, String absolutePath, ITipCallback tipCallback) {
        Logger.d(TAG, "实际tipId是：" + tipId);
        playExtralStorageFile(tipId, absolutePath, TipPriority.NORMAL.ordinal(), tipCallback);
    }

    @Override
    public void playExtralStorageFile(long tipId, String absolutePath, int priority, ITipCallback tipCallback) {
        Logger.d(TAG, "playExtralStorageFile absolutePath=" + absolutePath + " priority=" + priority + " tipCallback=" + tipCallback);
        mCallbackMaps.put(tipId, tipCallback);
        if (tipId == mTipId) {
            Logger.d(TAG, "playTTS tip id is the same as current tip id.");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "playTTS tip id is the same as current tip id.");
            return;
        }

        if (!requestAudioFocus()) {
            Logger.w(TAG, "Not grant music stream audio focus !");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "Not grant music stream audio focus !");
            return;
        }

        if (priority >= mTipPriority) {
            Logger.d(TAG, "priority ok, ready to play");
            forceStopPlayTip();
            mTipId = tipId;
            mTipPriority = priority;
            mTipChannel = TipChannel.MEDIA_PLAYER;
            isPlaying = true;
            notifyTipCallbackBegin();
            ZiLeSoundPlayer.getInstance().playExtralStorageFile(absolutePath, mSoundPlayerListener);
        } else {
            Logger.d(TAG, "priority failed");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "priority is lower than current");
        }
    }

    @Override
    public void playAudioUrl(long tipId, String audioUrl, ITipCallback tipCallback) {
        playAudioUrl(tipId, audioUrl, TipPriority.NORMAL.ordinal(), tipCallback);
    }

    @Override
    public void playAudioUrl(long tipId, String audioUrl, int priority, ITipCallback tipCallback) {
        Logger.d(TAG, "playAudioUrl audioUrl=" + audioUrl + " priority=" + priority + " tipCallback=" + tipCallback);
        mCallbackMaps.put(tipId, tipCallback);
        if (tipId == mTipId) {
            Logger.d(TAG, "playTTS tip id is the same as current tip id.");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "playTTS tip id is the same as current tip id.");
            return;
        }

        if (!requestAudioFocus()) {
            Logger.w(TAG, "Not grant music stream audio focus !");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "Not grant music stream audio focus !");
            return;
        }

        if (priority >= mTipPriority) {
            Logger.d(TAG, "priority ok, ready to play");
            forceStopPlayTip();
            mTipId = tipId;
            mTipPriority = priority;
            mTipChannel = TipChannel.MEDIA_PLAYER;
            isPlaying = true;
            notifyTipCallbackBegin();
            ZiLeSoundPlayer.getInstance().playSound(audioUrl, mSoundPlayerListener);
        } else {
            Logger.d(TAG, "priority failed");
            abandonAudioFocus();
            resetInitialState();
            notifyTipCallbackFailure(tipId, "priority is lower than current");
        }
    }

    @Override
    public boolean isPlaying() {
        return isPlaying;
    }

    @Override
    public void pausePlayTip(long tipId) {
        Logger.d(TAG, "pausePlayTip tip id=" + tipId);
        if (isPlaying && (tipId == mTipId)) {
            Logger.d(TAG, "isPlaying=" + isPlaying);
            pausePlay();
        } else {
            Logger.d(TAG, "isPlaying=" + isPlaying);
        }
    }

    @Override
    public void resumePlayTip(long tipId) {
        Logger.d(TAG, "resumePlayTip tip id=" + tipId);
        if ((!isPlaying) && (tipId == mTipId)) {
            Logger.d(TAG, "isPlaying=" + isPlaying);
            resumePlay();
        } else {
            Logger.d(TAG, "isPlaying=" + isPlaying);
        }
    }

    @Override
    public void stopPlayTip(long tipId) {
        Logger.d(TAG, "stopPlayTip tip id=" + tipId);
        if (isPlaying && (tipId == mTipId)) {
            Logger.d(TAG, "isPlaying=" + isPlaying);
            interruptPlay();
        } else {
            Logger.d(TAG, "isPlaying=" + isPlaying);
        }
    }

    @Override
    public void stopPlay() {
        Logger.d(TAG, "stopPlay");
        if (isPlaying) {
            Logger.d(TAG, "isPlaying=" + isPlaying);
            interruptPlay();
        } else {
            Logger.d(TAG, "isPlaying=" + isPlaying);
        }
    }

    private void forceStopPlayTip() {
        Logger.d(TAG, "forceStopPlayTip");
        if (isPlaying) {
            Logger.d(TAG, "isPlaying=" + isPlaying);
            interruptPlay();
        } else {
            Logger.d(TAG, "isPlaying=" + isPlaying);
        }
    }

    /**
     * 如果当前在播放，打断当前的播放
     */
    private void interruptPlay() {
        Logger.d(TAG, "interruptPlay");
        if (mTipChannel == TipChannel.MEDIA_PLAYER) {
            stopMediaPlayerPlay();
        }
        abandonAudioFocus();
        resetInitialState();
        notifyTipCallbackInterrupt();
    }

    private void pausePlay() {
        Logger.d(TAG, "interruptPlay");
        if (mTipChannel == TipChannel.MEDIA_PLAYER) {
            isPlaying = false;
            ZiLeSoundPlayer.getInstance().pausePlay();
        }
        notifyTipCallbackPause();
    }

    private void resumePlay() {
        Logger.d(TAG, "interruptPlay");
        if (mTipChannel == TipChannel.MEDIA_PLAYER) {
            isPlaying = true;
            ZiLeSoundPlayer.getInstance().resumePlay();
        }
//        abandonAudioFocus();
//        resetInitialState();
        notifyTipCallbackResume();
    }

    private void stopMediaPlayerPlay() {
        Logger.d(TAG, "stopMediaPlayerPlay");
        ZiLeSoundPlayer.getInstance().stopPlaySound();
    }


    private void resetInitialState() {
        Logger.d(TAG, "resetInitialState");
        isPlaying = false;
        mTipPriority = TipPriority.NONE.ordinal();
        mTipChannel = TipChannel.NONE;
    }

    private void notifyTipCallbackBegin() {
        ITipCallback tipCallback = mCallbackMaps.get(mTipId);
        Logger.d(TAG, "notifyTipCallbackBegin mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipBegin(mTipId);
        }
    }

    private void notifyTipCallbackEnd() {
        ITipCallback tipCallback = mCallbackMaps.get(mTipId);
        Logger.d(TAG, "notifyTipCallbackEnd mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipEnd(mTipId);
        }
    }

    private void notifyTipCallbackFailure(long tipId, String msg) {
        ITipCallback tipCallback = mCallbackMaps.get(tipId);
        Logger.d(TAG, "notifyTipCallbackFailure mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipFailure(tipId, msg);
        }
    }

    private void notifyTipCallbackInterrupt() {
        ITipCallback tipCallback = mCallbackMaps.get(mTipId);
        Logger.d(TAG, "notifyTipCallbackInterrupt mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipInterrupted(mTipId);
        }
    }

    private void notifyTipCallbackPause() {
        ITipCallback tipCallback = mCallbackMaps.get(mTipId);
        Logger.d(TAG, "notifyTipCallbackPause mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipPause(mTipId);
        }
    }

    private void notifyTipCallbackResume() {
        ITipCallback tipCallback = mCallbackMaps.get(mTipId);
        Logger.d(TAG, "notifyTipCallbackResume mTipCallback:" + (null == tipCallback ? "null" : tipCallback));
        if (null != tipCallback) {
            tipCallback.onTipResume(mTipId);
        }
    }

    private boolean requestAudioFocus() {
        // Request audio focus for playback
        int result = mAudioManager.requestAudioFocus(mAudioFocusChangeListener,
                // Use the music stream.
                AudioManager.STREAM_MUSIC,
                // Request permanent focus.
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK);
        Logger.d(TAG, "requestAudioFocus result: " + result);
        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }

    private boolean abandonAudioFocus() {
        int result = mAudioManager.abandonAudioFocus(mAudioFocusChangeListener);
        Logger.d(TAG, "abandonAudioFocus result: " + result);
        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }
}
