package anchor.app.base.tip;

public interface ITip {
    long requestTipId();

    void unRegisterTipCallback(long tipId);

    void playAssetFile(long tipId, String assetFile, ITipCallback tipCallback);

    void playAssetFile(long tipId, String assetFile, int priority, ITipCallback tipCallback);

    void playExtralStorageFile(long tipId, String assetFile, ITipCallback tipCallback);

    void playExtralStorageFile(long tipId, String assetFile, int priority, ITipCallback tipCallback);

    void playAudioUrl(long tipId, String audioUrl, ITipCallback tipCallback);

    void playAudioUrl(long tipId, String audioUrl, int priority, ITipCallback tipCallback);

    void pausePlayTip(long tipId);

    void resumePlayTip(long tipId);

    void stopPlayTip(long tipId);

    boolean isPlaying();

    void stopPlay();

}
