package anchor.app.base.core;

public interface Constants {

    interface APP_REGITEST_ID {

        String BUGLY_APP_ID_DEBUG = "";
        String BUGLY_APP_ID_RELEASE = "";

        String AGORA_APP_TEST_ID = "********************************";
        String AGORA_APP_RELEASE_ID = "********************************";
    }


    interface HTTP_URL {

        String DEV_BASE_URL = "https://test-api.mindmate.vip";
        String DEV_BASE_H5_URL = "https://";

        String TEST_BASE_URL = "https://test-api.mindmate.vip";

        String TEST_BASE_H5_URL = "https://";

        String UAT_BASE_URL = "https://uat-api.mindmate.vip";

        String API_BASE_URL = "https://api.mindmate.vip";
        String API_BASE_H5_URL = "https://";

        String PrivatePolicy = "https://www.mindmate.vip/PrivatePolicy.html";
        String Useragreement = "https://www.mindmate.vip/Useragreement.html";


        /**************** 日程相关接口 ****************/

        String get_history_list_anchor = "/mateuser/diamond/anchor/record/page";
        String get_diamond_list_anchor = "/blade-auth/diamond/anchor/dayRecord/page";

        String get_invite_list_anchor = "/matechat/invite/list";

        String get_rank_list_anchor = "/matechat/anchor/rank/list";
        String get_config_list_anchor = "/matechat/anchor/rank/config";

        String get_invite_user_list_anchor = "/matechat/invite/user/list";

        String anchor_pay_info = "/matechat/anchor/pay/Info";

        String anchor_pay_country = "/matechat/anchor/pay/country";

        String anchor_pay_method = "/matechat/anchor/pay/method";

        String anchor_pay_param = "/matechat/anchor/pay/param";

        String order_pay_org = "/mateorder/order/pay/org";
        String anchor_pay_saveInfo = "/matechat/anchor/pay/saveInfo";

        String anchorExtract = "/matechat/anchor/extract";

        //获取黑名单列表
        String get_black_list = "/mateuser/user/black/list";

        //获取粉丝列表
        String get_fans_list = "/mateuser/user/fans";
        // 查询群组主播成员
        String get_user_list = "/matechat/anchor/group/list";
        // 等级信息
        String get_level_info = "/mateuser/diamond/level/list";
        //获取关注列表
        String get_follow_list = "/mateuser/user/follows";
        // 从黑名单移除
        String remove_black_list = "/mateuser/user/black/remove";
        // 取消关注
        String user_set_follow = "/mateuser/user/follow";
        String video_missed_call = "/ks-mikchat/video/missed/call"; //主播端未接调用
        //关注
        String user_set_unfollow = "/mateuser/user/unfollow";

        //拉黑
        String user_set_black = "/mateuser/user/black";

        //舉報
        String publish_report = "/mateuser/user/report";

        String user_bug_photo = "/matechat/anchorFile/buy";

        String publish_feedback = "/mateuser/user/feedback";

        String modifyChildInfo = "/mateuser/user/update/anchor";

        String modifyAnchorUpdateNew = "/matechat/anchor/updateNew";
        String modifyAnchorUpdateFiles = "/matechat/anchor/updateFiles";
        String modifyAnchorUpdateInfo = "/matechat/anchor/updateInfo";


        String groundFileName = "/mateuser/user/update/anchor/groundFileName";
        String headFileName = "/mateuser/user/update/anchor/headFileName";
        String nickName = "/mateuser/user/update/anchor/nickName";
        String showVideoUrl = "/mateuser/user/update/anchor/showVideoUrl";
        String coverVideoUrl = "/mateuser/user/update/anchor/coverVideoUrl";
        String highGroundFileName = "/mateuser/user/update/anchor/highGroundFileName";

        String anchorRegister = "/mateuser/auth/anchor/register";

        String anchorEmailCode = "/mateuser/auth/email/code";

        String incomeStatistics = "/matechat/video/anchor/incoming/coin";
        String incomeStatementList = "/matechat/video/anchor/incoming/report";
        String settlementList = "/ks-mikchat/anchor/extract/record";

        String auditUpdate = "/mateuser/user/audit/update";

        String login = "/mateuser/auth/anchor/token";

        String logout = "/mateuser/auth/logout";

        String union_check = "/mateuser/auth/union/check";

        //上传用户资源
        String upload_picture = "/materesource/resource/upload";

        //查询用户粉丝
        String query_user_fans = "/mateuser/user/fans";
        String query_user_detail = "/matechat/anchor/detail";
        String query_user_detail_consumer = "/mateuser/user/detail";

        String query_user_language = "/mateuser/auth/language/list";

        //上传用户头像
        String user_upload_picture = "/account/api/v1/user/avatar/uploadurl";

        String query_discover_course = "/mateuser/user/page";//主播端

        String query_anchor_work = "/mateuser/user/anchor/work";

        String query_anchor_share_url = "/matechat/anchor/share/url";
        String query_union_anchor_data = "/matechat/anchor/union/data";

        String query_union_anchor_work = "/matechat/anchor/union/anchor/list";

        String query_union_anchor_work_child = "/matechat/anchor/union/child/list";

        //查询主播当天通话记录
        String query_video_list = "/matechat/video/list";

        String query_call_anchor = "/matechat/video/status";

        String online_status = "/mateuser/user/update/anchor/onlineStatus";

        //扣除费用-每分钟
        String get_video_deduct = "/matechat/video/deduct";

        String anchorFileSave = "/matechat/anchorFile/save";

        String query_video_history = "/ks-mikchat/video/anchor/call/record";
        //查询热门推荐
        String query_content_feed = "/content/api/v1/feed";
        //获取视频通话的频道id
        String get_video_channel = "/matechat/video/channel";

        String get_video_call_time = "/matechat/video/call/time";

        String anchor_charts = "/matechat/anchor/charts";

        //推送给主播
        String get_video_push = "/matechat/video/push";

        //挂断视频通话是否需要标签评价 0不需要1需要
        String evaluate_label_need = "/matechat/video/evaluate/label/need";

        //主播拒接时推送用户
        String get_video_push_reject = "/matechat/video/push/reject";
        //获取主播个人详情页
        String get_personal_center = "/matechat/anchor/detail";
        //获取动态详情
        String get_trends_detail = "/matechat/trends/detail";
        //获取动态列表
        String get_trends_list = "/matechat/trends/list/page";

        String get_video_token = "/matechat/video/rtc/token";
        //挂断视频通话
        String set_video_hangup = "/matechat/video/hangup";

        // 获取礼物数据信息
        String get_gift_list = "/matechat/gift/list";
        // 赠送礼物
        String send_gift = "/matechat/gift/give";

        String ask_gift = "/matechat/gift/request";

        //心跳
        String heartbeat = "/mateuser/user/heartbeat";

        String rongcloud_token = "/matechat/message/rongcloud/token";
        String rongcloud_token_translate = "/matechat/message/rongcloud/translate/token";

        // 查看用户-主播基础信息
        String user_simple = "/mateuser/user/simple";

        String moment_audit = "/matechat/trends/get/audit";

        String package_renew = "/mateuser/auth/package/renew";
        // 是否关注
        String follow_flag = "/mateuser/user/follow/flag";

        String rongclound_appId = "/mateuser/auth/rongclound/appId";

        String sys_param = "/mateuser/auth/sys/param";

        String video_warn = "/matechat/video/warn";

        String country_list = "/mateuser/auth/country/list";

        String modifyOnlineStatus = "/mateuser/user/update/anchor/onlineStatus";

        String get_online_status = "/anchor/online/status";

        String album_list = "/matechat/anchorFile/all";

        String album_delete = "/matechat/anchorFile/removeBatch";

        String gift_list = "/matechat/gift/anchor/list";

        String gift_total_value = "/matechat/gift/anchor/total/incomeDiamond";

        String modify_pwd = "/mateuser/auth/anchor/pwd/update";
        String scenario_script = "/matechat/anchor/scenario/script"; //主播端收到礼物之后获取自定义场景文本

        String rewardTaskList = "/mateuser/auth/anchor/mission";
        String claimTask = "/mateuser/auth/anchor/mission/bonus/{id}";
        //设置价格
        String setupCallPrice = "/blade-auth/auth/anchor/price/slide";

        String inviteIntroducedList = "/ks-mikchat/invite/introduced/bonus";

        String fillInviteCode = "/matechat/invite/code/fillIn";

        String uploadRecordVideo = "/blade-auth/auth/anchor/record/video";

        String profileAuditStatus = "/ks-mikchat/anchor/update/auditFlag";

        //首页匹配开关
        String match_switch = "/blade-auth/user/update/anchor/matchOpen";

        //首页任务
        String home_task = "/blade-auth/user/anchor/match/progress";
        //获取敏感词
        String query_sensitive_words = "/blade-auth/user/sensitive/words";

        //主播端录制主播视频
        String anchor_addVideo = "/ks-mikchat/video/anchor/addVideo";

        String marquee_list = "/blade-auth/auth/notification/list";
    }

    interface PREF {
        String IS_LOGIN = "pref_is_login";
        String IS_FIRST_LOGIN = "pref_is_first_login";

        String IS_FIRST_PASSTIP = "pref_is_first_passtip";
        String TOKEN = "pref_access_token";
        String PHONE_NUMBER = "pref_phone_number";

        String VIDEO_CHAT_ID = "pref_video_chat_id";
        String VIDEO_READ_MOMENT_ID = "pref_read_moment_id";

        String GIFT_STATE = "pref_gift_state";
        String MIKCHAT_USERID = "pref_duyaya_user_id";
        String BASE_API_URL = "base_api_url";
        String BASE_H5_URL = "base_h5_url";
        String SHOW_MOBILE_NET_TIPS = "show_mobile_net_tips";
        String LOAD_DEVICE_STATUS = "setLoadDeviceStatus";
        String LOAD_DEVICE_ACTIVITE_STATUS = "setLoadDeviceActiviteStatus";
        String APP_START_STATUS = "appStartStatus";
        String firstDuyayaTab = "first_duyaya_tab";

        String USER = "user_info";

        String RONG_TOKEN = "rong_token";

    }

    interface KEY {
        String Url = "url";
        //是否覆盖在html上面，不占用窗口高度
        String ISFIXED = "mIsFixed";
        String TOOLBARTEXTCOLOR = "TOOLBARTEXTCOLOR";
        //是否是深色模式
        String TOOLBARDARKMODE = "toolbardarkmode";
        //设置返回按钮
        String TOOLBARBACKBTNIMGID = "toolbarbackbtnimgid";

        String MESSAGE_EXPEND_KEY_BLOCK = "isBlocked"; //消息拦截设置key
    }
}
