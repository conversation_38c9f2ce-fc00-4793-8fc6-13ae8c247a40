package anchor.app.base.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.load.resource.gif.GifOptions;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import jp.wasabeef.glide.transformations.GrayscaleTransformation;
import anchor.app.base.R;

/**
 * 图片Util
 */
public class ImageUtil {

    private static final int DEFAULT_PIC = R.mipmap.base_ic_main_default;

    /**
     * 加载图片，支持普通图片和gif 并且gif 格式使用的是PREFER_ARGB_8888
     *
     * @param context
     * @param imageView
     * @param imageUrl  全路径图片地址
     */
    public static void displayImageGif8888(Context context, ImageView imageView, String imageUrl) {
        displayImageGif8888(context,imageView,imageUrl, R.mipmap.base_ic_main_default);
    }

    public static void displayImageGif8888(Context context, ImageView imageView, String imageUrl, int defaultPic) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(imageUrl)
                .set(GifOptions.DECODE_FORMAT, DecodeFormat.PREFER_ARGB_8888)
                .placeholder(defaultPic)
                .apply(requestOptions)
                .error(defaultPic)
                .into(imageView);
    }

    /**
     * 加载图片，支持普通图片和gif 格式为PREFER_RGB_565
     *
     * @param context
     * @param imageView
     * @param imageUrl   全路径图片地址
     * @param defaultPic 默认占位图
     */
    public static void displayImage(Context context, ImageView imageView, String imageUrl, int defaultPic) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(imageUrl)
                .placeholder(defaultPic)
                .apply(requestOptions)
                .error(defaultPic)
                .into(imageView);
    }

    /**
     * 加载图片，支持普通图片和gif 格式为PREFER_RGB_565
     *
     * @param context
     * @param imageView
     * @param imageUrl  全路径图片地址
     */
    public static void displayImage(Context context, ImageView imageView, String imageUrl) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(imageUrl)
                .placeholder(R.mipmap.base_ic_main_default)
                .apply(requestOptions)
                .error(R.mipmap.base_ic_main_default)
                .into(imageView);
    }

    /**
     * 加载 Resource
     *
     * @param context
     * @param imageView
     * @param resourceId
     */
    public static void displayResource(Context context, ImageView imageView, int resourceId) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(resourceId)
                .apply(requestOptions)
                .into(imageView);
    }


    /**
     * 加载图片，支持普通图片和gif 格式为PREFER_RGB_565  图片url补全
     *
     * @param context
     * @param imageView
     * @param imageUrl
     */
    public static void displayStaticImage(Context context, ImageView imageView, String url) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(url)
                .placeholder(R.mipmap.base_ic_main_default)
                .apply(requestOptions)
                .error(R.mipmap.base_ic_main_default)
                .into(imageView);
    }

    public static void displayCircleImage(Context context, ImageView imageView, String url) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(url)
                .placeholder(R.mipmap.base_ic_main_default)
                .apply(requestOptions)
                .error(R.mipmap.ic_avatar_default)
                .transform(new CircleCrop())
                .into(imageView);
    }

    /**
     * 加载 File
     *
     * @param context
     * @param imageView
     * @param file
     * @param defaultPic 默认占位图
     */
    public static void displayFile(Context context, ImageView imageView, File file, int defaultPic) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(file)
                .placeholder(defaultPic)
                .apply(requestOptions)
                .error(defaultPic)
                .into(imageView);
    }

    /**
     * 加载 File
     * @param context
     * @param imageView
     * @param file
     */

    public static void displayFile(Context context, ImageView imageView, File file) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(file)
                .apply(requestOptions)
                .into(imageView);
    }



    /**
     * 加载图片，支持普通图片和gif 格式为PREFER_RGB_565
     *
     * @param context
     * @param imageView
     * @param imageUrl   全路径图片地址
     * @param defaultPic 默认占位图
     */
    public static void displayStaticImage(Context context, ImageView imageView, String imageUrl, int defaultPic) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(context)
                .load(imageUrl)
                .placeholder(defaultPic)
                .apply(requestOptions)
                .error(defaultPic)
                .into(imageView);
    }

    /**
     * 加载 Assets
     * @param context
     * @param imageView
     * @param path
     */
    public static void displayAssetsFile(Context context, ImageView imageView, String path) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);

        Glide.with(context)
                .load("file:///android_asset/" + path)
                .apply(requestOptions)
                .into(imageView);
    }


    /**
     * 只支持播放gif
     * @param context
     * @param imageView
     * @param path  url 或者其他
     */
    public static void displayGif(Context context, ImageView imageView, Object path) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        Glide.with(context)
                .asGif()
                .load(path)
                .format(DecodeFormat.PREFER_ARGB_8888)
                .apply(getMyOptions(0, ImageView.ScaleType.CENTER_CROP))
                .fitCenter()
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .into(imageView);
    }

    private static RequestOptions getMyOptions(int defaultImg, ImageView.ScaleType scaleType) {
        RequestOptions requestOptions = new RequestOptions()
                .placeholder(defaultImg)
                .error(defaultImg)
                .fallback(defaultImg)
                .diskCacheStrategy(DiskCacheStrategy.ALL);
        if (scaleType == ImageView.ScaleType.CENTER_INSIDE) requestOptions.centerInside();
        else if (scaleType == ImageView.ScaleType.CENTER_CROP) {
            requestOptions.centerCrop();
        } else if (scaleType == ImageView.ScaleType.FIT_CENTER) {
            requestOptions.fitCenter();
        } else {
            requestOptions.centerCrop();
        }
        return requestOptions;
    }

    /**
     * 播放表情
     * @param context
     * @param imageView
     * @param resId
     */
    public static void displayEmojGif(Context context, ImageView imageView, Object resId) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        Glide.with(context)
                .asGif()
                .load(resId)
                .format(DecodeFormat.PREFER_ARGB_8888)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .into(imageView);
    }

    private static boolean checkUrlIsEmpty(String url) {
        if (TextUtils.isEmpty(url)) {
            return true;
        }
        return false;
    }

    /***
     * 可以获得压缩后的图片
     *
     * @param width 期望的尺寸
     * @param height 期望的尺寸
     *
     * 有问题 问 billy
     * */
    public static String getResizeUrl(String url, int width, int height) {
        final String dot = ".";
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        int index = url.lastIndexOf(dot);
        if (index < 0) {
            return url;
        }
        String front = url.substring(0, index);
        String back = url.substring(index);
        StringBuffer sb = new StringBuffer();
        sb.append(front);
        sb.append("_").append(width).append("x").append(height);
        sb.append(back);
        return sb.toString();
    }

    /**
     * 下载图片
     * @param context
     * @param url
     * @param width
     * @param height
     * @param listener
     */
    public static void downloadImage(Context context, String url, int width, int height, OnDownloadOkListener listener) {
        if (context == null || checkUrlIsEmpty(url)) {
            return;
        }
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        Glide.with(context)
                .asBitmap()
                .load(url)
                .listener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        if (listener != null) {
                            listener.onResultError();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        if (listener != null) {
                            listener.onResultOk(resource);
                        }
                        return false;
                    }
                })
                .preload(width, height);
    }

    public static void downloadImage(Context context, String url, OnDownloadOkListener listener) {
        if (context == null || checkUrlIsEmpty(url)) {
            return;
        }
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        Glide.with(context)
                .asBitmap()
                .load(url)
                .listener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        if (listener != null){
                            listener.onResultError();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        if (resource != null && listener != null) {
                            listener.onResultOk(resource);
//                            Drawable drawable = new BitmapDrawable(context.getResources(), resource);
//                            listener.onResultOk(drawable);
                        }
                        return false;
                    }
                })
                .preload();
    }

    /**
     * glide返回结果
     */
    public interface OnDownloadOkListener {
        void onResultOk(Bitmap resource);

        void onResultError();
    }


    /**
     * 加载图片，并设置默认占位图
     */
    public static void displayImg(Context context, ImageView imageView, Object url, int defaultImg, RequestListener<Drawable> listener) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        //加入磁盘缓存，原图和转换图都会添加
        RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);

        Glide.with(context)
                .load(url)
                .placeholder(defaultImg)
                .apply(requestOptions)
                .listener(listener)
                .error(defaultImg)
                .into(imageView);
    }

    public static Drawable getAssetsImageDrawable(Context context, String name) {
        AssetManager asm = context.getAssets();

        InputStream is = null;//name:图片的名称
        try {
            is = asm.open(name);
            Drawable drawable = Drawable.createFromStream(is, null);
            return drawable;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }


    /**
     * 播放1遍 回调
     * @param context
     * @param imageView
     * @param resId
     * @param callback
     */
    public static void displayGif(Context context, ImageView imageView, final Object resId, final IGifOverCallback callback) {
        if (ActivityUtils.isDestroy(context)) {
            return;
        }
        Glide.with(context)
                .asGif()
                .load(resId)
                .format(DecodeFormat.PREFER_ARGB_8888)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .listener(new RequestListener<GifDrawable>() {
                    @Override
                    public boolean onLoadFailed(GlideException e, Object model,
                                                Target<GifDrawable> target, boolean isFirstResource) {
                        if (callback != null) {
                            callback.onOver();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(final GifDrawable resource, Object model,
                                                   Target<GifDrawable> target, DataSource dataSource,
                                                   boolean isFirstResource) {
                        resource.setLoopCount(1);
                        resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {

                            @Override
                            public void onAnimationEnd(Drawable drawable) {
                                resource.unregisterAnimationCallback(this);
                                if (callback != null) {
                                    callback.onOver();
                                }
                            }

                        });
                        return false;
                    }
                })
                .into(imageView);
    }


    public interface IGifOverCallback {
        public void onOver();

        public void onCancel();
    }

    /**
     * 获取缓存大小，单位已换算
     */
//    public static String getCacheSize() {
//        File photoCacheDir = Glide.getPhotoCacheDir(NanApplication.context);
//        if (photoCacheDir == null) {
//            return "0KB";
//        }
//        File parentFile = photoCacheDir.getParentFile();
//
//        double length = getDirSize(parentFile);
//        if (length < 1024) {
//            return length + "B";
//        }
//        length = DecimalUtil.getNum(length / 1024, 2);
//        if (length < 1024) {
//            return length + "KB";
//        }
//        length = DecimalUtil.getNum(length / 1024, 2);
//        if (length < 1024) {
//            return length + "M";
//        }
//        return DecimalUtil.getNum(length / 1024, 2) + "G";
//    }

    /**
     * 获取缓存原始数值
     */
//    public static long getCacheSizeLong() {
//        File photoCacheDir = Glide.getPhotoCacheDir(NanApplication.context);
//        if (photoCacheDir == null) {
//            return 0;
//        }
//        File parentFile = photoCacheDir.getParentFile();
//
//        return getDirSize(parentFile);
//
//    }

    private static long getDirSize(File dir) {
        if (dir == null) {
            return 0;
        }
        if (!dir.isDirectory()) {
            return 0;
        }
        long dirSize = 0;
        File[] files = dir.listFiles();
        for (File file : files) {
            if (file.isFile()) {
                dirSize += file.length();
            } else if (file.isDirectory()) {
                dirSize += file.length();
                dirSize += getDirSize(file); // 递归调用继续统计
            }
        }
        return dirSize;
    }

    /**
     * 根据分辨率压缩
     *
     * @param srcPath   图片路径
     * @param ImageSize 要压缩的大小，单位KB，例如压缩至1M ：1024 * 1024
     * @param savePath  压缩后的图片保存路径
     * @return
     */
    public static boolean compressBitmap(String srcPath, long ImageSize, String savePath) {
        int subtract;
        Bitmap bitmap = compressByResolution(srcPath, 500, 500); //分辨率压缩
        if (bitmap == null) {
            return false;
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int options = 100;
        bitmap.compress(Bitmap.CompressFormat.JPEG, options, baos);//质量压缩方法，这里100表示不压缩，把压缩后的数据存放到baos中
        while (baos.toByteArray().length > ImageSize * 1024 && options > 0) { //循环判断如果压缩后图片是否大于ImageSize kb,大于继续压缩
            subtract = setSubstractSize(baos.toByteArray().length / 1024);
            baos.reset();//重置baos即清空baos
            options -= subtract;//每次都减少10
            if (options < 0) {
                options = 0;
            }
            bitmap.compress(Bitmap.CompressFormat.JPEG, options, baos);//这里压缩options%，把压缩后的数据存放到baos中
        }
        try {
            FileOutputStream fos = new FileOutputStream(new File(savePath));//将压缩后的图片保存的本地上指定路径中
            fos.write(baos.toByteArray());
            fos.flush();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (bitmap != null) {
            bitmap.recycle();
        }

        return true; //压缩成功返回ture
    }

    /**
     * 根据分辨率压缩
     *
     * @param bitmap    图片原图
     * @param ImageSize 要压缩的大小，单位KB，例如压缩至1M ：1024
     * @return
     */
    public static byte[] compressBitmap(Bitmap bitmap, long ImageSize) {
        int subtract;
        if (bitmap == null) {
            return null;
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int options = 100;
        bitmap.compress(Bitmap.CompressFormat.JPEG, options, baos);//质量压缩方法，这里100表示不压缩，把压缩后的数据存放到baos中
        while (baos.toByteArray().length > ImageSize * 1024 && options > 0) { //循环判断如果压缩后图片是否大于ImageSize kb,大于继续压缩
            subtract = setSubstractSize(baos.toByteArray().length / 1024);
            baos.reset();//重置baos即清空baos
            options -= subtract;//每次都减少10
            if (options < 0) {
                options = 0;
            }
            bitmap.compress(Bitmap.CompressFormat.JPEG, options, baos);//这里压缩options%，把压缩后的数据存放到baos中
        }
        return baos.toByteArray();
    }

    /**
     * 根据分辨率压缩图片比例
     *
     * @param imgPath
     * @param w
     * @param h
     * @return
     */
    private static Bitmap compressByResolution(String imgPath, int w, int h) {
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imgPath, opts);

        int width = opts.outWidth;
        int height = opts.outHeight;
        int widthScale = width / w;
        int heightScale = height / h;

        int scale;
        if (widthScale < heightScale) { //保留压缩比例小的
            scale = widthScale;
        } else {
            scale = heightScale;
        }

        if (scale < 1) {
            scale = 1;
        }

        opts.inSampleSize = scale;

        opts.inJustDecodeBounds = false;

        Bitmap bitmap = BitmapFactory.decodeFile(imgPath, opts);

        return bitmap;
    }

    /**
     * 根据图片的大小设置压缩的比例，提高速度
     *
     * @param imageMB
     * @return
     */
    private static int setSubstractSize(int imageMB) {

        if (imageMB > 1000) {
            return 60;
        } else if (imageMB > 750) {
            return 40;
        } else if (imageMB > 500) {
            return 20;
        } else {
            return 10;
        }

    }

//    public static Bitmap blur(Bitmap sentBitmap, int radius) {
//        Bitmap bitmap = sentBitmap.copy(sentBitmap.getConfig(), true);
//
//        final RenderScript rs = RenderScript.create(getContent());
//        final Allocation input = Allocation.createFromBitmap(rs, sentBitmap, Allocation.MipmapControl.MIPMAP_NONE,
//                Allocation.USAGE_SCRIPT);
//        final Allocation output = Allocation.createTyped(rs, input.getType());
//        final ScriptIntrinsicBlur script = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));
//        script.setRadius(radius /* e.g. 3.f */);
//        script.setInput(input);
//        script.forEach(output);
//        output.copyTo(bitmap);
//        return bitmap;
//    }


    /**
     * 设置黑白图片到ImageView
     */
    public static void setBlackBitmapToImageView(Bitmap bmp, ImageView imageView) {
        Disposable subscribe = Observable.create(new ObservableOnSubscribe<Bitmap>() {

            @Override
            public void subscribe(ObservableEmitter<Bitmap> emitter) throws Exception {

                Bitmap bitmap = switchBlackBitmap(bmp);

                emitter.onNext(bitmap);
            }
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Bitmap>() {
                    @Override
                    public void accept(Bitmap bitmap) throws Exception {
                        if (imageView != null) {
                            imageView.setImageBitmap(bitmap);
                        }
                    }


                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (imageView != null) {
                            imageView.setImageResource(R.mipmap.base_ic_main_default);
                        }
                    }
                });

    }

    /**
     * 转换图片成黑白
     */
    private static Bitmap switchBlackBitmap(Bitmap originImg) {
        Bitmap grayImg = Bitmap.createBitmap(originImg.getWidth(), originImg.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(grayImg);
        Paint paint = new Paint();
        ColorMatrix colorMatrix = new ColorMatrix();
        colorMatrix.setSaturation(0);
        ColorMatrixColorFilter colorMatrixFilter = new ColorMatrixColorFilter(colorMatrix);
        paint.setColorFilter(colorMatrixFilter);
        canvas.drawBitmap(originImg, 0, 0, paint);
        return grayImg;
    }


    //Drawable----> Bitmap
    public static Bitmap drawableToBitmap(Drawable drawable, int size) {

        drawable.setBounds(0, 0, size, size);

        // 获取drawable的颜色格式
        Bitmap.Config config = drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888
                : Bitmap.Config.RGB_565;
        // 创建bitmap
        Bitmap bitmap = Bitmap.createBitmap(size, size, config);
        // 创建bitmap画布
        Canvas canvas = new Canvas(bitmap);
        // 将drawable 内容画到画布中
        drawable.draw(canvas);
        return bitmap;
    }

    /**
     * 图片黑白化
     */
    public static void displayBlackImage(Context context, ImageView giftPic, String realHeadPath, int defaultImg) {
        if (defaultImg == 0) {
            defaultImg = DEFAULT_PIC;
        }
        if (!ActivityUtils.isDestroy(context)) {
            Glide.with(context)
                    .load(realHeadPath)
                    .placeholder(defaultImg)
                    .apply(RequestOptions.bitmapTransform(new GrayscaleTransformation()).diskCacheStrategy(DiskCacheStrategy.ALL))
                    .error(defaultImg)
                    .into(giftPic);
        }
    }
}
