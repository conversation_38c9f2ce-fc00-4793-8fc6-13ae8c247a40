package anchor.app.base.utils;

import android.content.res.AssetFileDescriptor;
import android.content.res.AssetManager;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.core.content.FileProvider;

import java.io.File;
import java.io.IOException;

import anchor.app.base.BaseApp;

public class ZiLeSoundPlayer {

    private static final String TAG = ZiLeSoundPlayer.class.getSimpleName();

    private static ZiLeSoundPlayer mInstance;

    private OnSoundPlayListener mSoundPlayListener;
    private MediaPlayer mPlayer;

    private MediaPlayer.OnCompletionListener mOnCompletetionListener = new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            Logger.w(TAG, "onCompletion");
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayComplete();
                // mSoundPlayListener = null;
            }
        }
    };

    private MediaPlayer.OnErrorListener mOnErrorListener = new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            Logger.w(TAG, "what=" + what + " extra=" + extra);
//            mp.reset();
//            mp.release();
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
                // mSoundPlayListener = null;
            }
            return false;
        }
    };

    public static ZiLeSoundPlayer getInstance() {
        if (null == mInstance) {
            synchronized (ZiLeSoundPlayer.class) {
                if (null == mInstance) {
                    mInstance = new ZiLeSoundPlayer();
                }
            }
        }
        return mInstance;
    }

    private ZiLeSoundPlayer() {
    }

    private void createPlayerIfNeed() {
        if (null == mPlayer) {
            mPlayer = new MediaPlayer();
            mPlayer.setScreenOnWhilePlaying(true);
            mPlayer.setOnCompletionListener(mOnCompletetionListener);
            mPlayer.setOnErrorListener(mOnErrorListener);
            mPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
        }
    }

    public void playSound(String path, OnSoundPlayListener listener) {
        Logger.d(TAG, "playSound: " + path);
        this.mSoundPlayListener = listener;
        if (TextUtils.isEmpty(path)) {
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
            return;
        }

        if (mPlayer == null) {
            createPlayerIfNeed();
        } else {
            stopPlaySound();
        }
        try {
            mPlayer.setDataSource(path);
            mPlayer.prepareAsync();
            mPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mp) {
                    mp.start();
                }
            });
        } catch (IOException e) {
            Logger.e(this.getClass(), "Set media player datasource failed! ", e);
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
        }
    }

    public void pausePlay() {
        if (mPlayer != null && mPlayer.isPlaying()) {
            mPlayer.pause();
        }
    }

    public void resumePlay() {
        if (mPlayer != null && !mPlayer.isPlaying()) {
            mPlayer.start();
        }
    }

    public void stopPlaySound() {
        try {
            if (mPlayer != null) {
                mPlayer.stop();
                mPlayer.reset();
            }
        } catch (IllegalStateException ex) {
            ex.printStackTrace();
        }
    }

    public void playAsset(String aseetFile, OnSoundPlayListener listener) {
        this.mSoundPlayListener = listener;
        if (mPlayer == null) {
            createPlayerIfNeed();
        } else {
            stopPlaySound();
        }
        try {
            AssetManager assetManager;
            assetManager = BaseApp.getAppContext().getAssets();
            AssetFileDescriptor fileDescriptor = assetManager.openFd(aseetFile);
            mPlayer.setDataSource(fileDescriptor.getFileDescriptor(), fileDescriptor.getStartOffset(),
                    fileDescriptor.getLength());
            mPlayer.prepareAsync();
            mPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mp) {
                    mp.start();
                }
            });
        } catch (IOException e) {
            e.printStackTrace();
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
        } catch (IllegalStateException illex) {
            illex.printStackTrace();
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
        }
    }

    public void playExtralStorageFile(String absoluteFilePath, OnSoundPlayListener listener) {
        this.mSoundPlayListener = listener;
        if (mPlayer == null) {
            createPlayerIfNeed();
        } else {
            stopPlaySound();
        }
        try {
            File mp3File = new File(absoluteFilePath);
            Uri fileUri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                //通过FileProvider创建一个content类型的Uri
                fileUri = FileProvider.getUriForFile(BaseApp.getAppContext(), "mikchat.app.fileprovider", mp3File);
            } else {
                fileUri = Uri.fromFile(mp3File);
            }

            mPlayer.setDataSource(BaseApp.getAppContext(), fileUri);
            mPlayer.prepareAsync();
            mPlayer.setOnPreparedListener(MediaPlayer::start);
        } catch (IOException e) {
            e.printStackTrace();
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
        } catch (IllegalStateException illex) {
            illex.printStackTrace();
            if (null != mSoundPlayListener) {
                mSoundPlayListener.onSoundPlayError();
            }
        }
    }

    public interface OnSoundPlayListener {
        void onSoundPlayComplete();

        void onSoundPlayError();
    }
}
