package anchor.app.base.utils;

import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Handler;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.annotation.NonNull;

import anchor.app.base.BaseApp;
import anchor.app.base.R;

public class TextViewLinesUtils {

    public  interface Click{
        void router();
    }

    public static  void toggleEllipsizeDefault(final Context context, TextView textView,
                                        final String originText,Click click) {
        toggleEllipsize(context,textView,3,originText, BaseApp.getAppContext().getString(R.string.b72), R.color.colorPrimary,false,click);//context.getResources().getString()
    }
    /**
     * 设置textView结尾...后面显示的文字和颜色
     *
     * @param context    上下文
     * @param textView   textview
     * @param minLines   最少的行数
     * @param originText 原文本
     * @param endText    结尾文字
     * @param endColorID 结尾文字颜色id
     * @param isExpand   当前是否是展开状态
     *                   <p>
     *                   这个方法如果放在RecycleView里面的话，会有复用问题导致不显示，所以不能在Recyclerview里面用这个方法
     * @param click
     */
    public static final int MIN_CLICK_DELAY_TIME = 1500;
    public static long lastClickTime = 0;
    public static  void toggleEllipsize(final Context context, TextView textView,
                                        final int minLines,
                                        final String originText,
                                        final String endText,
                                        final int endColorID,
                                        final boolean isExpand, Click click) {
        if (TextUtils.isEmpty(originText)) {
            return;
        }
        textView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver
                .OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (isExpand) {
                    textView.setText(originText);
                } else {
                    int paddingLeft = textView.getPaddingLeft();
                    int paddingRight = textView.getPaddingRight();
                    TextPaint paint = textView.getPaint();
                    float moreText = textView.getTextSize() * endText.length();
                    float availableTextWidth = (textView.getWidth() - paddingLeft - paddingRight) *
                            minLines - moreText;

                    CharSequence ellipsizeStr = TextUtils.ellipsize(originText, paint,
                            availableTextWidth, TextUtils.TruncateAt.END);

                    if (ellipsizeStr.length() < originText.length()) {
                        CharSequence temp = ellipsizeStr + endText;
                        SpannableStringBuilder ssb = new SpannableStringBuilder(temp);
                        ssb.setSpan(new ForegroundColorSpan(context.getResources().getColor
                                        (endColorID)),
                                temp.length() - endText.length(), temp.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

                        textView.setMovementMethod(LinkMovementMethod.getInstance());
                        ssb.setSpan(new ClickableSpan() {
                            @Override
                            public void onClick(@NonNull View widget) {
                                textView.setText(originText);

                                new Handler().postDelayed(() -> {
                                    textView.setOnClickListener(v -> {

                                        if (System.currentTimeMillis() - lastClickTime - MIN_CLICK_DELAY_TIME >0) {
                                            click.router();
                                        }
                                        lastClickTime = System.currentTimeMillis();
                                    });
                                },500);
                            }
                        },temp.length() - endText.length(), temp.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

                        ssb.setSpan(new ClickableSpan() {
                            @Override
                            public void onClick(@NonNull View widget) {
                                if (System.currentTimeMillis() - lastClickTime - MIN_CLICK_DELAY_TIME >0) {
                                    click.router();
                                }
                                lastClickTime = System.currentTimeMillis();
                            }

                            @Override
                            public void updateDrawState(@NonNull TextPaint ds) {
                                super.updateDrawState(ds);
                                ds.setColor(Color.parseColor("#000000")); // 字体颜色
                                ds.setUnderlineText(false); // 是否有下划线
                            }
                        },0, temp.length() - endText.length()-1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                        textView.setText(ssb);
                    } else {
                        textView.setText(originText);
                            textView.setOnClickListener(v -> {
                                click.router();
                            });
                    }
                }
                if (Build.VERSION.SDK_INT >= 16) {
                    textView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                } else {
                    textView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                }
            }
        });
    }

    public static  void toggleEllipsize02(final Context context, TextView textView,
                                        final String originText,
                                        final String endText,
                                        final int endColorID, Click click) {
        if (TextUtils.isEmpty(originText)) {
            return;
        }
        textView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver
                .OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                    TextPaint paint = textView.getPaint();
                        CharSequence temp = originText + endText;
                        SpannableStringBuilder ssb = new SpannableStringBuilder(temp);

                        textView.setMovementMethod(LinkMovementMethod.getInstance());

                        ssb.setSpan(new ClickableSpan() {
                            @Override
                            public void onClick(@NonNull View widget) {
                                if (System.currentTimeMillis() - lastClickTime - MIN_CLICK_DELAY_TIME >0) {
                                    click.router();
                                }
                                lastClickTime = System.currentTimeMillis();
                            }

                            @Override
                            public void updateDrawState(@NonNull TextPaint ds) {
                                super.updateDrawState(ds);
                                ds.setColor(Color.parseColor("#FFFFFF")); // 字体颜色
                                ds.setUnderlineText(false); // 是否有下划线
                            }
                        },0, temp.length() -1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

                ssb.setSpan(new ForegroundColorSpan(context.getResources().getColor
                                (endColorID)),
                        temp.length() - endText.length(), temp.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);


                        textView.setText(ssb);

                if (Build.VERSION.SDK_INT >= 16) {
                    textView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                } else {
                    textView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                }
            }
        });
    }
}


