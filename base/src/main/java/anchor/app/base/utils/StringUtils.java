package anchor.app.base.utils;

import android.text.TextUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils {



    /**
     * 判断str1中包含str2的个数
     *
     * @param str
     * @param key
     * @return count
     */
    public static int countStr(String str, String key) {
        if (TextUtils.isEmpty(str) || TextUtils.isEmpty(key)) {
            return 0;
        }
        int index = 0; //定义变量。记录每一次找到的key的位置。
        int count = 0; //定义变量，记录出现的次数。

        //定义循环。只要索引到的位置不是-1，继续查找。
        while ((index = str.indexOf(key, index)) != -1) {
            //每循环一次，就要明确下一次查找的起始位置。
            index = index + key.length();
            //每查找一次，count自增。
            count++;
        }
        return count;
    }


    /**
     判断字符串是否含有Emoji表情
     **/
    public static boolean isHasEmoji(String reviewerName) {
        if(TextUtils.isEmpty(reviewerName)){
            return  false;
        }
        Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
        Matcher matcher = pattern.matcher(reviewerName);
        return matcher.find();
    }


    /**
     * 利用正则表达式判断字符串是否是数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        if (TextUtils.isEmpty(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }
    /**
     * 是否 还有 202D  202E 202e 202d
     */
    public static boolean isHas20Unicode(String string){
        if(TextUtils.isEmpty(string)){
            return false;
        }
        try {
            for (int i = 0; i < string.length(); i++) {

                // 取出每一个字符
                char c = string.charAt(i);

                String re= Integer.toHexString(c);

//                if("202D".equals(re) || "202E".equals(re) || "202e".equals(re) || "202d".equals(re)){
//                    return true;
//                }
                if( "202E".equals(re) || "202e".equals(re) ){
                    return true;
                }



            }
        } catch (Exception e) {
        }
        return false;
    }


    /**
     * 字符串转换unicode()去掉202D  202E 202e 202d
     */
    public static String string2Unicode2(String string) {
        if(TextUtils.isEmpty(string)){
            return "";
        }

        try {
            StringBuffer unicode = new StringBuffer();

            for (int i = string.length() -1; i >=0; i--) {

                // 取出每一个字符
                char c = string.charAt(i);

                String re= Integer.toHexString(c);

                if(!"202D".equals(re) && !"202E".equals(re) && !"202e".equals(re) && !"202d".equals(re)){
                    // 转换为unicode
                    unicode.append("\\u" + re);
                }


            }

            return unicode.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * unicode 转字符串
     */
    public static String unicode2String(String unicode) {
        if(TextUtils.isEmpty(unicode)){
            return "";
        }

        try {
            StringBuffer string = new StringBuffer();

            String[] hex = unicode.split("\\\\u");

            for (int i = 1; i < hex.length; i++) {

                // 转换出每一个代码点
                int data = Integer.parseInt(hex[i], 16);

                // 追加成string
                string.append((char) data);
            }

            return string.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

}
