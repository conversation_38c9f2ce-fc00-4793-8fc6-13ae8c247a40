package anchor.app.base.utils;

import android.text.TextUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.cert.CertificateException;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;


//目前只下载svga 和 mp4 使用
public class DownloadSimpleSvgaUtil {

    private static DownloadSimpleSvgaUtil downloadUtil;
    private final OkHttpClient okHttpClient;

    public static DownloadSimpleSvgaUtil get() {
        if (downloadUtil == null) {
            downloadUtil = new DownloadSimpleSvgaUtil();
        }
        return downloadUtil;
    }

    public DownloadSimpleSvgaUtil() {
        try {
            // Create a trust manager that does not validate certificate chains
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // Create an ssl socket factory with our all-trusting manager
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });

            builder.connectTimeout(20, TimeUnit.SECONDS);
            builder.readTimeout(20,TimeUnit.SECONDS);

            okHttpClient = builder.build();

            okHttpClient.dispatcher().setMaxRequestsPerHost(1);
            okHttpClient.dispatcher().setMaxRequests(1);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param url      下载连接
     * @param saveDir  储存下载文件的SDCard目录
     * @param fileName
     * @param listener 下载监听
     *                 fromType 11 全量下载 22 播放动画的时候下载
     */
    public void download(int fromType, final String url, final String saveDir, String fileName, final OnDownloadListener listener) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (!url.startsWith("http:") && !url.startsWith("https:")) {
            return;
        }

//        String fileName = UriUtil.getName(url);
        // 需要token的时候可以这样做
        // SharedPreferences sp=MyApp.getAppContext().getSharedPreferences("loginInfo", MODE_PRIVATE);
        // Request request = new Request.Builder().header("token",sp.getString("token" , "")).url(url).build();

        Request request = new Request.Builder().url(url).build();

        Callback  callback =  new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                // 下载失败
                listener.onDownloadFailed(0);

            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (DownloadSvgaManagerUtil.get().isLoadingOrSuccess(url)) {
                    listener.onDownloadEnd();
//                    System.out.println(url+fromType+"yyyy");
                    return;
                }
                //fromType 正好对应下载中
                DownloadSvgaManagerUtil.get().loading(url,fromType);
//                System.out.println(url+fromType+"hh");
                InputStream is = null;
                byte[] buf = new byte[2048];
                int len = 0;
                FileOutputStream fos = null;
                // 储存下载文件的目录
                String savePath = saveDir;
                File downloadFile = new File(savePath);
                if (!downloadFile.mkdirs()) {
                    downloadFile.createNewFile();
                }
                try {
                    is = response.body().byteStream();
                    long total = response.body().contentLength();
                    File file = new File(savePath, fileName);
                    fos = new FileOutputStream(file);
                    long sum = 0;
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                        sum += len;
                        int progress = (int) (sum * 1.0f / total * 100);
                        // 下载中
                        listener.onDownloading(progress);
//                        System.out.println(url+fromType+"iii");
                    }
                    fos.flush();
                    // 下载完成
                    listener.onDownloadSuccess();
                } catch (Exception e) {
                    if (e != null && e.getMessage() != null && e.getMessage().contains("CANCEL")) {
                        listener.onDownloadFailed(1);
                    } else {
                        listener.onDownloadFailed(0);
                    }
                } finally {
                    try {
                        if (is != null)
                            is.close();
                    } catch (IOException e) {
                    }
                    try {
                        if (fos != null)
                            fos.close();
                    } catch (IOException e) {
                    }
                }
            }
        };
        if (okHttpClient != null) {
            okHttpClient.newCall(request).enqueue(callback);
        }
    }

    public void cancelTask() {
        if (okHttpClient != null) {
            okHttpClient.dispatcher().cancelAll();
        }
    }

    public interface OnDownloadListener {
        /**
         * 下载成功
         */
        void onDownloadSuccess();

        /**
         * @param progress
         * 下载进度
         */
        void onDownloading(int progress);

        /**
         * 下载失败
         */
        void onDownloadFailed(int code);

        /**
         * 结束不在写入
         */
        void onDownloadEnd();
    }
}
