package anchor.app.base.utils;

import anchor.app.base.utils.ActivityUtil;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.RequiresApi;

import com.ta.utdid2.device.UTDevice;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;

import io.rong.imlib.RongIMClient;
import anchor.app.base.BaseApp;
import anchor.app.base.BuildConfig;

/**
 * 公共辅助类
 */
public class Utils {
    private Utils() {
        throw new IllegalStateException("you can't instantiate me!");
    }

    public static void navigation(String path) {
//        ARouter.getInstance().build(path).navigation();
    }

    public static boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    /**
     * 获取AndroidId
     *
     * @return
     */
    public static String getAndroidId() {
        return UTDevice.getUtdid(BaseApp.getAppContext());
    }

    /**
     * 判断是否是Y1/Y1S设备
     *
     * @return
     */
    public static boolean isY1OrY1s(String deviceModel) {
        return "DYY-Y1S".equals(deviceModel) || "DYY-Y1".equals(deviceModel);
    }

    public static boolean isDevelopBuild() {
        return BuildConfig.BUILD_TYPE.equals("develop");
    }

    public static boolean isQaBuild() {
        return BuildConfig.BUILD_TYPE.startsWith("debug");
    }

    private static String getFlavor() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            return (String) getBuildConfigValue(BaseApp.getAppContext(), "FLAVOR");
        }
        return "";
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static Object getBuildConfigValue(Context context, String fieldName) {
        try {
            Class<?> clazz = Class.forName(context.getPackageName() + ".BuildConfig");
            Field field = clazz.getField(fieldName);
            return field.get(null);
        } catch (ClassNotFoundException | NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 退出登录清除登录相关数据
     */
    public static void logOut() {
        //在 App 需要执行切换用户登录、注销登录的操作时，需要断开与融云的 IM 连接。SDK 支持设置断开 IM 连接之后是否允许向用户发送消息推送通知。
        RongIMClient.getInstance().disconnect(true);
        SharePreUtil.setPhoneNumber(null);
        SharePreUtil.setAccessToken(null);
        SharePreUtil.setIsLogin(false);
        SharePreUtil.deleShareAll();
        //退出所有Activity
        ActivityUtil.getInstance().finishAllActivityThenLogin();
    }

    /**
     * 获取进程号对应的进程名
     *
     * @param pid 进程号
     * @return 进程名
     */
    public static String getProcessName(int pid) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader("/proc/" + pid + "/cmdline"));
            String processName = reader.readLine();
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim();
            }
            return processName;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException exception) {
                exception.printStackTrace();
            }
        }
        return null;
    }

}
