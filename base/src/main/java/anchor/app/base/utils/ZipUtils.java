package anchor.app.base.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 解压zip包
 */
public class ZipUtils {

    private final static int BUFFER_SIZE = 4096;

    /**
     * 解压zip
     * @param zipPath   zip包路径
     * @param unZipPath 解压到路径
     */
    public static void unZip(String zipPath, String unZipPath) {
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(zipPath);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        File dirFile = new File(unZipPath);
        if(!dirFile.exists()) {
            dirFile.mkdir();
        }

        InputStream input = null;
        OutputStream output = null;
        try {
            Enumeration<?> entries = zipFile.entries();
            while(entries.hasMoreElements()) {
                ZipEntry zipEntry = (ZipEntry)entries.nextElement();
                if(zipEntry.isDirectory()) {
                    String nameString = zipEntry.getName();
                    nameString = nameString.substring(0,nameString.length()-1);
                    File file = new File(unZipPath + File.separator + nameString);
                    file.mkdirs();
                } else {
                    String filePath = unZipPath + File.separator + zipEntry.getName();
                    filePath = new String(filePath.getBytes(),"gbk");
                    File file = new File(filePath);
                    file.getParentFile().mkdirs();
                    file.createNewFile();
                    input = zipFile.getInputStream(zipEntry);
                    output = new FileOutputStream(file);
                    byte[] buffer = new byte[BUFFER_SIZE];
                    int realLength = 0;
                    while((realLength = input.read(buffer)) != -1) {
                        output.write(buffer,0,realLength);
                    }
                }
            }
            zipFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if(output != null) {
                try {
                    output.flush();
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
