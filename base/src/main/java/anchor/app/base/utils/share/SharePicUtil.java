package anchor.app.base.utils.share;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bigkoo.convenientbanner.utils.ScreenUtil;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.transition.Transition;

import java.util.ArrayList;
import java.util.List;

import anchor.app.base.R;
import anchor.app.base.utils.FileUtil;
import anchor.app.base.utils.ZxingQrCodeUtil;

public class SharePicUtil {

//    public static void createSharePic(@NonNull Context context, @NonNull ShareInfo shareInfo, @NonNull String fileName,
//                                      PicCreateView.Listener shareListener) {
//
//        View rootView = LayoutInflater.from(context).inflate(R.layout.course_view_share, null, false);
//
//        // 顶部图片
//        ImageView ivTop = rootView.findViewById(R.id.iv_top_share);
//        // 中间内容
//        TextView tvTotal = rootView.findViewById(R.id.account_total);
//        TextView tvTotalOpening = rootView.findViewById(R.id.account_opening);
//        TextView tvCurrentOpening = rootView.findViewById(R.id.current_opening);
//
//        // 底部二维码部分
//        ImageView ivPhoto = rootView.findViewById(R.id.iv_photo);
//        TextView tvUserName = rootView.findViewById(R.id.tv_user_name);
//        View qrCodeLayout = rootView.findViewById(R.id.layout_qcode);
//        ImageView ivQcode = rootView.findViewById(R.id.iv_qcode);
//        View star1 = rootView.findViewById(R.id.iv_star_1);
//        View star2 = rootView.findViewById(R.id.iv_star_2);
//        View star3 = rootView.findViewById(R.id.iv_star_3);
//
//        tvTotal.setText(shareInfo.getAccountTotal() + "");
//        tvTotalOpening.setText(shareInfo.getAccountOpening() + "");
//        tvCurrentOpening.setText(shareInfo.getCurrentOpening() + "");
//
//        tvUserName.setText(shareInfo.getUserName());
//
//        // 顶部图片
//        ivTop.setImageBitmap(getBitmap(context, shareInfo.getTopPicBitmap(), R.drawable.course_image_share_top_default));
//        // 头像
//        ivPhoto.setImageBitmap(BitmapUtil.getCirleBitmap(getBitmap(context, shareInfo.getBabyPhotoBitmap(), R.mipmap.ic_launcher)));
//        // 二维码
//        Bitmap qrCodeBitmap = shareInfo.getQrCOdeBitmap();
//        if (null == qrCodeBitmap) {
//            qrCodeLayout.setVisibility(View.INVISIBLE);
//        } else {
//            qrCodeLayout.setVisibility(View.VISIBLE);
//            ivQcode.setImageBitmap(qrCodeBitmap);
//        }
//
//        int starCount = shareInfo.getStarCount();
//        star1.setVisibility(starCount >= 1 ? View.VISIBLE : View.INVISIBLE);
//        star2.setVisibility(starCount >= 2 ? View.VISIBLE : View.INVISIBLE);
//        star3.setVisibility(starCount >= 3 ? View.VISIBLE : View.INVISIBLE);
//
//        // 创建图片大小 750 x 1334
//        PicCreateView createPicUtil = new PicCreateView(context, rootView, 750, 1334);
//        createPicUtil.setListener(shareListener);
//        createPicUtil.startDraw(getShareFileDirPath(context), fileName);
//    }

    public static void createTVClassesSharePic(@NonNull Context context, @NonNull ShareTvClassInfo shareInfo,
                                               @NonNull String fileName,
                                               PicCreateView.Listener shareListener) {

        View rootView = LayoutInflater.from(context).inflate(R.layout.course_view_share_tv_class, null, false);

        // 顶部图片
        ImageView ivTop = rootView.findViewById(R.id.iv_top_share);
        ImageView imageViewHead = rootView.findViewById(R.id.imageViewHead);
        TextView textViewName = rootView.findViewById(R.id.textViewName);
        // 中间内容
        TextView tvTotal = rootView.findViewById(R.id.account_total);
        TextView tvTotalOpening = rootView.findViewById(R.id.account_opening);
        TextView tvCurrentOpening = rootView.findViewById(R.id.current_opening);

        // 底部二维码部分
        ImageView ivQcode = rootView.findViewById(R.id.iv_qcode);

        tvTotal.setText(shareInfo.getAccountTotal() + "");
        tvTotalOpening.setText(shareInfo.getAccountOpening() + "");
        tvCurrentOpening.setText(shareInfo.getCurrentOpening() + "");

        textViewName.setText(shareInfo.getUserName());
        List<Boolean> list = new ArrayList<>();
        // 顶部图片
        Glide.with(context).load(shareInfo.getTopPicBitmap())
                .into(new DrawableImageViewTarget(ivTop) {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        super.onResourceReady(resource, transition);
                        list.add(true);
                        createPic(context, list, rootView, fileName, shareListener);
                    }
                });
        // 头像
        Glide.with(context)
                .load(shareInfo.getBabyPhotoBitmap())
                .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                .into(new DrawableImageViewTarget(imageViewHead) {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        super.onResourceReady(resource, transition);
                        list.add(true);
                        createPic(context, list, rootView, fileName, shareListener);
                    }
                });
        // 二维码
        Bitmap logoBitmap = BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_launcher);
        Bitmap qrImage = null;
        try {
            qrImage = ZxingQrCodeUtil.createCode(shareInfo.getQrCOdeBitmap(), ScreenUtil.dip2px(context, 65),
                    ScreenUtil.dip2px(context, 65), logoBitmap);
        } catch (Exception e) {
            e.printStackTrace();
            qrImage = ZxingQrCodeUtil.createQRImage(shareInfo.getQrCOdeBitmap(), ScreenUtil.dip2px(context, 65),
                    ScreenUtil.dip2px(context, 65));
        }
        ivQcode.setImageBitmap(qrImage);
    }

    public static void createPic(Context context, List<Boolean> flag, View rootView, String fileName, PicCreateView.Listener shareListener) {
        if (flag.size() == 2) {
            PicCreateView createPicUtil = new PicCreateView(context, rootView, ScreenUtil.getScreenWidth(context),
                    ScreenUtil.getScreenHeight(context));
            createPicUtil.setListener(shareListener);
            createPicUtil.startDraw(getShareFileDirPath(context), fileName);
        }
    }

    private static Bitmap getBitmap(@NonNull Context context, Bitmap b, @DrawableRes int resId) {
        if (null != b) return b;
        return BitmapFactory.decodeResource(context.getResources(), resId);
    }

    public static String getShareFileDirPath(Context context) {
        return FileUtil.getCacheRootDir(context) + "/Pictures/share/";
    }

}