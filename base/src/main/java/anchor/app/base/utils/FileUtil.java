package anchor.app.base.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Environment;
import android.text.TextUtils;


import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

import anchor.app.base.BaseApp;

public class FileUtil {
    private static final String TAG = "FileUtil";

    public static String getStorageRootDir(Context ctx) {
        String path;
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {
            path = Environment.getExternalStorageDirectory().getAbsolutePath();
        } else {
            path = ctx.getFilesDir().getPath();
        }
        return path;
    }

    public static File getPicassoCacheDir(Context ctx) {
        File file = new File(getCacheRootDir(ctx), "/zile/picasso-cache");
        if ((!file.exists()) || (!file.isDirectory())) {
            file.mkdirs();
        }
        return file;
    }

    public static String getCacheRootDir(Context ctx) {
        String path;
        try {
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                    || !Environment.isExternalStorageRemovable()) {
                path = ctx.getExternalFilesDir(null).getAbsolutePath();
            } else {
                path = ctx.getCacheDir().getPath();
            }
        } catch (IllegalArgumentException ex) {
            path = ctx.getCacheDir().getPath();
        }
        return path;
    }

    public static String getCacheDir(String directory) {
        String path = getStorageRootDir(BaseApp.getAppContext()) + "/zile";
        if (TextUtils.isEmpty(directory) || "/".equals(directory)) {
            path = path + "";
        } else if (directory.startsWith("/")) {
            path = path + directory;
        } else {
            path = path + "/" + directory;
        }
        FileUtils.createOrExistsDir(path);

        return path;
    }

    public static String generateAudioRecordPath() {
        String path = getCacheDir("record");
        FileUtils.createOrExistsDir(path);

        return path + File.separator + System.currentTimeMillis() + ".wav";
    }

    public static byte[] getFileBytes(String filePath) {
        File file = new File(filePath);
        ByteArrayOutputStream out = null;
        try {
            FileInputStream in = new FileInputStream(file);
            out = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int i = 0;
            while ((i = in.read(b)) != -1) {
                out.write(b, 0, b.length);
            }
            out.close();
            in.close();
        } catch (IOException ie) {
            ie.printStackTrace();
        }
        return out == null ? new byte[]{} : out.toByteArray();
    }

    public synchronized static void writeFileToString(String filePath, String fileName, String content) {
        File writename = new File(filePath, fileName); // 相对路径，如果没有则要建立一个新的output。txt文件
        if (!writename.getParentFile().exists()) {
            writename.getParentFile().mkdirs();
        }

        try {
            if (!writename.exists()) {
                writename.createNewFile(); // 创建新文件
            }
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(content); // \r\n即为换行
            out.flush(); // 把缓存区内容压入文件
            out.close(); // 最后记得关闭文件
        } catch (IOException ie) {
            ie.printStackTrace();
        }
    }

    public static String readFileToString(String fileName) {
        String outString = null;
        File file = new File(fileName);
        if (!file.exists())
            return null;

        try {
            BufferedReader br = new BufferedReader(new FileReader(file));
            String line = "";
            StringBuilder buffer = new StringBuilder();
            while ((line = br.readLine()) != null) {
                buffer.append(line);
            }
            outString = buffer.toString();

            br.close();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

        return outString;
    }

    public static String readFromRaw(Context context, int resId) {
        String text = null;
        try {
            InputStream is = context.getResources().openRawResource(resId);
            text = readTextFromSdcard(is);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.w(TAG, "read error:" + e.getMessage());
        }
        return text;
    }

    public static String readFromAssets(Context context, String filePath) {
        String text = null;
        try {
            InputStream is = context.getResources().getAssets().open(filePath);
            text = readTextFromSdcard(is);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.w(TAG, "read error:" + e.getMessage());
        }
        return text;
    }

    private static String readTextFromSdcard(InputStream is) throws Exception {
        InputStreamReader reader = new InputStreamReader(is);
        BufferedReader bufferedReader = new BufferedReader(reader);
        StringBuilder buffer = new StringBuilder("");
        String str;
        while ((str = bufferedReader.readLine()) != null) {
            buffer.append(str);
            buffer.append("\n");
        }
        return buffer.toString();
    }

    public static void copyAssetFileTo(String assetFileName, String targetFolder) {
        AssetManager assetManager = BaseApp.getAppContext().getAssets();
        FileUtils.createOrExistsDir(targetFolder);

        InputStream in = null;
        OutputStream out = null;
        try {
            in = assetManager.open(assetFileName);
            String newFileName = targetFolder + "/" + assetFileName;
            out = new FileOutputStream(newFileName);

            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            in.close();
            in = null;
            out.flush();
            out.close();
            out = null;
        } catch (Exception e) {
            Logger.e("tag", e.getMessage());
        }
    }

    /**
     * 判断assets文件夹下的文件是否存在
     *
     * @return false 不存在 true 存在
     */
    public static boolean isAssetFileExists(String filename) {
        AssetManager assetManager = BaseApp.getAppContext().getAssets();
        try {
            String[] names = assetManager.list("");
            if (filename.contains("/")) {
                names = assetManager.list(FileUtils.getFileName(filename));
            }
            if (names != null) {
                for (String name : names) {
                    if (name.equals(filename.trim())) {
                        Logger.d(TAG, "Asset file not exist: " + filename + "");
                        return true;
                    }
                }
            }
        } catch (IOException e) {
            Logger.w(TAG, "Asset file not exist: " + filename + e);
            return false;
        }
        Logger.w(TAG, "Asset file not exist: " + filename + "");
        return false;
    }
}