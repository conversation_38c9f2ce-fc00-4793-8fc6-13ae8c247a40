package anchor.app.base.utils;


import androidx.lifecycle.AndroidViewModel;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

import anchor.app.base.repository.IRepository;

/**
 * Created by jing<PERSON> on 2018/12/26.
 */

public class ClassUtil {

    /**
     * 获取泛型ViewModel的class对象
     */
    public static <T> Class<T> getViewModel(Object obj) {
        /**  <AUTHOR>  Description : 获取obj的class文件*/
        Class<?> currentClass = obj.getClass();
        Class<T> tClass = getGenericClass(currentClass, AndroidViewModel.class);
        if (tClass == null || tClass == AndroidViewModel.class) {
            return null;
        }
        return tClass;
    }


    public static <T> Class<T> getRepository(Object obj) {
        /**  <AUTHOR>  Description : 获取obj的class文件*/
        Class<?> currentClass = obj.getClass();
        Class<T> tClass = getGenericClass(currentClass, IRepository.class);
        if (tClass == null || tClass == IRepository.class) {
            return null;
        }
        return tClass;
    }


    /** <AUTHOR>
     * getGenericClass() Description : 从传入的当前类中过滤出类型是AndroidViewModel.class类型的class类
     * @param klass : 当前类的class
     * @param filterClass : 需要过滤出的class
     * @return java.lang.Class<T> : 返回过滤出的class对象文件 */
    private static <T> Class<T> getGenericClass(Class<?> klass, Class<?> filterClass) {
        Type type = klass.getGenericSuperclass();//获取父类中参数的type,类中的实现type是ParameterizedType是Type的子类
        if (type == null || !(type instanceof ParameterizedType)) return null;
        ParameterizedType parameterizedType = (ParameterizedType) type;
        Type[] types = parameterizedType.getActualTypeArguments();//获取当前类中各类型的所有参数
        for (Type t : types) {
            Class<T> tClass = (Class<T>) t;//将type转换为class
            if (filterClass.isAssignableFrom(tClass)) {//和当前类中所有参数的class逐一校对，当发现是是AndroidViewModel.class，就将该参数对象的class进行返回
                return tClass;
            }
        }
        return null;
    }
}
