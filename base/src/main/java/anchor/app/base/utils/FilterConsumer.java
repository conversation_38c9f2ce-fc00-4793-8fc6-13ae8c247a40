package anchor.app.base.utils;

import android.view.View;

import io.reactivex.functions.Consumer;

/**
 * 过滤Consumer
 */
public  class FilterConsumer<T> implements Consumer<T> {

    public Consumer<T> consumer;
    public ConsumerFilter<T> consumerFilter;

    private FilterConsumer(Consumer<T> consumer, ConsumerFilter<T> consumerFilter) {
        this.consumer = consumer;
        this.consumerFilter = consumerFilter;
    }

    public static <T> FilterConsumer<T> create(Consumer<T> consumer, ConsumerFilter<T> consumerFilter){
        return new FilterConsumer<>(consumer, consumerFilter);
    }

    @Override
    public void accept(T t) throws Exception {
        if (consumerFilter.filter()) {
            consumerFilter.accept(t);
        } else {
            consumer.accept(t);
        }
    }

    public interface ConsumerFilter<T> extends Consumer<T> {
        boolean filter();
    }

    public abstract static class ConsumerHealthy<T> implements ConsumerFilter<T> {
        public abstract void accept() ;
    }

}



