package anchor.app.base.utils;

import android.net.Uri;
import android.text.TextUtils;

import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Iterator;
import java.util.Map;

//import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import okhttp3.OkHttpClient;

public class OkHttpUtil {
    private static final String TAG = "OkHttpUtil";

//    public static OkHttpClient.Builder getUnsafeOkHttpClientBuilder() {
//        try {
//            final TrustManager[] TRUST_ALL_CERTS = new TrustManager[]{
//                    new X509TrustManager() {
//                        @Override
//                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
//                            //do nothing，接受任意客户端证书
//                        }
//
//                        @Override
//                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
//                            //do nothing，接受任意客户端证书
//                        }
//
//                        @Override
//                        public X509Certificate[] getAcceptedIssuers() {
//                            return new X509Certificate[]{};
//                        }
//                    }
//            };
//            // Install the all-trusting trust manager
//            final SSLContext sslContext = SSLContext.getInstance("TLS");
//            sslContext.init(null, TRUST_ALL_CERTS, new SecureRandom());
//            // Create an ssl socket factory with our all-trusting manager
//            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
//
//            HostnameVerifier hostnameVerifier = new HostnameVerifier() {
//                @Override
//                public boolean verify(String hostname, SSLSession sslSession) {
//                    // Always return true，接受任意域名服务器
//                    return true;
//                }
//            };
//
//            return new OkHttpClient().newBuilder()
//                    .sslSocketFactory(sslSocketFactory)
//                    .hostnameVerifier(hostnameVerifier);
//        } catch (Exception e) {
//            Logger.e(TAG, "getUnsafeOkHttpClientBuilder failed! ", e);
//        }
//        return new OkHttpClient.Builder();
//    }

    public static void setCertificates(OkHttpClient okHttpClient, InputStream... certificates) {
        try {
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null);
            int index = 0;
            for (InputStream certificate : certificates) {
                String certificateAlias = Integer.toString(index++);
                keyStore.setCertificateEntry(certificateAlias,
                        certificateFactory.generateCertificate(certificate));
                try {
                    if (certificate != null) {
                        certificate.close();
                    }
                } catch (IOException ignored) {
                    Logger.w(TAG, "certificate inputStream closed failed!", ignored);
                }
            }

            SSLContext sslContext = SSLContext.getInstance("TLS");

            TrustManagerFactory trustManagerFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

            trustManagerFactory.init(keyStore);
            sslContext.init(null, trustManagerFactory.getTrustManagers(), new SecureRandom());
            okHttpClient.newBuilder().sslSocketFactory(sslContext.getSocketFactory());
        } catch (Exception e) {
            Logger.e(TAG, "setCertificates failed!", e);
        }
    }

    public static String assembleUrlWithParams(String url, Map<String, String> params) {
        StringBuilder stringBuilder = new StringBuilder(url);
        String requestParam = getParams(params, false);
        if (!TextUtils.isEmpty(requestParam)) {
            stringBuilder.append("?").append(requestParam);
        }
        url = stringBuilder.toString();
        return url.replace(" ", "%20");
    }

    public String assembleUrlWithAllParams(String url, Map<String, String> params) {
        StringBuilder stringBuilder = new StringBuilder(url);
        String requestParam = getParams(params, true);
        if (!TextUtils.isEmpty(requestParam)) {
            stringBuilder.append("?").append(requestParam);
        }
        url = stringBuilder.toString();
        return url.replace(" ", "%20");
    }

    private static String getParams(Map<String, String> map, boolean allowNullParam) {
        if (map == null || map.isEmpty()) {
            return "";
        }
        StringBuilder builder = new StringBuilder("");
        Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String value = entry.getValue();
            if (!allowNullParam && (TextUtils.isEmpty(value) || "null".equals(value))) {
                Logger.w(TAG, "Ignore null value when assemble params!!");
                continue;
            }
            if (iterator.hasNext()) {
                builder.append(entry.getKey());
                builder.append("=");
                builder.append(encodeValue(value));
                builder.append("&");
            } else {
                builder.append(entry.getKey());
                builder.append("=");
                builder.append(encodeValue(value));
            }
        }
        return builder.toString();
    }

    private static String encodeValue(String value) {
        if (TextUtils.isEmpty(value)) {
            return value;
        }
        return Uri.encode(value);
    }
}
