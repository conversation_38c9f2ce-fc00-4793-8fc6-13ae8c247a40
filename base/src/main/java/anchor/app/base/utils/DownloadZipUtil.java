package anchor.app.base.utils;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DownloadZipUtil {

    private static final String TAG = "zip";
    private static DownloadZipUtil downloadUtil;

    public static DownloadZipUtil get() {
        if (downloadUtil == null) {
            downloadUtil = new DownloadZipUtil();
        }
        return downloadUtil;
    }


    private DownloadSimpleUtil downloadSimpleUtil;
    //1 加载中 2加载失败
    private Map<String, Integer> concurrentHashMap = new ConcurrentHashMap<String, Integer>();

    public DownloadZipUtil() {
        downloadSimpleUtil = new DownloadSimpleUtil();
    }

    public void addDownloadTask(String url, boolean isNeedReDownload, String goodsId) {
        if (concurrentHashMap == null) {
            concurrentHashMap = new ConcurrentHashMap<String, Integer>();
        }
        if (url == null || (!url.startsWith("http:") && !url.startsWith("https:")) || !url.endsWith(".zip")) {
            return;
        }
        if (concurrentHashMap.containsKey(url) && concurrentHashMap.get(url) == 1) {
            return;
        }
        String name = UriUtil.getName(url);

        String fileName =  goodsId+name.substring(name.lastIndexOf('.') );

//        File file = new File(PathUtils.getPathGoods() + "/" + name.substring(0, name.indexOf(".")));
        File file = new File(PathUtils.getPathGoods() + "/" + goodsId);

        if (!file.exists() || isNeedReDownload) {
            File file1 = new File(PathUtils.getPathGoods() + "/" + fileName);
            if (file1 != null && file1.exists()) {
                file1.delete();
            }
            if (downloadSimpleUtil != null) {
                concurrentHashMap.put(url, 1);
                downloadSimpleUtil.download(null, url, PathUtils.getPathGoods(),fileName, new DownloadSimpleUtil.OnDownloadListener() {
                    @Override
                    public void onDownloadSuccess() {
                        concurrentHashMap.remove(url);
//                        String name = UriUtil.getName(url);
//                        ZipUtils.unZip(PathUtils.getPathGoods() + "/" + goodsId+".zip", PathUtils.getPathGoods() + "/" + goodsId);
//                        SharePreUtil.setGiftState(goodsId);
                    }

                    @Override
                    public void onDownloading(int progress) {

                    }

                    @Override
                    public void onDownloadFailed(int code) {
                        concurrentHashMap.put(url, 2);
                    }
                });
            }
        }
    }

    public void reaDownloadTask() {
        if (concurrentHashMap == null) {
            return;
        }
        for (Map.Entry<String, Integer> entry : concurrentHashMap.entrySet()) {
            if (entry.getValue() == 2) {
//                addDownloadTask(entry.getKey(),true, goodsItem.id.toString());
            }
        }
    }
}
