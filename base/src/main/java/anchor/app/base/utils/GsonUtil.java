package anchor.app.base.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;


public class GsonUtil {
    //不用创建对象,直接使用Gson.就可以调用方法
    private static Gson gson = null;

    //判断gson对象是否存在了,不存在则创建对象
    static {
        if (gson == null) {
            //gson = new Gson();
            //当使用GsonBuilder方式时属性为空的时候输出来的json字符串是有键值key的,显示形式是"key":null，而直接new出来的就没有"key":null的
            gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        }
    }

    //无参的私有构造方法
    private GsonUtil() {
    }

    /**
     * 将对象转成json格式
     *
     * @param object
     * @return String
     */
    public static String GsonString(Object object) {
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object);
        }
        return gsonString;
    }

    /**
     * 将json转成特定的cls的对象
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> T GsonToBean(String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null && gsonString != null && gsonString.length() > 0) {
            //传入json对象和对象类型,将json转成对象
            try {
                t = gson.fromJson(gsonString, cls);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return t;
    }

    /**
     * json字符串转成list
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> List<T> GsonToList(String gsonString, Class<T> cls) {
        ArrayList<T> list = new ArrayList<>();
        if (gson != null) {
            try {
                JsonArray array = JsonParser.parseString(gsonString).getAsJsonArray();
                for (final JsonElement elem : array) {
                    list.add(gson.fromJson(elem, cls));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    public static <T> T getBean(String json, TypeToken<T> type) {
        T t = null;
        try {
            if (gson != null) {
                t = gson.fromJson(json, type.getType());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.e(e.getMessage());
        }

        return t;
    }

    public static <T> T getBean(String json, Type type) {
        T t = null;
        try {
            if (gson != null) {
                t = gson.fromJson(json, type);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.e(e.getMessage());
        }

        return t;
    }

    /**
     * 判断字符串是否是JSON
     * 如果解析结果为String，代表非json，返回false
     * 如果是对象，返回true
     */
    public static boolean isJson(String content) {
        try {
            String json = (String) gson.fromJson(content, Object.class);
            return false;
        } catch (Exception e) {
            if (content.startsWith("{")) {
                return true;
            }
            return false;
        }
    }

}