package anchor.app.base.utils.share;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bigkoo.convenientbanner.utils.ScreenUtil;

import java.io.IOException;
import java.lang.ref.SoftReference;

import anchor.app.base.utils.BitmapUtil;

public class PicCreateView extends LinearLayout {

    //    private Context context;
    private SoftReference<Listener> listener;

    private View rootView;

    // 长图的宽度，默认为屏幕宽度
    private int longPictureWidth = 750;
    private int longPictureHeight = 1334;

    public interface Listener {

        /**
         * 生成长图成功的回调
         *
         * @param path 长图路径
         */
        void onSuccess(String path);

        /**
         * 生成长图失败的回调
         */
        void onFail();
    }

    public PicCreateView(Context context, View v, int w, int h) {
        super(context);
        init(v, w, h);
    }

    public PicCreateView(Context context) {
        super(context);
        init(null, 0, 0);
    }

    public PicCreateView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(null, 0, 0);
    }

    public PicCreateView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(null, 0, 0);
    }

    public void setListener(Listener listener) {
        if (listener == null) {
            if (this.listener != null) this.listener.clear();
        } else {
            if (this.listener != null) this.listener.clear();
            this.listener = new SoftReference<>(listener);
        }
    }

    private void init(View view, int w, int h) {
//        this.context = context;
        this.longPictureWidth = w;
        this.longPictureHeight = h;
        // 长图两边的间距
        rootView = view;

        if (null != view) layoutView(view, w, h);

    }

    private void layoutView(View v, int width, int height) {
        v.layout(0, 0, width, height);
        int measuredWidth = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY);
        int measuredHeight = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
        v.measure(measuredWidth, measuredHeight);
        v.layout(0, 0, width, height);
    }


    public void startDraw(@NonNull String dirPath, @NonNull String fileName) {
        // 需要先下载全部需要用到的图片（用户头像、图片等），下载完成后再进行长图的绘制操作
//        downloadAllImage();
        draw(dirPath, fileName);
    }

    private Bitmap getLinearLayoutBitmap(View view, int w, int h) {
        Bitmap originBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(originBitmap);
        view.draw(canvas);
        return BitmapUtil.resizeImage(originBitmap, longPictureWidth, h);
    }

    private void draw(@NonNull String dirPath, @NonNull String fileName) {
        // 计算出最终生成的长图的高度 = 上、中、图片总高度、下等个个部分加起来

        // 创建空白画布
        Bitmap.Config config = Bitmap.Config.ARGB_8888;
        Bitmap bitmapAll;
        try {
            bitmapAll = Bitmap.createBitmap(longPictureWidth, longPictureHeight, config);
        } catch (Exception e) {
            e.printStackTrace();
            config = Bitmap.Config.RGB_565;
            bitmapAll = Bitmap.createBitmap(longPictureWidth, longPictureHeight, config);
        }
        Canvas canvas = new Canvas(bitmapAll);
        canvas.drawColor(Color.WHITE);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setFilterBitmap(true);

        // 绘制top view
        canvas.drawBitmap(getLinearLayoutBitmap(rootView, longPictureWidth, longPictureHeight), 0, -ScreenUtil.dip2px(getContext(), 25), paint);
        canvas.save();

        // 生成最终的文件，并压缩大小，这里使用的是：implementation 'com.github.nanchen2251:CompressHelper:1.0.5'
        try {
            String path = BitmapUtil.saveBitmapBackPath(getContext(), bitmapAll,
                    dirPath, fileName);
//            float imageRatio = ImageUtil.getImageRatio(path);
            // 最终压缩后的长图宽度
//            int finalCompressLongPictureWidth;
            // 由于长图一般比较大，所以压缩时应注意OOM的问题，这里并不处理OOM问题，请自行解决。
            if (this.listener != null && this.listener.get() != null) {
                this.listener.get().onSuccess(path);
            }
        } catch (IOException e) {
            e.printStackTrace();
            if (this.listener != null && this.listener.get() != null) {
                this.listener.get().onFail();
            }
        }
    }

}