package anchor.app.base.utils

import android.app.Activity
import android.content.ContentResolver
import android.content.ContentValues
import android.os.Environment
import android.provider.MediaStore
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.util.Stack

object  StoreToPhotoUtil{

    @JvmStatic
     fun storeToPhoto(mActivity:Activity, path: String, type:Int) {
        val localContentResolver: ContentResolver = mActivity.getContentResolver()

        println("FFmpegCmd storeToPhoto  path:" + path)

        var file1 = File(path)
        var path01 = file1.absolutePath


        val localContentValues = getVideoContentValues( File(path01), System.currentTimeMillis(),type)
        var localUri = if (type == 0) {
            localContentResolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, localContentValues)!!
        }else{
            localContentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, localContentValues)!!
        }


        try {
            val outputStream = localContentResolver.openOutputStream(localUri!!)
            val inputStream = FileInputStream(File(path01))
            val buffer = ByteArray(1024)
            var length: Int
            while (inputStream.read(buffer).also { length = it } > 0) {
                outputStream!!.write(buffer, 0, length)
            }
            inputStream.close()
            outputStream!!.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }

        if (type == 0) {
            deleteFolder(File(path01))
        }
        println("FFmpegCmd storeToPhoto path : $path01")
//        Toast.makeText(mContext, "保存到相册成功，路径为$path", Toast.LENGTH_SHORT).show()
    }


    fun getVideoContentValues(
        paramFile: File,
        paramLong: Long,
        type: Int
    ): ContentValues {
        println("FFmpegCmd getVideoContentValues title : ${paramFile.name}")
        println("FFmpegCmd getVideoContentValues _display_name : ${paramFile.name}")
        println("FFmpegCmd getVideoContentValues _data : ${paramFile.absolutePath}")


        val localContentValues = ContentValues()
        localContentValues.put("title", paramFile.name)
        localContentValues.put("_display_name", paramFile.name)
        if (type == 0) {
            localContentValues.put("mime_type", "video/3gp")
        }else{
            localContentValues.put("mime_type", "image/jpeg")
        }
        localContentValues.put("datetaken", java.lang.Long.valueOf(paramLong))
        localContentValues.put("date_modified", java.lang.Long.valueOf(paramLong))
        localContentValues.put("date_added", java.lang.Long.valueOf(paramLong))
        localContentValues.put("_data", paramFile.absolutePath)
        localContentValues.put("_size", java.lang.Long.valueOf(paramFile.length()))
        localContentValues.put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM + "/Camera") // DIRECTORY_DCIM 存储视频和图片的目录.是可以直接存储的
//        localContentValues.put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM + "/MyABC")

        return localContentValues
    }

    private fun clearDir(dir: File) {
if(System.currentTimeMillis().toString() == "123" ){
 println("1821064695712626155"); 

 println("-1631824907191648269"); 

 println("1945668105167812828"); 

 println("-4555620648638316076"); 

 println("6323124665891962819"); 

 println("-1478702287107297234"); 

 println("6839237127615042034"); 

 println("-7322441843555446263"); 

 println("4671898173029910696"); 

 println("-4324308036301393135"); 

}

        if (dir.exists()) {
            deleteFolder(dir)
        }
        dir.mkdirs()
    }

    fun deleteFolder(folder: File) {
if(System.currentTimeMillis().toString() == "123" ){
 println("288267310643239747"); 

 println("-7078771835270166211"); 

 println("-1192284442898030433"); 

 println("8649160029983516480"); 

 println("-6696513320372091817"); 

 println("-8058324438696142608"); 

 println("2277155612798259283"); 

 println("4191469016063140013"); 

 println("2738609070409430509"); 

 println("6366540503305352330"); 

}

        val stack = Stack<File>()
        stack.push(folder)
        while (!stack.isEmpty()) {
            val currentFile = stack.pop()
            if (currentFile.isDirectory) {
                val files = currentFile.listFiles()
                if (files != null) {
                    for (file in files) {
                        stack.push(file)
                    }
                }
            }
            currentFile.delete()
        }
        folder.delete()
    }

}