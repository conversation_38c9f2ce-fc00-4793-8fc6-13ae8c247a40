package anchor.app.base.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;

import java.util.HashMap;
import java.util.Map;

import anchor.app.base.R;

public class CountryUtil {

    public static  HashMap<String, Integer> getCountryMap(){
        HashMap<String, Integer> hashMap = new HashMap();
        hashMap.put("越南",R.mipmap.base_ic_country_01);
        hashMap.put("阿拉伯联合酋长国",R.mipmap.base_ic_country_02);
        hashMap.put("埃及",R.mipmap.base_ic_country_03);
        hashMap.put("巴基斯坦",R.mipmap.base_ic_country_04);
        hashMap.put("巴西",R.mipmap.base_ic_country_05);
        hashMap.put("菲律宾",R.mipmap.base_ic_country_06);
        hashMap.put("马来西亚",R.mipmap.base_ic_country_07);
        hashMap.put("孟加拉国",R.mipmap.base_ic_country_08);
        hashMap.put("摩洛哥",R.mipmap.base_ic_country_09);
        hashMap.put("沙特阿拉伯 ",R.mipmap.base_ic_country_10);
        hashMap.put("土耳其",R.mipmap.base_ic_country_11);
        hashMap.put("乌克兰",R.mipmap.base_ic_country_12);
        hashMap.put("叙利亚",R.mipmap.base_ic_country_13);
        hashMap.put("印度",R.mipmap.base_ic_country_14);
        hashMap.put("印度尼西亚", R.mipmap.base_ic_country_15);
        hashMap.put("哥伦比亚", R.mipmap.base_ic_country_16);
        hashMap.put("秘鲁", R.mipmap.base_ic_country_17);
        hashMap.put("委内瑞拉", R.mipmap.base_ic_country_18);
        return hashMap;
    }
    public static  HashMap<String, Integer> getCountryMap02(){
        HashMap<String, Integer> hashMap = new HashMap();
        hashMap.put("ALL",R.mipmap.course_icon_wall);
        hashMap.put("VN",R.mipmap.base_ic_country_01);
        hashMap.put("AE",R.mipmap.base_ic_country_02);
        hashMap.put("EG",R.mipmap.base_ic_country_03);
        hashMap.put("PK",R.mipmap.base_ic_country_04);
        hashMap.put("BR",R.mipmap.base_ic_country_05);
        hashMap.put("PH",R.mipmap.base_ic_country_06);
        hashMap.put("MY",R.mipmap.base_ic_country_07);
        hashMap.put("BD",R.mipmap.base_ic_country_08);
        hashMap.put("MA",R.mipmap.base_ic_country_09);
        hashMap.put("SA ",R.mipmap.base_ic_country_10);
        hashMap.put("TR",R.mipmap.base_ic_country_11);
        hashMap.put("UA",R.mipmap.base_ic_country_12);
        hashMap.put("SY",R.mipmap.base_ic_country_13);
        hashMap.put("IN",R.mipmap.base_ic_country_14);
        hashMap.put("ID", R.mipmap.base_ic_country_15);
        hashMap.put("CL", R.mipmap.base_ic_country_16);
        hashMap.put("PE", R.mipmap.base_ic_country_17);
        hashMap.put("VE", R.mipmap.base_ic_country_18);
        return hashMap;
    }

    public static String countryNameToCode(String key){

        if (key.equals("越南")){
            return "VN";
        } else if (key.equals("阿拉伯联合酋长国")) {
            return "AE";
        }else if (key.equals("埃及")) {
            return "EG";
        }else if (key.equals("巴基斯坦")) {
            return "PK";
        }else if (key.equals("巴西")) {
            return "BR";
        }else if (key.equals("菲律宾")) {
            return "PH";
        }else if (key.equals("马来西亚")) {
            return "MY";
        }else if (key.equals("孟加拉国")) {
            return "BD";
        }else if (key.equals("摩洛哥")) {
            return "MA";
        }else if (key.equals("沙特阿拉伯")) {
            return "SA";
        }else if (key.equals("土耳其")) {
            return "TR";
        }else if (key.equals("乌克兰")) {
            return "UA";
        }else if (key.equals("叙利亚")) {
            return "SY";
        }else if (key.equals("印度")) {
            return "IN";
        }else if (key.equals("印度尼西亚")) {
            return "ID";
        }else if (key.equals("哥伦比亚")) {
            return "CL";
        }else if (key.equals("秘鲁")) {
            return "PE";
        }else if (key.equals("委内瑞拉")) {
            return "VE";
        }else  {
            return "";
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    public static Drawable countryCodeToImage(Context context, String key){
        if (key == null || key.isEmpty()){
            return context.getDrawable(R.mipmap.ic_launcher);
        }
        else if (key.equals("VN")){
            return context.getDrawable(R.mipmap.base_ic_country_01);
        } else if (key.equals("AE")) {
            return context.getDrawable(R.mipmap.base_ic_country_02);
        }else if (key.equals("EG")) {
            return context.getDrawable(R.mipmap.base_ic_country_03);
        }else if (key.equals("PK")) {
            return context.getDrawable(R.mipmap.base_ic_country_04);
        }else if (key.equals("BR")) {
            return context.getDrawable(R.mipmap.base_ic_country_05);
        }else if (key.equals("PH")) {
            return context.getDrawable(R.mipmap.base_ic_country_06);
        }else if (key.equals("MY")) {
            return context.getDrawable(R.mipmap.base_ic_country_07);
        }else if (key.equals("BD")) {
            return context.getDrawable(R.mipmap.base_ic_country_08);
        }else if (key.equals("MA")) {
            return context.getDrawable(R.mipmap.base_ic_country_09);
        }else if (key.equals("SA")) {
            return context.getDrawable(R.mipmap.base_ic_country_10);
        }else if (key.equals("TR")) {
            return context.getDrawable(R.mipmap.base_ic_country_11);
        }else if (key.equals("UA")) {
            return context.getDrawable(R.mipmap.base_ic_country_12);
        }else if (key.equals("SY")) {
            return context.getDrawable(R.mipmap.base_ic_country_13);
        }else if (key.equals("IN")) {
            return context.getDrawable(R.mipmap.base_ic_country_14);
        }else if (key.equals("ID")) {
            return context.getDrawable(R.mipmap.base_ic_country_15);
        }else if (key.equals("CL")) {
            return context.getDrawable(R.mipmap.base_ic_country_16);
        }else if (key.equals("PE")) {
            return context.getDrawable(R.mipmap.base_ic_country_17);
        }else if (key.equals("VE")) {
            return context.getDrawable(R.mipmap.base_ic_country_18);
        }else  {
            return context.getDrawable(R.mipmap.ic_launcher);
        }
    }

    public static int getDrawableByName(String resourceName) {
        try {
            return R.mipmap.class.getField("country_"+resourceName.toLowerCase()).getInt(null);
        } catch (Exception e) {
            return R.mipmap.ic_launcher;
        }
    }
}
