package anchor.app.base.utils.share;

import android.graphics.Bitmap;

public class ShareInfo {

    private Bitmap topPicBitmap;
    // 累计上课
    private String accountTotal;
    // 累计开口
    private String accountOpening;
    // 本课开口
    private String currentOpening;

    private Bitmap babyPhotoBitmap;
    private String userName;
    private int starCount;
    private Bitmap qrCOdeBitmap;

    public ShareInfo(Bitmap topPicBitmap, String accountTotal, String accountOpening, String currentOpening,
                     Bitmap babyPhotoBitmap, String userName, Integer starCount, Bitmap qrCOdeBitmap) {
        this.topPicBitmap = topPicBitmap;
        this.accountTotal = accountTotal;
        this.accountOpening = accountOpening;
        this.currentOpening = currentOpening;
        this.babyPhotoBitmap = babyPhotoBitmap;
        this.userName = userName;
        if (null == starCount) starCount = 1;
        this.starCount = starCount;
        this.qrCOdeBitmap = qrCOdeBitmap;
    }

    public String getAccountTotal() {
        return accountTotal;
    }

    public void setAccountTotal(String accountTotal) {
        this.accountTotal = accountTotal;
    }

    public String getAccountOpening() {
        return accountOpening;
    }

    public void setAccountOpening(String accountOpening) {
        this.accountOpening = accountOpening;
    }

    public String getCurrentOpening() {
        return currentOpening;
    }

    public void setCurrentOpening(String currentOpening) {
        this.currentOpening = currentOpening;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getStarCount() {
        return starCount;
    }

    public void setStarCount(int starCount) {
        this.starCount = starCount;
    }

    public Bitmap getTopPicBitmap() {
        return topPicBitmap;
    }

    public void setTopPicBitmap(Bitmap topPicBitmap) {
        this.topPicBitmap = topPicBitmap;
    }

    public Bitmap getBabyPhotoBitmap() {
        return babyPhotoBitmap;
    }

    public void setBabyPhotoBitmap(Bitmap babyPhotoBitmap) {
        this.babyPhotoBitmap = babyPhotoBitmap;
    }

    public Bitmap getQrCOdeBitmap() {
        return qrCOdeBitmap;
    }

    public void setQrCOdeBitmap(Bitmap qrCOdeBitmap) {
        this.qrCOdeBitmap = qrCOdeBitmap;
    }
}
