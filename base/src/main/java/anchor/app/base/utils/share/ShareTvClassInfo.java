package anchor.app.base.utils.share;

import android.text.TextUtils;

public class ShareTvClassInfo {

    private String topPicBitmap;
    // 累计上课
    private String accountTotal;
    // 累计开口
    private String accountOpening;
    // 本课开口
    private String currentOpening;

    private String babyPhotoBitmap;
    private String userName;
    private String qrCOdeBitmap;

    public ShareTvClassInfo(String topPicBitmap, String accountTotal, String accountOpening, String currentOpening,
                            String babyPhotoBitmap, String userName, String qrCOdeBitmap) {
        this.topPicBitmap = topPicBitmap;
        this.accountTotal = accountTotal;
        this.accountOpening = accountOpening;
        this.currentOpening = currentOpening;
        this.babyPhotoBitmap = babyPhotoBitmap;
        this.userName = userName;
        this.qrCOdeBitmap = qrCOdeBitmap;
    }

    public String getTopPicBitmap() {
        return topPicBitmap;
    }

    public void setTopPicBitmap(String topPicBitmap) {
        this.topPicBitmap = topPicBitmap;
    }

    public String getAccountTotal() {
        if (TextUtils.isEmpty(accountTotal) || "null".equals(accountTotal)) return "0";
        return accountTotal;
    }

    public void setAccountTotal(String accountTotal) {
        this.accountTotal = accountTotal;
    }

    public String getAccountOpening() {
        if (TextUtils.isEmpty(accountOpening) || "null".equals(accountOpening)) return "0";
        return accountOpening;
    }

    public void setAccountOpening(String accountOpening) {
        this.accountOpening = accountOpening;
    }

    public String getCurrentOpening() {
        if (TextUtils.isEmpty(currentOpening) || "null".equals(currentOpening)) return "0";
        return currentOpening;
    }

    public void setCurrentOpening(String currentOpening) {
        this.currentOpening = currentOpening;
    }

    public String getBabyPhotoBitmap() {
        return babyPhotoBitmap;
    }

    public void setBabyPhotoBitmap(String babyPhotoBitmap) {
        this.babyPhotoBitmap = babyPhotoBitmap;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getQrCOdeBitmap() {
        return qrCOdeBitmap;
    }

    public void setQrCOdeBitmap(String qrCOdeBitmap) {
        this.qrCOdeBitmap = qrCOdeBitmap;
    }
}
