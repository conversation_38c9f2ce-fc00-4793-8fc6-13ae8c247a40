package anchor.app.base.retrofit;

import android.content.Context;
import android.text.TextUtils;

import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import anchor.app.base.BaseApp;
import anchor.app.base.BuildConfig;
import anchor.app.base.core.Constants;
import anchor.app.base.utils.ApkUtil;
import anchor.app.base.utils.NetUtil;
import anchor.app.base.utils.SharePreUtil;
import anchor.app.base.utils.UniqueIDUtils;
import okhttp3.Cache;
import okhttp3.CacheControl;
import okhttp3.ConnectionSpec;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.Buffer;
import okio.BufferedSource;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * 封装基础的Retrofit
 */

public class BaseApiClient {

    // api地址
    public static String baseApiUrl;
    // h5 地址
    public static String baseH5Url;

    static {
        if ("debug".equals(BuildConfig.BUILD_TYPE)) {
            //从SP中取值，如果没有再设置默认值
            String url = SharePreUtil.getBaseApiUrl();
            String h5url = SharePreUtil.getBaseH5Url();
            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(h5url)) {
                baseApiUrl = url;
                baseH5Url = h5url;
            } else {
                baseApiUrl = Constants.HTTP_URL.TEST_BASE_URL;
                baseH5Url = Constants.HTTP_URL.TEST_BASE_H5_URL;
            }
//            baseApiUrl = Constants.HTTP_URL.API_BASE_URL;
//            baseH5Url = Constants.HTTP_URL.API_BASE_H5_URL;
        } else if ("uat".equals(BuildConfig.BUILD_TYPE)) {
            //从SP中取值，如果没有再设置默认值
            String url = SharePreUtil.getBaseApiUrl();
            String h5url = SharePreUtil.getBaseH5Url();
            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(h5url)) {
                baseApiUrl = url;
                baseH5Url = h5url;
            } else {
                baseApiUrl = Constants.HTTP_URL.UAT_BASE_URL;
                baseH5Url = Constants.HTTP_URL.API_BASE_H5_URL;
            }
        } else {
            baseApiUrl = Constants.HTTP_URL.API_BASE_URL;
            baseH5Url = Constants.HTTP_URL.API_BASE_H5_URL;
        }
    }

    private static final int DEFAULT_TIMEOUT = 15000;

    private static Retrofit retrofit;

    private BaseApiClient() {
        OkHttpClient okHttpClient = getOkhttpClient();
        retrofit = new Retrofit.Builder().client(okHttpClient).baseUrl(baseApiUrl).addConverterFactory(new NullOnEmptyConverterFactory()).addConverterFactory(GsonConverterFactory.create()).addCallAdapterFactory(RxJava2CallAdapterFactory.create()).build();
    }

    private static class SingletonHolder {
        private static final BaseApiClient INSTANCE = new BaseApiClient();
    }

    //获取单例
    public static BaseApiClient getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public <T> T create(Class<T> clazz) {
        return retrofit.create(clazz);
    }

    /**
     * 设置OkHttpClient，添加拦截器等
     *
     * @return 可以返回为null
     */
    public OkHttpClient getOkhttpClient() {
        try {
            // Install the all-trusting trust manager TLS
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new SecureRandom());
            //cache url
            File httpCacheDirectory = new File(BaseApp.getAppContext().getCacheDir(), "responses");
            // 50 MiB
            int cacheSize = 50 * 1024 * 1024;
            Cache cache = new Cache(httpCacheDirectory, cacheSize);
            // Create an ssl socket factory with our all-trusting manager
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            OkHttpClient.Builder okBuilder = new OkHttpClient.Builder();
            okBuilder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]).readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS).connectionSpecs(Arrays.asList(ConnectionSpec.CLEARTEXT, ConnectionSpec.MODERN_TLS)).connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS).writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS).addInterceptor(new HttpHeadInterceptor()).eventListenerFactory(PrintingEventListener.FACTORY)
//                    .dns(OkHttpDns.getInstance(BaseApp.getAppContext()))
                    .addInterceptor(new AddCookiesInterceptor()).addInterceptor(new AddCacheInterceptor(BaseApp.getAppContext())).addInterceptor(new LoggerInterceptor())
//                    .addInterceptor(new HttpLoggingInterceptor()
//                            .setLevel(
//                                    HttpLoggingInterceptor.Level.BODY))
                    .hostnameVerifier((hostname, session) -> true);
            return okBuilder.build();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }


    private class HttpHeadInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Request.Builder builder = request.newBuilder();
            builder.addHeader("Accept", "application/json;");
            if (NetUtil.isNetworkConnected(BaseApp.getAppContext())) {
                int maxAge = 60;
                builder.addHeader("Cache-Control", "public, max-age=" + maxAge);
            } else {
                int maxStale = 60 * 60 * 24 * 28;
                builder.addHeader("Cache-Control", "public, only-if-cached, max-stale=" + maxStale);
            }
            return chain.proceed(builder.build());
        }
    }

    private class AddCacheInterceptor implements Interceptor {
        private Context context;

        AddCacheInterceptor(Context context) {
            super();
            this.context = context;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {

            CacheControl.Builder cacheBuilder = new CacheControl.Builder();
            cacheBuilder.maxAge(0, TimeUnit.SECONDS);
            cacheBuilder.maxStale(365, TimeUnit.DAYS);
            CacheControl cacheControl = cacheBuilder.build();
            Request request = chain.request();
            if (!NetUtil.isNetworkConnected(context)) {
                request = request.newBuilder().cacheControl(cacheControl).build();
            }
            Response originalResponse = chain.proceed(request);
            if (NetUtil.isNetworkConnected(context)) {
                // read from cache
                int maxAge = 0;
                return originalResponse.newBuilder().removeHeader("Pragma").header("Cache-Control", "public ,max-age=" + maxAge).build();
            } else {
                // tolerate 4-weeks stale
                int maxStale = 60 * 60 * 24 * 28;
                return originalResponse.newBuilder().removeHeader("Pragma").header("Cache-Control", "public, only-if-cached, max-stale=" + maxStale).build();
            }
        }
    }

    private class AddCookiesInterceptor implements Interceptor {

        @Override
        public Response intercept(Chain chain) throws IOException {
            //需要添加判断，登录接口是不需要token的
            Request request = chain.request();
            Request.Builder requestBuilder = chain.request().newBuilder();
            requestBuilder.addHeader("x-MikChat-versionCode", ApkUtil.getVersionCode(BaseApp.getAppContext()) + "");
            requestBuilder.addHeader("x-MikChat-versionName", ApkUtil.getZileUpdateVersionName());
            requestBuilder.addHeader("x-MikChat-pkg", "com.mobile.anchor.app");
            requestBuilder.addHeader("x-pkg", "com.mobile.anchor.app");
//            requestBuilder.addHeader("X-MikChat-os", android.os.Build.VERSION.RELEASE);
            requestBuilder.addHeader("x-MikChat-model", android.os.Build.BRAND + "," + android.os.Build.MODEL + "," + android.os.Build.VERSION.RELEASE);
//            requestBuilder.addHeader("X-MikChat-platform", "android");
//            requestBuilder.addHeader("X-MikChat-deviceId", UniqueIDUtils.getUniqueID(BaseApp.getAppContext()));
//            requestBuilder.addHeader("X-MikChat-deviceId", Utils.getAndroidId());
            requestBuilder.addHeader("x-MikChat-deviceId", UniqueIDUtils.getAndroidID(BaseApp.getAppContext()));

            boolean needToken = checkRequestNeedToken(request.url().toString());
            if (!needToken) {
                return chain.proceed(requestBuilder.build());
            }
            String accessToken = SharePreUtil.getAccessToken();
            if (accessToken != null)
//                requestBuilder.addHeader("Authorization", accessToken);
                requestBuilder.addHeader("Blade-Auth", accessToken);
            Response response = chain.proceed(requestBuilder.build());

            ResponseBody responseBody = response.body();
            BufferedSource source = responseBody.source();
            source.request(Long.MAX_VALUE); // Buffer the entire body.
            Buffer buffer = source.buffer();
            Charset charset = Charset.forName("UTF8");
            MediaType contentType = responseBody.contentType();
            if (contentType != null) {
                charset = contentType.charset(charset);
            }
            String bodyString = buffer.clone().readString(charset);
            if (isTokenExpired(bodyString) && needToken) {//根据和服务端的约定判断token过期
                //本身的解决办法是如果token过期，同步重新请求token，然后重新组装新的请求（即继续执行原先的请求数据操作）
                throw new TokenInvalideException();
            }
            return response;
        }
    }

    /**
     * 获取新的token或者cookie
     *
     * @return
     * @throws IOException
     */
    private String getNewToken() {
        //请求token接口
        retrofit2.Response<BaseResult> response = null;
        try {
            if (response.body().getCode() == 0) {
                //保存Cookie
                List<String> cookies = response.headers().values("Set-Cookie");//获取所有cookies
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < cookies.size(); i++) {
                    if (!(i == cookies.size())) {
                        sb.append(cookies.get(i) + ";");
                    } else {
                        sb.append(cookies.get(i));
                    }
                }
                SharePreUtil.setAccessToken(sb.toString());
                return sb.toString();
            } else {
                throw new TokenInvalideException();
            }
        } catch (Exception e) {
            throw new TokenInvalideException();
        }
    }

    /**
     * 判断token是否过期
     *
     * @param bodyString
     * @return
     */
    private boolean isTokenExpired(String bodyString) {
        try {
            JSONObject jsonObject = new JSONObject(bodyString);
            if (jsonObject.getInt("code") == BaseResult.TOKEN_INVALID) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 在header中添加cookie的方法
     *
     * @param chain
     * @param request
     * @param response
     * @param newToken
     * @return
     * @throws IOException
     */
    private Response getResponse(Interceptor.Chain chain, Request request, Response response, String newToken) throws IOException {
        Request.Builder builder = chain.request().newBuilder();
        String cookie = SharePreUtil.getAccessToken();
        builder.addHeader("Authorization", cookie);
        response.body().close();
        return chain.proceed(builder.build());
    }

    final TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }};

    public boolean checkRequestNeedToken(String url) {
        return true;
    }

//    public static class OkHttpDns implements Dns {
//        HttpDnsService httpdns;//httpdns 解析服务
//        private static OkHttpDns instance = null;
//
//        private OkHttpDns(Context context) {
//            this.httpdns = HttpDns.getService(context, "179004");
//            httpdns.setPreResolveAfterNetworkChanged(true);
//        }
//
//        public static OkHttpDns getInstance(Context context) {
//            if (instance == null) {
//                instance = new OkHttpDns(context);
//            }
//            return instance;
//        }
//
//        @Override
//        public List<InetAddress> lookup(String hostname) throws UnknownHostException {
//            //通过异步解析接口获取ip
//            String ip = httpdns.getIpByHostAsync(hostname);
//            if (ip != null) {
//                //如果ip不为null，直接使用该ip进行网络请求
//                List<InetAddress> inetAddresses = Arrays.asList(InetAddress.getAllByName(ip));
//                Logger.e("OkHttpDns", "inetAddresses:" + inetAddresses);
//                return inetAddresses;
//            }
//            //如果返回null，走系统DNS服务解析域名
//            return Dns.SYSTEM.lookup(hostname);
//
//        }
//    }
}
