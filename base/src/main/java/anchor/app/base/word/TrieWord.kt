package anchor.app.base.word

class TrieNode {
    val children: MutableMap<Char, TrieNode> = mutableMapOf()
    var isEndOfWord: Boolean = false
    var wordType: Int = -1 // 2: 屏蔽词, 1: 敏感词
}

class TrieWord {
    private val root = TrieNode()

    // 添加词汇到Trie树中
    fun insert(wordGroup: String, type: Int) {
        var node = root
        // 按照空格或其他分隔符拆分词组为单词
        val words = wordGroup.split("\\s+".toRegex())

        for (word in words) {
            for (char in word) {
                node = node.children.getOrPut(char) { TrieNode() }
            }
            // 处理每个单词之间的分隔符，如空格或标点符号
            if (word != words.last()) {
                node = node.children.getOrPut(' ') { TrieNode() } // 用空格连接词组中的每个词
            }
        }
        node.isEndOfWord = true
        node.wordType = type // 记录该词组是敏感词（1）还是屏蔽词（2）
    }


    // 检查并替换敏感词
    fun filterText(input: String): String {
        val result = StringBuilder(input)
        val inputLength = input.length

        var index = 0
        while (index < inputLength) {
            var node = root // Trie树的根节点
            val matchStart = index
            var matchEnd = index

            // 检查每个可能的敏感词或屏蔽词
            while (matchEnd < inputLength) {
                val char = input[matchEnd]
                if (!node.children.containsKey(char)) break

                node = node.children[char]!!
                if (node.isEndOfWord && node.wordType == 1) {
                    // 整词匹配，确保前后是单词边界
                    if (isWordBoundary(input, matchStart, matchEnd)) {
                        // 用 * 替换整个屏蔽词
                        result.replace(matchStart, matchEnd + 1, "*".repeat(matchEnd - matchStart + 1))
                        index = matchEnd // 跳过已替换的部分
                        break
                    }
                }
                matchEnd++
            }

            index++
        }

        return result.toString()
    }

    fun containsBlockedWord(input: String): Boolean {
        var index = 0
        val inputLength = input.length

        while (index < inputLength) {
            var node = root // Trie树的根节点
            val matchStart = index
            var matchEnd = index

            // 检查从当前索引开始的每个可能的敏感词
            while (matchEnd < inputLength) {
                val char = input[matchEnd]
                if (!node.children.containsKey(char)) break

                node = node.children[char]!!

                if (node.isEndOfWord && node.wordType == 2) {
                    // // 如果是屏蔽词（type 2），返回 true，阻止发送 整词匹配，确保前后是单词边界
                    if (isWordBoundary(input, matchStart, matchEnd)) {
                        return true // 找到屏蔽词，立即返回
                    }
                }
                matchEnd++
            }

            index++
        }

        return false
    }

    private fun isWordBoundary(input: String, start: Int, end: Int): Boolean {
        val before = if (start > 0) input[start - 1] else ' ' // 前一个字符
        val after = if (end + 1 < input.length) input[end + 1] else ' '// 后一个字符

        // 判断是否为中文字符
        val isChineseChar = { char: Char -> char in '\u4e00'..'\u9fff' }

        // 对于中文字符，直接返回 true（不需要边界判断）
        if (isChineseChar(input[start]) || isChineseChar(input[end])) {
            return true
        }

        // 对于其他字符，检查是否是字母或数字，以支持多语言边界识别
        return !before.isLetterOrDigit() && !after.isLetterOrDigit()
    }
}
