package anchor.app.base.web.res;

import com.jakewharton.disklrucache.DiskLruCache;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;

import anchor.app.base.utils.HttpsUtil;
import anchor.app.base.utils.Logger;

public class WebResourceDownloadTask implements Runnable {
    private static final String TAG = "WebResourceDownloadTask";

    private DiskLruCache mDiskCache;
    private String mCacheFolder;
    private ICacheEntry mCacheEntry;

    public WebResourceDownloadTask(DiskLruCache cache, String cacheFolder, ICacheEntry cacheEntry) {
        this.mDiskCache = cache;
        this.mCacheFolder = cacheFolder;
        this.mCacheEntry = cacheEntry;
    }

    @Override
    public void run() {
        try {
            boolean downloadOk = false;
            boolean cancelled = false;
            int retryTime = 0;
            // 添加正常标志位判断，当有线程出错时，无需 retry
            while ((!downloadOk) && (retryTime < 3)) {
                //得到DiskLruCache.Editor
                DiskLruCache.Editor editor = mDiskCache.edit(mCacheEntry.getCacheKey());
                if (editor != null) {
                    Logger.d(TAG, "[" + Thread.currentThread().getName() + "]" + " try download time:" + retryTime);
                    Logger.d(TAG, "[" + Thread.currentThread().getName() + "]" + " cache key:" + mCacheEntry.getCacheKey());
                    OutputStream outputStream = editor.newOutputStream(0);
                    int result = downloadUrlToStream(mCacheEntry.getDownloadUrl(), outputStream);
                    if (0 == result) {
                        //写入缓存
                        editor.commit();
                        downloadOk = true;
                    } else if (1 == result) {
                        //写入失败
                        editor.abort();
                        try {
                            mDiskCache.remove(mCacheEntry.getCacheKey());
                            mDiskCache.flush();
                            File file = new File(mCacheFolder, mCacheEntry.getCacheKey() + ".0");
                            Logger.w(TAG, "[" + Thread.currentThread().getName() + "]" + " write failed as network, " + file.getAbsolutePath() + " existed?" + file.exists());
                        } catch (IOException ioe) {
                            ioe.printStackTrace();
                        }
                        retryTime++;
                    } else if (2 == result) {
                        // 被取消
                        editor.abort();
                        try {
                            mDiskCache.remove(mCacheEntry.getCacheKey());
                            mDiskCache.flush();
                            File file = new File(mCacheFolder, mCacheEntry.getCacheKey() + ".0");
                            Logger.w(TAG, "[" + Thread.currentThread().getName() + "]" + " write failed as cancel, " + file.getAbsolutePath() + " existed?" + file.exists());
                        } catch (IOException ioe) {
                            ioe.printStackTrace();
                        }
                        break;
                    }
                }
            }

            if (!downloadOk) {
                Logger.w(TAG, "[" + Thread.currentThread().getName() + "]" + " download error");
                return;
            }
        } catch (IOException e) {
            Logger.w(TAG, "[" + Thread.currentThread().getName() + "]" + " download error");
            e.printStackTrace();
            return;
        }
    }

    /**
     * @param urlString
     * @param outputStream
     * @return 0 ok, 1 failed, 2 stop
     */
    private int downloadUrlToStream(String urlString, OutputStream outputStream) {
        Logger.d(TAG, "[" + Thread.currentThread().getName() + "]" + " downloadUrlToStream:" + urlString);
        HttpURLConnection urlConnection = null;
        BufferedOutputStream out = null;
        BufferedInputStream in = null;

        try {
            final URL url = new URL(urlString);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setConnectTimeout(5000);
            httpURLConnection.setReadTimeout(5000);
            boolean useHttps = url.toString().startsWith("https");
            if (useHttps) {
                HttpsURLConnection https = (HttpsURLConnection) httpURLConnection;
                HttpsUtil.trustAllHosts(https);
                https.getHostnameVerifier();
//                https.setHostnameVerifier(HttpsUtil.DO_NOT_VERIFY);
                in = new BufferedInputStream(https.getInputStream(), WebResourceHelper.IO_BUFFER_SIZE);
            } else {
                in = new BufferedInputStream(httpURLConnection.getInputStream(), WebResourceHelper.IO_BUFFER_SIZE);
            }
            out = new BufferedOutputStream(outputStream, WebResourceHelper.IO_BUFFER_SIZE);
            int b;
            boolean stopped = false;
            while ((b = in.read()) != -1) {
                out.write(b);
            }

            if (!stopped) {
                out.flush();
                return 0;
            } else {
                return 2;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (final IOException e) {
                e.printStackTrace();
            }
        }

        return 1;
    }
}
