package anchor.app.base.web;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import anchor.app.base.utils.Logger;
import anchor.app.base.utils.SharePreUtil;
import anchor.app.base.utils.ToastUtil;

public class BaseJsAPI {
    private static final String TAG = "JsAPI";

    protected Handler mUIHandler;
    protected OnJsCallback mJsCallback;
    protected OnJsConfrimCallback mOnJsConfrimCallback;
    protected OnProgressStatusChanged mOnProgressStatusChanged;
    protected Context mContext;
    private ProgressDialog loadingDialog;

    public BaseJsAPI(Context context) {
        mUIHandler = new Handler(Looper.getMainLooper());
        mContext = context;
    }

    public void setJsCallback(OnJsCallback jsCallback) {
        this.mJsCallback = jsCallback;
    }

    public void setOnJsConfrimCallback(OnJsConfrimCallback jsCallback) {
        this.mOnJsConfrimCallback = jsCallback;
    }

    public void setOnProgressStatusChanged(OnProgressStatusChanged jsCallback) {
        this.mOnProgressStatusChanged = jsCallback;
    }

    @JavascriptInterface
    public String showToast(Object msg) {
        Logger.d(TAG, "showToast msg:" + msg);
        if ((null != msg) && (msg instanceof String)) {
            if (!TextUtils.isEmpty((String) msg)) {
                ToastUtil.show((String) msg);
            }
        }
        return null;
    }

    @JavascriptInterface
    public String refresh(Object msg) {
        Logger.d(TAG, "refresh msg:" + msg);
        mUIHandler.post(new Runnable() {
            @Override
            public void run() {
                if (null != mJsCallback) {
                    mJsCallback.onRefresh();
                }
            }
        });
        return null;
    }

    @JavascriptInterface
    public String getToken(Object msg) {
        String token = SharePreUtil.getAccessToken();
        Logger.d(TAG, "getToken msg:" + msg + "  token = " + token);
        return token;
    }

    @JavascriptInterface
    public String pageLoadEnd(Object msg) {
        Logger.d(TAG, "pageLoadEnd msg:");
        mUIHandler.post(new Runnable() {
            @Override
            public void run() {
                if (null != mJsCallback) {
                    mJsCallback.onPageLoadEnd();
                }
            }
        });
        return null;
    }

    public interface OnProgressStatusChanged {
        void onProgressStatusChanged(boolean show, int progress);
    }

    public interface OnJsConfrimCallback {
        void onConfirmDeleted();
    }

    public interface OnJsCallback {
        void onRefresh();

        void onPageLoadEnd();
    }
}