package anchor.app.base.web;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import anchor.app.base.core.Constants;
import anchor.app.base.dialog.NoTitleTipDialog;
import anchor.app.base.dialog.SelectTipDialog;

/**
 * 通用的WebViewActivity
 */
public class AppWebViewActivity extends BaseWebViewActivity {

    private static final String TAG = "AppWebViewActivity";

    private int mLessonType;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        Intent it = getIntent();
        if (null != it) {
            mLessonType = it.getIntExtra("lessonType", -1);
        }
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initView() {
        super.initView();
        loadUrl(mOriginUrl);
    }

    /**
     * 子类如果有js bridge方法，复写该方法进行返回
     */
    protected BaseJsAPI getJsAPI() {
        return new BaseJsAPI(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mWebView.callHandler("stopAudio", new Object[]{});
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void startWorkWall() {
        NoTitleTipDialog.showTips(mContext);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void onPageLoadDuration(String url, float duration) {
    }
}