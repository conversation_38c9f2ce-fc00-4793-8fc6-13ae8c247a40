package anchor.app.base.web.res;

import android.content.Context;
import android.text.TextUtils;
import android.webkit.WebResourceResponse;

import com.jakewharton.disklrucache.DiskLruCache;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;

import anchor.app.base.BaseApp;
import anchor.app.base.utils.FileUtil;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.Md5Util;
import anchor.app.base.utils.ThreadPoolManager;

public class WebResourceHelper {
    private static final String TAG = "WebResourceHelper";

    public static final int IO_BUFFER_SIZE = 4096;

    private static final int WEB_CACHE_SIZE = 100 * 1024 * 1024; // 100MB

    private String mWebResourceRoot;
    private Context mAppContext;
    private DiskLruCache mDiskLruCache;
    private ConcurrentHashMap<String, String> mMap;

    static class LazyHolder {
        private static WebResourceHelper mInstance = new WebResourceHelper();
    }

    private WebResourceHelper() {
        mAppContext = BaseApp.getAppContext();
        mMap = new ConcurrentHashMap();
        mWebResourceRoot = FileUtil.getCacheRootDir(mAppContext) + "/web";
        File file = new File(mWebResourceRoot);
        if (!file.exists()) {
            file.mkdirs();
        }
        try {
            File cacheDir = new File(mWebResourceRoot);
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }
            //初始化DiskLruCache
            mDiskLruCache = DiskLruCache.open(cacheDir, 1, 1, WEB_CACHE_SIZE);
        } catch (IOException e) {
            Logger.w(TAG, "init disk cache failure");
            e.printStackTrace();
            return;
        }
    }

    public static WebResourceHelper getInstance() {
        return LazyHolder.mInstance;
    }

    public WebResourceResponse getReplacedWebResourceResponse(String url) {
        Logger.w(TAG, "getReplacedWebResourceResponse:" + url);
        String mimeType = null;
        if (url.endsWith("css")) {
            mimeType = "text/css";
        } else if (url.endsWith("jpg") ||
                url.endsWith("jpeg")) {
            mimeType = "image/jpeg";
        } else if (url.contains("png")) {
            mimeType = "image/png";
        } else if (url.contains("js")) {
            mimeType = "application/x-javascript";
        }
        if (TextUtils.isEmpty(mimeType)) {
            Logger.w(TAG, "url is not replaced res, skip.");
            return null;
        }

        File localResFile = new File(mWebResourceRoot, Md5Util.md5(url) + ".0");
        if ((null == localResFile) || !localResFile.exists() || !localResFile.isFile()) {
            Logger.w(TAG, "local web resource is null");
            updateWebResource(url, mDiskLruCache, mWebResourceRoot);
            return null;
        }

        InputStream is = null;
        try {
            is = new FileInputStream(localResFile);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.w(TAG, e.getMessage());
            return null;
        }
        WebResourceResponse response = new WebResourceResponse(mimeType, "utf-8", is);
        Logger.w(TAG, url + " hit!");
        return response;
    }

    private void updateWebResource(final String url, final DiskLruCache diskLruCache, final String cacheFolder) {
        ICacheEntry cacheEntry = new ICacheEntry() {
            @Override
            public String getDownloadUrl() {
                return url;
            }

            @Override
            public String getCacheKey() {
                return Md5Util.md5(url);
            }
        };
        WebResourceDownloadTask downloadTask = new WebResourceDownloadTask(diskLruCache, cacheFolder, cacheEntry);
        ThreadPoolManager.getInstance().execute(downloadTask);
    }
}
