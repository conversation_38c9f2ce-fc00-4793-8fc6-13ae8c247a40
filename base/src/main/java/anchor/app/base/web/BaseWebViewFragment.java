package anchor.app.base.web;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;

import com.uber.autodispose.AutoDispose;

import java.util.concurrent.TimeUnit;

import anchor.app.base.R;
import anchor.app.base.databinding.BaseFragmentWebviewBinding;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.ui.BaseNoModelFragment;
import anchor.app.base.utils.Logger;
import anchor.app.base.web.res.WebResourceHelper;
import io.reactivex.Flowable;
import io.reactivex.functions.Consumer;

public class BaseWebViewFragment extends BaseNoModelFragment<BaseFragmentWebviewBinding> {

    private static final String TAG = "BaseWebViewFragment";

    private String mUrl;
    protected BaseJsAPI mJSAPI;

    private BaseJsAPI.OnJsCallback mJsCallback = new BaseJsAPI.OnJsCallback() {
        @Override
        public void onRefresh() {
            loadUrl(mUrl);
        }

        @Override
        public void onPageLoadEnd() {
            onPageEnd();
        }
    };

    @Override
    protected int getLayoutId() {
        return R.layout.base_fragment_webview;
    }

    @Override
    public void onViewCreated(@NonNull View view, @androidx.annotation.Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView();
    }

    @SuppressLint("JavascriptInterface")
    protected void initWebview() {
        bindingView.webView.getSettings().setUseWideViewPort(true);
        bindingView.webView.getSettings().setLoadWithOverviewMode(true);
        bindingView.webView.getSettings().setAllowFileAccess(true);
        bindingView.webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
//        bindingView.webView.getSettings().setAppCacheEnabled(true);
        bindingView.webView.getSettings().setDomStorageEnabled(true);
        bindingView.webView.getSettings().setDatabaseEnabled(true);
        bindingView.webView.getSettings().setJavaScriptEnabled(true);
        bindingView.webView.setHorizontalScrollBarEnabled(false);
        bindingView.webView.setVerticalScrollBarEnabled(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            bindingView.webView.getSettings().setMediaPlaybackRequiresUserGesture(false);
        }
        // 初始化JSAPI
        if (null != getJsAPI()) {
            mJSAPI = getJsAPI();
            mJSAPI.setJsCallback(mJsCallback);
            bindingView.webView.addJavascriptObject(mJSAPI, null);
        }

        bindingView.webView.getSettings().setTextZoom(100);
        bindingView.webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if(Build.VERSION.SDK_INT < 26) {
                    view.loadUrl(url);
                    return true;
                } else {
                    return false;
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
            }

            /**
             * 这里进行无网络或错误处理，具体可以根据errorCode的值进行判断，做跟详细的处理。
             *
             * @param view
             */
            // 旧版本，会在新版本中也可能被调用，所以加上一个判断，防止重复显示
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Logger.w(TAG, "onReceivedError: errorCode:" + errorCode + " desc:" + errorCode);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    return;
                }
                loadErrorPage();
            }

            // 新版本，只会在Android6及以上调用
            @TargetApi(Build.VERSION_CODES.M)
            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Logger.w(TAG, "onReceivedError: errorCode:" + error.getErrorCode() + " desc:" + error.getDescription());
                loadErrorPage();
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
                Logger.w(TAG, "shouldInterceptRequest:" + url);
                WebResourceResponse resourceResponse = WebResourceHelper.getInstance().getReplacedWebResourceResponse(url);
                if (null != resourceResponse) {
                    return resourceResponse;
                }
                return super.shouldInterceptRequest(view, url);
            }
        });

        bindingView.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                if (newProgress < 85) {
                    bindingView.webprogress.setNormalProgress(newProgress);
                } else {
                    if (!TextUtils.isEmpty(mUrl) && mUrl.contains(BaseApiClient.baseH5Url)) {
                        //定时，如果5秒后h5仍然没有调用pageLoadEnd的话，展示页面
                        Flowable.timer(5, TimeUnit.SECONDS)
                                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                                .observeOn(RxSchedulers.INSTANCE.getUi())
                                .as(AutoDispose.autoDisposable(getScopeProvider()))
                                .subscribe(new Consumer<Long>() {
                                    @Override
                                    public void accept(Long aLong) throws Exception {
                                        onPageEnd();
                                    }
                                });
                    } else {
                        onPageEnd();
                    }
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != mJSAPI) {
            mJSAPI.setJsCallback(null);
        }
    }

    /**
     * 初始化视图
     */
    protected void initView() {
        initWebview();
    }

    protected void loadUrl(String url) {
        mUrl = url;
        bindingView.webView.loadUrl(mUrl);
    }

    /**
     * 子类如果有js bridge方法，复写该方法进行返回
     */
    protected BaseJsAPI getJsAPI() {
        return new BaseJsAPI(mContext);
    }

    private void loadErrorPage() {
        //js与android调用
        bindingView.webView.loadUrl("file:///android_asset/web/netError.html");
    }

    @SuppressLint({"AutoDispose", "CheckResult"})
    private void onPageEnd() {
        if (mContext == null) return;
        Flowable.just("")
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi())
                .subscribe(o -> bindingView.webprogress.setCurProgress(1000, () -> {
                    if (bindingView.webprogress.getVisibility() == View.VISIBLE) {
                        hideProgress();
                    }
                }), throwable -> {

                });
    }

    private void hideProgress() {
        if (mContext == null) return;
        AnimationSet animation = getDismissAnim(mContext);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                bindingView.webprogress.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }
        });
        bindingView.webprogress.startAnimation(animation);
    }

    private AnimationSet getDismissAnim(Context context) {
        AnimationSet dismiss = new AnimationSet(context, null);
        AlphaAnimation alpha = new AlphaAnimation(1.0f, 0.0f);
        alpha.setDuration(1000);
        dismiss.addAnimation(alpha);
        return dismiss;
    }
}
