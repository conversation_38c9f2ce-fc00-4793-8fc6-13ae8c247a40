/*
 * Copyright 2016 Freelander
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package anchor.app.base.web;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.webkit.WebBackForwardList;
import android.webkit.WebChromeClient;
import android.webkit.WebHistoryItem;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import com.uber.autodispose.AutoDispose;

import java.util.concurrent.TimeUnit;

import anchor.app.base.BaseApp;
import anchor.app.base.R;
import anchor.app.base.core.Constants;
import anchor.app.base.databinding.BaseActivityWebviewBinding;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.ui.BaseNoModelActivity;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.immersionbar.standard.ImmersionBar;
import anchor.app.base.web.bridge.DWebView;
import anchor.app.base.web.res.WebResourceHelper;
import io.reactivex.Flowable;
import io.reactivex.functions.Consumer;

/**
 * BaseWebViewActivity
 */
public abstract class BaseWebViewActivity extends BaseNoModelActivity<BaseActivityWebviewBinding> {

    private static final String TAG = "droid-baseWebView";

    private static final String ERROR_LOAD_URL = "file:///android_asset/web/netError.html";

    protected String mOriginUrl;
    protected BaseJsAPI mJSAPI;
    protected DWebView mWebView;
    //是否显示title 在loadUrl()加载方法中标识
    protected boolean isDisplayTitle = true;
    //是否覆盖在html上面，不占用窗口高度
    protected boolean mIsFixed;
    protected boolean mIsDarkMode;
    protected int mToolbarBackColor;
    //返回键图标ID
    protected int toolBarBackBtnImgId;

    protected boolean isNewPage;
    protected String mCurrentUrl; // 用于保存当前正在加载的url，可能是原始页面，也可能是错误页面。originUrl只是非错误页面

    // 计算页面加载时长
    private long mStartLoadTime; // 页面开始加载时间

    private BaseJsAPI.OnJsCallback mJsCallback = new BaseJsAPI.OnJsCallback() {
        @Override
        public void onRefresh() {
            // 刷新页面作为new，不然多次加载失败返回页面都是失败页
            isNewPage = true;
            loadUrl(mOriginUrl);
            mWebView.capturePicture();
        }

        @Override
        public void onPageLoadEnd() {
            onPageEnd();
        }
    };

    @Override
    protected int getLayoutId() {
        return R.layout.base_activity_webview;
    }

    @Override
    protected void initView() {
        //判断是否是固定标题栏 如果是覆盖的，设置背景色 继续判断是深色模式，还是浅色模式
        //                                       设置文字颜色和返回按钮
        //                   如果不是，那么隐藏掉固定的标题栏，根据深色模式还是浅色模式设置返回按钮
        //另外有个特殊需求就是调查问卷页面需要单独设置返回按钮为叉号
        //是否显示toolbar
        mIsFixed = getIntent().getBooleanExtra(Constants.KEY.ISFIXED, false);
        if (mIsFixed) {
            isDisplayTitle = false;
        }
        //toolbar背景颜色
        mToolbarBackColor = getIntent().getIntExtra(Constants.KEY.TOOLBARTEXTCOLOR, Color.parseColor("#ffffff"));
        //是否是深色模式
        mIsDarkMode = getIntent().getBooleanExtra(Constants.KEY.TOOLBARDARKMODE, false);
        //toolbar返回按钮资源图标
        toolBarBackBtnImgId = getIntent().getIntExtra(Constants.KEY.TOOLBARBACKBTNIMGID, 0);
        mOriginUrl = getIntent().getStringExtra(Constants.KEY.Url);
        bindingView.setViewModel(this);
        isNewPage = true;

//        if (mIsFixed) {
//            //显示透明工具栏，适用于webview页面沉浸
//            bindingView.layoutToolbar.setVisibility(View.GONE);
//            bindingView.layoutToolbarTransparent.setVisibility(View.VISIBLE);
//            if (mIsDarkMode) {
//                bindingView.ivBackPressed.setImageResource(R.mipmap.ic_back_white);
//            } else {
//                bindingView.ivBackPressed.setImageResource(R.mipmap.base_ic_back);
//            }
//        } else {
//            bindingView.layoutToolbar.setVisibility(View.VISIBLE);
//            bindingView.layoutToolbarTransparent.setVisibility(View.GONE);
//            bindingView.layoutToolbar.setBackgroundColor(mToolbarBackColor);
//            if (mIsDarkMode) {
//                bindingView.tvTitle.setTextColor(Color.parseColor("#ffffff"));
//                bindingView.ivBackPressedBlack.setImageResource(R.mipmap.ic_back_white);
//            } else {
//                bindingView.tvTitle.setTextColor(Color.parseColor("#475460"));
//                bindingView.ivBackPressedBlack.setImageResource(R.mipmap.base_ic_back);
//            }
//            if (toolBarBackBtnImgId != 0) {
//                bindingView.ivBackPressedBlack.setImageResource(toolBarBackBtnImgId);
//            }
//        }
        if (mIsDarkMode) {
            ImmersionBar.with(this)
                    .keyboardEnable(false)
                    .statusBarDarkFont(false, 0.2f)
                    .init();
        }
        initWebview();
    }

    @Override
    protected void initImmersionBar() {
        ImmersionBar.with(this)
                .keyboardEnable(false)
                .statusBarDarkFont(true, 0.2f)
                .init();
    }

    @SuppressLint("JavascriptInterface")
    private void initWebview() {
        mWebView = BaseApp.getAppContext().getNewGlobalWebView();
        // 如果要加载的url和webview当前加载的host不一致，重新创建一个
        boolean needNewWebView = false;
        try {
            String curWebViewUrl = getCurrentWebUrl();
            if (!TextUtils.isEmpty(mOriginUrl) && !TextUtils.isEmpty(curWebViewUrl)) {
                Uri uriOrigin = Uri.parse(mOriginUrl);
                Uri uriCurrrent = Uri.parse(curWebViewUrl);
                if (!TextUtils.equals(uriOrigin.getHost(), uriCurrrent.getHost())) {
                    needNewWebView = true;
                }
            }
        } catch (Exception e) {
            Logger.w(TAG, "uri parse error:" + e.getMessage());
        }

        Logger.d(TAG, "initWebview needNewWebView=" + needNewWebView);
        if (mWebView.getParent() != null) {
            ((ViewGroup) mWebView.getParent()).removeView(mWebView);
        }
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        bindingView.webContainer.addView(mWebView, layoutParams);
        mWebView.getSettings().setUseWideViewPort(true);
        mWebView.getSettings().setLoadWithOverviewMode(true);
        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
//        mWebView.getSettings().setAppCacheEnabled(true);
        mWebView.getSettings().setDomStorageEnabled(true);
        mWebView.getSettings().setDatabaseEnabled(true);
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.setHorizontalScrollBarEnabled(false);
        mWebView.setVerticalScrollBarEnabled(false);
        // 初始化JSAPI
        if (null != getJsAPI()) {
            mJSAPI = getJsAPI();
            mJSAPI.setJsCallback(mJsCallback);
            mJSAPI.setOnProgressStatusChanged(new BaseJsAPI.OnProgressStatusChanged() {
                @Override
                public void onProgressStatusChanged(boolean show, int progress) {
//                    if (show) {
//                        bindingView.progressBar.setVisibility(View.VISIBLE);
//                        bindingView.progressBar.setProgress(progress);
//                    } else {
//                        bindingView.progressBar.setVisibility(View.GONE);
//                    }
                }
            });
            mJSAPI.setOnJsConfrimCallback(() -> mWebView.callHandler("confirmDeleted", new Object[] {}));
            mWebView.addJavascriptObject(mJSAPI, null);
        }

        mWebView.getSettings().setTextZoom(100);
        mWebView.setWebViewClient(new WebViewClient() {

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (Build.VERSION.SDK_INT < 26) {
                    view.loadUrl(url);
                    return true;
                } else {
                    return false;
                }
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                Logger.w(TAG, "onPageStarted:" + url);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                long curTime = System.currentTimeMillis();
                long duration = (curTime - mStartLoadTime);
                if (duration > 0) {
                    float pageLoadDuration = duration * 1.0f / 1000; // 转化为秒
//                    try {
//                        BigDecimal b = new BigDecimal(pageLoadDuration);
//                        float durationFloat = b.setScale(3, BigDecimal.ROUND_HALF_UP).floatValue();
//                        onPageLoadDuration(url, durationFloat);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        Logger.e(TAG, "get page load duration error.");
//                    }
                    onPageLoadDuration(url, pageLoadDuration);
                }
                //页面reload，页面正常加载结束都会走这个方法
                mWebView.callHandler("stopAudio", new Object[] {});
                Logger.w(TAG, "onPageFinished:" + url + " isNewPage:" + isNewPage);
                // 强制DWebView清空CallInfo，保证调用js方法ok
                mWebView.clearCallInfo();
                if (isNewPage) {
                    isNewPage = false;
                    clearWebViewHistory();
                }
                WebBackForwardList history = mWebView.copyBackForwardList();
                if (null != history) {
                    Logger.w(TAG, "history webSize:" + history.getSize());
                    for (int i = 0; i < history.getSize(); i++) {
                        WebHistoryItem historyItem = history.getItemAtIndex(i);
                        Logger.w(TAG, i + ":" + historyItem.getUrl());
                    }
                }
                mCurrentUrl = url;
            }

            /**
             * 这里进行无网络或错误处理，具体可以根据errorCode的值进行判断，做跟详细的处理。
             *
             * @param view
             */
            // 旧版本，会在新版本中也可能被调用，所以加上一个判断，防止重复显示
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Logger.w(TAG, "onReceivedError(old version): errorCode:" + errorCode + " desc:" + errorCode);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    return;
                }
                loadErrorPage();
            }

            // 新版本，只会在Android6及以上调用
            @TargetApi(Build.VERSION_CODES.M)
            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Logger.w(TAG, "onReceivedError(new version): errorCode:" + error.getErrorCode() + " desc:" + error.getDescription());
                loadErrorPage();
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
                // Logger.w(TAG, "shouldInterceptRequest:" + url);
                WebResourceResponse resourceResponse = WebResourceHelper.getInstance().getReplacedWebResourceResponse(url);
                if (null != resourceResponse) {
                    return resourceResponse;
                }
                return super.shouldInterceptRequest(view, url);
            }
        });
        mWebView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                //标记是否显示title
                if (isDisplayTitle) {
                    //判断title是否有问题，网络不好时title为url
                    if (!view.getTitle().contains("duyaya.com") && !view.getTitle().contains("http") && !view.getTitle().contains("weixin")) {
//                        bindingView.tvTitle.setText(view.getTitle());
                    }
                }
                //如果进度条隐藏则让它显示
                if (View.GONE == bindingView.webprogress.getVisibility()) {
                    bindingView.webprogress.setVisibility(View.VISIBLE);
                }

                if (newProgress < 85) {
                    bindingView.webprogress.setNormalProgress(newProgress);
                } else {
                    //标记是否显示title
                    if (isDisplayTitle) {
                        //判断title是否有问题，网络不好时title为url
                        if (!view.getTitle().contains("duyaya.com") && !view.getTitle().contains("http") && !view.getTitle().contains("weixin")) {
//                            bindingView.tvTitle.setText(view.getTitle());
                        }
                    }
                    if (!TextUtils.isEmpty(mCurrentUrl) && mCurrentUrl.contains(BaseApiClient.baseH5Url)) {
                        //定时，如果5秒后h5仍然没有调用pageLoadEnd的话，展示页面
                        Flowable.timer(5, TimeUnit.SECONDS)
                                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                                .observeOn(RxSchedulers.INSTANCE.getUi())
                                .as(AutoDispose.autoDisposable(getScopeProvider()))
                                .subscribe(new Consumer<Long>() {
                                    @Override
                                    public void accept(Long aLong) throws Exception {
                                        onPageEnd();
                                    }
                                });
                    } else {
                        onPageEnd();
                    }
                }
            }
        });
    }

    @SuppressLint( {"AutoDispose", "CheckResult"})
    protected void onPageEnd() {
        Logger.w(TAG, "onPageEnd");
        if (mContext == null) {
            return;
        }
        Flowable.just("")
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi())
                .subscribe(o -> bindingView.webprogress.setCurProgress(0, () -> {
                    if (bindingView.webprogress.getVisibility() == View.VISIBLE) {
                        hideProgress();
                    }
                }), throwable -> {
                });
    }

    protected void loadUrl(String url) {
        mStartLoadTime = System.currentTimeMillis();
        this.mOriginUrl = url;
        this.mCurrentUrl = url;
        Logger.w(TAG, "loadUrl:" + url);
        String curUrl = getCurrentWebUrl();
        Logger.w(TAG, "current webview url:" + curUrl);
        if (!TextUtils.equals(curUrl, url)) {
            Logger.w(TAG, "load");
            mWebView.loadUrl(url);
        } else {
            Logger.w(TAG, "reload");
            mWebView.reload();
        }
    }

    /**
     * 子类如果有js bridge方法，复写该方法进行返回
     */
    protected BaseJsAPI getJsAPI() {
        return new BaseJsAPI(mContext);
    }

    private void loadErrorPage() {
        //js与android调用
        mWebView.loadUrl(ERROR_LOAD_URL);
        this.mCurrentUrl = ERROR_LOAD_URL;
    }

    private void hideProgress() {
        if (mContext == null) {
            return;
        }
        AnimationSet animation = getDismissAnim(mContext);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                bindingView.webprogress.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }
        });
        bindingView.webprogress.startAnimation(animation);
    }

    private AnimationSet getDismissAnim(Context context) {
        AnimationSet dismiss = new AnimationSet(context, null);
        AlphaAnimation alpha = new AlphaAnimation(1.0f, 0.0f);
        alpha.setDuration(1000);
        dismiss.addAnimation(alpha);
        return dismiss;
    }

    private String getCurrentWebUrl() {
        if (null == mWebView) {
            return null;
        }

        WebBackForwardList history = mWebView.copyBackForwardList();
        if ((null != history) && (history.getSize() > 0)) {
            WebHistoryItem historyItem = history.getItemAtIndex(history.getSize() - 1);
            if (null != historyItem) {
                return historyItem.getUrl();
            }
        }
        return null;
    }

    private void clearWebViewHistory() {
        Logger.w(TAG, "clearWebViewHistory");
        if (null != mWebView) {
            mWebView.clearHistory();
        }
    }

    @Override
    public void onBackPressed() {
        //校验是否是二级页面，二级页面则返回上一级
        if (!mCurrentUrl.startsWith("file") && mWebView.canGoBack()) {
            mWebView.goBack();
        } else {
            clearWebViewHistory();
            super.onBackPressed();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        /**
         * 对于从一个webview activity跳转到另一个webview activity的情况，
         * 可能webview会被移走，回来的时候需要再check下进行确认
         */
        mWebView = BaseApp.getAppContext().getGlobalWebView();
        if (0 == bindingView.webContainer.getChildCount()) {
            isNewPage = true;
            initWebview();
            loadUrl(mOriginUrl);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 为了避免内存泄漏，需要检查当前容器的webview是否还存在，如果存在则移除
        if (bindingView.webContainer.getChildCount() > 0) {
            Logger.w(TAG, "web view exists, remove it");
            bindingView.webContainer.removeAllViews();
            Logger.w(TAG, "now webview container has child:" + bindingView.webContainer.getChildCount());
        }
    }


    //    @Override
//    protected void initImmersionBar() {
//        ImmersionBar.with(this)
//                .statusBarDarkFont(true, 0.2f).init();
//    }
    public void startWorkWall() {
    }

    protected void onPageLoadDuration(String url, float duration) {
    }
}