package anchor.app.base.rx;

/**
 * Created by jing<PERSON> on 2016/12/2.
 */

public class RxCodeConstants {
    //关闭首页消息
    public static final int JUMP_TYPE = 0;
    //微信登录通知登录页面
    public static final int JUMP_TYPE_TO_ONE = 1;
    public static final int JUMP_TYPE_TO_TWO = 2;
    public static final int JUMP_TYPE_TO_THREE = 3;
    //口模页面设置是否可滑动
    public static final int JUMP_TYPE_TO_WORD_MOULD = 5;

    //课程showtime关卡页面切换
    public static final int JUMP_TYPE_TO_FOUR = 4;
    //首页设置tab选择
    public static final int JUMP_TYPE_TO_FIVE = 5;
    //CourseFragment刷新星星
    public static final int JUMP_TYPE_TO_SIX = 6;
    //CourseFragment刷新列表
    public static final int JUMP_TYPE_TO_SEVEN = 7;
    // 重复朗读结束
    public static final int JUMP_TYPE_TO_REPEAT_WORD = 10;
    //绑定微信
    public static final int JUMP_TYPE_TO_BIND_WECHAT = 11;
    //首页刷新未读消息数量
    public static final int JUMP_TYPE_TO_REFRESH_UNREAD_COUNT = 12;
    //修改日程选择星期弹窗
    public static final int JUMP_TYPE_TO_SELECT_WEEK = 13;
    //每次点击小杜页面刷新设备状态信息
    public static final int JUMP_TYPE_TO_REFRESH_MIKCHAT = 14;
    //设备防沉迷时间段修改，通知防沉迷页面刷新
    public static final int JUMP_TYPE_TO_REFRESH_ADDTICTION = 15;
    //通知发现页面检查优惠券
    public static final int JUMP_TYPE_TO_GET_COUPON = 16;
    //登录完成刷新原先页面
    public static final int JUMP_TYPE_REFRESH_PAGE = 17;
    //token过期通知弹窗
    public static final int JUMP_TYPE_TOKEN_INVILADATED = 18;
    //
    public static final int JUMP_TYPE_START_VIDEO = 19;

    // 跳转到会话页面
    public static final int JUMP_TYPE_TO_CONVERSATION = 20;

    //收到礼物
    public static final int JUMP_TYPE_GET_GIFT = 21;
    // 送出礼物
    public static final int JUMP_TYPE_TO_SEND_GIFT = 22;
    // 刷新条目的关注状态
    public static final int JUMP_TYPE_TO_MOMENTFRAGMENT_ITEM = 23;

    // 挂断电话
    public static final int JUMP_TYPE_VIDEO_CANCEL = 24;

    // 刷新首页列表
    public static final int JUMP_TYPE_REFRESH_PAGE02 = 25;

    // 视频房间里面的聊天消息
    public static final int JUMP_TYPE_VIDEO_CHAT_MSG = 26;

    // 视频房间里面的聊天消息--發送
    public static final int JUMP_TYPE_VIDEO_CHAT_MSG_SEND = 27;


    public static final int JUMP_TYPE_WALLETDIALOG = 28;

    public static final int JUMP_TYPE_RECHARGE_SUCCESS = 29;

    // 刷新聊天头像
    public static final int JUMP_TYPE_REFRESH_PAGE03 = 30;

    public static final int JUMP_TYPE_REFRESH_NEW_PEOPLE_DIALOG = 31;


    public static final int JUMP_TYPE_NOTIFICATION = 32;

    public static final int JUMP_TYPE_REFRESH_MOMENT = 33;

    public static final int JUMP_TYPE_REFRESH_BADGE = 34;

    public static final int JUMP_TYPE_REFRESH_ALBUMIMAGE = 35;

    public static final int JUMP_TYPE_REFRESH_CONVERSATIONACTIVITY = 36;

    public static final int JUMP_TYPE_REFRESH_UPLOADVIDEURL = 37;

    public static final int JUMP_TYPE_WARN_MSG = 38;

    public static final int JUMP_TYPE_APP_FOREGROUND = 39;

    public static final int JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT = 40;

    public static final int JUMP_TYPE_REFRESH_WORKBENCH = 41;

    public static final int JUMP_TYPE0_CLEAN_MAIN_INTENT = 42;

    public static final int JUMP_TYPE_AUTO_PLAY_VIDEO_INDEX = 43;

    public static final int JUMP_TYPE_MAIN_TAB_INDEX = 44;

    public static final int JUMP_TYPE_TO_REFRESH_USERINFO_CACHE = 45;

    public static final int JUMP_TYPE_TO_CHATTIP = 46;

    public static final int JUMP_TYPE_TO_SEND_ASK02 = 47;


    public static final int JUMP_TYPE_REFRESH_WITHDRAW = 48;

    public static final int JUMP_TYPE_SEND_ASK_GIFT = 49;
    public static final int JUMP_TYPE_SEND_VIDEO_MSG = 50;
    public static final int JUMP_TYPE_VIDEO_REJECT = 51;
    public static final int JUMP_TYPE_BLOCK = 52;
    public static final int JUMP_TYPE_REFRESH_RONGYUN_CACHE = 53;

    public static final int JUMP_TYPE_UPLOAD_VIRTUAL_VIDEO = 54;


    public static final int JUMP_TYPE_AUTOMATIC_ONLINE = 55;


}
