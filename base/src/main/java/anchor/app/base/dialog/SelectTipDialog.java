package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;

import anchor.app.base.R;


/**
 * app选择（确定，取消）统一弹窗
 */
public class SelectTipDialog extends Dialog {

    public static final String TAG = SelectTipDialog.class.getSimpleName();
    private View mRootView;
    ITipsClickListener tipsClickListener;
    private TextView mTextviewConcel;
    private TextView mTextViewConfirm;
    private TextView mTextViewTitle;
    private TextView mTextViewContent;

    public SelectTipDialog(@NonNull Context context) {
        super(context);
        init(context);
    }

    public SelectTipDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    public void setTipsClickListener(ITipsClickListener tipsClickListener) {
        this.tipsClickListener = tipsClickListener;
    }

    private void init(Context context) {
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_select_tips, null);
        }

        mTextViewTitle = mRootView.findViewById(R.id.textViewTitle);
        mTextViewContent = mRootView.findViewById(R.id.textViewContent);
        mTextviewConcel = mRootView.findViewById(R.id.textViewConcle);
        mTextViewConfirm = mRootView.findViewById(R.id.textViewConfirm);

        mTextviewConcel.setOnClickListener(v -> {
            if (tipsClickListener == null) {
                dismiss();
            } else {
                tipsClickListener.concelClick();
                dismiss();
            }
        });
        mTextViewConfirm.setOnClickListener(v -> {
            if (tipsClickListener == null) {
                dismiss();
            } else {
                dismiss();
                tipsClickListener.confirmClick();
            }

        });
        setContentView(mRootView);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

    public void setTitle(String text) {
        if (TextUtils.isEmpty(text)) mTextViewTitle.setVisibility(View.GONE);
        mTextViewTitle.setText(text);
    }

    public void setConfirmText(String text) {
        mTextViewConfirm.setText(text);
    }

    public void setConfirmTextColor(int color) {
        mTextViewConfirm.setTextColor(color);
    }

    public void setConcelText(String text) {
        mTextviewConcel.setText(text);
    }

    public void setConcelTextColor(int color) {
        mTextviewConcel.setTextColor(color);
    }

    public void setContent(String text) {
        if (TextUtils.isEmpty(text)) mTextViewContent.setVisibility(View.GONE);
        mTextViewContent.setText(text);
    }

    /**
     * @param context
     * @param title    标题
     * @param content  内容
     * @param listener 点击事件
     */
    public static void showTips(Context context, String title, String content, ITipsClickListener listener) {
        SelectTipDialog tipDialog = new SelectTipDialog(context);
        tipDialog.setTitle(title);
        tipDialog.setContent(content);
        tipDialog.setTipsClickListener(listener);
        tipDialog.show();
    }

    /**
     * @param context
     * @param title    标题
     * @param content  内容
     * @param listener 点击事件
     */
    public static SelectTipDialog showTips(Context context, String confrimText, String concelText,
                                           String title, String content, ITipsClickListener listener) {
        SelectTipDialog tipDialog = new SelectTipDialog(context);
        tipDialog.setTitle(title);
        tipDialog.setContent(content);
        tipDialog.setConcelText(concelText);
        tipDialog.setConfirmText(confrimText);
        tipDialog.setTipsClickListener(listener);
        tipDialog.show();
        return tipDialog;
    }

    /**
     * ShowTime 关卡的特殊需求，重试按钮和取消按钮颜色需要调换过来
     *
     * @param context
     * @param title    标题
     * @param content  内容
     * @param listener 点击事件
     */
    public static void showNetWorkTips(Context context, String confrimText, String concelText,
                                       String title, String content, ITipsClickListener listener) {
        SelectTipDialog tipDialog = new SelectTipDialog(context);
        tipDialog.setTitle(title);
        tipDialog.setContent(content);
        tipDialog.setConcelText(concelText);
        tipDialog.setConcelTextColor(Color.parseColor("#FF6619"));
        tipDialog.setConfirmText(confrimText);
        tipDialog.setConfirmTextColor(Color.parseColor("#545C65"));
        tipDialog.setTipsClickListener(listener);
        tipDialog.show();
    }

    public interface ITipsClickListener {
        void confirmClick();

        void concelClick();
    }

    public abstract static class ISimpleTipsClickListener implements ITipsClickListener {
        public abstract void confirmClick();

        @Override
        public void concelClick() {

        }
    }


    @Override
    public void onBackPressed() {
//        super.onBackPressed();
    }

}
