package anchor.app.base.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.textfield.TextInputLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import anchor.app.base.BuildConfig;
import anchor.app.base.R;
import anchor.app.base.core.Constants;
import anchor.app.base.utils.ActivityUtil;
import anchor.app.base.utils.SharePreUtil;
import anchor.app.base.utils.ToastUtil;
import io.reactivex.Flowable;
import io.reactivex.functions.Consumer;

/**
 * BaseUrl选择dialog
 */

public class ServerSelectDialog implements View.OnClickListener {


    private AlertDialog dialog;
    private AlertDialog.Builder builder;
    private LinearLayout layout;
    private TextInputLayout apiInputLayout;
    private TextInputLayout h5InputLayout;
    private RecyclerView recyclerView;
    private List<String> serverList;

    public ServerSelectDialog(final Activity context) {
        builder = new AlertDialog.Builder(context);
        layout = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.base_dialog_base_server, null);
        apiInputLayout = layout.findViewById(R.id.apiInputLayout);
        h5InputLayout = layout.findViewById(R.id.h5InputLayout);
        setServerUrl();
        recyclerView = layout.findViewById(R.id.rv);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        serverList = new ArrayList<>();
        serverList.addAll(defaultServerUrlList());
        ServerDialogAdapter adapter = new ServerDialogAdapter(serverList, (view, position, url) -> {
            if (url.contains("开发环境")) {
                apiInputLayout.getEditText().setText(Constants.HTTP_URL.DEV_BASE_URL);
                h5InputLayout.getEditText().setText(Constants.HTTP_URL.DEV_BASE_H5_URL);
            } else if (url.contains("测试环境")) {
                apiInputLayout.getEditText().setText(Constants.HTTP_URL.TEST_BASE_URL);
                h5InputLayout.getEditText().setText(Constants.HTTP_URL.TEST_BASE_H5_URL);
            } else if (url.contains("正式环境")) {
                apiInputLayout.getEditText().setText(Constants.HTTP_URL.API_BASE_URL);
                h5InputLayout.getEditText().setText(Constants.HTTP_URL.API_BASE_H5_URL);
            }
            ToastUtil.show("选择了" + url);
        });
        recyclerView.setAdapter(adapter);
        Button btnNext = layout.findViewById(R.id.btn);
        btnNext.setOnClickListener(this);

    }

    /**
     * 确定后保存Url，重启App
     */
    @Override
    public void onClick(View view) {
        if (view.getId() == view.getId()) {
            String apiUrl = apiInputLayout.getEditText().getText().toString().trim();
            String h5Url = h5InputLayout.getEditText().getText().toString().trim();
            SharePreUtil.setBaseApiUrl(apiUrl);
            SharePreUtil.setBaseH5Url(h5Url);
            Flowable.timer(1, TimeUnit.SECONDS).subscribe(new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    dismiss();
                    ActivityUtil.getInstance().finishAllActivity();
                    android.os.Process.killProcess(android.os.Process.myPid());
                }
            });
        }
    }

    /**
     * use this method to show dialog, do not use getBuilder to show
     */
    public void show() {
        if (dialog == null) {
            dialog = builder.create();
            dialog.setCanceledOnTouchOutside(false);//点击外部是否消失
        }
        dialog.show();
        dialog.getWindow().setContentView(layout);
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
    }

    public void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    protected void setServerUrl() {
        String baseApiUrl, baseH5Url;
        if ("debug".equals(BuildConfig.BUILD_TYPE)) {
            //从SP中取值，如果没有再设置默认值
            String url = SharePreUtil.getBaseApiUrl();
            String h5url = SharePreUtil.getBaseH5Url();
            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(h5url)) {
                baseApiUrl = url;
                baseH5Url = h5url;
            } else {
                baseApiUrl = Constants.HTTP_URL.TEST_BASE_URL;
                baseH5Url = Constants.HTTP_URL.TEST_BASE_H5_URL;
            }

        } else if ("develop".equals(BuildConfig.BUILD_TYPE)) {
            //从SP中取值，如果没有再设置默认值
            String url = SharePreUtil.getBaseApiUrl();
            String h5url = SharePreUtil.getBaseH5Url();
            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(h5url)) {
                baseApiUrl = url;
                baseH5Url = h5url;
            } else {
                baseApiUrl = Constants.HTTP_URL.DEV_BASE_URL;
                baseH5Url = Constants.HTTP_URL.DEV_BASE_H5_URL;
            }
        } else {
            baseApiUrl = Constants.HTTP_URL.API_BASE_URL;
            baseH5Url = Constants.HTTP_URL.API_BASE_H5_URL;
        }
        apiInputLayout.getEditText().setText(baseApiUrl);
        h5InputLayout.getEditText().setText(baseH5Url);
    }

    protected List<String> defaultServerUrlList() {
        List<String> serverList = new ArrayList<>();
        serverList.add("开发环境 develop.duyaya.com");
        serverList.add("测试环境 test.duyaya.com");
        serverList.add("正式环境 api.duyaya.com");
        return serverList;
    }

    static class ServerDialogAdapter extends RecyclerView.Adapter<ServerDialogAdapter.ViewHolder> {

        /**
         * 数据源
         */
        private List<String> datas;

        /**
         * 回调
         */
        private OnItemClickListener onItemClickListener;

        /**
         * @param datas    数据源
         * @param listener item 点击回调
         */
        public ServerDialogAdapter(List<String> datas, OnItemClickListener listener) {
            this.datas = datas;
            this.onItemClickListener = listener;
            notifyDataSetChanged();
        }


        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_item_server_url, parent, false);
            ViewHolder viewHolder = new ViewHolder(view);
            return viewHolder;
        }

        @Override
        public void onBindViewHolder(final ViewHolder holder, final int position) {
            holder.tv.setText(datas.get(position));
            holder.tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(holder.itemView, holder.getLayoutPosition(), datas.get(position));
                    }
                }
            });

        }


        @Override
        public int getItemCount() {
            return datas == null ? 0 : datas.size();
        }


        public static class ViewHolder extends RecyclerView.ViewHolder {

            TextView tv;

            public ViewHolder(View itemView) {
                super(itemView);
                tv = itemView.findViewById(R.id.tv);
            }
        }


        public interface OnItemClickListener {
            void onItemClick(View view, int position, String url);
        }

    }
}
