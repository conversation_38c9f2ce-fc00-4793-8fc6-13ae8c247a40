package anchor.app.base.dialog

import anchor.app.base.R
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.InputFilter
import android.text.InputType
import android.view.Gravity
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import com.bigkoo.convenientbanner.utils.ScreenUtil


/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/4/26 17:20
 * @description : 通用输入框弹窗
 * 支持自定义标题、提示文本、输入类型、最大长度等
 */
class CommonInputDialog(
    context: Context, private val config: DialogConfig, private val callback: (String) -> Unit
) : Dialog(context, R.style.CommonDialog) {

    data class DialogConfig(
        val title: String, // 弹窗标题
        val hint: String = "", // 输入框提示文本
        val content: String = "", // 输入框默认内容
        val maxLength: Int = 20, // 最大输入长度
        val inputType: Int = InputType.TYPE_CLASS_TEXT, // 输入类型
        val confirmText: String? = null, // 确认按钮文本
        val cancelText: String? = null // 取消按钮文本
    )

    private lateinit var etInput: EditText
    private lateinit var tvTitle: TextView
    private lateinit var tvCancel: TextView
    private lateinit var tvConfirm: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_common_input)

        initWindow()
        initView()
        initListener()
    }

    private fun initWindow() {
        window?.apply {
            val params = attributes
            params.width = (ScreenUtil.getScreenWidth(this.context) * 0.8).toInt()
            params.height = WindowManager.LayoutParams.WRAP_CONTENT
            params.gravity = Gravity.CENTER
            attributes = params
        }
    }

    private fun initView() {
        tvTitle = findViewById(R.id.tv_title)
        etInput = findViewById(R.id.et_input)
        tvCancel = findViewById(R.id.tv_cancel)
        tvConfirm = findViewById(R.id.tv_confirm)

        // 设置配置项
        tvTitle.text = config.title
        etInput.hint = config.hint
        etInput.setText(config.content)
        etInput.inputType = config.inputType
        etInput.filters = arrayOf(InputFilter.LengthFilter(config.maxLength))
        tvCancel.text =
            config.cancelText?.takeIf { it.isNotEmpty() } ?: context.getString(R.string.Cancel)
        tvConfirm.text = config.confirmText?.let { it } ?: context.getString(R.string.Confirm)

        // 默认选中全部文字
        etInput.setSelection(etInput.text.length)
    }

    private fun initListener() {
        tvCancel.setOnClickListener {
            dismiss()
        }

        tvConfirm.setOnClickListener {
            val input = etInput.text.toString().trim()
            callback.invoke(input)
            dismiss()
        }
    }
}