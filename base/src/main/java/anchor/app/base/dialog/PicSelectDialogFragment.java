package anchor.app.base.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.fragment.app.DialogFragment;

import anchor.app.base.R;
import io.reactivex.annotations.Nullable;

/**
 * 头像选择提示页面
 */
public class PicSelectDialogFragment extends DialogFragment {

    public static final String TAG = PicSelectDialogFragment.class.getSimpleName();
    private View mRootView;
    private View.OnClickListener takePhotoClickListener;
    private View.OnClickListener selctAlbumClickListener;


    public PicSelectDialogFragment() {
    }

    public PicSelectDialogFragment(View.OnClickListener selctAlbumClickListener, View.OnClickListener takePhotoClickListener) {
        this.takePhotoClickListener = takePhotoClickListener;
        this.selctAlbumClickListener = selctAlbumClickListener;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (null == mRootView) {
            mRootView = inflater.inflate(R.layout.base_layout_select_picture, null);
        }
        mRootView.findViewById(R.id.textViewSelectAlbum).setOnClickListener(selctAlbumClickListener);

        mRootView.findViewById(R.id.textViewTakePhoto).setOnClickListener(takePhotoClickListener);

        View tvCancel = mRootView.findViewById(R.id.tvCancel);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        TextView wmTextViewContent01 = mRootView.findViewById(R.id.textViewSelectAlbum);
        TextView mTextViewContent02 = mRootView.findViewById(R.id.textViewTakePhoto);

        wmTextViewContent01.setText(getContext().getString(R.string.a0016));
        mTextViewContent02.setText(getContext().getString(R.string.a0017));
        getDialog().setCanceledOnTouchOutside(true);
        final Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.BOTTOM;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
        return mRootView;
    }


}
