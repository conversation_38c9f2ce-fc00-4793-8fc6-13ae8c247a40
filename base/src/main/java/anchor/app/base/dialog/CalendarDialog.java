package anchor.app.base.dialog;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.ToastUtils;
import com.haibin.calendarview.Calendar;
import com.haibin.calendarview.CalendarView;

import java.util.Locale;

import anchor.app.base.R;
import anchor.app.base.utils.Logger;

/**
 * 创建日期：2021/8/10 0010 17:54
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @since JDK 1.8.0
 * 类说明： 日历筛选弹框$
 */
public class CalendarDialog extends AlertDialog {


    Context mContext;
    CalendarView mCalendarView;
    TextView tvTime;

    private OnCalendarListener listener;
    private int mYear; //年
    private int mMonth; //月
    private String mLeftDate;//开始日期
    private String mRightDate;//结束日期

    /**
     * 选择日历回调事件
     *
     * @param listener
     */
    public void setOnCalendarListener(OnCalendarListener listener) {
        this.listener = listener;
    }

    /**
     * 初始化CalendarScreenDialog
     *
     * @param context
     */
    public CalendarDialog(Context context) {
        super(context, R.style.dialog_custom);
        mContext = context;
    }

    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //指定为自己的布局
        setContentView(R.layout.dialog_calendar);
        mCalendarView = findViewById(R.id.calendarView);
        tvTime = findViewById(R.id.tv_time);//time
        ImageView ivLaterYear = findViewById(R.id.iv_later_year);//上年
        ImageView ivLastMonth = findViewById(R.id.iv_last_month);//上月
        ImageView ivLaterMonth = findViewById(R.id.iv_later_month);//后月
        ImageView ivLastYear = findViewById(R.id.iv_last_year);//后年

        initCalendar();

        mYear = mCalendarView.getCurYear();
        mMonth = mCalendarView.getCurMonth();

        if (Locale.getDefault().getCountry().equals(Locale.CHINESE.getCountry())) {
            tvTime.setText(mYear + getContext().getResources().getString(R.string.year) + mMonth + getContext().getResources().getString(R.string.month));
        } else {
            tvTime.setText(mMonth + "/" + mYear);
        }

        //点击事件 用了Java8 lambda表达式取消
        ivLaterYear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //上年
                if (mCalendarView.isYearSelectLayoutVisible()) {
                    mYear = mYear - 1;
                    mCalendarView.scrollToYear(mYear);
                } else {
                    mCalendarView.showYearSelectLayout(mYear);
                }
            }
        });
        ivLastMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //上月
                mCalendarView.scrollToPre();
            }
        });
        ivLaterMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //后月
                mCalendarView.scrollToNext();
            }
        });
        ivLastYear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //后年
                if (mCalendarView.isYearSelectLayoutVisible()) {
                    mYear = mYear + 1;
                    mCalendarView.scrollToYear(mYear);
                } else {
                    mCalendarView.showYearSelectLayout(mYear);
                }
            }
        });

        Window window = this.getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));// 有白色背景，加这句代码
        //保证EditText能弹出键盘
        this.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        this.setCanceledOnTouchOutside(true);
        this.setCancelable(true);

        this.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
                    dismiss();
                    return true;
                }
                return false;
            }
        });
    }

    private static final String[] WEEK = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    private void initCalendar() {
        mCalendarView.setOnCalendarRangeSelectListener(new CalendarView.OnCalendarRangeSelectListener() {
            @Override
            public void onCalendarSelectOutOfRange(Calendar calendar) {
                // TODO: 2018/9/13 超出范围提示
            }

            @Override
            public void onSelectOutOfRange(Calendar calendar, boolean isOutOfMinRange) {
                ToastUtils.showShort(calendar.toString() + (isOutOfMinRange ? "小于最小选择范围" : "超过最大选择范围"));
            }

            @Override
            public void onCalendarRangeSelect(Calendar calendar, boolean isEnd) {
                String mMonth;
                if (calendar.getMonth() < 10) {
                    mMonth = "0" + calendar.getMonth();
                } else {
                    mMonth = "" + calendar.getMonth();
                }
                String mDay;
                if (calendar.getDay() < 10) {
                    mDay = "0" + calendar.getDay();
                } else {
                    mDay = "" + calendar.getDay();
                }
                if (!isEnd) {//开始日期
                    if (mLeftDate != null) {
                        mRightDate = mYear + "-" + mMonth + "-" + mDay;
                        if (listener != null) {
                            listener.OnCalendarListener(mLeftDate, mRightDate);
                        }
                        dismiss();
                    }
                    mLeftDate = mYear + "-" + mMonth + "-" + mDay;
                } else {//结束日期
                    mRightDate = mYear + "-" + mMonth + "-" + mDay;
//                    WEEK[calendar.getWeek()] //星期几
                    if (listener != null) {
                        listener.OnCalendarListener(mLeftDate, mRightDate);
                    }
                    dismiss();
                }
            }
        });
        mCalendarView.setOnYearChangeListener(new CalendarView.OnYearChangeListener() {
            @Override
            public void onYearChange(int year) {
                //切换后返回年
                mYear = year;
                tvTime.setText(mYear + getContext().getResources().getString(R.string.year) + mMonth + getContext().getResources().getString(R.string.month));
            }
        });
        mCalendarView.setOnMonthChangeListener(new CalendarView.OnMonthChangeListener() {
            @Override
            public void onMonthChange(int year, int month) {
                //切换后返回年月
                mYear = year;
                mMonth = month;
                tvTime.setText(mYear + getContext().getResources().getString(R.string.year) + mMonth + getContext().getResources().getString(R.string.month));
            }
        });
        //设置日期拦截事件，当前有效
        mCalendarView.setOnCalendarInterceptListener(new CalendarView.OnCalendarInterceptListener() {
            @Override
            public boolean onCalendarIntercept(Calendar calendar) {
                return false;
            }

            @Override
            public void onCalendarInterceptClick(Calendar calendar, boolean isClick) {
                ToastUtils.showShort(calendar.toString() + (isClick ? "拦截不可点击" : "拦截设定为无效日期"));
            }
        });


        Log.e("getSelectRangeMin:", String.format("min range = %s", mCalendarView.getMinSelectRange()));
        Log.e("getSelectRangeMax:", String.format("min range = %s", mCalendarView.getMaxSelectRange()));
    }


    @Override
    public void show() {
        super.show();
    }

    public void close(View view) {
        dismiss();
    }

    public interface OnCalendarListener {
        void OnCalendarListener(String left_date, String right_date);
    }
}
