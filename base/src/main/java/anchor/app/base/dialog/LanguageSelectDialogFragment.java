package anchor.app.base.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.luck.picture.lib.decoration.GridSpacingItemDecoration;

import java.util.List;

import io.reactivex.annotations.Nullable;
import anchor.app.base.R;
import anchor.app.base.bean.LanguagesBean;

/**
 * 头像选择提示页面
 */
public class LanguageSelectDialogFragment extends DialogFragment {

    public static final String TAG = LanguageSelectDialogFragment.class.getSimpleName();
    private View mRootView;
    private View.OnClickListener okClickListener;
    private me.drakeet.multitype.MultiTypeAdapter mAdpter;

    private List<LanguagesBean> list;

    public LanguageSelectDialogFragment(List<LanguagesBean> list, View.OnClickListener okClickListener) {
        this.okClickListener = okClickListener;
        this.list = list;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        if (null == mRootView) {
            mRootView = inflater.inflate(R.layout.language_dialog_select, null);
        }
        RecyclerView rvRecyclerView = mRootView.findViewById(R.id.rvRecyclerView);

        int spanCount = 3;
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), spanCount);
        gridLayoutManager.setSmoothScrollbarEnabled(true);
        rvRecyclerView.setLayoutManager(gridLayoutManager);

        rvRecyclerView.setHasFixedSize(true);
        rvRecyclerView.setNestedScrollingEnabled(false);
        int spacing = getContext().getResources().getDimensionPixelSize(R.dimen.dp_6);
        rvRecyclerView.addItemDecoration(new GridSpacingItemDecoration(spanCount, spacing, false));

        mAdpter = new me.drakeet.multitype.MultiTypeAdapter();
        LanguageSelectBinder lanuageSelectBinder = new LanguageSelectBinder();
        lanuageSelectBinder.setOnItemChildClickListener((view, position) -> {
            if (position >= 0 && position < list.size()) {
                list.get(position).setSelected(!list.get(position).isSelected());
                mAdpter.notifyItemChanged(position);
            }
        });
        mAdpter.register(LanguagesBean.class, lanuageSelectBinder);


        rvRecyclerView.setAdapter(mAdpter);
        mAdpter.setItems(list);
        mAdpter.notifyDataSetChanged();
        mRootView.findViewById(R.id.screen_cancle).setOnClickListener(view -> dismiss());


        mRootView.findViewById(R.id.screen_sure).setOnClickListener(view -> {
            dismiss();
            if (okClickListener != null) {
                okClickListener.onClick(view);
            }
        });


        getDialog().setCanceledOnTouchOutside(true);
        final Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.BOTTOM;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
        return mRootView;
    }


}
