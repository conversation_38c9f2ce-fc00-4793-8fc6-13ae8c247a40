package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import anchor.app.base.R;
import anchor.app.base.ext.AppKtKt;
import anchor.app.base.utils.ActivityUtil;
import anchor.app.base.utils.Utils;

/**
 * Token过期提示页面
 */
public class TokenInvailidDialog extends Dialog {

    public static final String TAG = TokenInvailidDialog.class.getSimpleName();
    private View mRootView;

    public TokenInvailidDialog(@NonNull Context context) {
        super(context);
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.base_layout_dialog_token_invalid, null);
        }
        mRootView.findViewById(R.id.textViewConfirm).setOnClickListener(v -> {
            Utils.logOut();
//            ARouter.getInstance().build(ArouterPath.PATH_LOGIN_SPLASH).navigation();
            //退出所有Activity
            ActivityUtil.getInstance().finishAllActivity();
//            ARouter.getInstance().build(ArouterPath.PATH_LOGIN_SELECT).navigation();
            ComponentName componentName = new ComponentName("com.mobile.anchor.app", "com.mobile.anchor.app.module.login.login.LoginSelectActivity");
            Intent intent = new Intent();
            intent.setComponent(componentName);
            AppKtKt.jump(v.getContext(), intent);
            dismiss();
        });
        mRootView.findViewById(R.id.textViewConcle).setOnClickListener(view -> {
            Utils.logOut();
            //退出所有Activity
            ActivityUtil.getInstance().finishAllActivity();
//                ARouter.getInstance().build(ArouterPath.PATH_LOGIN_LOGIN).navigation();
            dismiss();
        });
        setContentView(mRootView);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

}
