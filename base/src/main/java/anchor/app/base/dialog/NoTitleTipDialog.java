package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import anchor.app.base.R;


/**
 * 无title,无按钮统一弹窗
 */
public class NoTitleTipDialog extends Dialog {

    public static final String TAG = NoTitleTipDialog.class.getSimpleName();
    private View mRootView;

    private boolean isDubbingTips;

    public NoTitleTipDialog(@NonNull Context context, boolean isDubbingTips) {
        super(context);
        this.isDubbingTips = isDubbingTips;
        init(context);
    }

    public NoTitleTipDialog(@NonNull Context context, int themeResId, boolean isDubbingTips) {
        super(context, themeResId);
        this.isDubbingTips = isDubbingTips;
        init(context);
    }

    private void init(Context context) {
        if (null == mRootView) {
            if (isDubbingTips) {
                mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_notitle_dubbing_tips, null);
            } else {
                mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_notitle_tips, null);
            }
        }

        setContentView(mRootView);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

    public static void showTips(Context context) {
        NoTitleTipDialog tipDialog = new NoTitleTipDialog(context, false);
        tipDialog.show();
    }

    public static void showDubbingTips(Context context) {
        NoTitleTipDialog tipDialog = new NoTitleTipDialog(context, true);
        tipDialog.show();
    }
}
