package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import anchor.app.base.R;


/**
 * 设备电量不足选择弹窗
 */
public class NoPowerTipDialog extends Dialog implements View.OnClickListener {

    public static final String TAG = NoPowerTipDialog.class.getSimpleName();
    private View mRootView;

    public NoPowerTipDialog(@NonNull Context context) {
        super(context);
        init(context);
    }

    public NoPowerTipDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    private void init(Context context) {
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_no_power_tips, null);
        }

        setContentView(mRootView);
        mRootView.findViewById(R.id.textViewTips2).setOnClickListener(this);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

    /**
     * @param context
     */
    public static void showTips(Context context) {
        NoPowerTipDialog tipDialog = new NoPowerTipDialog(context);
        tipDialog.show();
    }

    @Override
    public void onClick(View v) {
        dismiss();
    }
}
