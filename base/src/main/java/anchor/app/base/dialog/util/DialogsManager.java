package anchor.app.base.dialog.util;


import android.app.Dialog;
import android.content.DialogInterface;

import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * https://github.com/crazyqiang/AndroidStudy/tree/master/sydialoglib
 * 支持多个Dialog依次弹出
 * Created by mq on 2018/9/1 下午4:35
 * <EMAIL>
 */

public class DialogsManager {

    private volatile boolean showing = false;//是否有dialog在展示
    private ConcurrentLinkedQueue<Dialog> dialogQueue = new ConcurrentLinkedQueue<>();

    private DialogsManager() {
    }

    public static DialogsManager getInstance() {
        return DialogHolder.instance;
    }

    private static class DialogHolder {
        private static DialogsManager instance = new DialogsManager();
    }

    /**
     * 请求加入队列并展示
     *
     * @param dialogWrapper DialogWrapper
     * @return 加入队列是否成功
     */
    public synchronized boolean requestShow(Dialog dialogWrapper) {
        boolean b = dialogQueue.offer(dialogWrapper);
        checkAndDispatch();
        return b;
    }

    /**
     * 结束一次展示 并且检查下一个弹窗
     */
    public synchronized void over() {
        showing = false;
        next();
    }

    private synchronized void checkAndDispatch() {
        if (!showing) {
            next();
        }
    }

    /**
     * 弹出下一个弹窗
     */
    private synchronized void next() {
        Dialog dialog = dialogQueue.poll();
        if (dialog != null) {
            showing = true;
            try {
                dialog.show();
                dialog.setOnDismissListener(dialog1 -> over());
            } catch (Exception e) {
                showing = false;
                e.printStackTrace();
            }
        }
    }
}
