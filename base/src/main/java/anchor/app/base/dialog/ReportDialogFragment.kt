package anchor.app.base.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import anchor.app.base.R
import anchor.app.base.databinding.BaseDialogReportBinding

/**
 * 头像选择提示页面
 */
class ReportDialogFragment(var refer: (Int) -> Unit) : DialogFragment() {
    private var bindingView: BaseDialogReportBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (null == bindingView) {
            bindingView = DataBindingUtil.inflate(
                layoutInflater, R.layout.base_dialog_report, null, false
            )
        }
        bindingView!!.dialog = this
        bindingView!!.lifecycleOwner = this

//        RxViewUtils.setOnClickListeners(mRootView.close, view -> {});
//        RxViewUtils.setOnClickListeners(mRootView.submit, view -> {});
        dialog!!.setCanceledOnTouchOutside(true)
        dialog!!.window!!.setWindowAnimations(R.style.AnimBottom)
        val window = dialog!!.window
        window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window.decorView.setPadding(0, 0, 0, 0)
        val wlp = window.attributes
        wlp.gravity = Gravity.BOTTOM
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.attributes = wlp
        return bindingView!!.root
    }

    fun select(index: Int) {
        refer(index)
        dismiss()
    }


}