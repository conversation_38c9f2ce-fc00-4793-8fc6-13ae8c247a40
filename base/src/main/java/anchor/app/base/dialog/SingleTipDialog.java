package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import anchor.app.base.R;


/**
 * app单按钮提示统一弹窗（例如发现页面提示用户完善宝宝信息、绑定设备、异常提示）
 */
public class SingleTipDialog extends Dialog {

    public static final String TAG = SingleTipDialog.class.getSimpleName();
    private View mRootView;
    ISingleTipsClickListener tipsClickListener;
    private TextView mBtnConfirm;
    private TextView mTextViewTitle;
    private TextView mTextViewContent;
    private ImageView mImageViewClose;
    private ImageView mImageViewBack;

    public SingleTipDialog(@NonNull Context context) {
        super(context);
        init(context);
    }

    public SingleTipDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    public void setTipsClickListener(ISingleTipsClickListener tipsClickListener) {
        this.tipsClickListener = tipsClickListener;
    }

    private void init(Context context) {
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_single_tips, null);
        }

        mTextViewTitle = mRootView.findViewById(R.id.textViewTitle);
        mImageViewClose = mRootView.findViewById(R.id.imageViewClose);
        mImageViewBack = mRootView.findViewById(R.id.imageViewBack);
        mTextViewContent = mRootView.findViewById(R.id.textViewContent);
        mBtnConfirm = mRootView.findViewById(R.id.btnConfirm);
        mBtnConfirm.setOnClickListener(v -> {
            if (tipsClickListener == null) {
                dismiss();
            } else {
                dismiss();
                tipsClickListener.confirmClick();
            }
        });
        mImageViewClose.setOnClickListener(v -> dismiss());

        setContentView(mRootView);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

    public void setTitle(String text) {
        mTextViewTitle.setText(text);
    }

    public void setConfirmText(String text) {
        mBtnConfirm.setText(text);
    }

    public void setContent(CharSequence text) {
        mTextViewContent.setText(text);
    }

    public void setImageResourceId(int id) {
        mImageViewBack.setImageResource(id);
    }

    public void setContentGravity(int gravity){
        mTextViewContent.setGravity(gravity);
    }

    /**
     * 设置确定按钮是否可见
     *
     * @param visiblity
     */
    public void setConfirmButtonVisiblity(boolean visiblity) {
        mBtnConfirm.setVisibility(visiblity ? View.VISIBLE : View.GONE);
    }

    /**
     * @param context
     * @param title           标题
     * @param content         内容
     * @param confirmText     按钮文字
     * @param imageResourceId 设置背景图
     * @param listener        点击事件
     */
    public static void showTips(Context context, String title, CharSequence content, String confirmText, int imageResourceId, ISingleTipsClickListener listener) {
        showTips(context,title,content,confirmText,imageResourceId,true,listener);
    }
    public static void showTips(Context context, String title, CharSequence content, String confirmText, int imageResourceId, boolean canceledOnTouchOutside, ISingleTipsClickListener listener) {
        SingleTipDialog tipDialog = new SingleTipDialog(context);
        tipDialog.setTitle(title);
        tipDialog.setContent(content);
        tipDialog.setConfirmText(confirmText);
        tipDialog.setImageResourceId(imageResourceId);
        tipDialog.setTipsClickListener(listener);
        tipDialog.setConfirmButtonVisiblity(!TextUtils.isEmpty(confirmText));
        tipDialog.setCanceledOnTouchOutside(canceledOnTouchOutside);
        tipDialog.show();
    }
    public static void showTips(Context context,  CharSequence content, ISingleTipsClickListener listener) {
        SingleTipDialog tipDialog = new SingleTipDialog(context);
        tipDialog.setContent(content);
        tipDialog.setTipsClickListener(listener);
        tipDialog.show();
    }
    public static void showTips(Context context,  CharSequence content,int gravity, ISingleTipsClickListener listener) {
        SingleTipDialog tipDialog = new SingleTipDialog(context);
        tipDialog.setContentGravity(gravity);
        tipDialog.setContent(content);
        tipDialog.setTipsClickListener(listener);
        tipDialog.show();
    }

    public interface ISingleTipsClickListener {
        void confirmClick();
    }
}
