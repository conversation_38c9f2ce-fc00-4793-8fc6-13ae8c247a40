package anchor.app.base.dialog

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import me.drakeet.multitype.ItemViewBinder
import anchor.app.base.R
import anchor.app.base.bean.LanguagesBean
import anchor.app.base.view.picbind.binder.OnItemChildClickListener

class LanguageSelectBinder : ItemViewBinder<LanguagesBean, LanguageSelectBinder.AddHolder?>() {
    var onItemChildClickListener: OnItemChildClickListener? = null
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): AddHolder {
        val root = inflater.inflate(R.layout.language_dialog_item, parent, false)
        return AddHolder(root)
    }

    override fun onBindViewHolder(holder: AddHolder, item: LanguagesBean) {
        holder.tv_content.setText(item.languageName)
        holder.tv_content.isSelected = item.isSelected
        if (item.isSelected) {
            holder.tv_content.setTypeface(holder.tv_content.getTypeface(), Typeface.BOLD);
        }else{
            holder.tv_content.setTypeface(holder.tv_content.getTypeface(), Typeface.NORMAL);
        }
        addOnClickListener(holder.tv_content, holder.layoutPosition)
    }

    class AddHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tv_content: TextView

        init {
            tv_content = itemView.findViewById(R.id.tv_content)
        }
    }

    fun addOnClickListener(view: View?, p: Int) {
        view?.setOnClickListener { v ->
            if (onItemChildClickListener != null) {
                onItemChildClickListener!!.onItemChildClick(v, p)
            }
        }
    }
}