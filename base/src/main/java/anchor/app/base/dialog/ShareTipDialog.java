package anchor.app.base.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import anchor.app.base.R;


/**
 * 分享选择弹窗
 */
public class ShareTipDialog extends Dialog implements View.OnClickListener {

    public static final String TAG = ShareTipDialog.class.getSimpleName();
    private View mRootView;
    private IOnClickListener mListener;

    public ShareTipDialog(@NonNull Context context) {
        super(context);
        init(context);
    }

    public ShareTipDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    public void setListener(IOnClickListener mListener) {
        this.mListener = mListener;
    }

    private void init(Context context) {
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.base_dialog_share_tips, null);
        }

        setContentView(mRootView);
        mRootView.findViewById(R.id.imageViewTips1).setOnClickListener(this);
        mRootView.findViewById(R.id.imageViewTips2).setOnClickListener(this);
        mRootView.findViewById(R.id.textViewTips1).setOnClickListener(this);
        mRootView.findViewById(R.id.textViewTips2).setOnClickListener(this);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }

    /**
     * @param context
     */
    public static ShareTipDialog showTips(Context context, IOnClickListener listener) {
        ShareTipDialog tipDialog = new ShareTipDialog(context);
        tipDialog.setListener(listener);
        tipDialog.show();
        return tipDialog;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.imageViewTips1 || v.getId() == R.id.textViewTips1) {
            if (mListener != null) {
                mListener.onWechatTimeLineClick();
                dismiss();
            }
        } else if (v.getId() == R.id.imageViewTips2 || v.getId() == R.id.textViewTips2) {
            if (mListener != null) {
                mListener.onWechatClick();
                dismiss();
            }
        }
    }

    public interface IOnClickListener {
        void onWechatClick();

        void onWechatTimeLineClick();
    }
}
