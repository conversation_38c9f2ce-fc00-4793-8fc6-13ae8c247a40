package anchor.app.base.manager

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import anchor.app.base.api.BaseApi
import anchor.app.base.bean.GiftBean
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.utils.RxTimerUtil
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil

class HeartBeatManager(val activity: FragmentActivity) {


    companion object{
        var giftManager: HeartBeatManager? = null;

        public fun heartBeatManager(activity: FragmentActivity): HeartBeatManager {
            if (giftManager == null) {
                giftManager = HeartBeatManager(activity)
            }
            return giftManager as HeartBeatManager
        }
        }

    val data = MutableLiveData<GiftBean>()

    fun  initHeartBeat(){
//            BaseApi.getHeartBeat()
//                .map(ResultMap())
//                .compose(RxErrorUtil.handleGlobalError(activity))
//                .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(activity, Lifecycle.Event.ON_DESTROY)))
//                .subscribe({ any: Any ->
//                    var i = any as Double
//                    initTimer(i.toLong())
//                }) { throwable: Throwable ->
//                    ToastUtil.show(throwable.message)
//                }
        initTimer(30)
    }

    private fun initTimer(toInt: Long) {
        RxTimerUtil.interval(activity, toInt) {
            if (SharePreUtil.isLogin()) {// 在登录后又退出,这个心跳还一直在执行,就有token过期的问题
            BaseApi.getHeartBeat()
                .map(ResultMap())
                .compose(RxErrorUtil.handleGlobalError(activity))
                .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(activity, Lifecycle.Event.ON_DESTROY)))
                .subscribe({ any: Any ->
                }) { throwable: Throwable ->
                    ToastUtil.show(throwable.message)
                }
                }
        }
    }

}