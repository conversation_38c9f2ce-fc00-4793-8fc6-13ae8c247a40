package anchor.app.base.manager

import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import anchor.app.base.api.BaseApi
import anchor.app.base.bean.GiftBean
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.utils.DownloadSvgaUtil
import anchor.app.base.utils.DownloadZipUtil
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.IOException
import java.net.MalformedURLException
import java.net.URL

class LoadGiftManager(val activity: FragmentActivity) {


    companion object{
        var mgiftManager: LoadGiftManager? = null;

        public fun giftManager(activity: FragmentActivity): LoadGiftManager {
            if (mgiftManager == null) {
                mgiftManager = LoadGiftManager(activity)
            }
            return mgiftManager as LoadGiftManager
        }
    }


    fun displayGoodAnim(svgaImageView: SVGAImageView, gift: GiftBean.DataDTO.GiftslistDTO) {
        var file: File? = null
        if (gift.giftSvgaUrl.endsWith(".svga")) {
            file = File(PathUtils.getPathGoods(), gift.giftCode + ".svga")
        } else if (gift.giftSvgaUrl.endsWith(".zip")) {
            file = File(PathUtils.getPathGoods(), gift.giftCode)
        }
        if (SharePreUtil.getGiftState(gift.giftCode) && file!!.exists()) {
            getLocalGift(svgaImageView, gift, file)
        } else {
            otherLoadSvga(gift, svgaImageView)
        }
    }

    private fun otherLoadSvga(
        gift: GiftBean.DataDTO.GiftslistDTO,
        svgaImageView: SVGAImageView
    ) {
        addGoodsDownloadTask(gift.giftSvgaUrl, gift.giftCode)
        //播放网络SVGA
        displayHttpSVGA(svgaImageView, gift.giftSvgaUrl)
    }


    fun addGoodsDownloadTask(downloadUrl: String, goodsId: String) {
        if (!TextUtils.isEmpty(downloadUrl)) {
            //zip 使用DownloadZipUtil 因为有时候安卓10  无法监听下载成功
            if (!TextUtils.isEmpty(downloadUrl) && downloadUrl.endsWith(".zip")) {
                DownloadZipUtil.get()
                    .addDownloadTask(downloadUrl, true, goodsId)
            } else {
//                DownloadUtil.getInstance().addDownloadTask(UrlManager.getRealHeadPath(downloadUrl), new File(PathUtils.getPathGoods()), UriUtil.getName(downloadUrl), this);
                DownloadSvgaUtil.get()
                    .addDownloadTask(downloadUrl, true, goodsId)
            }
        }
    }


    private fun getLocalGift(svgaImageView: SVGAImageView, gift: GiftBean.DataDTO.GiftslistDTO, file: File) {
        try {
            val parser = SVGAParser(svgaImageView.context)
            parser.decodeFromInputStream(FileInputStream(file), "", object :
                SVGAParser.ParseCompletion {
                override  fun onError() {
//                    LogUtils.iTag(TAG, "GoodAnim_SVGA加载File失败");
                }

                override fun onComplete(svgaVideoEntity: SVGAVideoEntity) {
//                    LogUtils.iTag(TAG, "GoodAnim_SVGA_SUCCESS");
                    val drawable = SVGADrawable(svgaVideoEntity)
                    svgaImageView.setImageDrawable(drawable)
                    svgaImageView.startAnimation()
                }
            }, true)
        } catch (e: FileNotFoundException) {

            addGoodsDownloadTask(gift.giftSvgaUrl, gift.giftCode)
            otherLoadSvga(gift, svgaImageView)
            //            LogUtils.iTag(TAG, "GoodAnim_本地File未查询到");
        } catch (e: IOException) {
            otherLoadSvga(gift, svgaImageView)
//            throw RuntimeException(e)
        }
    }

    fun displayHttpSVGA(svgaImageView: SVGAImageView?, url: String?) {
        if (svgaImageView == null) {
            return
        }
        try {
            val parser = SVGAParser(svgaImageView.context)
            parser.decodeFromURL(URL(url), object : SVGAParser.ParseCompletion {
                override  fun onError() {
                    if (svgaImageView != null && svgaImageView.callback != null) {
                        svgaImageView.callback!!.onFinished()
                    }
                }

                override  fun onComplete(svgaVideoEntity: SVGAVideoEntity) {
                    val drawable = SVGADrawable(svgaVideoEntity)
                    svgaImageView.setImageDrawable(drawable)
                    svgaImageView.startAnimation()
                }
            })
        } catch (e: MalformedURLException) {
            e.printStackTrace()
        }
    }

    private fun downloadGoods(goodsListData: MutableList<GiftBean.DataDTO.GiftslistDTO>) {
        if (goodsListData == null || goodsListData.size == 0) {
            return
        }
        for (goodsItem in goodsListData) {
            //只下载SVGA和zip
            if (!goodsItem.giftSvgaUrl.endsWith(".svga") && !goodsItem.giftSvgaUrl.endsWith(".zip")) {
                continue
            }

            var file: File? = null
            if (goodsItem.giftSvgaUrl.endsWith(".svga")) {
                file = File(PathUtils.getPathGoods(), goodsItem.giftCode + ".svga")
            } else if (goodsItem.giftSvgaUrl.endsWith(".zip")) {
                file = File(PathUtils.getPathGoods(), goodsItem.giftCode)
            }
            if (!SharePreUtil.getGiftState(goodsItem.giftCode) ||  !file!!.exists()) {
                //开始下载
                addGoodsDownloadTask(goodsItem.giftSvgaUrl, goodsItem.giftCode)
            }
        }
    }

    val data = MutableLiveData<GiftBean>()

    fun  initGiftList(){
        BaseApi.getGiftList()
            .map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(activity, Lifecycle.Event.ON_DESTROY)))
            .subscribe({ list: List<GiftBean.DataDTO> ->
                var giftBean = GiftBean()
                giftBean.data = list
                data.value = giftBean
                for (datum in list) {
                    downloadGoods(datum.giftslist)
                }

            }) { throwable: Throwable ->
                ToastUtil.show(throwable.message)
            }
//        var result = GsonUtil.GsonToBean(
//            "{\"code\":0,\"data\":[{\"giftTypeId\":1,\"giftTypeName\":\"礼物\",\"giftslist\":[{\"createTime\":\"\",\"giftCode\":\"a524\",\"giftIcon\":\"https://mikcat.s3.ap-east-1.amazonaws.com/1686367559489a524.png\",\"giftName\":\"飞机\",\"giftPrice\":200,\"giftStatus\":\"\",\"giftSvgaUrl\":\"https://mikcat.s3.ap-east-1.amazonaws.com/1686367862244524.svga\",\"id\":0,\"giftGrade\":2,\"updateTime\":\"\"},{\"createTime\":\"\",\"giftCode\":\"a541\",\"giftIcon\":\"https://mikcat.s3.ap-east-1.amazonaws.com/1686367635779a541.png\",\"giftName\":\"飞机66\",\"giftPrice\":200,\"giftStatus\":\"\",\"giftSvgaUrl\":\"https://mikcat.s3.ap-east-1.amazonaws.com/1686367840627541.svga\",\"id\":0,\"giftGrade\":2,\"updateTime\":\"\"}]}],\"msg\":\"\",\"success\":true}",
//            GiftBean::class.java
//        )
//
//                data.value = result
//                for (datum in result.data) {
//                    downloadGoods(datum.giftslist)
//                }


    }


    fun  sendGift(anchorId: Long, giveNum: Int, id: String, source: String, userRole: String): MutableLiveData<Any> {
        var data01 = MutableLiveData<Any>()
        BaseApi.sendGift(anchorId,giveNum,id,source,userRole)
            .map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(activity, Lifecycle.Event.ON_DESTROY)))
            .subscribe({ result: Any ->
                data01.value = result
            }) { throwable: Throwable ->
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) data01.value="" else ToastUtil.show(throwable.message)
                }
            }
        return data01
    }

    fun  askGift(anchorId: Long, giveNum: Int, id: String): MutableLiveData<Any> {
        var data01 = MutableLiveData<Any>()
        BaseApi.askGift(anchorId,giveNum,id)
            .map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(activity, Lifecycle.Event.ON_DESTROY)))
            .subscribe({ result: Any ->
                data01.value = result
            }) { throwable: Throwable ->
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) data01.value="" else ToastUtil.show(throwable.message)
                }
            }
        return data01
    }

}