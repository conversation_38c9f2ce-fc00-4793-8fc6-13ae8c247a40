package anchor.app.base.manager

import androidx.fragment.app.FragmentActivity

class ResourceManager (val activity: FragmentActivity) {


    companion object{
        var manager: ResourceManager? = null;
        var activity: FragmentActivity? = null;

        public fun manager(activity: FragmentActivity): ResourceManager {
            if (manager == null) {
                manager = ResourceManager(activity)
                this.activity = activity
            }
            return manager as ResourceManager
        }
    }


    fun init(){
        //        初始化下載礼物资源
        LoadGiftManager.giftManager(activity).initGiftList()
        //        初始化钻石
        DiamondManager.diamondManager(activity).initDiamond()
        //
        OtherManager.manager(activity).getSysParam()
    }
}