# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-dontwarn javax.lang.model.element.Element
-dontwarn java.lang.invoke.StringConcatFactory
-keep class com.lxj.xpopup.**{*;}

-keep class anchor.app.base.view.at.AtWrapper
-keep class anchor.app.base.**{*;}
-keep public class anchor.app.base.BaseApp
-keepclassmembers class anchor.app.base.BaseApp {
    <init>();
}

-keep class * implements anchor.app.base.repository.IRepository

-keep class io.reactivex.**{*;}
-keep interface io.reactivex.** { *; }
-keep interface anchor.app.base.api.IBaseApi
-keep class anchor.app.base.api.BaseApi
-keep class anchor.app.base.bean.GiftBean$DataDTO
-keep class anchor.app.base.bean.GiftBean$DataDTO$GiftslistDTO
-keep class anchor.app.base.utils.**{*;}
-keep class anchor.app.base.manager.**{*;}
-keep class anchor.app.base.core.**{*;}

-keep class * extends anchor.app.base.ui.AutoDisposeFragment
-keep class * extends androidx.lifecycle.ViewModel { *; }
-keep class * extends androidx.lifecycle.ViewModelProvider$Factory { *; }

# 如果使用了 Kotlin，需要添加下面这行
-keep class **.*_RoutingAdapter{*;}

-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# 保持 DataBindingComponent 类名不变
-keep class androidx.databinding.DataBindingComponent { *; }

# 保持所有实现了 DataBindingComponent 的类
-keep class * implements androidx.databinding.DataBindingComponent {
    <init>();
}

# 保持所有生成的 DataBinding 类
-keep class * extends androidx.databinding.ViewDataBinding {
    <init>(android.view.View);
}

# 保持所有生成的 Binding 类
-keepclassmembers class ** implements androidx.databinding.generated.binding.** {
    <methods>;
}

# 保持所有带有 @Bindable 注解的方法
-keepclassmembers class * {
    @androidx.databinding.Bindable <methods>;
}

-keep class androidx.room.RoomDatabase {
    *;
}
-keepclassmembers class * extends androidx.room.RoomDatabase {
    *;
}
-keep @androidx.room.* class *
-keep class * extends androidx.room.RoomDatabase
