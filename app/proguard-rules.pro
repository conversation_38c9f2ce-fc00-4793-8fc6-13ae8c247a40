# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-keepattributes *Annotation*,InnerClasses
-keepattributes SourceFile,LineNumberTable
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Notification
-keep public class * extends android.app.Appliction
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep public class com.android.vending.licensing.ILicensingService
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep class com.tencent.**{*;}
-dontwarn com.google.android.material.**
-dontwarn androidx.**

# 声网
-keep class io.agora.**{*;}

#bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}

# glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}

# ucrop
-dontwarn com.yalantis.ucrop**
-keep class com.yalantis.ucrop** { *; }
-keep interface com.yalantis.ucrop** { *; }

# rxpermissions
-dontwarn com.tbruyelle.rxpermissions.**

-dontwarn retrofit2.**
-dontwarn rx.**
-keep class retrofit2.** { *; }
-keep class com.google.gson.** { *; }
-keep interface com.google.gson.** { *; }

-dontwarn okio.**
-dontwarn com.squareup.okhttp3.**
-dontwarn okhttp3.**
-dontwarn javax.annotation.**

-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

-keepclassmembers class * implements java.io.Serializable { *; }

-keepclassmembers class * {
    void *(**On*Event);
    void *(**On*Listener);
}

-keepclassmembers class fqcn.of.javascript.interface.for.webview {
    public *;
}
-keepclassmembers class * extends android.webkit.webViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.webViewClient {
    public void *(android.webkit.webView, jav.lang.String);
}

-dontwarn javax.annotation.**

-keepclasseswithmembers class * implements android.arch.lifecycle.GenericLifecycleObserver {
<init>(...);
}
-keepclassmembers class android.arch.lifecycle.Lifecycle$* { *; }
-keepclassmembers class * {
    @android.arch.lifecycle.OnLifecycleEvent *;
}
-keepclassmembers class * extends android.arch.lifecycle.ViewModel {
<init>(...);
}

 #保留R$*类中静态成员的变量名
-keepclassmembers class **.R$*{
    public static <fields>;
}
#保留在Activity中以View为参数的方法不变
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

#去除日志
-assumenosideeffects class android.util.Log{
   public static boolean isLoggable(java.lang.String,int);
   public static int v(...);
   public static int i(...);
   public static int w(...);
   public static int d(...);
   public static int e(...);
}
-assumenosideeffects class anchor.app.base.utils.Logger{
   public static int v(...);
   public static int i(...);
   public static int w(...);
   public static int d(...);
   public static int e(...);
}
# 保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}
# 保持自定义控件类不被混淆
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# 保持自定义控件类不被混淆
-keepclasseswithmembers class * {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
# gsyvideoplayer
-keep class com.shuyu.gsyvideoplayer.video.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.**
-keep class com.shuyu.gsyvideoplayer.video.base.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.base.**
-keep class com.shuyu.gsyvideoplayer.utils.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.utils.**
-keep class tv.danmaku.ijk.** { *; }
-dontwarn tv.danmaku.ijk.**

-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}
# 保持枚举 enum 类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Retrofit
-dontnote retrofit2.Platform
-dontnote retrofit2.Platform$IOS$MainThreadExecutor
-dontwarn retrofit2.Platform$Java8
-keepattributes Signature
-keepattributes Exceptions

# okhttp
-dontwarn okio.**

# 所有的Bean
-keep class com.mobile.anchor.app.bean.**{*;}
-keep class anchor.app.base.bean.**{*;}
-keep class anchor.app.base.bean.GiftBean{*;}
-keep class anchor.app.base.bean.GiftBean$DataDTO
-keep class anchor.app.base.bean.GiftBean$DataDTO$GiftslistDTO
-keep class anchor.app.base.retrofit.**{*;}
-keep class com.naman14.androidlame.singsound.result.**{*;}
-keep class com.mobile.anchor.app.module.update.impl.**{*;}
-keep class com.mobile.anchor.app.module.update.**{*;}
-keep class com.mobile.anchor.app.module.workbench.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.bean.**{*;}
-keep class com.mobile.anchor.app.module.login.bean.**{*;}
-keep class com.mobile.anchor.app.module.user.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.dialog.selectcountry.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.main.center.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.main.center.moment.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.main.homelist.bean.**{*;}
-keep class com.mobile.anchor.app.module.discover.beautyapi.**{*;}
-keep class com.mobile.anchor.app.module.user.follow.bean.**{*;}
-keep class com.mobile.anchor.app.module.user.setting.blacklist.bean.**{*;}
-keep class com.mobile.anchor.app.module.user.wallet.history.bean.**{*;}

-keep class io.rong.imkit.parsemessage.**{*;}

-keep class anchor.app.base.commonservice.**{*;}

-keep class io.reactivex.**{*;}
-keep interface io.reactivex.** { *; }
-keep class * implements anchor.app.base.repository.IRepository
-keep interface anchor.app.base.api.IBaseApi
-keep class anchor.app.base.api.BaseApi
-keep interface com.mobile.anchor.app.module.workbench.api.IWorkbenchApi
-keep class com.mobile.anchor.app.module.workbench.api.WorkbenchApi
-keep interface com.mobile.anchor.app.module.discover.api.IDiscoverApi
-keep class com.mobile.anchor.app.module.discover.api.DiscoverApi
-keep interface com.mobile.anchor.app.module.user.api.IUserApi
-keep class com.mobile.anchor.app.module.user.api.UserApi
-keep interface com.mobile.anchor.app.module.login.api.ILoginApi
-keep class com.mobile.anchor.app.module.login.api.LoginApi

-keep class * extends anchor.app.base.ui.AutoDisposeFragment
-keep class * extends androidx.lifecycle.ViewModel { *; }
-keep class * extends androidx.lifecycle.ViewModelProvider$Factory { *; }

# 如果使用了 Kotlin，需要添加下面这行
-keep class **.*_RoutingAdapter{*;}


# 保留自定义的 Fragment
-keep public class * extends androidx.fragment.app.Fragment


# fastjosn
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.**{*;}

-keep class com.hjq.toast.** {*;}

-keep class com.tencent.mm.opensdk.** {*;}
# zxing
-keep class com.google.zxing.client.android.** {*;}
## Android architecture components: Lifecycle
# LifecycleObserver's empty constructor is considered to be unused by proguard
-keepclassmembers class * implements android.arch.lifecycle.LifecycleObserver {
    <init>(...);
}
# ViewModel's empty constructor is considered to be unused by proguard
-keepclassmembers class * extends android.arch.lifecycle.ViewModel {
    <init>(...);
}
# keep Lifecycle State and Event enums values
-keepclassmembers class android.arch.lifecycle.Lifecycle$State { *; }
-keepclassmembers class android.arch.lifecycle.Lifecycle$Event { *; }
# keep methods annotated with @OnLifecycleEvent even if they seem to be unused
# (Mostly for LiveData.LifecycleBoundObserver.onStateChange(), but who knows)
-keepclassmembers class * {
    @android.arch.lifecycle.OnLifecycleEvent *;
}

# rxjava rxandroid
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}
-dontnote rx.internal.util.PlatformDependent
-keep class retrofit2.** { *; }
-keep class io.reactivex.** { *; }
-keepattributes Signature
-keepattributes Exceptions
-keepattributes *Annotation*
-dontwarn retrofit2.**
-dontwarn io.reactivex.**


-dontwarn javax.annotation.**
-dontwarn javax.inject.**
# OkHttp3
-dontwarn okhttp3.logging.**
-keep class okhttp3.internal.**{*;}
-dontwarn okio.**
# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

#组件化application
-keep class anchor.app.base.IComponentApplication
-keep public class * extends anchor.app.base.IComponentApplication

#先声SDK
-dontwarn com.singsong.dubbing.ui.**
-keep class com.singsong.dubbing.ui.** { *; }
-keep class com.singsound.caidou.entity.** {*;}
-dontwarn com.singsound.caidou.**
-dontwarn com.singsong.mockexam.entity.**
-keep class com.singsong.mockexam.entity.** { *; }
-dontwarn com.singsong.corelib.core.EventBusManager
-keep class com.singsong.corelib.core.EventBusManager { *; }
-dontwarn com.singsong.corelib.**
-keep class com.singsong.corelib.** { *; }
-dontwarn com.Tool.**
-keep class com.Tool.** { *; }
-dontwarn com.czt.**
-keep class com.czt.** { *; }
-dontwarn zty.**
-keep class zty.** { *; }
-dontwarn vavi.**
-keep class vavi.** { *; }
-keep class com.tt.** { *; }
-keep class com.xs.** { *; }
-keep interface com.xs.** { *; }
-keep enum com.xs.** { *; }
-dontwarn com.alibaba.android.**
-keep class com.alibaba.android.** { *;}
-keep class com.facebook.** {*;}
-dontwarn com.facebook.**
-keep interface com.facebook.** {*;}
-keep enum com.facebook.** {*;}
-keep class com.alibaba.sdk.android.oss.** { *; }
-dontwarn okio.**
-dontwarn org.apache.commons.codec.binary.**
-keep class tv.danmaku.ijk.** { *; }
-dontwarn tv.danmaku.ijk.**
-keep class fm.manager.** { *; }
-dontwarn fm.manager.**
-keep class video.controller.** {*;}
-dontwarn video.controller.**
-dontwarn okhttp3.logging.**
-keep class okhttp3.internal.**{*;}
-dontwarn okio.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions
-dontwarn sun.misc.**

-keep class org.openudid.** { *; }
-keep class ly.count.android.sdk.** { *; }

#阿里推送
-keepclasseswithmembernames class ** {
    native <methods>;
}
-keepattributes Signature
-keep class sun.misc.Unsafe { *; }
-keep class com.taobao.** {*;}
-keep class com.alibaba.** {*;}
-keep class com.alipay.** {*;}
-dontwarn com.taobao.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-keep class com.ut.** {*;}
-dontwarn com.ut.**
-keep class com.ta.** {*;}
-dontwarn com.ta.**
-keep class anet.**{*;}
-keep class org.android.spdy.**{*;}
-keep class org.android.agoo.**{*;}
-dontwarn anet.**
-dontwarn org.android.spdy.**
-dontwarn org.android.agoo.**

-dontwarn com.alibaba.wireless.security.**
-dontwarn com.alibaba.analytics.**
-dontwarn com.taobao.**
-dontwarn android.os.**
-dontwarn com.google.firebase.**
-dontwarn android.app.Notification
-dontwarn anchor.app.base.viewmodel.BaseViewModel
-dontwarn anchor.app.base.repository.IRepository
-dontwarn anchor.app.base.repository.ILocalDataSource
-dontwarn anchor.app.base.repository.IRemoteDataSource
-dontwarn anchor.app.base.api.BaseApi

-keep class com.alibaba.sdk.android.**{*;}
-keep class com.ut.**{*;}
-keep class com.ta.**{*;}

#-keep public class com.google.firebase.* {*;}
# GCM/FCM通道
-keep public class com.google.firebase.**{*;}
-dontwarn com.google.firebase.**

#声网
-keep class io.agora.**{*;}
#融云
-keepattributes Exceptions,InnerClasses

-keepattributes Signature

-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

-ignorewarnings


#EventBus#
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

-keep class androidx.room.RoomDatabase {
    *;
}
-keepclassmembers class * extends androidx.room.RoomDatabase {
    *;
}
-keep @androidx.room.* class *
-keep class * extends androidx.room.RoomDatabase

