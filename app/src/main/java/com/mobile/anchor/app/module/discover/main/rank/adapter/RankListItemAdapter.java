package com.mobile.anchor.app.module.discover.main.rank.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.discover.adapter.DiscoverBindingViewAdapter;

public class RankListItemAdapter extends DiscoverBindingViewAdapter {

    public static final int ITEM_MATCH = 1;

    public RankListItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_MATCH, R.layout.discover_item_rank);
    }

    @Override
    public int getViewType(@NotNull Object item) {
        return ITEM_MATCH;
    }
}
