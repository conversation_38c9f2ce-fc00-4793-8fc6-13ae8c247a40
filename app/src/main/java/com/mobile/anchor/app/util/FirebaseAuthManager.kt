package com.mobile.anchor.app.util

import android.app.Activity
import android.content.Intent
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.firebase.auth.FirebaseAuth


object FirebaseAuthManager {

    private val webClientId =
        "************-khahnbeh6tm8da9rvinj6qr1hk2a2lrd.apps.googleusercontent.com"
    private val webClientToken = "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"

    val auth by lazy {
        FirebaseAuth.getInstance()
    }

    val signInRequest by lazy {
        BeginSignInRequest.builder().setGoogleIdTokenRequestOptions(
            BeginSignInRequest.GoogleIdTokenRequestOptions.builder().setSupported(true)
                .setServerClientId(webClientId)
                .setFilterByAuthorizedAccounts(true).build()
        ).build()
    }

    fun authGoogle(activity: Activity): Intent {
        val gso = GoogleSignInOptions
            .Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(webClientId)
            .requestEmail()
            .build()
        return GoogleSignIn.getClient(activity, gso).signInIntent
    }

//    fun authFacebook(activity: Activity) {
//        val callbackManager = CallbackManager.Factory.create()
//    }
}