package com.mobile.anchor.app.module.discover.main.chat.videochat.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.databinding.DiscoverDialogLayoutVideochatMoreBinding;

import io.reactivex.annotations.Nullable;
import anchor.app.base.utils.RxViewUtils;

/**
 * 头像选择提示页面
 */
public class MoreDialogFragment extends DialogFragment {

    public interface Liste {
        abstract void clickListener(int index, boolean type);
    }

    private Liste liste;
    private boolean isOpenLocalVideo;
    private boolean isOpenLocalVoicePitch;


    public MoreDialogFragment(boolean isOpenLocalVideo, boolean isOpenLocalVoicePitch, Liste liste) {
        this.liste = liste;
        this.isOpenLocalVideo = isOpenLocalVideo;
        this.isOpenLocalVoicePitch = isOpenLocalVoicePitch;
    }

    //    ComponentActivity activity;
    public static final String TAG = MoreDialogFragment.class.getSimpleName();
    private DiscoverDialogLayoutVideochatMoreBinding bindingView;

//    public DiscoverSelectCountryDialogFragment(ComponentActivity activity) {
//        this.activity = activity;
//    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        if (null == bindingView) {
            bindingView = DataBindingUtil.inflate(getLayoutInflater(), R.layout.discover_dialog_layout_videochat_more, null, false);
        }

        bindingView.setDialog(this);
        bindingView.setLifecycleOwner(this);
        bindingView.switch01.setChecked(isOpenLocalVideo);
        bindingView.switch02.setChecked(isOpenLocalVoicePitch);

        RxViewUtils.setOnClickListeners(bindingView.switch01, view -> {

            liste.clickListener(1, bindingView.switch01.isChecked());
        });

        RxViewUtils.setOnClickListeners(bindingView.switch02, view -> {

            liste.clickListener(2, bindingView.switch02.isChecked());
        });

        RxViewUtils.setOnClickListeners(bindingView.LL03, view -> {

            liste.clickListener(3, false);
        });

        RxViewUtils.setOnClickListeners(bindingView.LL04, view -> {
            getDialog().cancel();
        });
//        RxViewUtils.setOnClickListeners(mRootView.submit, view -> {});
//        RxViewUtils.setOnClickListeners(mRootView.submit, view -> {});

        getDialog().setCanceledOnTouchOutside(true);

        getDialog().getWindow().setWindowAnimations(R.style.AnimBottom);

        final Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.BOTTOM;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
        return bindingView.getRoot();
    }


}
