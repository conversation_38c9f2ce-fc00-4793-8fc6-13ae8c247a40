package com.mobile.anchor.app.module.user.album

import anchor.app.base.bean.AlbumType
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityAlbumManageBinding
import anchor.app.base.ext.attach
import anchor.app.base.ui.BaseActivity
import com.mobile.anchor.app.module.user.main.UserViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hachao
 * @date: 2024/8/16
 * @description :相册维护
 */
class AlbumManageActivity : BaseActivity<UserViewModel, ActivityAlbumManageBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_album_manage

    override fun initView() {
        bindingView.activity = this
        showContentView()
        bindingView.tabLayout.apply {
            setTitles(resources.getStringArray(R.array.album_manage))
        }

        bindingView.viewPager.attach(
            supportFragmentManager, lifecycle, arrayListOf(
                AlbumManageFragment.newInstance(AlbumType.COMMON), AlbumManageFragment.newInstance(AlbumType.PRIVATE)
            )
        ) { position ->
            bindingView.tabLayout.setCurrentTab(position)
        }
        bindingView.tabLayout.setViewPager(bindingView.viewPager)

//        bindingView.tabLayout.setViewPager(
//            bindingView.viewPager, resources.getStringArray(R.array.album_manage), this,
//            arrayListOf(
//                AlbumManageFragment.newInstance(AlbumType.COMMON),
//                AlbumManageFragment.newInstance(AlbumType.PRIVATE)
//            )
//        )
//        bindingView.tvAdd.setOnClickListener {
//            ARouter.getInstance().build(ArouterPath.PATH_USER_ALBUM_ADDITION)
//                .withInt("type", if (bindingView.tabLayout.currentTab==0) AlbumType.PHOTO.value else AlbumType.VIDEO.value)
//                .navigation()
//        }
    }
}