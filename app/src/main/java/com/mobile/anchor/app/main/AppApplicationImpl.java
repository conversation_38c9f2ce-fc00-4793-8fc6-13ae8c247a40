package com.mobile.anchor.app.main;

import android.app.Application;


import com.mobile.anchor.app.BuildConfig;
import anchor.app.base.IComponentApplication;
import anchor.app.base.core.Constants;
import anchor.app.base.utils.ApkUtil;
import anchor.app.base.utils.Utils;

public class AppApplicationImpl implements IComponentApplication {
    @Override
    public void initBefore(Application application) {
        initBugly(application);
    }

    @Override
    public void initAfter(Application application) {
    }

    private void initBugly(Application application) {
        // 获取当前进程名
//        String processName = Utils.getProcessName(android.os.Process.myPid());
        // 设置是否为上报进程
//        strategy.setUploadProcess(processName == null || processName.equals(BuildConfig.APPLICATION_ID));

        //Bugly.init(this, Constants.BUGLY_APP_ID, false, strategy);
        String buglyId;
        boolean isDebug;
        if ("debug".equals(BuildConfig.BUILD_TYPE) || "develop".equals(BuildConfig.BUILD_TYPE)) {
            buglyId = Constants.APP_REGITEST_ID.BUGLY_APP_ID_DEBUG;
            isDebug = true;
        } else {
            buglyId = Constants.APP_REGITEST_ID.BUGLY_APP_ID_RELEASE;
            isDebug = false;
        }

//        setUserId(SharePreUtil.getUserId());
    }

}
