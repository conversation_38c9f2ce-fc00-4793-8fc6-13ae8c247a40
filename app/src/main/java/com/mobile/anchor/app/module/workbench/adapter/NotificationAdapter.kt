package com.mobile.anchor.app.module.workbench.adapter

import android.content.Context
import androidx.databinding.ObservableArrayList
import com.mobile.anchor.app.R
import com.mobile.anchor.app.module.workbench.bean.NotificationBean

class NotificationAdapter(
    context: Context,
    list: ObservableArrayList<NotificationBean>
) : NotificationBindingViewAdapter<NotificationBean>(context, list) {
    init {
        addViewTypeToLayoutMap(ITEM_IMAGE0, R.layout.notification_item)
    }

    override fun getViewType(item: Any): Int {
        return  ITEM_IMAGE0
    }

    companion object {
        const val ITEM_IMAGE0 = 0
    }
}