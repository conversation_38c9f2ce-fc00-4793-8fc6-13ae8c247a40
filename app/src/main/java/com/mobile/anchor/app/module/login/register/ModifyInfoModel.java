package com.mobile.anchor.app.module.login.register;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;

import java.io.File;
import java.util.Map;

import anchor.app.base.dialog.LoadingDialog;
import anchor.app.base.ext.rxweaver.RxErrorUtil;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.retrofit.ResultMap;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.SharePreUtil;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.viewmodel.BaseViewModel;

import static com.uber.autodispose.AutoDispose.autoDisposable;

public class ModifyInfoModel extends BaseViewModel<ModifyInfoRepository> {


    public ModifyInfoModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public ModifyInfoRepository getRepository() {
        return new ModifyInfoRepository();
    }

    public MutableLiveData<Object> save(FragmentActivity activity, Map<String, String> bean) {
        LoadingDialog.getInstance(activity).show();
        MutableLiveData<Object> data = new MutableLiveData<>();
        repository.modifyChildInfo(bean)
                .map(new ResultMap<>())
                .compose(RxErrorUtil.handleGlobalError(activity))
                .as(autoDisposable(this))
                .subscribe(result -> {
                    SharePreUtil.setIsLogin(true);
                    LoadingDialog.getInstance(activity).dismiss();
                    data.setValue(result.toString());

                }, throwable -> {
                    LoadingDialog.getInstance(activity).dismiss();
                    if (throwable instanceof ResultException) {
                        if (((ResultException) throwable).getCode() == BaseResult.SUCCESS) data.setValue("");
                        else  {
                            ToastUtil.show(((ResultException) throwable).getMessage());
                        } ;
                    }else {
                        error.setValue(throwable);
                    }
                });
        return data;
    }

    public MutableLiveData<Object> auditUpdate(FragmentActivity activity, Map<String, Object> bean) {
        LoadingDialog.getInstance(activity).show();
        MutableLiveData<Object> data = new MutableLiveData<>();
        repository.auditUpdate(bean)
                .map(new ResultMap<>())
                .compose(RxErrorUtil.handleGlobalError(activity))
                .as(autoDisposable(this))
                .subscribe(result -> {
                    SharePreUtil.setIsLogin(true);
                    LoadingDialog.getInstance(activity).dismiss();
                    data.setValue(result.toString());

                }, throwable -> {
                    LoadingDialog.getInstance(activity).dismiss();
                    if (throwable instanceof ResultException) {
                        if (((ResultException) throwable).getCode() == BaseResult.SUCCESS) data.setValue("");
                        else  {
                            ToastUtil.show(((ResultException) throwable).getMessage());
                        } ;
                    }else {
                        error.setValue(throwable);
                    }
                });
        return data;
    }

    public MutableLiveData<String> uploadPicture(File file) {
        MutableLiveData<String> data = new MutableLiveData<>();
        repository.uploadPicture(file).as(autoDisposable(this))
                .subscribe(baseResult -> {
                    Gson gson = new Gson();
                    Logger.d(gson.toJson(baseResult.getData()));
//                    JSONObject jsonObject = JSONObject.parseObject(gson.toJson(baseResult.getData()));
//                    String strPath = jsonObject.getString("imageUrl");
//                    data.setValue(strPath);
                    data.setValue(baseResult.getData().toString());
                }, throwable -> {
//                    error.setValue(throwable);
                    if (throwable instanceof ResultException) {
                        if (((ResultException) throwable).getCode() == BaseResult.SUCCESS) data.setValue("");
                    }else {
                        ToastUtil.show(throwable.getMessage());
                        data.setValue("000");
                    }
                });
        return data;
    }
}
