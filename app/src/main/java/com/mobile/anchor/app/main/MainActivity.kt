package com.mobile.anchor.app.main


import anchor.app.base.BaseApp
import anchor.app.base.bean.ChatTipBean
import anchor.app.base.bean.GiftBean
import anchor.app.base.bean.LoginBean
import anchor.app.base.commonservice.CallWindowServices
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.SingleTipDialog
import anchor.app.base.ext.jump
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.ResourceManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.manager.UserInfoManager.Companion.user
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.retrofit.RxSchedulers.ui
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxBusBaseMessage
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.Logger
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.viewpager.widget.ViewPager
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.android.material.bottomnavigation.BottomNavigationMenuView
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityMainBinding
import com.mobile.anchor.app.main.conversion.ConversionTabFragment
import com.mobile.anchor.app.module.discover.bean.RecordVideoBean
import com.mobile.anchor.app.module.discover.main.HomeTabFragment
import com.mobile.anchor.app.module.discover.main.chat.videochat.CallVideoChatActivity
import com.mobile.anchor.app.module.discover.main.homelist.HomeListViewmodel
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import com.mobile.anchor.app.module.update.impl.ZlUpdateHelper
import com.mobile.anchor.app.module.user.album.AlbumManageActivity
import com.mobile.anchor.app.module.user.editedInfo.BuildProfileActivity
import com.mobile.anchor.app.module.user.editedInfo.EditInfoModel
import com.mobile.anchor.app.module.user.editedInfo.ProfileActivity
import com.mobile.anchor.app.module.user.main.UserFragment
import com.mobile.anchor.app.module.user.record.RecordVideoActivity
import com.mobile.anchor.app.module.user.wallet.BankBindActivity
import com.mobile.anchor.app.module.workbench.home.WorkbenchFragment
import com.mobile.anchor.app.registration.RegistrationContext
import com.mobile.anchor.app.registration.states.AlbumUploadState
import com.mobile.anchor.app.registration.states.AvatarApprovalState
import com.mobile.anchor.app.registration.states.BindCardState
import com.mobile.anchor.app.registration.states.FaceVideoApprovalState
import com.mobile.anchor.app.registration.states.NicknameApprovalState
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.AutoDispose.autoDisposable
import com.uber.autodispose.ObservableSubscribeProxy
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import io.rong.imkit.IMCenter
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.manager.UnReadMessageManager
import io.rong.imkit.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.parsemessage.MikChatVideoCallMessage
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.view.chatTip.ChatTipManager
import io.rong.imkit.view.chatTip.ChatTipView
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier
import io.rong.imlib.model.Message
import io.rong.imlib.model.Message.SentStatus
import io.rong.imlib.model.UserInfo
import io.rong.message.TextMessage
import java.io.File

/**
 * app主页面
 */
class MainActivity : BaseActivity<MainViewModel, ActivityMainBinding>(),
    ViewPager.OnPageChangeListener {

    private var badge: View? = null
    private val editViewModel by viewModels<EditInfoModel>()

    override fun getLayoutId(): Int = R.layout.activity_main

    @SuppressLint("RestrictedApi")
    private fun setBadge() {

        //获取底部菜单view
        val menuView = bindingView.navigation.getChildAt(0) as BottomNavigationMenuView
        //获取第2个itemView
//        val itemView02 = menuView.getChildAt(0) as BottomNavigationItemView //工作统计页面  WorkbenchFragment
        val itemView03 = menuView.getChildAt(2) as BottomNavigationItemView//消息
        //引入badgeView
//        val badgeView02 = LayoutInflater.from(this).inflate(R.layout.app_badge_view_02, menuView, false)
        val badgeView03 =
            LayoutInflater.from(this).inflate(R.layout.app_badge_view, menuView, false)
//        badgeView02.visibility = View.GONE
        badgeView03.visibility = View.GONE

        //把badgeView添加到itemView中
//        itemView02.addView(badgeView02)
//        val count02 = badgeView02.findViewById<TextView>(R.id.tv_badge)

//        count02.text = (if(SharePreUtil.getReadMoment()<=0) 0 else SharePreUtil.getReadMoment()).toString()


        itemView03.addView(badgeView03)
        //获取子view并设置显示数目
        val count03 = badgeView03.findViewById<TextView>(R.id.tv_badge)

        //        获取当前的消息的未读数量
        RongIMClient.getInstance()
            .getTotalUnreadCount(object : RongIMClient.ResultCallback<Int?>() {
                override fun onSuccess(integer: Int?) {
                    if (integer != null) {
                        count03.text = if (integer > 99) "99+" else integer.toString()
                        if (integer <= 0) {
                            badgeView03.visibility = View.GONE
                            OtherManager.manager?.totalUnreadCount?.value = "0"
                            RxBus.getDefault()
                                .post(RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, "0")
                        } else {
                            badgeView03.visibility = View.VISIBLE
                            OtherManager.manager?.totalUnreadCount?.value = integer.toString()
                            RxBus.getDefault().post(
                                RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT,
                                integer.toString()
                            )
                        }

                    } else {
                        badgeView03.visibility = View.GONE
                        OtherManager.manager?.totalUnreadCount?.value = "0"
                        RxBus.getDefault()
                            .post(RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, "0")
                    }
                }

                override fun onError(e: RongIMClient.ErrorCode) {}
            })

        //        消息的未读数量的变化监听
        UnReadMessageManager.getInstance()
            .addObserver(arrayOf(Conversation.ConversationType.PRIVATE)) { integer: Int? ->
                if (integer != null) {
                    count03.text = if (integer > 99) "99+" else integer.toString()
                    if (integer <= 0) {
                        badgeView03.visibility = View.GONE
                        OtherManager.manager?.totalUnreadCount?.value = "0"
                        RxBus.getDefault()
                            .post(RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, "0")

                    } else {
                        badgeView03.visibility = View.VISIBLE
                        OtherManager.manager?.totalUnreadCount?.value = integer.toString()
                        RxBus.getDefault().post(
                            RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, integer.toString()
                        )

                    }

                } else {
                    badgeView03.visibility = View.GONE
                    OtherManager.manager?.totalUnreadCount?.value = "0"
                    RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_MESSAGE_UNREAD_COUNT, "0")
                }
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_BADGE, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe {
                val readMoment = SharePreUtil.getReadMoment()

//                count02.text = (if(readMoment<=0) 0 else readMoment).toString()
//                if (SharePreUtil.getReadMoment() <= 0) badgeView02.visibility =
//                    View.GONE else badgeView02.visibility = View.VISIBLE
            }
        //不显示则隐藏
        //count.visibility=View.GONE

//        if (SharePreUtil.getReadMoment() <= 0) badgeView02.visibility =
//            View.GONE else badgeView02.visibility = View.VISIBLE
    }

    fun initFragments(): List<Fragment> =
        if (user()?.isGuild == true || user()?.isDisable == true) listOf(
            getFragmentList(0), getFragmentList(3)
        ) else listOf(
            getFragmentList(0), getFragmentList(1), getFragmentList(2), getFragmentList(3)
        )

    private fun getFragmentList(index: Int): Fragment {
        var fragment: Fragment? = null

        when (index) {
            0 -> fragment = WorkbenchFragment()

            1 -> fragment = HomeTabFragment()

            2 -> fragment = ConversionTabFragment()

            3 -> fragment = UserFragment()
        }
        return fragment!!
    }

    override fun initView() {
        // 清理之前可能未成功删除的视频文件
        cleanPendingVideoFiles()

        if (user()?.isGuild == true || user()?.isDisable == true) {
            bindingView.navigation.inflateMenu(R.menu.menu_main_guild_bottom_nav)
        } else {
            bindingView.navigation.inflateMenu(R.menu.menu_main_bottom_nav)
        }
        RongConfigCenter.featureConfig().rc_translation_src_language = "zh_CN"
        RongConfigCenter.featureConfig().rc_translation_target_language = BaseApp.getAppLanguage()
        bindingView.mainActivity = this@MainActivity
        ZlUpdateHelper.update(mContext)
        if (user()?.isAnchor == true && user()?.isDisable == false) {
            setBadge()
        }
        initRxBus()
        bindingView.viewPager.addOnPageChangeListener(this@MainActivity)

        viewModel.check()

//        初始化下載礼物资源
        ResourceManager.manager(this).init()

        if (SharePreUtil.isFirstLogin()) {
            // 第一次登陆成功上报数据
            viewModel.reportLoginSuccess()
        }

        bindingView.navigation.itemIconTintList = null
        SharePreUtil.setFirstDuyayaTab(false)
        showContentView()
    }

    override fun onResume() {
        super.onResume()
        viewModel.getAndInsertFilterWords()
        checkStartVideoChat()

        viewModel.getAnchorDetail().observe(this, {
            if (it.payInfoStatus != "1") {
                checkRegistrationState(it)
            }
        })
    }

    private fun checkRegistrationState(userBean: LoginBean?) {
        //是否完善注册基本信息
        if (userBean?.nickName == userBean?.userCode || userBean?.birthday.isNullOrEmpty() || userBean?.unionId.isNullOrEmpty()) {
            jump(BuildProfileActivity::class.java)
            return
        }

        val context = RegistrationContext()
        context.setState(AvatarApprovalState())
        context.process()

        if (!context.getState().isCompleted()) {
            when (context.getState()) {
                is AvatarApprovalState -> {
                    Logger.e("头像审核未通过，跳转到编辑信息页面")
                    SingleTipDialog.showTips(
                        mContext,
                        getResources().getString(R.string.Operation_Tips),
                        when {
                            context.getState()
                                .isReviewing() -> getString(R.string.avatar_is_under_review)

                            else -> userBean?.headFileNameRemark.takeIf { !it.isNullOrBlank() }
                                ?: getString(R.string.avatar_approval_failed)
                        },
                        getString(R.string.Confirm),
                        R.mipmap.base_ic_tips_bind_device,
                        false) {
                        jump(ProfileActivity::class.java)
                    }
                }

                is NicknameApprovalState -> {
                    Logger.e("昵称审核未通过，跳转到编辑信息页面")
                    SingleTipDialog.showTips(
                        mContext,
                        getResources().getString(R.string.Operation_Tips),
                        when {
                            context.getState()
                                .isReviewing() -> getString(R.string.nickname_is_under_review)

                            else -> userBean?.nickNameRemark.takeIf { !it.isNullOrBlank() }
                                ?: getString(R.string.nickname_approval_failed)
                        },
                        getString(R.string.Confirm),
                        R.mipmap.base_ic_tips_bind_device,
                        false) {
                        jump(ProfileActivity::class.java)
                    }
                }

                is AlbumUploadState -> {
                    Logger.e("相册审核未通过，跳转到相册管理页面")
                    val content = when {
                        context.getState()
                            .isMissing() -> getString(R.string.please_upload_your_album_first)

                        context.getState().isReviewing() -> {
                            val inReviewCount =
                                userBean.anchorFileList?.filter { it.status == 0 }?.size ?: 0
                            val approvedCount =
                                userBean.anchorFileList?.filter { it.status == 1 }?.size ?: 0

                            if (approvedCount < 5 && inReviewCount <= 0) {
                                getString(
                                    R.string.please_continue_to_upload_more_photos_or_videos,
                                    5 - approvedCount
                                )
                            } else {
                                getString(R.string.album_is_under_review)
                            }
                        }

                        else -> getString(R.string.album_approval_failed)
                    }

                    SingleTipDialog.showTips(
                        mContext,
                        getResources().getString(R.string.Operation_Tips),
                        content,
                        getString(R.string.Confirm),
                        R.mipmap.base_ic_tips_bind_device,
                        false
                    ) {
                        jump(AlbumManageActivity::class.java)
                    }
                }

                is FaceVideoApprovalState -> {
                    Logger.e("人脸录制视频审核未通过，重新上传人脸视频")
                    val content = when {
                        context.getState()
                            .isMissing() -> getString(R.string.please_upload_your_face_video_first)

                        context.getState()
                            .isReviewing() -> getString(R.string.face_video_is_under_review)

                        else -> getString(R.string.face_video_approval_failed_please_re_upload)
                    }

                    SingleTipDialog.showTips(
                        mContext,
                        getResources().getString(R.string.Operation_Tips),
                        content,
                        if (context.getState().isReviewing()) "" else getString(R.string.Confirm),
                        R.mipmap.base_ic_tips_bind_device,
                        false
                    ) {
                        jump(RecordVideoActivity::class.java)
                    }
                }

                is BindCardState -> {
                    SingleTipDialog.showTips(
                        mContext,
                        getResources().getString(R.string.Operation_Tips),
                        getString(R.string.please_bind_your_bank_card_first),
                        getString(R.string.Confirm),
                        R.mipmap.base_ic_tips_bind_device,
                        false
                    ) {
                        jump(BankBindActivity::class.java)
                    }
                }
            }
        }
    }


    // port-mode only
    fun onPageSelectChangedPort(index: Int) {
        for (position in 0..index) {
            if (bindingView.navigation.visibility == View.VISIBLE) bindingView.navigation.menu.getItem(
                position
            ).isChecked = index == position
        }
    }


    // port-mode only
    fun onBottomNavigationSelectChanged(menuItem: MenuItem) {
        when (menuItem.itemId) {
            R.id.nav_workbench -> {
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE02, 0)
                bindingView.viewPager.currentItem = 0
            }

            R.id.nav_discover -> {
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE02, 0)
                bindingView.viewPager.currentItem = 1
            }

            R.id.nav_message -> {
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE03, 0)
                bindingView.viewPager.currentItem = 2
            }

            R.id.nav_user -> {
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)
                bindingView.viewPager.currentItem = 3
            }
        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(false, 0.2f)
            .navigationBarColor(R.color.colorTheme).init()
    }

    override fun onPageScrollStateChanged(state: Int) {
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_MAIN_TAB_INDEX, position.toString())

        when (position) {
//            0 -> {
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_WORKBENCH, "0")
//                bindingView.navigation.selectedItemId = R.id.nav_workbench
//            }
//
//            1 -> {
//                bindingView.navigation.selectedItemId = R.id.nav_discover
//            }
//
//            2 -> {
//                bindingView.navigation.selectedItemId = R.id.nav_course
//            }
//
//            3 -> {
//                bindingView.navigation.selectedItemId = R.id.nav_schedule
//            }
//
//            4 -> {
//                bindingView.navigation.selectedItemId = R.id.nav_user
//                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_REFRESH_MIKCHAT, 0)
//            }

            0 -> {
                bindingView.navigation.selectedItemId = R.id.nav_workbench
            }

            1 -> {
                bindingView.navigation.selectedItemId = R.id.nav_discover
            }

            2 -> {
                bindingView.navigation.selectedItemId = R.id.nav_message
            }

            3 -> {
                bindingView.navigation.selectedItemId = R.id.nav_user
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_REFRESH_MIKCHAT, 0)
            }
        }
    }

    /**
     * BottomNavigationView显示角标
     * 自定义布局实现
     *
     * @param viewIndex tab索引
     * @param showNumber 显示的数字，小于等于0是将不显示
     */
    @SuppressLint("RestrictedApi")
    private fun showBadgeView(viewIndex: Int, showNumber: Int) {
        if (0 > showNumber || showNumber == 0) {
            hideBadgeView(viewIndex)
        } else {
            val menuView = bindingView.navigation.getChildAt(0) as BottomNavigationMenuView
            if (viewIndex < menuView.childCount) {
                hideBadgeView(viewIndex)
                val view = menuView.getChildAt(viewIndex) as BottomNavigationItemView
                if (badge == null) {
                    badge =
                        LayoutInflater.from(this).inflate(R.layout.app_layout_badge, view, false)
                }
                val msgCount = badge!!.findViewById<TextView>(R.id.textViewMsgCount)
                val msgRedPoint = badge!!.findViewById<TextView>(R.id.textViewPoint)
                if (showNumber > 99) {
                    msgCount.visibility = View.GONE
                    msgRedPoint.visibility = View.VISIBLE
                } else {
                    msgCount.text = showNumber.toString()
                    msgCount.visibility = View.VISIBLE
                    msgRedPoint.visibility = View.GONE
                }
                view.addView(badge)
            }
        }
    }

    @SuppressLint("RestrictedApi")
    private fun hideBadgeView(viewIndex: Int) {
        val menuView = bindingView.navigation.getChildAt(0) as BottomNavigationMenuView
        if (viewIndex < menuView.childCount) {
            val view = menuView.getChildAt(viewIndex) as BottomNavigationItemView
            if (badge != null) view.removeView(badge)
        }
    }

    @SuppressLint("SuspiciousIndentation", "AutoDispose")
    private fun initRxBus() {
        RxBus.getDefault().toObservable(
            RxCodeConstants.JUMP_TYPE_SEND_ASK_GIFT, GiftBean.DataDTO.GiftslistDTO::class.java
        ).`as`(autoDisposable(scopeProvider)).subscribe { giftBean ->
            val nickName = user()!!.nickName
            val textMessage = MikChatAskGiftMessage.obtain(
                nickName,
                giftBean.giftCode,
                giftBean.num.toString(),
                giftBean.giftIcon,
                giftBean.giftName,
                giftBean.giftPrice.toString(),
                giftBean.id
            )

            val message = Message.obtain(
                ConversationIdentifier.obtain(
                    Conversation.ConversationType.PRIVATE, giftBean.userId.toString(), ""
                ), textMessage
            )

            val loginBean = user()
            loginBean!!.role = "1"
            textMessage.extra = GsonUtil.GsonString(loginBean)
            IMCenter.getInstance().sendMessage(
                message, "Ask for " + giftBean.giftName + " x " + giftBean.num, null, null
            )
        }
        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_BLOCK, Long::class.java)
            .observeOn(ui).`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe { id: Long ->
                //删除会话
                IMCenter.getInstance().removeConversation(
                    Conversation.ConversationType.PRIVATE,
                    id.toString(),
                    object : RongIMClient.ResultCallback<Boolean?>() {
                        override fun onSuccess(aBoolean: Boolean?) {
                            Logger.e("block anchor remove conversation success")
                        }

                        override fun onError(e: RongIMClient.ErrorCode) {
                            Logger.e("block anchor remove conversation failed,code: \${errorCode.code}, msg:\${errorCode.msg}")
                        }
                    })
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE, RxBusBaseMessage::class.java)
            .`as`<ObservableSubscribeProxy<RxBusBaseMessage>>(autoDisposable(scopeProvider))
            .subscribe { finish() }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_TO_FIVE, Integer::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { value ->
                bindingView.navigation.menu.getItem(value.toInt()).setChecked(true)
                onPageSelectChangedPort(value.toInt())
                bindingView.viewPager.currentItem = value.toInt()
            }
        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_REFRESH_UNREAD_COUNT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { value ->
//                    println("JUMP_TYPE_TO_REFRESH_UNREAD_COUNT")
            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE0_CLEAN_MAIN_INTENT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { value ->
                <EMAIL> = null
            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_CHATTIP, ChatTipBean::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { value ->
//                <EMAIL> = null
                val tipView = ChatTipView(this@MainActivity)
                //切换至主线程执行
                tipView.setTitle(value.nickName)
//                        tipView.setType(currentMessage.messageType);
                tipView.setContent(value.content)
                tipView.userId = value.id
                tipView.setPic(value.headFileName)
                ChatTipManager.getInstance()
                    .show(this@MainActivity as AppCompatActivity?, tipView, value)
            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_RONGYUN_CACHE, UserInfo::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { value ->
                RongUserInfoManager.getInstance().refreshUserInfoCache(value)
            }


        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { str ->

                val hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(str, HashMap::class.java) as HashMap<String, String>
                val nickName = hashMap["nickName"]
                val headFileName = hashMap["headFileName"]
                val id = hashMap["id"]


//                val userInfo = UserInfo(id, nickName, Uri.parse(headFileName))
//                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
//                    println("JUMP_TYPE_TO_CONVERSATION")
//                    RouteUtils.routeToConversationActivity(this@MainActivity, Conversation.ConversationType.PRIVATE, id.toString(), false);
                RouteUtils.routeToConversationActivity(
                    this@MainActivity, Conversation.ConversationType.PRIVATE, id, false
                );
            }

        //收到礼物调用后台发送感谢话术
        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_GET_GIFT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { str ->

                val hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(str, HashMap::class.java) as HashMap<String, String>
                val userId = hashMap["userId"]
                Logger.e("vvvvvvvv userId $userId")
                viewModel.anchorScenarioScript(this, userId)
            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_REFRESH_USERINFO_CACHE, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(autoDisposable(scopeProvider)).subscribe { str ->
                var hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(str, HashMap::class.java) as HashMap<String, String>
                var nickName = hashMap["nikeName"]
                var groundFileName = hashMap["headFileName"]
                var id = hashMap["id"]

                val userInfo = UserInfo(id, nickName, Uri.parse(groundFileName))
                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
            }

        RxBus.getDefault()
            .toObservable<String>(RxCodeConstants.JUMP_TYPE_SEND_VIDEO_MSG, String::class.java)
            .observeOn(ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe { hashMapStr: String? ->
                val hashMap: java.util.HashMap<String, String> = GsonUtil.GsonToBean(
                    hashMapStr, java.util.HashMap::class.java
                ) as HashMap<String, String>
                val id = hashMap["id"]
                val time = hashMap["time"]
                val conversationType: Conversation.ConversationType =
                    Conversation.ConversationType.PRIVATE
                val sentStatus = SentStatus.SENT
                val nickName: String = UserInfoManager.user()?.nickName ?: ""
                val textMessage: MikChatVideoCallMessage =
                    MikChatVideoCallMessage.obtain(id, "2", time)

                val loginBean = UserInfoManager.user()
                loginBean?.role = "2"
                textMessage.extra = GsonUtil.GsonString(loginBean)
                val sentTime: Long = System.currentTimeMillis()
                IMCenter.getInstance().insertOutgoingMessage(
                    conversationType,
                    id,
                    sentStatus,
                    textMessage,
                    sentTime,
                    object : RongIMClient.ResultCallback<Message>() {
                        override fun onSuccess(message: Message) {
                        }

                        override fun onError(errorCode: RongIMClient.ErrorCode) {
                            Logger.e("insertOutgoingMessage ${errorCode.code}  ${errorCode.message}")
                        }
                    })

            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_CHAT_MSG_SEND, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe({ str: String? ->
                val hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(str, HashMap::class.java) as HashMap<String, String>
                val targetId = hashMap["id"]
                val content = hashMap["content"]
                val conversationType = Conversation.ConversationType.PRIVATE
                val messageContent = TextMessage.obtain(content)
                val loginBean = user()
                loginBean?.role = "1"

                messageContent.extra = GsonUtil.GsonString(loginBean)
                val message = Message.obtain(targetId, conversationType, messageContent)
                RongIMClient.getInstance().sendMessage(message, null, null, null)
            }) { throwable: Throwable? -> }



        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_APP_FOREGROUND, String::class.java)
            .observeOn(RxSchedulers.ui)
            .`as`<ObservableSubscribeProxy<String>>(AutoDispose.autoDisposable(scopeProvider))
            .subscribe { boo: String ->
                if (this@MainActivity == null || <EMAIL>) {
                    return@subscribe
                }
                println("ActivityLifeCallbackSdk  CallWindowServices  unbindService 00 boo:" + boo)

                if (boo == "1") {
                    println("ActivityLifeCallbackSdk CallWindowServices unbindService 01")

                    //不显示悬浮框
                    if (hasBind) {
                        println("ActivityLifeCallbackSdk CallWindowServices unbindService 02")
                        unbindService(mVideoServiceConnection)
                        hasBind = false
                    }
                } else {
                    println("ActivityLifeCallbackSdk CallWindowServices bindService")
                    if (!hasBind) {

//                        if (!MyAccessibilityService.isStart()) {
//                            try {
//                                startActivity(Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS))
//                            } catch (e: Exception) {
//                                startActivity(Intent(Settings.ACTION_SETTINGS))
//                                e.printStackTrace()
//                            }
//                        }

                        println("ActivityLifeCallbackSdk CallWindowServices bindService true")
                        val intent = Intent(this@MainActivity, CallWindowServices::class.java)
//                        startService(intent)
//                        val intent2 = Intent(this@MainActivity, AppStartListenerService::class.java)
//                        startService(intent2)
                        hasBind = bindService(intent, mVideoServiceConnection, BIND_AUTO_CREATE)

//                        // 获取 AlarmManager 实例
//                        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
//
//// 创建一个 PendingIntent，用于与闹钟关联
//                        val intent02 = Intent(this, AlarmReceiver::class.java)
//                        val pendingIntent = PendingIntent.getBroadcast(this, 0, intent02, PendingIntent.FLAG_IMMUTABLE)
//
//// 设置闹钟触发时间（这里是设置为当前时间后的 10 秒）
//                        val calendar: Calendar = Calendar.getInstance()
//                        calendar.setTimeInMillis(System.currentTimeMillis())
//                        calendar.add(Calendar.SECOND, 3)
//// 设置闹钟类型为 RTC_WAKEUP，确保在设备睡眠时也能触发
//                        alarmManager[AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis()] = pendingIntent
//                        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC, calendar.getTimeInMillis(), pendingIntent);

                    }
                }
            }


        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_CANCEL, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(
                    AndroidLifecycleScopeProvider.from(
                        this, Lifecycle.Event.ON_DESTROY
                    )
                )
            ).subscribe({ hashMapStr: String? ->
                isContainCallVideo = true
            }) { throwable: Throwable? -> }

        RxBus.getDefault().toObservable(
            RxCodeConstants.JUMP_TYPE_UPLOAD_VIRTUAL_VIDEO, RecordVideoBean::class.java
        ).observeOn(RxSchedulers.ui).`as`(
            autoDisposable(
                AndroidLifecycleScopeProvider.from(
                    this, Lifecycle.Event.ON_DESTROY
                )
            )
        ).subscribe({ recordVideoBean: RecordVideoBean? ->
            recordVideoBean?.let { bean ->
                try {
                    val videoFile = File(bean.videoUrl)
                    if (!videoFile.exists()) {
                        Logger.e("Video file does not exist: ${bean.videoUrl}")
                        return@let
                    }

                    // 记录文件路径到SharedPreferences，以便在上传失败时也能清理
                    val sharedPreferences =
                        getSharedPreferences("video_files", Context.MODE_PRIVATE)
                    val editor = sharedPreferences.edit()
                    val fileKey = "pending_upload_${System.currentTimeMillis()}"
                    editor.putString(fileKey, bean.videoUrl)
                    editor.apply()

                    val videos = mutableListOf<LocalMedia>()
                    videos.add(LocalMedia().apply {
                        realPath = bean.videoUrl
                        mimeType = PictureMimeType.MIME_TYPE_VIDEO
                    })

                    editViewModel.uploadVideo(videos)?.observe(this) { result ->
                        try {
                            user()?.id?.let { id ->
                                viewModel.anchorAddVideo(id, result.url, bean.channelId)
                            }

                            // 上传成功后，删除文件和记录
                            if (videoFile.exists()) {
                                val deleted = videoFile.delete()
                                if (deleted) {
                                    Logger.i("Successfully deleted video file after upload: ${bean.videoUrl}")
                                    // 删除成功，从 SharedPreferences 中移除该条目
                                    editor.remove(fileKey)
                                    editor.apply()
                                } else {
                                    Logger.e("Failed to delete video file after upload: ${bean.videoUrl}")
                                }
                            }
                        } catch (e: Exception) {
                            Logger.e("Error processing uploaded video: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Logger.e("Error uploading video: ${e.message}")
                    // 如果发生错误，尝试删除文件
                    try {
                        val file = File(bean.videoUrl)
                        if (file.exists()) {
                            file.delete()
                        }
                    } catch (e2: Exception) {
                        Logger.e("Failed to delete video file after error: ${e2.message}")
                    }
                }
            }
        }) { throwable: Throwable? ->
            Logger.e("Error in JUMP_TYPE_UPLOAD_VIRTUAL_VIDEO subscription: ${throwable?.message}")
        }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_START_VIDEO, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe({ hashMapStr: String? ->
                Logger.d("onReceivedMessageaa  JUMP_TYPE_START_VIDEO ")

                val hashMap =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, String>
                val bean = AnchorItemBean.RecordsDTO()
                bean.headFileName = hashMap["headFileName"]
                bean.nickName = hashMap["nikeName"]
                bean.followFlag = hashMap["followFlag"]
                bean.id = hashMap["id"]?.toLong()
                bean.channelID = hashMap["channelID"]
                bean.userRole = hashMap["userRole"]
                bean.newUserFlag = hashMap["newUserFlag"]
                Logger.e("vvvvvvvvvv ${hashMap["recordingStartTime"]}, ${hashMap["recordingEndTime"]}")

                val homeListViewmodel: HomeListViewmodel by lazy {
                    HomeListViewmodel(mContext.application).apply {
                        initFliterAttribute(
                            mContext as FragmentActivity
                        )
                    }
                }
                homeListViewmodel.getCallAnchor(hashMap["channelID"] ?: "", "1").observeForever {
                    Logger.d(
                        "vvvvvvonReceivedMessageaa  JUMP_TYPE_START_VIDEO  state:" + it.toString()
                            .toInt()
                    )

                    if (it.toString().toInt() == 0) {

                        Handler().postDelayed({
//                            println("DemoHmsMessageService666")
//                            val notification =
//                                NotificationCompat.Builder(this@MainActivity, "my_channel_ID")
//                                    .setContentTitle("Call")
//                                    .setContentText("${bean.nickName} Video call request...")
//                                    .setContentIntent(pendingIntent)
//                                    .setSmallIcon(mikchat.app.discover.R.mipmap.ic_launcher)
//                                    .setPriority(NotificationCompat.PRIORITY_MAX)
//                                    .setAutoCancel(true)
//                                    .setVibrate(longArrayOf(0,5000,0,5000,0,5000))//设置的震动是无效的只能是默认震动
////                                .setDefaults(Notification.DEFAULT_ALL)
//                                    .setWhen(System.currentTimeMillis())
//                            notificationManager?.notify(System.currentTimeMillis().toInt(), notification.build())

                            Logger.d("onReceivedMessageaa  JUMP_TYPE_START_VIDEO  isForeground :" + BaseApp.isForeground())

                            if (BaseApp.isForeground()) {
                                jump(CallVideoChatActivity::class.java, Bundle().apply {
                                    putLong("id", hashMap["id"]?.toLong() ?: 0)
                                    putInt("type", 0)
                                    putString("channelID", hashMap["channelID"])
                                    putString(
                                        "recordingStartTime", hashMap["recordingStartTime"] ?: "0"
                                    )
                                    putString(
                                        "recordingEndTime", hashMap["recordingEndTime"] ?: "0"
                                    )
                                    putString("beanstr", hashMapStr)
//                                    putSerializable("bean", bean)
                                })
                            }
                            //不显示悬浮框
//                            if (hasBind) {
//                                unbindService(mVideoServiceConnection)
//                                hasBind = false
//                            }
                        }, 100)

                        //TODO 注释掉通知
//                        val intent = Intent(this@MainActivity, MainActivity::class.java)
//                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//                        intent.putExtra("id", bean.id.toLong())
//                        intent.putExtra("type", 0)
//                        intent.putExtra("channelID", hashMap["channelID"])
//                        intent.putExtra("bean", bean)
//                        val pendingIntent = PendingIntent.getActivity(
//                            this@MainActivity, 0, intent, PendingIntent.FLAG_IMMUTABLE
//                        )
////                        if (BaseApp.isForeground()) {
////                            <EMAIL> = null
////                        }else{
//                        <EMAIL> = intent
////                        }
//
//                        var count = 0
//
//                        RxTimerUtil.interval(this@MainActivity, 1) {
////                            isContainCallVideo = false
//
//                            val stack = ActivityUtil.getStack()
//                            for (i in stack.indices) {
//                                if (ActivityUtil.getStack()[i].componentName.className == "com.mobile.anchor.app.discover.main.chat.videochat.CallVideoChatActivity") {
//                                    isContainCallVideo = true
//                                }
//                            }
//
////                        当在视频聊天的时候,直接阻断
//                            if (isContainCallVideo || count >= 35) {
//                                RxTimerUtil.cancel(this@MainActivity)
//                                isContainCallVideo = false
//                                return@interval
//                            }
//                            count++
////                            var vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
////                            vibrator.vibrate(longArrayOf(0, 1300),0)
//                            NotificationUtil.getInstance().showNotification(
//                                this,
//                                "Call",
//                                "${bean.nickName} Video call request...",
//                                pendingIntent,
//                                132321
//                            )
//                        }
                    } else {
                        ToastUtil.show("CallAnchor  state:" + it.toString().toInt())
                    }
                }
            }) { throwable: Throwable? -> }
    }

    var isContainCallVideo = false

    var mVideoServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            // 获取服务的操作对象
//            val binder: CallWindowServices.MyBinder = service as MyBinder
//            binder.getService()
        }

        override fun onServiceDisconnected(name: ComponentName) {}
    }

    private fun checkStartVideoChat() {
        LoadingDialog.getInstance(this).cancel()
        val bean = intent?.getSerializableExtra("bean")
        if (bean == null) {
            return
        } else {
            println("onNewIntentonNewIntentonNewIntent bean != null")
        }
        <EMAIL>?.let {
            val bean = <EMAIL>("bean")
            if (bean != null) {
                val homeListViewmodel: HomeListViewmodel by lazy {
                    HomeListViewmodel(mContext.application).apply {
                        initFliterAttribute(
                            mContext as FragmentActivity
                        )
                    }
                }
                val recordsDTO = bean as AnchorItemBean.RecordsDTO
                homeListViewmodel.getCallAnchor(recordsDTO.channelID, "1").observe(this) {
                    if (it.toString().toInt() == 0) {
                        jump(CallVideoChatActivity::class.java, Bundle().apply {
                            putLong("id", recordsDTO.id)
                            putString("channelID", recordsDTO.channelID)
                            putInt("type", 0)
                            putSerializable("bean", recordsDTO)
                        })
                    } else {
                        ToastUtil.show("Call is hung up...")
                        <EMAIL> = null
                    }
                }
            }
        }
    }

    private var hasBind = false

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_HOME || keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_MENU) {
            moveTaskToBack(true)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    /**
     * 清理之前可能未成功删除的视频文件
     */
    private fun cleanPendingVideoFiles() {
        try {
            val sharedPreferences = getSharedPreferences("video_files", Context.MODE_PRIVATE)
            val allEntries = sharedPreferences.all

            if (allEntries.isNotEmpty()) {
                val editor = sharedPreferences.edit()

                // 遍历所有待删除的文件路径
                for ((key, value) in allEntries) {
                    if (value is String) {
                        val file = File(value)
                        if (file.exists()) {
                            val deleted = file.delete()
                            if (deleted) {
                                // 如果删除成功，从 SharedPreferences 中移除该条目
                                editor.remove(key)
                                Logger.i("Successfully deleted pending video file: $value")
                            } else {
                                Logger.e("Failed to delete pending video file: $value")
                            }
                        } else {
                            // 文件不存在，从 SharedPreferences 中移除该条目
                            editor.remove(key)
                        }
                    }
                }

                // 提交更改
                editor.apply()
            }
        } catch (e: Exception) {
            Logger.e("Error cleaning pending video files: ${e.message}")
        }
    }
}