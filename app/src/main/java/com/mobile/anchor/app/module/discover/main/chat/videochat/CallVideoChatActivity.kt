package com.mobile.anchor.app.module.discover.main.chat.videochat

import anchor.app.base.BaseApp
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.core.Constants.APP_REGITEST_ID
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.OperationTipDialog
import anchor.app.base.dialog.SelectTipDialog
import anchor.app.base.dialog.SelectTipDialog.ISimpleTipsClickListener
import anchor.app.base.ext.jump
import anchor.app.base.ext.loadImage
import anchor.app.base.ext.makeVisible
import anchor.app.base.manager.LoadGiftManager
import anchor.app.base.manager.LoadGiftManager.Companion.mgiftManager
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.ActivityUtils
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.IntentUtils
import anchor.app.base.utils.Logger
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.RxTimerUtil
import anchor.app.base.utils.RxViewUtils
import anchor.app.base.utils.SharePreUtil.setVideoChatID
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import anchor.app.base.view.gift.GiftNumInputPopupWindow
import anchor.app.base.word.WordFilter
import android.Manifest
import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.os.Vibrator
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.DisplayMetrics
import android.view.MotionEvent
import android.view.TextureView
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.annotation.RequiresApi
import androidx.core.text.isDigitsOnly
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.faceunity.core.faceunity.FURenderKit
import com.faceunity.wrapper.faceunity
import com.hjq.language.MultiLanguages
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverHomeVideoChatCallBinding
import com.mobile.anchor.app.databinding.DiscoverVideoChatItemBinding
import com.mobile.anchor.app.module.discover.bean.HomeListItemBean
import com.mobile.anchor.app.module.discover.bean.RecordVideoBean
import com.mobile.anchor.app.module.discover.faceunity.utils.nama.FURenderer
import com.mobile.anchor.app.module.discover.main.center.PersonalCenterActivity
import com.mobile.anchor.app.module.discover.main.center.report.ReportActivity
import com.mobile.anchor.app.module.discover.main.chat.videochat.adapter.VideoChatItemAdapter
import com.mobile.anchor.app.module.discover.main.chat.videochat.dialog.MoreDialogFragment
import com.mobile.anchor.app.module.discover.main.chat.videochat.result.VideoResultActivity
import com.mobile.anchor.app.module.discover.main.homelist.HomeListViewmodel
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import com.mobile.anchor.app.module.user.wallet.WalletActivity2
import com.permissionx.guolindev.PermissionX
import com.permissionx.guolindev.request.ExplainScope
import com.permissionx.guolindev.request.ForwardScope
import com.uber.autodispose.AutoDispose
import io.agora.rtc2.AgoraMediaRecorder
import io.agora.rtc2.AgoraMediaRecorder.MediaRecorderConfiguration
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants
import io.agora.rtc2.Constants.VideoSourceType
import io.agora.rtc2.IMediaRecorderCallback
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RecorderInfo
import io.agora.rtc2.RecorderStreamInfo
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.BeautyOptions
import io.agora.rtc2.video.VideoCanvas
import io.rong.imkit.picture.config.PictureConfig
import io.rong.imkit.picture.tools.PictureFileUtils
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.translation.TranslationClient
import jp.wasabeef.glide.transformations.BlurTransformation
import org.json.JSONObject
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.io.IOException
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.Objects
import kotlin.math.abs
import androidx.core.view.isGone


/**
 * ..
 */
class CallVideoChatActivity : BaseActivity<VideoChatViewModel?, DiscoverHomeVideoChatCallBinding>(),
    ItemClickPresenter<Any>, ItemDecorator {
    private var localSurfaceView: TextureView? = null
    private var remoteSurfaceView: TextureView? = null
    var id: Long = 0
    var type: Int = 1

    var bean: AnchorItemBean.RecordsDTO? = null
    var localVideoRID = R.id.local_video_view_container
    var remoteVideoRID = R.id.remote_video_view_container
    var enoughFlag = 1
    var handler = MyHandler(this)
    var isWaiting = true

    var adapter: VideoChatItemAdapter? = null
    var dataList: ObservableArrayList<HomeListItemBean> = ObservableArrayList<HomeListItemBean>()

    private var recordingStartTime = 0;
    private var recordingEndTime = 0;
    private var agoraMediaRecorder: AgoraMediaRecorder? = null


    inner class MyHandler(activity: CallVideoChatActivity) : Handler() {
        //弱引用，在垃圾回收时，activity可被回收
        val mWeakReference: WeakReference<CallVideoChatActivity>

        init {
            mWeakReference = WeakReference(activity)
        }

        @SuppressLint("ServiceCast")
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {

                1 -> {
                    handler.sendEmptyMessageDelayed(1, 60000)

                    homeListViewmodel.getCallAnchor(channelID, "4").observeForever {
                        if (it.toString().toInt() != 1) {
                            finishActivity(true)
                        }
                    }
                }

                2 -> if (bindingView!!.CLLeave.visibility == View.VISIBLE) {
                    handler.sendEmptyMessageDelayed(2, 3000)
                    vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator

                    val pattern = longArrayOf(1000L, 1000L) // 关 1000 毫秒，开 1000 毫秒
                    vibrator?.vibrate(pattern, 0)
                    if (!isWaiting) {
                        vibrator?.cancel()
                    }
                }

                3 -> {
                    if (isFilstFlag) {
                        isFilstFlag = false
                        isWaiting = false
//                    if(mPlayer.isPlaying()){
//                        mPlayer.stop();
//                    }
////                    mPlayer.stop()

                        vibrator?.cancel()
                        mPlayer.release()
//                        reverseVideo()
                        bindingView!!.CLLeave.visibility = View.GONE
                        LoadingDialog.getInstance(mContext).dismiss()
                        initTimer()
                    }
                }

                4 -> {
                    OtherManager.Companion.manager(this@CallVideoChatActivity).sysParam.value?.get("faceLeaveTime")
                        ?.toString()?.toDouble()?.toInt()?.let {
                            if (currentime <= it && ActivityUtils.isDestroy(this@CallVideoChatActivity)
                                    .not()
                            ) {

                                if (faceunity.fuIsTracking() == 0) {
                                    ToastUtil.show("Keep your face not away from the camera")
                                    localSurfaceView?.bitmap.apply {
                                        var saveBitmapFile =
                                            CameraUtil(this@CallVideoChatActivity).saveBitmapFile(
                                                this
                                            )
                                        onResultDeal(saveBitmapFile)
                                        //                            bindingView!!.isFollow.setImageBitmap(this)
                                    }

                                } else {
                                    var result = FURenderer.getInstance().trackFaceInfo()
                                    //                        var result = faceUnityBeautyAPI.trackFaceInfo()
                                    //                        val result = FloatArray(4)
                                    //                        fuRenderKit?.FUAIController?.getFaceInfo(0, "face_rect",result)
                                    //                        if(result[0]<bindingView!!.face.left || result[1]<bindingView!!.face.top || result[2]>bindingView!!.face.right || result[3]>bindingView!!.face.bottom)
                                    if (result[0] < bindingView!!.face.left || result[3] < bindingView!!.face.top || (result[1].toInt() + result[3].toInt() - 75) > bindingView!!.face.right || (result[2].toInt() + result[3].toInt() + result[0].toInt()) > bindingView!!.face.bottom) ToastUtil.show(
                                        "Face to the screen, please"
                                    )
                                    println("mRtcEventHandler face: ${result[0].toInt()}  ${result[1].toInt()}  ${result[2].toInt()}  ${result[3].toInt()}  ")
                                    println("mRtcEventHandler face: ${result[0].toInt()}  ${result[3].toInt()}  ${result[1].toInt() + result[3].toInt() - 75}  ${result[2].toInt() + result[3].toInt() + result[0].toInt()}  ")
                                    println("mRtcEventHandler view: ${bindingView!!.face.left}  ${bindingView!!.face.top}  ${bindingView!!.face.right}  ${bindingView!!.face.bottom}  ")

                                    val mDisplayMetrics: DisplayMetrics =
                                        <EMAIL>()
                                            .getDisplayMetrics()
                                    val width = mDisplayMetrics.widthPixels
                                    val height = mDisplayMetrics.heightPixels
                                    println("mRtcEventHandler mDisplayMetrics: ${width}  ${height}")

                                    println("mRtcEventHandler =========================================")
                                }
                                OtherManager.Companion.manager(this@CallVideoChatActivity).sysParam.value?.get(
                                    "faceUploadTime"
                                )?.toString()?.toDouble()?.toInt()?.toLong()
                                    ?.let { handler.sendEmptyMessageDelayed(4, it * 1000) }
                            } else {
                                bindingView!!.RLFace.visibility = View.GONE
                            }
                        }


//                    handler.sendEmptyMessageDelayed(4, OtherManager.Companion.manager(this@CallVideoChatActivity).sysParam.value?.get("").toString().toLong())
                }
            }
        }
    }

    private fun onResultDeal(file: File?) {
        if (file != null) {
            //因为上传图片大小后端有限制 gif 大于3m 的时候就压缩成普通 普通图片大于100kb就压缩
            var mLeastCompressSize = 100
            if (file.path.lowercase(Locale.getDefault()).endsWith(".gif")) {
                mLeastCompressSize = 1024 * 3
            }
            Luban.with(this@CallVideoChatActivity).load(file.path).ignoreBy(mLeastCompressSize)
                .setTargetDir(PathUtils.getPathIMg())
                .filter { path: String? -> !TextUtils.isEmpty(path) }
                .setCompressListener(object : OnCompressListener {
                    override fun onStart() {}
                    override fun onSuccess(index: Int, f: File?) {
                        f.let {
                            OtherManager.Companion.manager(this@CallVideoChatActivity)
                                .uploadPicture(f)?.observe(this@CallVideoChatActivity) {
                                    if (it != "000") {
                                        OtherManager.Companion.manager(this@CallVideoChatActivity)
                                            .getVideoWarn(channelID, it ?: "")
                                    }
                                }
                        }
                    }

                    override fun onError(index: Int, e: Throwable?) {
                    }

                }).launch()
        }
    }


    var tempCount = 1
    override fun getLayoutId(): Int {
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED //锁屏显示
//                    or WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD //解锁
                    or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON //保持屏幕不息屏
                    or WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
        ) //点亮屏幕
        return R.layout.discover_home_video_chat_call
    }

    @SuppressLint("SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.N)
    override fun initView() {
//        (<EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager).cancel(16657)
        (<EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager).cancelAll()

        // 清理之前可能未成功删除的视频文件
        cleanPendingVideoFiles()

        id = intent.getLongExtra("id", 0)
        type = intent.getIntExtra("type", 1)
        bean = intent.getSerializableExtra("bean") as? AnchorItemBean.RecordsDTO
        channelID = intent.getStringExtra("channelID").toString()
        val tmpRecordingStartTime = intent.getStringExtra("recordingStartTime")
        val tmpRecordingEndTime = intent.getStringExtra("recordingEndTime")
        if (tmpRecordingStartTime != null && tmpRecordingEndTime != null && tmpRecordingEndTime.toInt() > 0) {
            recordingStartTime = tmpRecordingStartTime.toInt()
            recordingEndTime = tmpRecordingEndTime.toInt()
        }
        val beanStr = intent.getStringExtra("beanstr")
        if (!Objects.isNull(beanStr) && bean == null) {
            val beanJson = JSONObject(beanStr)
            beanJson.let {
                bean = AnchorItemBean.RecordsDTO()
                bean?.id = beanJson.opt("id")?.toString()?.toLongOrNull()
                bean?.country = beanJson.opt("country")?.toString() ?: ""
                bean?.headFileName = beanJson.opt("headFileName")?.toString() ?: ""
                bean?.nickName = beanJson.opt("nickName")?.toString() ?: ""
                bean?.onlineStatus = beanJson.opt("onlineStatus")?.toString() ?: ""
                bean?.followFlag = beanJson.opt("followFlag")?.toString() ?: ""
                bean?.videoPrice = beanJson.opt("videoPrice")?.toString()?.toInt() ?: 0
                bean?.level = beanJson.opt("level")?.toString() ?: ""
                bean?.vipFlag = beanJson.opt("vipFlag")?.toString() ?: ""
                bean?.vipExpireFlag = beanJson.opt("vipExpireFlag")?.toString() ?: ""
                bean?.setUserRole(beanJson.opt("role")?.toString() ?: "")
            }
        }

        // 保存当前正在通话中的对方的id 用在IMcenter中接收对方消息
        setVideoChatID(id.toString())

//        initFaceUnity()

        showContentView()
        bindingView?.activity = this
        bindingView?.lifecycleOwner = this
        bean?.let {
//            bindingView?.price01?.text = it.videoPrice.toString() + "/min"
//            bindingView?.price02?.text = it.videoPrice.toString() + "/min"
//            bindingView?.areaImg?.setImageDrawable(
//                CountryUtil.countryCodeToImage(
//                    mContext, it.country
//                )
//            )
//            bindingView?.areaName?.text = it.country
            bindingView?.anchorName?.text = it.nickName
            bindingView?.anchorName02?.text = it.nickName
            bindingView?.ivVip?.makeVisible(it.isVIP)
            if (it.level != null && it.level.isNotEmpty() && it.level.isDigitsOnly()) {
                bindingView?.levelLabelView?.current =
                    it.level.takeIf { it.isNotEmpty() }?.toInt() ?: 0
            } else {
                bindingView?.levelLabelView?.current = 0
            }

//            updateUserInfo()
//            bindingView?.LLRiskUser?.visibility =
//                if (it.userCategory == "1") View.VISIBLE else View.GONE
//            bindingView?.newP?.visibility = if (it.newUserFlag == "1") View.VISIBLE else View.GONE
//
//            if (it.newUserFlag == "1") {
//                bindingView?.newP?.visibility = View.VISIBLE
//            } else {
//                bindingView?.newP?.visibility = View.GONE
//            }
        }

        adapter = VideoChatItemAdapter(mContext, dataList)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

//        Glide.with(mContext).load(bean?.groundFileName).into(bindingView!!.head)
//        Glide.with(mContext).load(bean?.groundFileName).into(bindingView!!.head02)
        bean?.headFileName?.let {
            bindingView!!.head.setPicAndStaticHeadgear(bean?.headFileName, "", 34)
            Glide.with(this).load(bean?.headFileName)
                .placeholder(anchor.app.base.R.mipmap.ic_default_image)
                .error(anchor.app.base.R.mipmap.ic_default_image).transform(RoundedCorners(64))
                .into(bindingView.head02)
        }

        val container = findViewById<FrameLayout>(localVideoRID)
        // 创建一个 SurfaceView 对象，并将其作为 FrameLayout 的子对象。
        localSurfaceView = TextureView(baseContext)
        container.addView(localSurfaceView)
        val container2 = findViewById<FrameLayout>(remoteVideoRID)
        remoteSurfaceView = TextureView(baseContext)
//        remoteSurfaceView!!.setZOrderMediaOverlay(true)
        container2.addView(remoteSurfaceView)

        container2.setOnTouchListener(FloatingListener())


        bindingView!!.giftPanel.dismissGiftPanel()
        initP()
//        RxViewUtils.setOnClickListeners(findViewById(localVideoRID)) { view: View? ->
//            reverseVideo()
//        }
        RxViewUtils.setOnClickListeners(findViewById(remoteVideoRID)) { view: View? ->
//            reverseVideo()
        }
        println("mRtcEventHandler  initView ：")

        UserInfoManager.user()?.let {
            Glide.with(this)
                .load(it.headFileName)
                .transform(CenterCrop(), BlurTransformation(25, 3)) // 25是模糊度，3是采样数
                .into(bindingView!!.ivVideoBlur)
        }

//        RxViewUtils.setOnClickListeners(bindingView!!.ivVideoBlurStatus) { view: View? ->
//            if(bindingView!!.ivVideoBlur.isGone){
//                bindingView!!.ivVideoBlur.visibility = View.VISIBLE
//                bindingView!!.ivVideoBlurStatus.setImageResource(R.mipmap.ic_video_blur_status_close)
//            }else {
//                bindingView!!.ivVideoBlur.visibility = View.GONE
//                bindingView!!.ivVideoBlurStatus.setImageResource(R.mipmap.ic_video_blur_status_open)
//            }
//        }


//        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_TO_SEND_GIFT, GiftBean.DataDTO.GiftslistDTO::class.java)
//            .observeOn(RxSchedulers.ui)
//            .`as`(AutoDispose.autoDisposable(scopeProvider))
//            .subscribe({ giftBean: GiftBean.DataDTO.GiftslistDTO ->
//                Logger.v("VideoChatActivity JUMP_TYPE_TO_SEND_GIFT")
//                LoadGiftManager.giftManager(this@CallVideoChatActivity).sendGift(id,giftBean.num,giftBean.id,"0")
//                    .observe(this@CallVideoChatActivity) {
//                        bindingView!!.giftPanel.sendGift(giftBean)
//                    }
//            }) { throwable: Throwable? -> }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_CANCEL, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe({ hashMapStr: String? ->

                var hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, String>
                var bean = AnchorItemBean.RecordsDTO()

                if (channelID == hashMap["channelID"]) {
                    sendVideoMsg(false, getString(R.string.call_cancel))
                    Handler().postDelayed({ finishActivity(false) }, 1000)
                }
            }) { throwable: Throwable? -> }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_REJECT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe({ hashMapStr: String? ->

                var hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, String>
                var bean = AnchorItemBean.RecordsDTO()

                if (channelID == hashMap["channelID"]) {
                    sendVideoMsg(false, getString(R.string.call_reject))
                    Handler().postDelayed({ finishActivity(false) }, 1000)
                }
            }) { throwable: Throwable? -> }


        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_VIDEO_CHAT_MSG, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe({ str: String? ->
                var homeListItemBean = HomeListItemBean()
                homeListItemBean.content = str
                homeListItemBean.nickName = bean?.nickName
                homeListItemBean.id = bean?.id.toString()
                homeListItemBean.headFileName = bean?.headFileName
                homeListItemBean.level = "0"
                dataList.add(homeListItemBean)
                adapter!!.notifyDataSetChanged()
                bindingView!!.recyclerView.scrollToPosition(dataList.size - 1)
            }) { throwable: Throwable? -> }


//        RxBus.getDefault().toObservable<GiftBean.DataDTO.GiftslistDTO>(
//            RxCodeConstants.JUMP_TYPE_TO_SEND_ASK02, GiftBean.DataDTO.GiftslistDTO::class.java
//        ).observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
//            .subscribe { giftBean: GiftBean.DataDTO.GiftslistDTO ->
//                LoadGiftManager.giftManager(this@CallVideoChatActivity).askGift(id, giftBean.num, giftBean.giftCode)
//                    .observe(this@CallVideoChatActivity) {
//                        giftBean.userId = id
//                        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_SEND_ASK_GIFT, giftBean)
//                    }
//            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_GET_GIFT, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe({ hashMapStr ->
                val hashMap: HashMap<String, String> =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, String>
                val giftCode = hashMap["giftCode"]
                var giftIcon = ""
                for (dto in mgiftManager!!.data.value!!.data[0].giftslist) {
                    if (dto.giftCode == giftCode) {
                        giftIcon = dto.giftIcon
                        break
                    }
                }
                val homeListItemBean = HomeListItemBean().apply {
                    imageUrl = giftIcon
                    nickName = bean?.nickName
                    id = bean?.id.toString()
                    headFileName = bean?.headFileName
                    content = getString(R.string.b45)
                    level = "0"
                }
                dataList.add(homeListItemBean)
                adapter!!.notifyItemInserted(dataList.size - 1)
                bindingView!!.recyclerView.scrollToPosition(dataList.size - 1)

            }) { throwable: Throwable? -> }

        if (channelID.isNotEmpty() && channelID != "0") {
            viewModel?.videoMissedCall(channelID)
        }
    }

    var channelID = "0"

    var mPlayer = MediaPlayer()

    var vibrator: Vibrator? = null

    @RequiresApi(Build.VERSION_CODES.N)
    fun loadData2() {
        println("mRtcEventHandler  loadData1 ：")

        Thread {
            handler.postDelayed({
                handler.sendEmptyMessage(2) // 震动，主播段要
                try {
                    mPlayer.reset()
                    mPlayer = MediaPlayer.create(VideoChatActivity@ this, R.raw.startvideobg)
                    mPlayer.setOnPreparedListener { mPlayer.start() }
//                mPlayer.prepareAsync()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }, 200)
            println("mRtcEventHandler  loadData2.5 ：")
        }.start()

        println("mRtcEventHandler  loadData2 ：")

        Thread {
            handler.postDelayed({
                VideoChatActivity@ this?.apply {
                    if (!isDestroyed) {
                        if (isWaiting) {
                            finishActivity(false)
                        }
                    }
                }
            }, 35000)
        }.start()

        OtherManager.Companion.manager(this).getFollowFlag(id.toString()).observe(this) {
            bindingView.llFollow.visibility = View.VISIBLE
            bindingView.llFollowTop.visibility = View.VISIBLE
            bean?.followFlag = it
            setFollowView(it == "1")
        }

        updateUserInfo()
    }

    private fun updateUserInfo() {
        OtherManager.Companion.manager(this).getPersonalCenter(this, id, "2").observe(this) {
            bindingView.apply {
                anchorName.text = it.nickName
                anchorName02.text = it.nickName
                ivVip.makeVisible(it.isVIP)
                if (it.level != null && it.level.isNotEmpty() && it.level.isDigitsOnly()) {
                    bindingView?.levelLabelView?.current =
                        it.level.takeIf { it.isNotEmpty() }?.toInt() ?: 0
                } else {
                    bindingView?.levelLabelView?.current = 0
                }

                tvAge1.text = "${it.age}"
                tvAge2.text = "${it.age}"
                tvCountry1.text = it.country
                tvCountry2.text = it.country
                Glide.with(this@CallVideoChatActivity).load(it.headFileName)
                    .placeholder(anchor.app.base.R.mipmap.ic_default_image)
                    .error(anchor.app.base.R.mipmap.ic_default_image).transform(RoundedCorners(64))
                    .into(head02)
            }
        }
    }

    //设置follow btn ui
    private fun setFollowView(followed: Boolean) {
        bindingView.apply {
            if (followed) {
                val labelCancelFollow = getString(R.string.btn_unfollow)
                tvFollow.text = labelCancelFollow
                tvFollowTop.text = labelCancelFollow
                ivFollow.setImageResource(R.mipmap.ic_call_unfollow)
                ivFollowTop.setImageResource(R.mipmap.ic_call_unfollow)
                llFollow.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
                llFollowTop.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
            } else {
                val labelFollow = getString(R.string.btn_follow)
                tvFollow.text = labelFollow
                tvFollowTop.text = labelFollow
                ivFollow.setImageResource(R.mipmap.ic_follow)
                ivFollowTop.setImageResource(R.mipmap.ic_follow)
                llFollow.setBackgroundResource(R.drawable.shape_follow_btn_bg)
                llFollowTop.setBackgroundResource(R.drawable.shape_follow_btn_bg)
            }
        }

    }


    override fun onResume() {
        super.onResume()

        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE0_CLEAN_MAIN_INTENT, "0")

    }

    var currentSurplus = 59
    var currentime = 0
    private var saveFile: File? = null
    private fun initTimer() {
        RxTimerUtil.interval(this@CallVideoChatActivity, 1) {
            if (enoughFlag <= 0) {
                currentSurplus--
            }
            currentime++

            if (recordingEndTime > 0 && currentime == recordingStartTime) {
                // 使用应用的隐藏目录保存视频，避免被相册扫描
                val videoDir = File(PathUtils.getPathVideo())
                if (!videoDir.exists()) {
                    videoDir.mkdirs()
                }
                val fileName = "VID_${System.currentTimeMillis()}.mp4"
                saveFile = File(videoDir, fileName)

                val mediaRecorderConfiguration = MediaRecorderConfiguration(
                    saveFile?.absolutePath, // 存储路径
                    AgoraMediaRecorder.CONTAINER_MP4, AgoraMediaRecorder.STREAM_TYPE_BOTH, 120000, 0
                )

                agoraMediaRecorder?.startRecording(mediaRecorderConfiguration)
            }

            if (recordingEndTime > 0 && currentime == recordingEndTime) {
                agoraMediaRecorder?.stopRecording()
                uploadAndDeleteVideo()
            }

            val totalSeconds = currentime.toLong()
            val hours = totalSeconds / 3600
            val remainderSeconds = totalSeconds % 3600
            val minutes = remainderSeconds / 60
            val seconds = remainderSeconds % 60

            // 格式化输出，确保时、分、秒都保持两位数
            val formattedTime = if (hours > 0) {
                java.lang.String.format("%02d:%02d:%02d", hours, minutes, seconds)
            } else {
                java.lang.String.format("%02d:%02d", minutes, seconds)
            }
            bindingView?.timer?.text = formattedTime
//            bindingView!!.timer.text = DateTimeUtils.longToString(
//                (currentime * 1000).toLong(),
//                if (currentime < 1 * 60 * 60) DateTimeUtils.get_mmss() else DateTimeUtils.getHH_MM_SS()
//            ) // "00:"+currentSurplus
            if (currentSurplus <= 0) {
                finishActivity(true)
            } else if (currentSurplus <= 40) {
                val tipText =
                    Html.fromHtml("<font color='#F6B715' size='16'>The call will be up to one minute in $currentSurplus seconds. Please make sure you have enough balance.If the balance is insufficient, it will hang up automatically.</font><font color='#169BD5' size='26'>Top up        </font>")
                bindingView!!.tip.movementMethod = LinkMovementMethod.getInstance()
                val stringBuilder = SpannableStringBuilder(tipText)
                stringBuilder.setSpan(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        jump(WalletActivity2::class.java)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.color = Color.parseColor("#169BD5") // 字体颜色
                        //                            ds.setUnderlineText(false); // 是否有下划线
                    }
                }, 152, 158, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                bindingView!!.tip.text = stringBuilder
                bindingView!!.tip.visibility = View.VISIBLE
            }
        }
    }

    // 帮助函数，用于管理视频的上传和删除
    private fun uploadAndDeleteVideo() {
        saveFile?.let { file ->
            if (file.exists()) {
                try {
                    val recordVideoBean = RecordVideoBean(videoUrl = file.absolutePath, channelId = channelID)
                    RxBus.getDefault()
                        .post(RxCodeConstants.JUMP_TYPE_UPLOAD_VIRTUAL_VIDEO, recordVideoBean)

                    // 记录文件路径到SharedPreferences，以便在应用重启后也能清理
                    val sharedPreferences = getSharedPreferences("video_files", Context.MODE_PRIVATE)
                    val editor = sharedPreferences.edit()
                    editor.putString("pending_video_${System.currentTimeMillis()}", file.absolutePath)
                    editor.apply()
                } catch (e: Exception) {
                    Logger.e("uploadAndDeleteVideo error: ${e.message}")
                    // 如果发生错误，尝试删除文件
                    try {
                        if (file.exists()) {
                            file.delete()
                        }
                    } catch (e2: Exception) {
                        Logger.e("Failed to delete video file: ${e2.message}")
                    }
                }
            }
        }
    }


    private var mRtcEngine: RtcEngine? = null
    var uid = 0
    var isFilstFlag = true
    var remoteVideoIsOpen = true
    var localVideoIsOpen = true
    var remoteVideoID = 111 // 999代表大的窗口
    var localVideoID = 999 // 111 代表小的窗口


    private val mRtcEventHandler: IRtcEngineEventHandler = object : IRtcEngineEventHandler() {

        override fun onConnectionStateChanged(state: Int, reason: Int) {
            super.onConnectionStateChanged(state, reason)
            println("mRtcEventHandler onConnectionStateChanged" + state + reason)
            //TODO 出现一个现象 用户端秒挂的话 回调是 5用户离开频道， 3用户被服务器禁止
            if (state == 5 && reason == 3) {
                runOnUiThread {
                    ToastUtil.show(getString(R.string.b84))
//                viewModel!!.setVideoHangup(this@CallVideoChatActivity,channelID)
//                    .observe(this@CallVideoChatActivity) {bean ->}
//                finishActivity(true)
                    Handler().postDelayed({ finishActivity(true) }, 1000)

                }
            }

        }

        // 监听频道内的远端主播，获取主播的 uid 信息。
        // 每次主播进入的时候会回调这个监听
        override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
            super.onJoinChannelSuccess(channel, uid, elapsed)
            println("mRtcEventHandler   onJoinChannelSuccess channel:$channel")
            runOnUiThread {
//                ToastUtil.show("onJoinChannelSuccess")
                if (type == 1) {
                    viewModel?.getVideoPush(this@CallVideoChatActivity, channelID)
                }
            }

        }

        override fun onUserJoined(uid: Int, elapsed: Int) {

            println("mRtcEventHandler  uid:$uid")

            runOnUiThread {
                if (uid != 0) {
                    <EMAIL> = uid
                    // 从 onUserJoined 回调获取 uid 后，调用 setupRemoteVideo，设置远端视频视图。
                    println("mRtcEventHandler")
                    val container2 = findViewById<FrameLayout>(remoteVideoRID)
                    remoteSurfaceView = TextureView(baseContext)
//                    remoteSurfaceView!!.setZOrderMediaOverlay(true)
                    container2.addView(remoteSurfaceView)
                    setupRemoteVideo(uid)
                    if (isFilstFlag) {
                        handler.sendEmptyMessageDelayed(3, 500)
                        handler.sendEmptyMessageDelayed(1, 60000) // 5000.0

                        OtherManager.Companion.manager(this@CallVideoChatActivity).sysParam.value?.get(
                            "faceUploadTime"
                        )?.toString()?.toDouble()?.toInt()?.toLong()
                            ?.let { handler.sendEmptyMessageDelayed(4, it * 1000) }


//                        RxTimerUtil.interval(this@CallVideoChatActivity, 5) {
//                            if (faceunity.fuIsTracking() == 0) {
//                                ToastUtil.show("Keep your face away from the camera")
//                            }else{
//                                ToastUtil.show("Keep your face ")
//                            }
//                        }
                    }
                }
            }
        }

        override fun onUserOffline(uid: Int, reason: Int) {
            super.onUserOffline(uid, reason)
            println("mRtcEventHandler onUserOffline uid : " + uid)
//            if (state == 4) {
//                mRtcEngine!!.leaveChannel()
//            }
//            close()
            runOnUiThread {
                ToastUtil.show(getString(R.string.b84))
//                viewModel!!.setVideoHangup(this@CallVideoChatActivity,channelID)
//                    .observe(this@CallVideoChatActivity) {bean ->}
//                finishActivity(true)
                Handler().postDelayed({ finishActivity(true) }, 1000)

            }

        }

        override fun onRemoteVideoStateChanged(uid: Int, state: Int, reason: Int, elapsed: Int) {
            super.onRemoteVideoStateChanged(uid, state, reason, elapsed)
            println("mRtcEventHandler onRemoteVideoStateChanged : " + state)
            remoteVideoIsOpen = state != 0
            runOnUiThread { setVideoWindowVisi() }
        }

        override fun onLocalVideoStateChanged(source: VideoSourceType, state: Int, error: Int) {
            super.onLocalVideoStateChanged(source, state, error)
            localVideoIsOpen = state != 0
            runOnUiThread { setVideoWindowVisi() }
        }
    }

    private fun setVideoWindowVisi() {
        if (!remoteVideoIsOpen) {
            if (remoteVideoID == 999) {
                bindingView!!.maxWindow.visibility = View.VISIBLE
            } else {
                bindingView!!.minWindow.visibility = View.VISIBLE
            }
        } else {
            if (remoteVideoID == 999) {
                bindingView!!.maxWindow.visibility = View.GONE
            } else {
                bindingView!!.minWindow.visibility = View.GONE
            }
        }
        if (!localVideoIsOpen) {
            if (localVideoID == 999) {
                bindingView!!.maxWindow.visibility = View.VISIBLE
            } else {
                bindingView!!.minWindow.visibility = View.VISIBLE
            }
        } else {
            if (localVideoID == 999) {
                bindingView!!.maxWindow.visibility = View.GONE
            } else {
                bindingView!!.minWindow.visibility = View.GONE
            }
        }
    }

    private fun setVideoVisi() {}
    private fun setVideoGone() {}
    private fun setupRemoteVideo(uid: Int) {
        val videoCanvas = VideoCanvas(remoteSurfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid)
        videoCanvas.renderMode = 1
        mRtcEngine?.setupRemoteVideo(videoCanvas)
        //        reverseVideo();
    }

    private fun reverseVideo() {

//        if (uid == 0) {
//            return;
//        }
//        int  temp = localVideoRID;
//        localVideoRID = remoteVideoRID;
//        remoteVideoRID = temp;

//        setupLocalVideo();
//        initializeAndJoinChannel();
//        setupRemoteVideo(uid);
        val tempID = localVideoID
        localVideoID = remoteVideoID
        remoteVideoID = tempID
        val temp = localSurfaceView
        localSurfaceView = remoteSurfaceView
        remoteSurfaceView = temp
        setupLocalVideo()
        setupRemoteVideo(uid)
        setVideoWindowVisi()
    }

    private fun setupLocalVideo() {
        val videoCanvas = VideoCanvas(localSurfaceView, VideoCanvas.RENDER_MODE_FIT, 0)
        videoCanvas.renderMode = 1
//        faceUnityBeautyAPI.setupLocalVideo(localSurfaceView!!, Constants.RENDER_MODE_HIDDEN)

//         将 SurfaceView 对象传入声网，以渲染本地视频。
        mRtcEngine!!.setupLocalVideo(videoCanvas)
    }

    //    private val faceUnityBeautyAPI: FaceUnityBeautyAPI = createFaceUnityBeautyAPI()
    private var fuRenderKit: FURenderKit? = null

    private fun initializeAndJoinChannel() {
        try {
            val config = RtcEngineConfig()
            config.mContext = baseContext
            config.mAppId = if (BuildConfig.DEBUG || Objects.equals(
                    anchor.app.base.BuildConfig.BUILD_TYPE, "uat"
                )
            ) APP_REGITEST_ID.AGORA_APP_TEST_ID else APP_REGITEST_ID.AGORA_APP_RELEASE_ID
            config.mEventHandler = mRtcEventHandler
            mRtcEngine = RtcEngine.create(config)
        } catch (e: Exception) {
            throw RuntimeException("Check the error.")
        }
//
//        faceUnityBeautyAPI.initialize(
//            Config(
//                mRtcEngine!!, fuRenderKit!!, null, CaptureMode.Agora, 0, false
//            )
//        )
//        faceUnityBeautyAPI.enable(true)
//        faceUnityBeautyAPI.setBeautyPreset(BeautyPreset.DEFAULT)

        // 视频默认禁用，你需要调用 enableVideo 启用视频流。

        mRtcEngine?.apply {
            UserInfoManager.user()?.userCode?.let {
                val recorderStreamInfo = RecorderStreamInfo(channelID, it.toInt())
                agoraMediaRecorder = createMediaRecorder(recorderStreamInfo)
                agoraMediaRecorder?.setMediaRecorderObserver(object : IMediaRecorderCallback {
                    override fun onRecorderStateChanged(
                        channelId: String?, uid: Int, state: Int, reason: Int
                    ) {
                        Logger.d("onRecorderStateChanged $channelId  $uid $state $reason")
                    }

                    override fun onRecorderInfoUpdated(
                        channelId: String?, uid: Int, info: RecorderInfo?
                    ) {
                        Logger.d("onRecorderInfoUpdated $channelId  $uid $info ")
                    }

                })
            }
            enableVideo()
            val options = ChannelMediaOptions()
            // 视频通话场景下，设置频道场景为 BROADCASTING。
            options.channelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
            // 将用户角色设置为 BROADCASTER。
            options.clientRoleType = Constants.CLIENT_ROLE_BROADCASTER

            // 使用临时 Token 加入频道。
            // 你需要自行指定用户 ID，并确保其在频道内的唯一性。
            var code = joinChannel(
                CURRENT_AGORA_ACCESS_TOKEN,
                channelID,
                (UserInfoManager.user()?.userCode)!!.toInt(),
                options
            )

            setupLocalVideo()
            // 开启本地视频预览。
            startPreview()
            setBeautyEffectOptions(true, BeautyOptions())
        }
//        mRtcEngine!!.enableVideo()


//        mRtcEngine!!
        //        mRtcEngine.joinChannel(CURRENT_AGORA_ACCESS_TOKEN, "125", (int) 1234, options);

//        if (code == 0) {
//        }
//        ToastUtil.show("链接完成: $type ----  ${channelID}")
    }

    @RequiresApi(Build.VERSION_CODES.N)
    private fun initP() {
        PermissionX.init(this)
            .permissions(Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO)
            .onExplainRequestReason { scope: ExplainScope, deniedList: List<String> ->
                scope.showRequestReasonDialog(
                    deniedList,
                    BaseApp.getAppContext().getString(anchor.app.base.R.string.b40),
                    BaseApp.getAppContext().getString(anchor.app.base.R.string.user_login_confirm),
                    BaseApp.getAppContext().getString(
                        anchor.app.base.R.string.Cancel
                    )
                )
            }.onForwardToSettings { scope: ForwardScope, deniedList: List<String> ->
                scope.showForwardToSettingsDialog(
                    deniedList,
                    BaseApp.getAppContext().getString(anchor.app.base.R.string.b41),
                    BaseApp.getAppContext().getString(anchor.app.base.R.string.user_login_confirm),
                    BaseApp.getAppContext().getString(
                        anchor.app.base.R.string.Cancel
                    )
                )
            }
            .request { allGranted: Boolean, grantedList: List<String?>?, deniedList: List<String?>? ->
                if (allGranted) {
//                            ToastUtil.show("All permissions are granted");
                    loadData2()
                } else {
//                            ToastUtil.show("These permissions are denied: $deniedList");
                    SelectTipDialog.showTips(
                        mContext,
                        mContext.getString(R.string.b24),
                        getResources().getString(R.string.Cancel),
                        getResources().getString(R.string.b78),
                        getResources().getString(R.string.b25),
                        object : ISimpleTipsClickListener() {
                            override fun confirmClick() {
                                IntentUtils.gotoPermissionSetting()
                            }

                            override fun concelClick() {
                                super.concelClick()
                                finishActivity(false)
                            }
                        })
                }
            }
    }

    var operationTipDialog: OperationTipDialog? = null
    fun close() {
        operationTipDialog = OperationTipDialog {
            if (!channelID.equals("0")) {
                viewModel!!.setVideoHangup(this@CallVideoChatActivity, channelID)
                    .observe(this@CallVideoChatActivity) { bean -> }
            }
            operationTipDialog?.dismiss();
            finishActivity(true)
        }
        operationTipDialog?.content = getString(R.string.a0010);
        operationTipDialog?.show(getSupportFragmentManager(), "VideoChatActivity");
//
    }


    fun finishActivity(isToResult: Boolean) {
//        viewModel!!.setVideoHangup(this@CallVideoChatActivity, channelID)
        if (isToResult) {
            bean?.duration = bindingView?.timer?.text.toString()
            bean?.channelID = channelID
            jump(VideoResultActivity::class.java, Bundle().apply {
                val tmpBean = GsonUtil.GsonString(bean)
                putString("bean", tmpBean)
                putString("userRole", bean?.userRole)
            })
        }
        if (uid > 0) {
            val text = bindingView?.timer?.text.toString()
            if (text.isNotEmpty()) {
                sendVideoMsg(false, text)
            }
        }

        // 处理视频文件
        try {
            // 如果开启了录制但没有录制成功，删除本地视频
            if (recordingEndTime > 0 && currentime < recordingEndTime) {
                saveFile?.let { file ->
                    if (file.exists()) {
                        val deleted = file.delete()
                        if (!deleted) {
                            // 如果删除失败，将文件路径保存下来，下次启动时清理
                            val sharedPreferences = getSharedPreferences("video_files", Context.MODE_PRIVATE)
                            val editor = sharedPreferences.edit()
                            editor.putString("pending_video_${System.currentTimeMillis()}", file.absolutePath)
                            editor.apply()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e("Error deleting video file: ${e.message}")
        }

        finish()
    }

    private fun sendVideoMsg(showToast: Boolean, msg: String) {
        if (showToast) {
            ToastUtil.show(msg)
        }
        bean?.apply {
            val map = mapOf("id" to id.toString(), "time" to msg)
            RxBus.getDefault()
                .post(RxCodeConstants.JUMP_TYPE_SEND_VIDEO_MSG, GsonUtil.GsonString(map))
        }
    }

    fun chat(view: View) {
        val inputPopupWindow = GiftNumInputPopupWindow(mContext)
        inputPopupWindow.setCallback { content: String ->

            if (!WordFilter.getInstance().canSendMessage(content)) {
                ToastUtil.show(getString(R.string.tip_forbidden_words))
                return@setCallback
            }
            val newContent = WordFilter.getInstance().filterMessage(content)

            var homeListItemBean = HomeListItemBean()
            val loginBean = UserInfoManager.user()
            homeListItemBean.content = newContent
            homeListItemBean.nickName = loginBean?.nickName
            homeListItemBean.id = loginBean?.id.toString()
            homeListItemBean.headFileName = loginBean?.headFileName
            homeListItemBean.level = loginBean?.level
            dataList.add(homeListItemBean)
            adapter!!.notifyDataSetChanged()
            bindingView!!.recyclerView.scrollToPosition(dataList.size - 1)

            val hashMap: HashMap<String, String> = HashMap<String, String>()
            hashMap["id"] = id.toString()
            hashMap["content"] = newContent
            RxBus.getDefault()
                .post(RxCodeConstants.JUMP_TYPE_VIDEO_CHAT_MSG_SEND, GsonUtil.GsonString(hashMap))
        }
        inputPopupWindow.show(view)
    }


    fun switchCamera() {
        if (localVideoIsOpen) {
            mRtcEngine!!.switchCamera()
        } else {
            ToastUtil.show(mContext.getString(R.string.b26))
        }
    }

    fun giftPanel() {
        bindingView!!.giftPanel.showGiftPanel() { giftBean ->
            LoadGiftManager.giftManager(this@CallVideoChatActivity)
                .askGift(id, giftBean.num, giftBean.giftCode).observe(this@CallVideoChatActivity) {
                    giftBean.userId = id
                    val homeListItemBean = HomeListItemBean()
                    val loginBean = UserInfoManager.user()
                    homeListItemBean.content = "Ask for one "
                    homeListItemBean.imageUrl = giftBean.giftIcon
                    homeListItemBean.nickName = loginBean?.nickName
                    homeListItemBean.id = loginBean?.id.toString()
                    homeListItemBean.headFileName = loginBean?.headFileName
                    homeListItemBean.level = loginBean?.level
                    dataList.add(homeListItemBean)
                    adapter!!.notifyItemInserted(dataList.size - 1)
                    bindingView!!.recyclerView.scrollToPosition(dataList.size - 1)
                    RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_SEND_ASK_GIFT, giftBean)
                }

        }
        println("mRtcEventHandler  giftPanel ：")

//        (view as GiftPanelLayout).let {
//            it.showGiftPanel()
//        }
//
    }

    var isOpenLocalVoicePitch = false
    fun VoicePitch() {
        isOpenLocalVoicePitch = !isOpenLocalVoicePitch
        mRtcEngine!!.muteLocalAudioStream(isOpenLocalVoicePitch)

//        if (isOpenLocalVoicePitch) {
//            mRtcEngine.adjustRecordingSignalVolume(100);
//        }else {
//            mRtcEngine.adjustRecordingSignalVolume(0);
//        }
    }


    fun isFollow() {
        if (bean?.followFlag == "1") {
            bean?.followFlag = "0"
            viewModel!!.removeAndFollowing(id, 0, "2")
        } else {
            bean?.followFlag = "1"
            viewModel!!.removeAndFollowing(id, 1, "2")
        }

        setFollowView(bean?.followFlag == "1")
    }


    var isOpenLocalVideo = true
    fun enableLocalVideo() {
        isOpenLocalVideo = !isOpenLocalVideo
        mRtcEngine!!.enableLocalVideo(isOpenLocalVideo)
    }

    fun more() {
        MoreDialogFragment(isOpenLocalVideo, !isOpenLocalVoicePitch) { index: Int, type: Boolean ->
            when (index) {
                1 -> //                    mRtcEngine.enableLocalVideo(type);
                    enableLocalVideo()

                2 -> //                    mRtcEngine.muteLocalAudioStream(type);
                    VoicePitch()

                3 -> {
                    jump(ReportActivity::class.java, Bundle().apply {
                        putString("reportUserId", id.toString())
                        putString("reportUserRole", bean?.userRole)
                    })
                }
            }
        }.show(<EMAIL>, "VideoChatActivity")
    }

    fun leave() {
        (<EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager).cancelAll()

        viewModel?.getVideoPushReject(this@CallVideoChatActivity, channelID)?.observe(this) {
            finishActivity(false)
        }
//        Handler().postDelayed({ finishActivity(false) },2500)
    }

    fun toCenter() {
        jump(PersonalCenterActivity::class.java, Bundle().apply {
            putLong("id", id)
            putString("userRole", bean?.userRole)
        })
    }

    override fun onDestroy() {
//        viewModel!!.setVideoHangup(this@CallVideoChatActivity, channelID)

//        faceUnityBeautyAPI.release()
        fuRenderKit?.release()
        agoraMediaRecorder?.release()

        RxTimerUtil.cancel(this@CallVideoChatActivity)
        mRtcEngine?.apply {
            stopPreview()
            leaveChannel()
        }
        mRtcEngine = null
        RtcEngine.destroy()

        handler.removeCallbacksAndMessages(null)
        handler.mWeakReference.clear()

        vibrator?.cancel()
//        mPlayer.stop()
        mPlayer.release()
//        bindingView!!.giftPanel =null

        // 销毁置空,防止IMCenter中阻断该id的消息
        setVideoChatID(null)
        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)

        bindingView!!.giftPanel.onDestroy()
        bindingView!!.gifshowviewId.onDestroy()
        super.onDestroy()

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
//        ToastUtil.show("有电话打进来,当前正在通话...")
    }

    /**
     * 清理之前可能未成功删除的视频文件
     */
    private fun cleanPendingVideoFiles() {
        try {
            val sharedPreferences = getSharedPreferences("video_files", Context.MODE_PRIVATE)
            val allEntries = sharedPreferences.all

            if (allEntries.isNotEmpty()) {
                val editor = sharedPreferences.edit()

                // 遍历所有待删除的文件路径
                for ((key, value) in allEntries) {
                    if (value is String) {
                        val file = File(value)
                        if (file.exists()) {
                            val deleted = file.delete()
                            if (deleted) {
                                // 如果删除成功，从 SharedPreferences 中移除该条目
                                editor.remove(key)
                                Logger.i("Successfully deleted pending video file: $value")
                            } else {
                                Logger.e("Failed to delete pending video file: $value")
                            }
                        } else {
                            // 文件不存在，从 SharedPreferences 中移除该条目
                            editor.remove(key)
                        }
                    }
                }

                // 提交更改
                editor.apply()
            }
        } catch (e: Exception) {
            Logger.e("Error cleaning pending video files: ${e.message}")
        }
    }

    override fun onBackPressed() {
//                             super.onBackPressed();
    }

    val homeListViewmodel: HomeListViewmodel by lazy {
        HomeListViewmodel(mContext.application).apply {
            initFliterAttribute(
                mContext as FragmentActivity
            )
        }
    }

    fun btnOk() {
        (<EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager).cancelAll()

//        ToastUtil.show("正在连接视频通话...")
        ToastUtil.show("Loading...")

        LoadingDialog.getInstance(mContext).show()

        channelID = intent.getStringExtra("channelID") ?: "0"
        println("mRtcEventHandler 远程获取发送过来的频道 ：" + String)

        bindingView!!.btnOk.isClickable = false
        bindingView!!.btnOk.setImageDrawable(resources.getDrawable(R.mipmap.discover_video_waiting_bg_04))

        homeListViewmodel.getCallAnchor(channelID, "2").observe(this) {
            if (it.toString().toInt() == 0) {
                initVideoToken()
            } else {
                ToastUtil.show(getString(R.string.b84))
                Handler().postDelayed({ finishActivity(false) }, 1000)
            }
        }

        handler.postDelayed({
            homeListViewmodel.getCallAnchor(channelID, "3").observe(this) {
                if (it.toString().toInt() == 0) {
                    Logger.v("get JUMP_TYPE_START_VIDEO 04")
                    ToastUtil.show(getString(R.string.b84))
                    Handler().postDelayed({ finishActivity(false) }, 1000)
                }
            }
        }, 15000)
    }

    private fun initVideoToken() {
        viewModel!!.getVideoToken(this, channelID, UserInfoManager.user()?.userCode.toString())
            .observe(this) { token: String ->
                println("mRtcEventHandler  远程 获取token成功 ：" + String)
                //                initTimer()
                CURRENT_AGORA_ACCESS_TOKEN = token
                initializeAndJoinChannel()
            }
    }

    private fun initFaceUnity(): Boolean {
        try {
            val authpack = Class.forName("com.mobile.anchor.app.discover.faceunity.authpack")
            val aMethod = authpack.getDeclaredMethod("A")
            aMethod.isAccessible = true
            val auth = aMethod.invoke(null) as ByteArray
            FURenderer.getInstance().setup(this@CallVideoChatActivity, auth)
//            fuRenderKit = FURenderKit.getInstance()
            fuRenderKit = FURenderer.getInstance().mFURenderKit
            return true
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            // do nothing
        }
        return false
    }

    fun login() {
//        viewModel!!.login(this, "authId", "1", "")
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        private const val TAG = "VideoChatActivity"
        private var CURRENT_AGORA_ACCESS_TOKEN =
            "007eJxTYHg0hevOrzjhL0vSWlPeaZ2/FpX1jNv/ENfWXTO43vy+mtmhwJBiaWlmaWJikWRgYmZibJlsYWxhkpxsaWhsbpFibJZqUamQndIQyMhw5k0/MyMDBIL4zAyGRqYMDADDSiAm"
    }


    override fun onItemClick(v: View, item: Any) {
        var homeListItemBean = item as HomeListItemBean

        if (v.id == R.id.tr02) {
            TranslationClient.getInstance()
                .translate(-1, homeListItemBean.content, "zh_CN", BaseApp.getAppLanguage())

            if (MultiLanguages.getAppLanguage().language == "zh_CN") {
                ToastUtil.show("当前系统语言为中文,被翻译文本为中文,无需翻译")
                return
            }

            TranslationClient.getInstance()
                .addTranslationResultListener { code, result -> // handle result
                    println("TranslationClient getSrcLanguage 01:" + result.srcText)
                    println("TranslationClient getSrcLanguage 02:" + result.srcLanguage)
                    println("TranslationClient getSrcLanguage 03:" + result.translatedText)
                    if (code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_SUCCESS.code) {
                        if (homeListItemBean.isShowTranslation.not()) {
                            homeListItemBean.isShowTranslation = true
                            homeListItemBean.contentTranslation = result.translatedText;

                            println("homeListItemBean 001 ${homeListItemBean.content}")
                            println("homeListItemBean 002 ${homeListItemBean.isShowTranslation}")
                            println("homeListItemBean 003 ${homeListItemBean.contentTranslation}")

//                        var homeListItemBean1 = dataList[dataList.indexOf(homeListItemBean)]
//                        homeListItemBean1.isShowTranslation = true
//                        homeListItemBean1.contentTranslation = result.translatedText;
//                        dataList[dataList.indexOf(homeListItemBean)] = homeListItemBean1
                            adapter?.notifyDataSetChanged()
                        }
                    } else {
                        ToastUtil.show("Translation failed, please try again")
                    }
                }
        }
    }

    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
        val binding = holder.binding
        if (binding is DiscoverVideoChatItemBinding) {
            if (dataList[position].isShowTranslation) {
                binding.tr02.setImageDrawable(getDrawable(R.mipmap.discover_video_chat_item_bg_02))
            } else {
                binding.tr02.setImageDrawable(getDrawable(R.mipmap.discover_video_chat_item_bg_01))
            }
            if (dataList[position].headFileName == null || dataList[position].headFileName.isEmpty()) {
                binding.head.setImageDrawable(resources.getDrawable(R.mipmap.discover_ic_default))
            }
//            binding.level.setImageResource(resources.getIdentifier("base_ic_level_num_0$index","mipmap", "mikchat.app"));
        }
    }


    private var lastX = 0
    private var lastY = 0
    private var offsetX = 0
    private var offsetY = 0
    private var initX = 0
    private var initY = 0
    var isRecordDown = true

    private inner class FloatingListener : View.OnTouchListener {
        override fun onTouch(p0: View, event: MotionEvent): Boolean {
            val x = event.x.toInt()
            val y = event.y.toInt()
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    lastX = x
                    lastY = y
                    initX = x
                    initY = y

                    println("FloatingListener ACTION_DOWN : ${lastX} ")
                    println("FloatingListener ACTION_DOWN : ${lastY} ")
                }

                MotionEvent.ACTION_MOVE -> {
                    offsetX = x - lastX
                    offsetY = y - lastY
                    initX = initX - offsetX
                    initY = initY - offsetY

                    val dm2 = resources.displayMetrics

                    val layoutParams =
                        bindingView!!.RLRemoteVideo.layoutParams as RelativeLayout.LayoutParams

                    println("FloatingListener ACTION_MOVE parentParams.width: ${dm2.widthPixels} ")
                    println("FloatingListener ACTION_MOVE layoutParams.width: ${layoutParams.width} ")

                    var currentLeftMargin = bindingView!!.RLRemoteVideo.left + offsetX
                    if (currentLeftMargin < 0) {
                        currentLeftMargin = 0
                    } else if (currentLeftMargin > dm2.widthPixels - layoutParams.width) {
                        currentLeftMargin = dm2.widthPixels - layoutParams.width
                    }

                    var currentTopMargin = bindingView!!.RLRemoteVideo.top + offsetY
                    if (currentTopMargin < 0) {
                        currentTopMargin = 0
                    } else if (currentTopMargin > dm2.heightPixels - layoutParams.height) {
                        currentTopMargin = dm2.heightPixels - layoutParams.height
                    }

                    layoutParams.leftMargin = currentLeftMargin
                    layoutParams.topMargin = currentTopMargin
                    bindingView!!.RLRemoteVideo.layoutParams = layoutParams

                    println("FloatingListener ACTION_MOVE : ${offsetX} ")
                    println("FloatingListener ACTION_MOVE : ${offsetY} ")
                }

                MotionEvent.ACTION_UP -> {
                    println("FloatingListener ACTION_UP : ${offsetX} ")
                    println("FloatingListener ACTION_UP : ${lastX} ")
                    println("FloatingListener ACTION_UP : ${offsetY} ")
                    println("FloatingListener ACTION_UP : ${lastY} ")

                    if (abs(initX - lastX) < 10 && abs(initY - lastY) < 10) {
//                        reverseVideo()
                    }
                }
            }
            return true
        }
    }

}