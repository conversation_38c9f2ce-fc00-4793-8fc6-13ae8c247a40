package com.mobile.anchor.app.module.user.gift

import androidx.databinding.ObservableArrayList
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityGiftListBinding
import anchor.app.base.ui.BaseActivity
import com.mobile.anchor.app.module.user.bean.GiftBean
import com.mobile.anchor.app.module.user.gift.adapter.GiftAdapter
import kotlin.collections.isNullOrEmpty

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/20 10:26
 * @description :主播礼物列表
 */
class MyGiftActivity : BaseActivity<GiftViewModel, ActivityGiftListBinding>() {

    private var pageIndex = 1
    private var items = ObservableArrayList<GiftBean>()

    override fun getLayoutId(): Int = R.layout.activity_gift_list

    override fun initView() {
        bindingView.activity = this
        bindingView.refreshLayout.setOnRefreshListener { fetch(true) }
        bindingView.refreshLayout.setOnLoadMoreListener { fetch(false) }
        bindingView.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)

        bindingView.recyclerView.adapter = GiftAdapter(mContext, items)
    }

    override fun loadData() {
        super.loadData()
        fetch(true)
        viewModel?.getGiftTotalValue()?.observe(this) {
            bindingView.tvTotalValue.text = it.toString()
        }
    }

    private fun fetch(refresh: Boolean) {
        pageIndex = if (refresh) 1 else pageIndex + 1
        viewModel?.getGiftList(buildMap {
            put("current", pageIndex.toString())
            put("size", "20")
        })?.observe(this) {
            showContentView()
            if (refresh) {
                items.clear()
            }
            bindingView.refreshLayout.finishRefresh()
            bindingView.refreshLayout.setNoMoreData(it.isNullOrEmpty() || items.size < 20)
            it?.let {
                items.addAll(it)
            }
        }
    }
}