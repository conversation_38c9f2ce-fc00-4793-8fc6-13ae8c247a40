package com.mobile.anchor.app.module.workbench.api;

import com.mobile.anchor.app.module.workbench.bean.ConnectRateListBean;
import com.mobile.anchor.app.module.workbench.bean.MarqueeBean;
import com.mobile.anchor.app.module.workbench.bean.MatchProgressBean;
import com.mobile.anchor.app.module.workbench.bean.MemberListBean;
import com.mobile.anchor.app.module.workbench.bean.TndAgentListBean;
import com.mobile.anchor.app.module.workbench.bean.WorkbenchBean;
import com.mobile.anchor.app.module.workbench.bean.WorkbenchUnionBean;

import java.util.HashMap;
import java.util.List;

import anchor.app.base.bean.LoginBean;
import anchor.app.base.bean.WordEntity;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.utils.SharePreUtil;
import io.reactivex.Flowable;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.api
 * @class describe
 * @time 2019/8/9 12:21
 * @class describe
 */
public class WorkbenchApi {

    public static Flowable<BaseResult<WorkbenchBean>> getWorkbench() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getWorkbench()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<String>> getWorkbenchAnchorShareUrl() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getWorkbenchAnchorShareUrl()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<WorkbenchUnionBean>> getWorkbenchUnionData(int unionId) {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getWorkbenchUnionData(unionId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<TndAgentListBean>>> getMemberChildList(int unionId) {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getMemberChildList(unionId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<WorkbenchUnionBean>> getWorkbenchUnionDataDetail(int unionId) {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getWorkbenchUnionData(unionId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<MemberListBean>> getMemberList(int countId, String country, int current, int size, int unionId) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("countId", countId);
        request.put("country", country);
        request.put("size", size);
        request.put("unionId", unionId);

        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getMemberList(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<ConnectRateListBean>>> getVideoList() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getVideoList()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> onlineStatus(String onlineStatus) {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).onlineStatus(onlineStatus)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> anchorPayInfo() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).anchorPayInfo()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> matchSwitch(String isOpen) {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).matchSwitch(isOpen)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<MatchProgressBean>> homeTask() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).homeTask()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<WordEntity>>> getSensitiveWords() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getSensitiveWords()
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> anchorAddVideo(Long anchorId, String url, String channelId) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("anchorId", anchorId);
        request.put("url", url);
        request.put("channelId", channelId);

        return BaseApiClient.getInstance().create(IWorkbenchApi.class).anchorAddVideo(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<LoginBean>> getAnchorDetail() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getAnchorDetail(SharePreUtil.getUserId())
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<MarqueeBean>>> marqueeList() {
        return BaseApiClient.getInstance().create(IWorkbenchApi.class).getMarqueeList().
                subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

}
