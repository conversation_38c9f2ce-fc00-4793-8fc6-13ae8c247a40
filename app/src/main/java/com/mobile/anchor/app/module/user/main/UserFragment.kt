package com.mobile.anchor.app.module.user.main

import anchor.app.base.bean.LoginBean
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeVisible
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseFragment
import anchor.app.base.utils.ImageUtil
import anchor.app.base.utils.ToastUtil
import android.view.View
import androidx.core.content.ContextCompat
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.FragmentUserBinding
import com.mobile.anchor.app.module.user.album.AlbumManageActivity
import com.mobile.anchor.app.module.user.dialog.OnlineStatus
import com.mobile.anchor.app.module.user.editedInfo.ProfileActivity
import com.mobile.anchor.app.module.user.invitation.InviteActivity
import com.mobile.anchor.app.module.user.level.LevelActivity2
import com.mobile.anchor.app.module.user.record.RecordVideoActivity
import com.mobile.anchor.app.module.user.setting.SettingActivity
import com.mobile.anchor.app.module.user.task.RewardTaskActivity
import com.mobile.anchor.app.module.user.wallet.WalletActivity2

class UserFragment : BaseFragment<UserViewModel?, FragmentUserBinding>() {
    override fun getLayoutId(): Int = R.layout.fragment_user

    override fun initView() {
        if (UserInfoManager.user()?.isGuild == true || UserInfoManager.user()?.isDisable == true) {
            bindingView.llCoin.visibility = View.GONE
        }

        bindingView.avatarContainer.click {
            bindingView.llBackIcon.performClick()
        }

        bindingView.llBackIcon.click {
            jump(ProfileActivity::class.java)
        }

        bindingView.llMenuAlbum.click {
            jump(AlbumManageActivity::class.java)
        }

        bindingView.llMenuLevel.click {
            jump(LevelActivity2::class.java)
        }

        bindingView.llWallet.click {
            jump(WalletActivity2::class.java)
        }

        bindingView.llTasks.click {
            jump(RewardTaskActivity::class.java)
        }

        bindingView.llShareFriend.click {
            jump(InviteActivity::class.java)
        }

        bindingView.llSetting.click {
//            jump(SettingActivity::class.java)
            jump(RecordVideoActivity::class.java)
        }
    }

    override fun onResume() {
        super.onResume()
        fetchUserInfo()
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            fetchUserInfo()
        }
    }

    private fun fetchUserInfo() {
        viewModel?.queryUserInfo(mActivity)?.observe(this) { loginBean: LoginBean ->
            UserInfoManager.setUser(loginBean)
            showContentView()
            ImageUtil.displayCircleImage(
                requireActivity(), bindingView.ivAvatar, loginBean.headFileName
            )
            bindingView.ivReview.makeVisible(loginBean.headFileNameStatus == 0)
            bindingView.tvReview.makeVisible(loginBean.headFileNameStatus == 0)

            bindingView.tvName.text = loginBean.nickName
            bindingView.tvId.text = "ID: ${loginBean.userCode}"
            bindingView.tvAge.text = "${loginBean.age}"
            bindingView.labels.setLabels(loginBean?.languages?.map { it.languageName })

            updateOnlineStatus(
                onlineStatus = OnlineStatus.values().find { loginBean.onlineStatus == it.value }
                    ?: run { OnlineStatus.ONLINE }, false
            )
        }
    }

    private fun updateOnlineStatus(onlineStatus: OnlineStatus, auto: Boolean = true) {
        val status = when (onlineStatus) {
            OnlineStatus.ONLINE -> Pair(
                getString(R.string.on_line), R.color.color_21c76e
            )

            OnlineStatus.INVISIBILITY -> Pair(
                getString(R.string.off_line), R.color.color_666666
            )

            OnlineStatus.BUSY -> Pair(
                getString(R.string.status_busy), R.color.color_FE4918
            )
        }

        fun updateText() {
            bindingView.tvOnlineStatus.apply {
                text = status.first
                setTextColor(ContextCompat.getColor(requireActivity(), status.second))
            }
        }

        if (auto) {
            viewModel?.updateOnlineStatus(if (onlineStatus == OnlineStatus.ONLINE || onlineStatus == OnlineStatus.BUSY) "1" else "0")
                ?.observe(this) {
                    if (it) {
                        OtherManager.manager?.onlineStatus?.value =
                            if (onlineStatus == OnlineStatus.ONLINE || onlineStatus == OnlineStatus.BUSY) "1" else "0"
                        updateText()
                        ToastUtil.show("update successful")
                    } else {
                        ToastUtil.show("update failure")
                    }
                }
        } else {
            updateText()
        }
    }
}