package com.mobile.anchor.app.module.workbench.proxy.tndagentlist

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.mobile.anchor.app.R
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.ext.jump
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.databinding.ActivityTndagentListBinding
import com.mobile.anchor.app.databinding.ItemTndagentListBinding
import com.mobile.anchor.app.module.workbench.bean.TndAgentListBean
import com.mobile.anchor.app.module.workbench.proxy.tndagentlist.adapter.tndAgentListItemAdapter

class TndAgentListActivity : BaseActivity<TndAgentListViewmodel, ActivityTndagentListBinding>(),
    ItemClickPresenter<Any>, ItemDecorator {
    val from get() = intent.getStringExtra("from")
    var adapter: tndAgentListItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var isMoreLoading = false
    var current = 0
    var size = 50
    override fun getLayoutId(): Int {
        return R.layout.activity_tndagent_list
    }

    override fun initView() {
        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
        unionId = intent.getIntExtra("unionId", 0);

        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        bindingView!!.refreshLayout.setOnRefreshListener { loadData2(true) }
        bindingView!!.refreshLayout.setOnLoadMoreListener {
            if (dataList != null && dataList!!.size > size) {
                loadData2(false)
            } else {
                bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(false)
                bindingView!!.refreshLayout.setNoMoreData(true)
            }
        }

        bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)
        dataList = ObservableArrayList<Any>()
        adapter = tndAgentListItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

//        for (int i = 0; i < 100; i++) {
//            dataList.add(new AnchorItemBean());
//        }
    }

    override fun loadData() {
        showLoading()
        isLoading = false
        loadData2(false)
    }

    var isLoading = false
    var unionId = 0
    var countId = 0
    var country = ""

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {
        if (isLoading) {
            return
        }
        if (reference) {
            current = 0;
        }
        isLoading = true
        viewModel!!.getWorkbenchUnionData(unionId).observe(
            this
        ) { bean: List<TndAgentListBean> ->
            showContentView()
            bindingView!!.refreshLayout.finishLoadMore()
            bindingView!!.refreshLayout.finishRefresh()
            isLoading = false
            current += 1

            if (reference) { //
                bindingView!!.refreshLayout.setNoMoreData(false)
                dataList!!.clear()
            }
//            isMoreLoading = false
            if (bean.size > 0) {
                dataList!!.addAll(bean)
            }
            if (bean.size < size) {
                bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(false)
                bindingView!!.refreshLayout.setNoMoreData(true)
            }

            if (dataList!!.size == 0) {
                viewModel!!.error.value = ResultException(BaseResult.NOT_DATA, getString(R.string.a0018))
            }
            adapter!!.notifyDataSetChanged()

        }
    }


    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any) {
        var tndAgentListBean = item as TndAgentListBean
//        viewModel.removeBlackList(bean.getId());

        jump(TndAgentlistDetailActivity::class.java, Bundle().apply {
            putInt("unionId", tndAgentListBean.unionId)
        })

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    @SuppressLint("SetTextI18n")
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>,
        position: Int,
        viewType: Int
    ) {
        val binding01 = holder.binding
        if (binding01 is ItemTndagentListBinding) {
            val bean = dataList!![position] as TndAgentListBean
            val binding = binding01

            binding?.apply {
                Agent.text = "Agent Name : " + bean.agentName
                Invite.text = "Invite Code ：" + bean.unionId
                currentAmount.text = "$" + bean.currentAmount
                weekAmount.text = "$" + bean.weekDiamond
                monthAmount.text = "$" + bean.monthDiamond
            }

//            binding.beforeNum.text = bean.beforeNum.toString()

        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        //        if (!"user".equals(from)) {
//            //跳转首页
//            ActivityUtil.getInstance().finishAllActivityExcept("mikchat.app.user.baby.BabyInfoActivity");
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            super.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "BlackListActivity"
    }
}