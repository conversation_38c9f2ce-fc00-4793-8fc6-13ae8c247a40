package com.mobile.anchor.app.module.login.login.fragment

import com.blankj.utilcode.util.RegexUtils
import anchor.app.base.ext.click
import anchor.app.base.ext.toast
import anchor.app.base.ui.BaseFragment
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.FragmentEmailLoginBinding
import com.mobile.anchor.app.module.login.login.LoginViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/27 16:54
 * @description :
 */
class EmailLoginFragment : BaseFragment<LoginViewModel, FragmentEmailLoginBinding>() {

    override fun getLayoutId(): Int = R.layout.fragment_email_login

    override fun initView() {
        showContentView()
        bindingView.btnCode.click {
            getEmailCode()
        }
    }

    fun getEmailCode() {
        var email = bindingView!!.etEmail.text.toString().replace(Regex("\\s+"), "")
        if (!RegexUtils.isEmail(email)) {
            toast(getString(R.string.register_email_err))
            return
        }

        parentFragmentManager.beginTransaction().replace(
            R.id.fragment_container,
            EmailVerificationFragment.newInstance(email),
            "EmailVerificationFragment"
        ).addToBackStack(null).commit()
    }
}