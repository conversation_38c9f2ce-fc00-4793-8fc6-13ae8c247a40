package com.mobile.anchor.app.module.user.record

import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityRecordVideoBinding
import anchor.app.base.ext.click
import anchor.app.base.ui.BaseActivity
import com.mobile.anchor.app.module.user.editedInfo.EditInfoModel

class RecordVideoActivity : BaseActivity<EditInfoModel, ActivityRecordVideoBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_record_video

    override fun initView() {
        showContentView()
        supportFragmentManager.beginTransaction()
            .replace(R.id.fl_container, RecordVideoFragment(), "RecordVideoFragment")
            .addToBackStack(null).commit()

        bindingView.cancel.click {
            finishFragment()
        }
    }

    override fun onBackPressed() {
//        finishFragment()
    }

    private fun finishFragment() {
        finish()
    }
}