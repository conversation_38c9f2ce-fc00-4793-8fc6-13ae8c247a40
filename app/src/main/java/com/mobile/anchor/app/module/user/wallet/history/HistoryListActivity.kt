package com.mobile.anchor.app.module.user.wallet.history

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserItemHistorylistBinding
import anchor.app.base.BaseApp
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.DateTimeUtils
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.databinding.ActivityHistoryListBinding
import com.mobile.anchor.app.module.user.wallet.history.adapter.HistoryListItemAdapter
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean

class HistoryListActivity : BaseActivity<HistoryListViewmodel, ActivityHistoryListBinding>(),
    ItemClickPresenter<Any>, ItemDecorator {
    val from get() = intent.getStringExtra("from")
    var adapter: HistoryListItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var isMoreLoading = false
    var current = 0
    var size = 50
    override fun getLayoutId(): Int {
        return R.layout.activity_history_list
    }

    override fun initView() {
        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
//        pageType = getArguments().getInt("pageType");
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        bindingView!!.refreshLayout.setOnRefreshListener { loadData2(true) }
        bindingView!!.refreshLayout.setOnLoadMoreListener { loadData2(false) }
        bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)
        dataList = ObservableArrayList<Any>()
        adapter = HistoryListItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

//        for (int i = 0; i < 100; i++) {
//            dataList.add(new AnchorItemBean());
//        }
    }

    override fun loadData() {
        showLoading()
        isLoading = false
        loadData2(false)
    }

    var isLoading = false

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {
        if (isLoading) {
            return
        }
        if (reference) {
            current = 1;
        }
        isLoading = true
        viewModel!!.getList(current, size).observe(
            this
        ) { bean: List<HistoryListBean.RecordsDTO?> ->
            showContentView()
            bindingView!!.refreshLayout.finishLoadMore()
            bindingView!!.refreshLayout.finishRefresh()
            isLoading = false
            current += 1

            if (reference) { //
                bindingView!!.refreshLayout.setNoMoreData(false)
                dataList!!.clear()
            }
//            isMoreLoading = false
            if (bean.size > 0) {
                dataList!!.addAll(bean)
            }
            if (bean.size < size) {
                bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)
                bindingView!!.refreshLayout.setNoMoreData(true)
            }

            if (dataList!!.size == 0) {
                viewModel!!.error.value =
                    ResultException(BaseResult.NOT_DATA, getString(R.string.a0018))
            }
            adapter!!.notifyDataSetChanged()

        }
    }


    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any) {
//        BlackListBean.RecordsDTO  bean = (BlackListBean.RecordsDTO) item;
//        viewModel.removeBlackList(bean.getId());

//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    @SuppressLint("SetTextI18n")
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
        val binding01 = holder.binding
        if (binding01 is UserItemHistorylistBinding) {
            val bean = dataList!![position] as HistoryListBean.RecordsDTO
            val binding = binding01
            if (bean.headFileName != null && bean.headFileName.length > 0) {
                Glide.with(mContext).load(bean.headFileName)
                    .apply(RequestOptions.bitmapTransform(CircleCrop())).into(binding.head)
            }

            binding.consumptionType.text = when (bean.changeType) {
                "0" -> {
                    BaseApp.getAppContext().getString(R.string.Top_up)
                }

                "1" -> {
                    BaseApp.getAppContext().getString(R.string.b55)
                }

                "2" -> {
                    BaseApp.getAppContext().getString(R.string.b56)
                }

                "3" -> {
                    BaseApp.getAppContext().getString(R.string.b57)
                }

                "10" -> {
                    "Chat"
                }

                "12" -> {
                    "Invitation"
                }

                "13" -> {
                    "Ranking reward"
                }

                else -> {
                    BaseApp.getAppContext().getString(R.string.b58)
                }
            }


//            binding.time.text = bean.createTime.toString()
            binding.time.text = DateTimeUtils.getZoneTime(bean.createTime.toString())

//            binding.beforeNum.text = bean.beforeNum.toString()

            if (bean.changeType == "0" || bean.changeType == "10" || bean.changeType == "12" || bean.changeType == "13") {
                binding.changeNum.setTextColor(Color.parseColor("#000000"))
                binding.changeNum.text = "+" + bean.changeNum.toString()
            } else {
                binding.changeNum.setTextColor(Color.parseColor("#FB4240"))
                binding.changeNum.text = "-" + bean.changeNum.toString()
            }
//        bindingView.country.setImageDrawable(mActivity.getDrawable(R.mipmap.user_ic_sex2));
//        bindingView.country.setText(loginBean.getLastCountry());
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        //        if (!"user".equals(from)) {
//            //跳转首页
//            ActivityUtil.getInstance().finishAllActivityExcept("mikchat.app.user.baby.BabyInfoActivity");
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            super.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "BlackListActivity"
    }
}