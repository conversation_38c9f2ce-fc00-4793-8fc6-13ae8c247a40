package com.mobile.anchor.app.main

import com.mobile.anchor.app.R
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.Handler
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage


//import mikchat.app.discover.main.chat.videochat.VideoChatActivity


//class DemoHmsMessageService : RongFirebaseMessagingService() {
class DemoHmsMessageService : FirebaseMessagingService() {

    private val NOTIFY_ID_DAILY = 0x2000

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate() {
        super.onCreate()
        println("FirebaseMessaging555")
//        createNotification()
//            ARouter.getInstance().build(ArouterPath.PATH_USER_ABOUT).navigation();

        Handler().postDelayed({
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//
//
//            val intent = Intent(this@DemoHmsMessageService, MainActivity::class.java)
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//            startActivity(intent)

//            2023-12-09 19:59:16.017 1537-4137/? D/ActivityStarterImpl: MIUILOG- Permission Denied Activity : Intent { flg=0x10000000 cmp=anchormikchat.app/mikchat.app.ui.main.MainActivity } pkg : anchormikchat.app uid : 10256 tuid : 10066
//            2023-12-09 19:59:10.220 1537-4137/? I/AutoStartManagerService: MIUILOG- Reject RestartService service :ComponentInfo{anchormikchat.app/anchor.app.base.commonservice.CallWindowServices} uid : 10256

//            val intent = Intent(this@DemoHmsMessageService, CallWindowServices::class.java)
//                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//            startService(intent)
            println("FirebaseMessaging777")

        }, 500)


        println("FirebaseMessaging666")

//        createNotification() //這個是在用的,下面是沒有的
//        getChannelNotificationQ()
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        println("FirebaseMessaging onMessageReceived")

        if (remoteMessage == null) {
            return
        }
        val remoteMessageMap: Map<String, String> = remoteMessage.data;
        if (remoteMessageMap == null || remoteMessageMap.isEmpty()) {
            return
        }
//        val json = JSON.toJSON(remoteMessageMap) as JSONObject
        remoteMessageMap.entries.forEach {
            println("FirebaseMessaging key${it.key}  value${it.value}")
        }

//        createNotification()
//        getChannelNotificationQ()
    }

    private fun getChannelNotificationQ() {
//        val notificationIntent = Intent("android.intent.category.LAUNCHER")
        val notificationIntent = Intent(this@DemoHmsMessageService, MainActivity::class.java)

        notificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

        val contentIntent =
            PendingIntent.getActivity(applicationContext, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)

        val mBuilder: NotificationCompat.Builder = NotificationCompat.Builder(this)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(contentIntent)
            .setContentTitle("Video")
            .setContentText("Play video")
            .setFullScreenIntent(contentIntent, true)

        val mNotificationManager = this.getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        mNotificationManager.notify(0, mBuilder.build())
    }


    private fun createNotification() {
        val intent = Intent(this@DemoHmsMessageService, MainActivity::class.java)

        val pendingIntent = PendingIntent.getActivity(
            this@DemoHmsMessageService,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val notificationManager = createNotificationChannel(
            "mikchat", "my_channel_NAME",
            NotificationManager.IMPORTANCE_HIGH
        )

//        val notificationManager = <EMAIL>(Context.NOTIFICATION_SERVICE) as NotificationManager

        val notification =
            NotificationCompat.Builder(this@DemoHmsMessageService, "mikchat")
                .setContentTitle("Call")
//                .setContentText("${bean.nickName}向您发起视频通话请求...")
                .setContentText(" 132 向您发起视频通话请求...")
                .setContentIntent(pendingIntent)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setPriority(NotificationCompat.PRIORITY_MIN)
                .setAutoCancel(true)
//                .setVibrate(longArrayOf(0,5000,0,5000,0,5000))//设置的震动是无效的只能是默认震动
                .setVibrate(longArrayOf(0))//设置的震动是无效的只能是默认震动
                .setSound(null)//设置的震动是无效的只能是默认震动
//                                .setDefaults(Notification.DEFAULT_ALL)
                .setWhen(System.currentTimeMillis())
                //以下为关键的3行
//                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_PROMO)
                .setFullScreenIntent(pendingIntent, true);
//        notification.setf = Notification.FLAG_INSISTENT or Notification.FLAG_AUTO_CANCEL
        notificationManager?.notify(System.currentTimeMillis().toInt(), notification.build())

        println("FirebaseMessaging888")

    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        try {
            println("FirebaseMessaging onNewToken“:{$token}")
        } catch (e: java.lang.Exception) {
            println("FirebaseMessaging onNewToken failed")
        }
    }

    private fun createNotificationChannel(channelID: String, channelNAME: String, level: Int): NotificationManager? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val manager = getSystemService(Service.NOTIFICATION_SERVICE) as NotificationManager
            val channel = NotificationChannel(channelID, channelNAME, level)
            channel.enableVibration(true)
//            channel.vibrationPattern = longArrayOf(0,5000,0,5000,0,5000)
            channel.vibrationPattern = longArrayOf(0)
            channel.setSound(null, null)
            manager.createNotificationChannel(channel)
            manager
        } else {
            null
        }
    }


//    override fun onStartCommand(p0: Intent?, p1: Int, p2: Int): Int {
//
////        val intent = Intent(this@DemoHmsMessageService, VideoChatActivity::class.java)
//        val intent = Intent(this@DemoHmsMessageService, MainActivity::class.java)
//        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
//        intent.putExtra("id",1658763375115179211L)
//        val pendingIntent = PendingIntent.getActivity(
//            this@DemoHmsMessageService,
//            0,
//            intent,
//            PendingIntent.FLAG_IMMUTABLE
//        )
//
//        println("DemoHmsMessageService666")
//        val channelId: String = createNotificationChannel(
//            "my_channel_ID",
//            "my_channel_NAME",
//            NotificationManager.IMPORTANCE_HIGH
//        )
//        val notificationManager =
//            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//        val notification = NotificationCompat.Builder(this,channelId)
//            .setContentTitle("通话")
//            .setContentText("视频通话请求接听...")
//            .setContentIntent(pendingIntent)
//            .setSmallIcon(R.mipmap.ic_launcher)
//            .setAutoCancel(true)
//            .setWhen(System.currentTimeMillis())
////        notification.setf = Notification.FLAG_INSISTENT or Notification.FLAG_AUTO_CANCEL
//        notificationManager.notify(16657, notification.build())
//
//        val vibrator1 = getSystemService(VIBRATOR_MANAGER_SERVICE) as VibratorManager
//        var defaultVibrator = vibrator1.defaultVibrator
//        defaultVibrator.vibrate(300)
//
////        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation()
////        ARouter.getInstance().build(ArouterPath.PATH_USER_WALLETA_DESCRIBTION).navigation()
//        return super.onStartCommand(p0, p1, p2)
//    }
//
//    private fun createNotificationChannel(
//        channelID: String,
//        channelNAME: String,
//        level: Int
//    ): String {
//        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
//            val channel = NotificationChannel(channelID, channelNAME, level)
//            manager.createNotificationChannel(channel)
//            channelID
//        } else {
//            ""
//        }
//    }
}