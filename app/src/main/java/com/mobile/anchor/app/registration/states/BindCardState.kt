package com.mobile.anchor.app.registration.states

import anchor.app.base.manager.UserInfoManager
import anchor.app.base.utils.Logger
import com.mobile.anchor.app.registration.RegistrationContext
import com.mobile.anchor.app.registration.RegistrationState

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/26 17:21
 * @description : 绑卡状态
 * 处理绑卡阶段的逻辑，绑卡完成后切换到完成状态
 */
class BindCardState : RegistrationState {
    override fun handle(context: RegistrationContext) {
        Logger.i("RegistrationState 相册审核通过，进入绑卡审核阶段")
        if (UserInfoManager.user()?.payInfoStatus == "1") {
            context.setState(CompleteState())
            context.process()
        }
    }

    override fun isMissing(): Boolean = false

    override fun isReviewing(): Boolean = UserInfoManager.user()?.payInfoStatus == "0"

    override fun isCompleted(): Boolean = UserInfoManager.user()?.payInfoStatus == "1"
}