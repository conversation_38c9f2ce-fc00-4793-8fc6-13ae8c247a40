package com.mobile.anchor.app.module.update.util;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;

import androidx.core.app.NotificationCompat;

public class NotificationUtil {


    public static NotificationCompat.Builder startNotificationManager(Context applicationContext, String contentText) {

        NotificationManager notificationManager = (NotificationManager) applicationContext.getSystemService(Context.NOTIFICATION_SERVICE);

        NotificationCompat.Builder notificationBuilder;
        notificationBuilder = new NotificationCompat.Builder(applicationContext, applicationContext.getPackageName());
        notificationBuilder
                // 应用名
                .setContentTitle(applicationContext.getString(applicationContext.getApplicationInfo().labelRes))
                .setContentText(contentText)
                .setLargeIcon(BitmapFactory.decodeResource(applicationContext.getResources(), applicationContext.getApplicationInfo().icon))
//                .setOnlyAlertOnce(true)
                .setAutoCancel(false)
//                .setDefaults(Notification.DEFAULT_LIGHTS) //设置通知的提醒方式： 呼吸灯
                .setPriority(NotificationCompat.PRIORITY_MAX) //设置通知的优先级：最大
//                .setDefaults(Notification.DEFAULT_ALL)
                .setWhen(System.currentTimeMillis())
                .setProgress(100, 0, false)
                .setSmallIcon(applicationContext.getApplicationInfo().icon)
                .setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE)
                .setVibrate(new long[]{0})
                .setSound(null)
//                .setColor(ContextCompat.getColor(context, R.color.c_ff6633))
        ;


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel("to-do", "待办消息",
                    NotificationManager.IMPORTANCE_HIGH);
            channel.enableLights(false);
            channel.enableVibration(false);
            channel.setVibrationPattern(new long[]{0});
            channel.setSound(null, null);
            notificationManager.createNotificationChannel(channel);
            notificationBuilder.setChannelId("to-do");
        }

//        notificationManager.notify(0, notificationBuilder.build());
        return notificationBuilder;
    }

    /**
     * 显示下载的notification
     */
    public static NotificationCompat.Builder showInstallNotification(Context applicationContext, Uri apkUri) {

        NotificationManager notificationManager = (NotificationManager) applicationContext.getSystemService(Context.NOTIFICATION_SERVICE);

        NotificationCompat.Builder notificationBuilder;
        notificationBuilder = new NotificationCompat.Builder(applicationContext, applicationContext.getPackageName());
        notificationBuilder
                .setContentTitle(applicationContext.getApplicationInfo().name)
                .setContentText("应用下载成功，点击安装")
                .setLargeIcon(BitmapFactory.decodeResource(applicationContext.getResources(), applicationContext.getApplicationInfo().icon))
//                .setOnlyAlertOnce(true)
                .setAutoCancel(false)
                .setDefaults(Notification.DEFAULT_LIGHTS) //设置通知的提醒方式： 呼吸灯
                .setPriority(NotificationCompat.PRIORITY_MAX) //设置通知的优先级：最大
                .setDefaults(Notification.DEFAULT_ALL)
                .setWhen(System.currentTimeMillis())
                .setProgress(100, 0, false)
                .setSmallIcon(applicationContext.getApplicationInfo().icon)
                .setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE)
//                .setColor(ContextCompat.getColor(context, R.color.c_ff6633))
        ;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(applicationContext.getPackageName(), "应用更新",
                    NotificationManager.IMPORTANCE_HIGH);
            channel.enableLights(true);
            channel.enableVibration(true);
//            channel.setVibrationPattern(new long[]{0});
//            channel.setSound(null, null);
            notificationManager.createNotificationChannel(channel);
            notificationBuilder.setChannelId(applicationContext.getPackageName());
        }

        //点击安装代码块
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        PendingIntent pi = PendingIntent.getActivity(applicationContext, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        Notification notification = notificationBuilder.setContentIntent(pi).build();
        notificationManager.notify(1, notification);
//        notificationManager.notify(0, notificationBuilder.build());
        return notificationBuilder;
    }


}
