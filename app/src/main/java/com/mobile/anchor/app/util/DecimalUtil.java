package com.mobile.anchor.app.util;

import android.text.TextUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 保留小数位数处理
 */
public class DecimalUtil {

    /**
     * @param num            要处理的num
     * @param afterPointBits 保留小数点后位数
     * @return
     */
    public static double getNum(double num, int afterPointBits) {
        if (afterPointBits <= 0 || afterPointBits > 63) {
            afterPointBits = 2;
        }
        BigDecimal bg = new BigDecimal(num);
        return bg.setScale(afterPointBits, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static String getNumString1(double num, int afterPointBits) {
        if (afterPointBits <= 0 || afterPointBits > 63) {
            afterPointBits = 2;
        }
        String format = "#.0000000000000000000000000000000000000000000000000000000000000000";
        format = format.substring(0, afterPointBits + 2);
        DecimalFormat df = new DecimalFormat(format);
        return df.format(num);
    }

    public static String getNumString2(double num, int afterPointBits) {
        // 可以为0
        if (afterPointBits < 0 || afterPointBits > 63) {
            afterPointBits = 2;
        }
        String format = "%." + afterPointBits + "f";
        return String.format(Locale.ENGLISH,format, num);
    }

    public static String getNumString3(double num, int afterPointBits) {
        if (afterPointBits <= 0 || afterPointBits > 63) {
            afterPointBits = 2;
        }
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(afterPointBits);
        return nf.format(num);
    }

    // 金钱字符串样式（用逗号每三位隔开）
    public static String getMoneyNumString(double num, int afterPointBits) {
        // 可以为0
        if (afterPointBits < 0 || afterPointBits > 63) {
            afterPointBits = 2;
        }
        String format = "%." + afterPointBits + "f";
        String numString = String.format(Locale.ENGLISH,format, num);

        String tail = "";
        String str = "";
        if (numString.indexOf('.') != -1){ //处理小数点
            tail = numString.substring(numString.indexOf('.'));
            numString = numString.substring(0, numString.indexOf('.'));
        }


        StringBuilder sb = new StringBuilder(numString);
        if(sb.length() > 3) {
            for (int i = sb.length() - 3; i > 0; i -= 3) {
                sb.insert(i, ',');
            }
        }
        if (tail != null && !tail.isEmpty()){
            sb.append(tail);
        }
        return sb.toString();
    }

    /**
     * 整数格式化 + 截取两位小数（不四舍五入）
     */
    public static String valueFormatWithTwo(String value) {
        if (TextUtils.isEmpty(value)) {
            return "0";
        }

        BigDecimal bd = new BigDecimal(value);
        DecimalFormat df = new DecimalFormat("##,###,##0.00");//小数点点不够两位补0，例如："0" --> 0.00（个位数补成0因为传入的是0则会显示成：.00，所以各位也补0；）
        String xs = df.format(bd.setScale(2, BigDecimal.ROUND_DOWN));//直接截取小数点后两位（不四舍五入）
        return xs;
    }

    /**
     * 数值超过1万，以"W"为单位保留两位小数，小数为零取整
     * @param number number
     */
    public static String getFormatNumber(int number){
        if (number < 0){
            return "0";
        }
        if (number < 10000){
            return String.valueOf(number);
        }
        double num = number / 10000d;
        return formatDouble(num) + "w";
    }

    public static String formatDouble(double d) {
        BigDecimal bg = new BigDecimal(d).setScale(2, RoundingMode.DOWN);
        double num = bg.doubleValue();
        if (Math.round(num) - num == 0) {
            return String.valueOf((long) num);
        }
        return String.valueOf(num);
    }

    /**
     * double * double = value(精准)
     */
    public static String valueMultiply(String value1,String value2) {
        if (TextUtils.isEmpty(value1)) {
            return "0";
        }
        if (TextUtils.isEmpty(value2)) {
            return "0";
        }
        BigDecimal b1 = new BigDecimal(value1);
        BigDecimal b2 = new BigDecimal(value2);
        BigDecimal b3 = b1.multiply(b2);
        return b3.toString();
    }

    public static long valueMultiplyToInt(String value1, String value2) {
        if (TextUtils.isEmpty(value1)) {
            return 0;
        }
        if (TextUtils.isEmpty(value2)) {
            return 0;
        }
        BigDecimal b1 = new BigDecimal(value1);
        BigDecimal b2 = new BigDecimal(value2);
        BigDecimal b3 = b1.multiply(b2);
        return b3.longValue();
    }
}
