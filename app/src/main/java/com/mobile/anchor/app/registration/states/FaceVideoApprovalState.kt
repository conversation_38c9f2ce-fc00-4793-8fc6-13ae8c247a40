package com.mobile.anchor.app.registration.states

import anchor.app.base.manager.UserInfoManager
import anchor.app.base.utils.Logger
import com.mobile.anchor.app.registration.RegistrationContext
import com.mobile.anchor.app.registration.RegistrationState

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/26 17:20
 * @description : 人脸视频审核状态
 * 处理人脸视频审核阶段的逻辑，审核通过后切换到绑卡状态
 */
class FaceVideoApprovalState : RegistrationState {
    override fun handle(context: RegistrationContext) {
        Logger.i("RegistrationState 相册审核通过，进入人脸视频审核阶段")
        if ((UserInfoManager.user()?.isUploadTestUrl == "1" && UserInfoManager.user()?.testUrlStatus == "1")) {
            context.setState(BindCardState())
            context.process()
        }
    }

    override fun isMissing(): Boolean = UserInfoManager.user()?.isUploadTestUrl == "0"

    override fun isReviewing(): Boolean =
        UserInfoManager.user()?.isUploadTestUrl == "1" && UserInfoManager.user()?.testUrlStatus == "0"

    override fun isCompleted(): Boolean =
        UserInfoManager.user()?.isUploadTestUrl == "1" && UserInfoManager.user()?.testUrlStatus == "1"
}