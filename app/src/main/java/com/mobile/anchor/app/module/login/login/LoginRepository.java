package com.mobile.anchor.app.module.login.login;


import java.util.Map;

import io.reactivex.Flowable;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.repository.IRepository;
import anchor.app.base.retrofit.BaseResult;
import com.mobile.anchor.app.module.login.api.LoginApi;

public class LoginRepository implements IRepository {

    public LoginRepository() {
    }

    public Flowable<BaseResult<LoginBean>> loginByPhone(String authId, String type, String username) {
        return LoginApi.loginByPhone(authId, type, username);
    }

    public Flowable<BaseResult<LoginBean>> loginByThird(String type, String authId) {
        return LoginApi.loginByThird(type, authId);
    }

    public Flowable<BaseResult<LoginBean>> loginByCaptcha(String email, String captchaCode) {
        return LoginApi.loginByCaptcha(email, captchaCode);
    }

    public Flowable<BaseResult<Object>> unionCheck(String unionId) {
        return LoginApi.unionCheck(unionId);
    }

    public Flowable<BaseResult<LoginBean>> modifyChildInfo(Map<String, String> bean) {
        return LoginApi.modifyChildInfo(bean);
    }

    public Flowable<BaseResult<Object>> anchorEmailCode(String email) {
        return LoginApi.anchorEmailCode(email);
    }
}
