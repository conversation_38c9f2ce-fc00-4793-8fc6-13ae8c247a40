package com.mobile.anchor.app.module.user.rank.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.module.user.adapter.UserBindingViewAdapter;
import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.user.bean.RankBean;

public class <PERSON>ItemAdapter extends UserBindingViewAdapter {

    public static final int ITEM_CONTENT = 0;
    public static final int ITEM_CONTENT01 = 1;

    public RankItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_CONTENT, R.layout.user_item_rank01);
        addViewTypeToLayoutMap(ITEM_CONTENT01, R.layout.user_item_rank02);
    }


    @Override
    public int getViewType(@NotNull Object item) {
        if (item instanceof RankBean.RankListDTO) {
            return ITEM_CONTENT;
        }
        return ITEM_CONTENT01;
    }
}
