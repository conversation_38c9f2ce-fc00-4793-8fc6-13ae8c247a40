package com.mobile.anchor.app.registration

import com.mobile.anchor.app.registration.states.AvatarApprovalState

class RegistrationContext {
    private var currentState: RegistrationState = AvatarApprovalState()

    fun setState(state: RegistrationState) {
        currentState = state
    }

    fun getState(): RegistrationState {
        return currentState
    }

    fun process() {
        currentState.handle(this)
    }
}
