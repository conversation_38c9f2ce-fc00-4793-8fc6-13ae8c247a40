package com.mobile.anchor.app.module.user.bean;

import java.io.Serializable;

public class BabyInfo implements Serializable {
    /**
     * kidId : 277
     * name : string
     * gender : 1
     * birthday : 2018-06-01
     * avatarUrl : https://www.baidu.com/img/baidu_jgylogo3.gif?x-oss-process=image/resize,l_200
     * uid : 251
     * relation : null
     */

    private int kidId;
    private String name;
    private int gender;//1代表男孩,2代表女孩
    private String birthday;
    private String avatarUrl;
    private int uid;
    private String relation;

    public int getKidId() {
        return kidId;
    }

    public void setKidId(int kidId) {
        this.kidId = kidId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }
}
