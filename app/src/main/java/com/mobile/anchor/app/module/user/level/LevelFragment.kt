package com.mobile.anchor.app.module.user.level

import android.os.Bundle
import android.view.View
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserLevelFragmentBinding
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseNoModelFragment
import com.mobile.anchor.app.module.user.bean.LevelBean

/**
 * 发现页面
 */
class LevelFragment : BaseNoModelFragment<UserLevelFragmentBinding>() {

    val bean get() = arguments?.getSerializable("bean")

    val index get() = arguments?.getInt("index")
    override fun getLayoutId(): Int {
        return R.layout.user_level_fragment
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    protected fun initView() {
        bindingView?.apply {
            levelBG.setImageResource(resources.getIdentifier("user_level_ic_bg_0$index", "mipmap", "mikchat.app"));

            val loginBean = UserInfoManager.user()
            loginBean?.levelNum

            progressBar.max = (bean as LevelBean.DataDTO).basicNum
            allProgress.text = "/${(bean as LevelBean.DataDTO).basicNum}"
            if (loginBean?.levelNum?.length!! > 0) {
                progressBar.progress = loginBean.levelNum.toInt()
                nowProgress.text = loginBean.levelNum

            } else {
                nowProgress.text = "0"
                progressBar.progress = 0
            }
        }
    }
}