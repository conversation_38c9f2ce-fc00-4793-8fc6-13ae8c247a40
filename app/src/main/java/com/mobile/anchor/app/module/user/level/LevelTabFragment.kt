package com.mobile.anchor.app.module.user.level

import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.RelativeSizeSpan
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserLevelFragmentTabBinding
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseFragment
import com.mobile.anchor.app.module.user.bean.LevelBean


class LevelTabFragment : BaseFragment<LevelViewmodel, UserLevelFragmentTabBinding>() {

    private var fragments = ArrayList<Fragment>()

    override fun getLayoutId(): Int {
        return R.layout.user_level_fragment_tab
    }

    /**
     * 初始化视图
     */
    private fun initViewAndData(homeAdapter: HomeAdapter) {
//        bindingView!!.appBar.visibility = View.VISIBLE
        bindingView?.apply {
            viewPager.adapter = homeAdapter
            viewPager.offscreenPageLimit = 3
            slidingTabLayout.setLineWidth(3)
            slidingTabLayout.setupWithViewPager(viewPager)

            val loginBean = UserInfoManager.user()
            loginBean?.level?.let {
                if (it.length > 0 && it.toInt() >= 1) {
                    viewPager.setCurrentItem(it.toInt() - 1, false)
                } else {
                    viewPager.setCurrentItem(0, false)
                }
            }

            viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                    val tab = slidingTabLayout.getTabAt(position)
                    var nextTab: TabLayout.Tab? = null
                    if (position + 1 < slidingTabLayout.tabCount) {
                        nextTab = slidingTabLayout.getTabAt(position + 1)
                    }
                    tab?.text?.let {
                        val span = SpannableString(it.toString())
                        span.setSpan(
                            RelativeSizeSpan(1.4f - positionOffset * 0.4f),
                            0,
                            span.length,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                        tab.text = span
                    }
                    nextTab?.text?.let {
                        val span = SpannableString(it.toString())
                        span.setSpan(
                            RelativeSizeSpan(1f + positionOffset * 0.4f),
                            0,
                            span.length,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                        nextTab.text = span
                    }
                }

                override fun onPageSelected(position: Int) {
//                    currentPosition = position
                }

                override fun onPageScrollStateChanged(state: Int) = Unit
            })

        }
    }

    override fun loadData() {
        super.loadData()
        viewModel.getLevelInfo().observe(this) { it ->
            var levelBean = LevelBean()
            levelBean.data = it
            levelBean.data?.let {
//        var listOf = listOf<LevelBean.DataDTO>(
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO(),
//            LevelBean.DataDTO()
//        )

                var homeAdapter = HomeAdapter(childFragmentManager)
//        listOf.forEachIndexed { index, dataDTO ->
                it.forEachIndexed { index, dataDTO ->
                    fragments.add(
                        LevelTabFragment().apply {
                            arguments = Bundle().apply {
                                putSerializable("bean", dataDTO)
                                putInt("index", index)
                            }
                        }
                    )
                    homeAdapter.titles.add("Lv${(index + 1).toString()}")
                }
                initViewAndData(homeAdapter)

            }
            showContentView()
        }
    }

    override fun initView() {}

    inner class HomeAdapter(fm: FragmentManager?) : FragmentPagerAdapter(fm!!, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

        //                private val titles = arrayOf(getString(R.string.home_top_tab_cp),getString(R.string.home_top_tab_team),getString(R.string.home_top_tab_entertainment))
//        public var titles = arrayOf("Lv1","Lv2","Lv3","Lv4","Lv5","Lv6","Lv7","Lv8","Lv9","Lv10",)
        public var titles = ArrayList<String>()

        override fun getItem(position: Int) = fragments[position]

        override fun getCount() = fragments.size

        override fun getPageTitle(position: Int): String = titles[position]
    }
}