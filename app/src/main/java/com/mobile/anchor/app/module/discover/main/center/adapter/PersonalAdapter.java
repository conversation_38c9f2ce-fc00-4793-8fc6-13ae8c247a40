package com.mobile.anchor.app.module.discover.main.center.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;


public class PersonalAdapter extends FragmentPagerAdapter {

    private String[] titles ;
    private Fragment[] fragments;

    public PersonalAdapter(@NonNull FragmentManager fm, int behavior, Fragment[] fragments) {
        super(fm, behavior);
        this.fragments = fragments;
    }

    public void setTitles(String[] titles){
        this.titles = titles;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        return fragments[position];
    }

    @Override
    public int getCount() {
        return fragments == null ? 0 : fragments.length;
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        return titles == null ? "" : titles[position];
    }
}
