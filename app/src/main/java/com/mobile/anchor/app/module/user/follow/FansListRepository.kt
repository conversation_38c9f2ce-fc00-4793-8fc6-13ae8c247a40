package com.mobile.anchor.app.module.user.follow

import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.user.api.UserApi
import com.mobile.anchor.app.module.user.follow.bean.FansListBean
import io.reactivex.Flowable

class FansListRepository : IRepository {



    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(id:Long,current: Int,size: Int,type: Int): Flowable<BaseResult<FansListBean>> = UserApi.getFansList(id,current,size,type)

    fun removeAndFollowing(UserId: Long, type: Int, role: String): Flowable<BaseResult<Any>> = UserApi.removeAndFollowing(UserId,type,role)
}