package com.mobile.anchor.app.module.user.level

import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityLevel2Binding
import anchor.app.base.ext.toast
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseActivity

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/23 16:53
 * @description :主播等级
 */
class LevelActivity2 : BaseActivity<LevelViewmodel, ActivityLevel2Binding>() {
    override fun getLayoutId(): Int = R.layout.activity_level2

    override fun initView() {
        bindingView.activity = this
        showContentView()

        bindingView.tvLevel.text = UserInfoManager.user()?.level
        val priceRange = OtherManager.manager(this).sysParam?.value?.let {
            it.get(UserInfoManager.user()?.levelKey)?.toString()?.split("-")?.map { it.toInt() }
        }

        priceRange?.let {
            bindingView.levelSlider.setRange(it.first(), it.last())
            UserInfoManager.user()?.videoPrice?.let {
                bindingView.tvDiamond.text = it.toString()
                bindingView.levelSlider.setCurrent(it)
            }
        }

        bindingView.levelSlider.setOnChangeListener { index, value ->
            bindingView.tvDiamond.text = value.toString()
            viewModel.setupPrice(value.toString()).observe(this) {
                if (it) {
                    toast(getString(R.string.price_seupt_successful))
                    UserInfoManager.user()?.apply {
                        videoPrice = value
                    }?.let { UserInfoManager.setUser(it) }
                }
            }
        }
    }
}