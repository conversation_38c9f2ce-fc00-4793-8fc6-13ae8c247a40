package com.mobile.anchor.app.module.user.gift.adapter

import android.content.Context
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import anchor.app.base.adapter.BindingViewHolder
import com.mobile.anchor.app.module.user.adapter.UserBindingViewAdapter
import com.mobile.anchor.app.BR
import com.mobile.anchor.app.R
import com.mobile.anchor.app.module.user.bean.GiftBean
import com.mobile.anchor.app.databinding.UserItemMyGiftBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/20 11:21
 * @description :礼物适配器
 */
class GiftAdapter(
    context: Context,
    private val items: ObservableArrayList<GiftBean>
) :
    UserBindingViewAdapter<GiftBean>(context, items) {
    init {
        addViewTypeToLayoutMap(0, R.layout.user_item_my_gift)
    }

    override fun getViewType(item: Any): Int = 0

    override fun onBindViewHolder(holder: BindingViewHolder<ViewDataBinding>, position: Int) {
        super.onBindViewHolder(holder, position)
        val binding = DataBindingUtil.bind<UserItemMyGiftBinding>(holder.itemView)
        val item = items[position]
        binding?.setVariable(BR.item, item)
        binding?.tvTitle?.text =
            holder.itemView.context.getString(R.string.received_gift_from_x, item.userNickName)
    }
}