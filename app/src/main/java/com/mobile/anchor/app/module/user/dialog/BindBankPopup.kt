package com.mobile.anchor.app.module.user.dialog

import android.content.Context
import android.view.Menu
import android.view.MenuInflater
import androidx.appcompat.widget.PopupMenu
import anchor.app.base.ext.click
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupBindBankBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/28 14:24
 * @description :绑定银行卡
 */
class BindBankPopup(private val context: Context) : CenterPopupView(context) {

    private lateinit var binding: PopupBindBankBinding
    private val bankList = listOf<String>("a", "b", "c")

    override fun getImplLayoutId(): Int = R.layout.popup_bind_bank

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupBindBankBinding.bind(popupImplView)

        binding.tvBank.click {
            showBankList()
        }

        binding.btnSubmit.click {
            if (binding.etRealName.text?.isEmpty() == true) {
                ToastUtil.show(context.getString(R.string.please_enter_real_name))
                return@click
            }

            if (binding.tvBank.text.isEmpty()) {
                ToastUtil.show(context.getString(R.string.please_choose_bank))
                return@click
            }

            if (binding.etBankAccount.text?.isEmpty() == true) {
                ToastUtil.show(context.getString(R.string.please_enter_bank_account))
                return@click
            }
        }
    }

    fun showBankList() {
        val popupMenu = PopupMenu(context, binding.tvBank) // anchorView是弹出位置的参照视图
        val inflater: MenuInflater = popupMenu.getMenuInflater()
        inflater.inflate(R.menu.menu_popup, popupMenu.getMenu())
        bankList.forEach {
            val menu: Menu = popupMenu.getMenu()
            menu.add(it)
        }

        popupMenu.setOnMenuItemClickListener { item ->
            true
        }
        if (bankList.isNotEmpty()) {
            popupMenu.show()
        }
    }
}

fun showBindBankPopup(context: Context) {
    XPopup.Builder(context).asCustom(BindBankPopup(context)).show()
}