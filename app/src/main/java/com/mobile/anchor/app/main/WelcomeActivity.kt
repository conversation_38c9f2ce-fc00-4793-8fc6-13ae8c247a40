package com.mobile.anchor.app.main

import android.Manifest
import android.annotation.SuppressLint
import android.app.AppOpsManager
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.provider.Settings
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.databinding.ViewDataBinding
import com.mobile.anchor.app.R
import com.mobile.anchor.app.module.login.login.LoginSelectActivity
import com.uber.autodispose.AutoDispose
import io.reactivex.Flowable
import io.reactivex.functions.Consumer
import anchor.app.base.dialog.SelectTipDialog
import anchor.app.base.dialog.SelectTipDialog.ISimpleTipsClickListener
import anchor.app.base.ext.jump
import anchor.app.base.ui.BaseNoModelActivity
import anchor.app.base.utils.Logger
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.immersionbar.standard.BarHide
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import java.util.concurrent.TimeUnit


class WelcomeActivity : BaseNoModelActivity<ViewDataBinding>() {
    private val DELAY_TIME = 1
    private var consumer: Consumer<Long>? = null
    var splashScreen: SplashScreen? = null
    override fun getLayoutId(): Int {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            splashScreen = installSplashScreen()
            splashScreen?.setKeepOnScreenCondition { true }
        }
        return 0
    }

    override fun initView() {
        // 后台返回时可能启动这个页面 http://blog.csdn.net/jianiuqi/article/details/54091181
        if (intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT != 0) {
            finish()
            return
        }
        checkPermissions()

//        splashScreen?.setOnExitAnimationListener { splashScreenView: SplashScreenViewProvider ->
//            splashScreenView.remove() // 一开始以为是remove导致的空指针，所以一开始的修复方式是改为隐藏splashScreenView.setVisibility(View.GONE)，然后上线后发现无效。。。最后只能是删除了这个监听，之后就没有崩溃了
        //  https://blog.csdn.net/qq_17766199/article/details/133494759
//        }
    }

    private fun checkPermissions() {

        val permissionList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
//                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.ACCESS_NETWORK_STATE,
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.POST_NOTIFICATIONS,
//                Manifest.permission.QUERY_ALL_PACKAGES,
//                Manifest.permission.PACKAGE_USAGE_STATS,
            )
        } else {
            arrayOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.ACCESS_NETWORK_STATE,
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO,
//                Manifest.permission.QUERY_ALL_PACKAGES,
//                Manifest.permission.PACKAGE_USAGE_STATS,
            )
        }

        rxPermissions.request(*permissionList).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe { granted: Boolean? ->
                if (!granted!!) {
                    val selectTipDialog = SelectTipDialog.showTips(mContext,
                        mContext.getString(R.string.b24),
                        resources.getString(
                            R.string.Cancel
                        ),
                        resources.getString(R.string.b78),
                        resources.getString(R.string.b79),
                        object : ISimpleTipsClickListener() {
                            override fun confirmClick() {
//                                IntentUtils.gotoPermissionSetting()
                                startActivity(Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS));
                                finish()
                            }

                            override fun concelClick() {
                                finish()
                            }
                        })
                    selectTipDialog.setCanceledOnTouchOutside(false)
                    selectTipDialog.setCancelable(false)

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        splashScreen?.setKeepOnScreenCondition { false }
                    }
                } else {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        splashScreen?.setKeepOnScreenCondition { false }
                    }

//                    getAllLauncherIconPackages(WelcomeActivity@this)
//                    var appInactive = isAppInactive(WelcomeActivity@ this, "anchormikchat.app")

                    println("getAllLauncherIconPackages ========================================================================================================================================================================")

//                    println("getAllLauncherIconPackages : appInactive : "+appInactive)
//                    println("getAllLauncherIconPackages : getTopActivityByUsageStatsManager : "+getTopActivityByUsageStatsManager())

//                    initRouter()
//                    OtherManager.manager(WelcomeActivity@this).getAD()

//                    goHuaWeiManager()
//                    checkVivoPermission()
//                    goVivoManager()


                    setBatteryOptimizations()
//                        setOverlayPermission();
//                        showPermissionsDialog();
//                    goHuaWeiManager();
                }
            }
    }

    private fun goVivoManager() { // com.vivo.permissionmanager/com.vivo.permissionmanager.activity.SoftPermissionDetailActivity
        var intent: Intent = Intent()
        var comp = ComponentName(
            "com.vivo.permissionmanager",
            "com.vivo.permissionmanager.activity.SoftPermissionDetailActivity"
        )
        intent.setComponent(comp)
        intent.putExtra("packagename", packageName)
        startActivity(intent)
    }

    @SuppressLint("Range")
    private fun checkVivoPermission(): Boolean {
        val uri =
            Uri.parse("content://com.vivo.permissionmanager.provider.permission/start_bg_activity")
        val selection = "pkgname = ?"
        val selectionArgs = arrayOf(packageName)
        var result = 1
        val contentResolver = contentResolver
        try {
            contentResolver.query(uri, null, selection, selectionArgs, null).use { cursor ->
                if (cursor!!.moveToFirst()) {
                    result = cursor.getInt(cursor.getColumnIndex("currentstate"))
                }
            }
        } catch (exception: Exception) {
            //ignore
        }
        println("getAllLauncherIconPackages : PopBackgroundPermissionUtil checkVivoPermission result:" + (AppOpsManager.MODE_ALLOWED == result))

        return result == AppOpsManager.MODE_ALLOWED
    }

    private fun goHuaWeiManager() {

        try {
//            var intent: Intent = Intent()
//            var comp = ComponentName("com.huawei.systemmanager", "com.huawei.permissionmanager.ui.MainActivity")
//            var comp = ComponentName("com.huawei.systemmanager", "com.huawei.systemmanager.addviewmonitor.AddViewMonitorActivity")
//            var comp = ComponentName("com.huawei.systemmanager", "com.huawei.notificationmanager.ui.NotificationManagmentActivity")
//            com.android.settings/com.android.settings.applications.InstalledAppDetailsTop
//            var comp = ComponentName("com.android.settings", "com.android.settings.applications.InstalledAppDetailsTop")
//            intent.setComponent(comp)
//            startActivity(intent)

            val intent = Intent()
            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            val uri = Uri.fromParts("package", packageName, null)
            intent.data = uri

// 检查是否存在匹配的Activity
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            }
            println("getAllLauncherIconPackages : goHuaWeiManager  ")

        } catch (e: Exception) {
            println("getAllLauncherIconPackages : goHuaWeiManager : catch")

//            goAppDetailSetting(context)
        }
    }

    private fun showPermissionsDialog() {
        val intent = Intent("miui.intent.action.APP_PERM_EDITOR")
        intent.setClassName(
            "com.miui.securitycenter", "com.miui.permcenter.permissions.PermissionsEditorActivity"
        )
        intent.putExtra("extra_pkgname", packageName)
        startActivity(intent)
    }

    //{@ - 获取所有安装的APK (MATCH_UNINSTALLED_PACKAGES 表示未卸载的APK, 包括APK已被删除但是保留数据的)
    // 需要获取所有apk 添加permission <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    private fun getInstalledPackages(context: Context): List<String>? {
        val installedPackageList: MutableList<String> = ArrayList()
        val installedPackageInfoList: List<PackageInfo> = context.getPackageManager()
            .getInstalledPackages(PackageManager.MATCH_UNINSTALLED_PACKAGES)
        for (packageInfo in installedPackageInfoList) {
            installedPackageList.add(packageInfo.packageName)
        }
        return installedPackageList
    }

    //{@add - 获取所有带有桌面属性的APK -
    // 需要获取所有apk 添加permission <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    private fun getAllLauncherIconPackages(context: Context): List<String>? {
        val launcherIconPackageList: MutableList<String> = ArrayList()
        val intent = Intent()
        intent.action = Intent.ACTION_MAIN
        intent.addCategory(Intent.CATEGORY_LAUNCHER)

        //set MATCH_ALL to prevent any filtering of the results
        val resolveInfos =
            context.packageManager.queryIntentActivities(intent, PackageManager.MATCH_ALL)
        for (info in resolveInfos) {
            println("getAllLauncherIconPackages : processName : " + info.activityInfo.processName)
            println("getAllLauncherIconPackages : packageName : " + info.activityInfo.packageName)
            launcherIconPackageList.add(info.activityInfo.packageName)
        }
        return launcherIconPackageList
    }
    //@add}

    @RequiresApi(Build.VERSION_CODES.M)
    fun isAppInactive(context: Context, packageName: String): Boolean {
        val usageStatsManager: UsageStatsManager =
            context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
        return usageStatsManager.isAppInactive(packageName)
    }

    fun getTopActivityByUsageStatsManager(): String? {
        val endTime = System.currentTimeMillis()
        val beginTime = endTime - 10000
        val usageStatsManager = getSystemService(USAGE_STATS_SERVICE) as UsageStatsManager
        var activityInfo = ""
        val event = UsageEvents.Event()
        val usageEvents = usageStatsManager.queryEvents(beginTime, endTime)
        while (usageEvents.hasNextEvent()) {
            usageEvents.getNextEvent(event)
            if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                activityInfo = event.packageName + "/" + event.className
            }
        }
        return activityInfo
    }

    private fun setBatteryOptimizations() {
        /**
         * 忽略电池优化
         */
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(POWER_SERVICE) as PowerManager
            val hasIgnored = powerManager.isIgnoringBatteryOptimizations(this.packageName)
            //  判断当前APP是否有加入电池优化的白名单，如果没有，弹出加入电池优化的白名单的设置对话框。
            if (!hasIgnored) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                intent.data = Uri.parse("package:" + this.packageName)
                if (intent.resolveActivity(packageManager) != null) {
//                                    startActivity(intent);
                    intentActivityResultLauncher.launch(intent)
                }
            } else {
                setOverlayPermission()
//                initRouter()

            }
        }
    }

    private fun initRouter() {
        consumer = Consumer { time: Long? ->
            SharePreUtil.setShowMobileNetTips(true)
            SharePreUtil.setLoadDeviceStatus(true)
            SharePreUtil.setStartStatus(true)
            if (SharePreUtil.isLogin()) {
                jump(MainActivity::class.java)
            } else {
//                ARouter.getInstance().build(ArouterPath.PATH_LOGIN_SELECT)
//                    .withString("from", "from").withInt("loginType", 1).navigation(mContext, 99)
                jump(LoginSelectActivity::class.java, Bundle().apply {
                    putString("from", "from")
                    putInt("loginType", 1)
                })
            }
            finish()
        }
        Flowable.timer(DELAY_TIME.toLong(), TimeUnit.MICROSECONDS)
            .`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe(consumer)
    }

    fun setOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!Settings.canDrawOverlays(this)) {
                val selectTipDialog = SelectTipDialog.showTips(mContext,
                    mContext.getString(R.string.b24),
                    resources.getString(
                        R.string.Cancel
                    ),
                    resources.getString(R.string.b78),
                    "Can't answer the phone in time without a floating window",
                    object : ISimpleTipsClickListener() {
                        override fun confirmClick() {
//                                startActivity(new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getPackageName())));
                            intentActivityResultLauncher02.launch(
                                Intent(
//                                    "android.permission.SYSTEM_ALERT_WINDOW"
//                                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse(
                                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse(
                                        "package:$packageName"
                                    )
                                )
                            )
                            //                                finish();
                        }

                        override fun concelClick() {
                            finish()
                        }
                    })
                selectTipDialog.setCanceledOnTouchOutside(false)
            } else {
                initRouter()
            }
        } else {
            ToastUtil.show("The system version is too early to use the app")
        }
    }

    var intentActivityResultLauncher = registerForActivityResult<Intent, ActivityResult>(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult? ->
//                ToastUtil.show("google ResultCode :"+result.getResultCode());
        val powerManager = getSystemService(POWER_SERVICE) as PowerManager
        val hasIgnored = powerManager.isIgnoringBatteryOptimizations(this.packageName)
        //  判断当前APP是否有加入电池优化的白名单，如果没有，弹出加入电池优化的白名单的设置对话框。
        Logger.v("intentActivityResultLauncher : $hasIgnored")
        if (hasIgnored) {
            setOverlayPermission()
        } else {
            finish()
        }
    }
    var intentActivityResultLauncher02 = registerForActivityResult<Intent, ActivityResult>(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult? ->
        val b = Settings.canDrawOverlays(this)
        Logger.v("intentActivityResultLauncher02 : $b")
        if (b) {
            initRouter()
        } else {
            finish()
        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).fullScreen(true).hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .statusBarDarkFont(true, 0.2f).init()
    }
}