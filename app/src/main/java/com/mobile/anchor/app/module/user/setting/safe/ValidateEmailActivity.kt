package com.mobile.anchor.app.module.user.setting.safe

import android.annotation.SuppressLint
import android.content.Intent
import android.view.View
import com.blankj.utilcode.util.StringUtils
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityValidateEmailBinding
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.RxTimerUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.view.CaptchaView

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/22 16:34
 * @description :验证邮箱
 */
class ValidateEmailActivity :
    BaseActivity<AccountSafeViewModel, ActivityValidateEmailBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_validate_email

    @SuppressLint("SetTextI18n")
    override fun initView() {
        showContentView()
        setTitleText(R.string.email_validate)
        UserInfoManager.user()?.let {
            bindingView?.tvEmail?.text = it.username
        }

        bindingView.tvGet.setOnClickListener {
            val countdownTime = 5 * 60
            RxTimerUtil.interval(this, 1) {
                bindingView.tvGet.text = "${countdownTime - it - 1} S"
                if (it.toInt() == countdownTime - 1) {
                    bindingView.tvGet.isEnabled = true
                    RxTimerUtil.cancel(this)
                    bindingView.tvGet.text =
                        StringUtils.getString(R.string.label_email_get_captcha)
                }
            }
            viewModel.getEmailCaptcha(bindingView.tvEmail.text.toString()).observe(this) {
                if (it) {
//                    RxTimerUtil.interval(this, 1) {
//                        bindingView.tvGet.text = "${countdownTime - it - 1} S"
//                        if (it.toInt() == countdownTime - 1) {
//                            bindingView.tvGet.isEnabled = true
//                            RxTimerUtil.cancel(this)
//                            bindingView.tvGet.text =
//                                StringUtils.getString(R.string.label_email_get_captcha)
//                        }
//                    }
                } else {
                    RxTimerUtil.cancel(this)
                }
            }
        }

        var captchaCode: String? = null
        bindingView?.captchaView?.setOnCodeFinishListener(object :
            CaptchaView.OnCodeFinishListener {
            override fun onTextChange(view: View, content: String) {
            }

            override fun onComplete(view: View, content: String) {
                captchaCode = content
            }
        })
        bindingView?.btnNext?.setOnClickListener {
            if (captchaCode == null || captchaCode.toString().length < 6) {
                ToastUtil.show(getString(R.string.toast_email_code_empty))
                return@setOnClickListener
            }
            startActivity(Intent(
                this, ModifyPasswordActivity::class.java
            ).apply {
                putExtra("type", ValidateType.EMAIL.value)
                putExtra("captcha", captchaCode)
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}