package com.mobile.anchor.app.module.discover.api;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.RxSchedulers;
import com.mobile.anchor.app.module.discover.bean.CallSettlementBean;
import com.mobile.anchor.app.module.user.bean.RankBean;
import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean;
import com.mobile.anchor.app.module.discover.main.center.bean.ReportBean;
import com.mobile.anchor.app.module.discover.main.center.moment.bean.MomentDetailBean;
import com.mobile.anchor.app.module.discover.main.center.moment.bean.MomentListBean;
import com.mobile.anchor.app.module.discover.main.chat.videochat.DeductBean;
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean;
import io.reactivex.Flowable;
import com.mobile.anchor.app.module.discover.main.homelist.bean.VideoHistoryBean;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.api
 * @class describe
 * @time 2019/8/9 12:21
 * @class describe
 */
public class DiscoverApi {

    public static Flowable<BaseResult<AnchorItemBean>> getHomeList(int current, int size, String type, String country) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);
        request.put("type", type);
        request.put("country", country);
        if (Objects.equals(type,"1")) {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).getFollowList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
        }else {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).getHomeList(request)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
        }
    }

    public static Flowable<BaseResult<Object>> getCallAnchor(String channelId, String type) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getCallAnchor(channelId,type)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 扣除费用-每分钟
     *
     * @return
     */
    public static Flowable<BaseResult<DeductBean>> getVideoDeduct(String channelId,String firstFlag) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("channelId", channelId);
        request.put("firstFlag", firstFlag);
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoDeduct(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }
    public static Flowable<BaseResult<Object>> evaluateLabelNeed(String channelId) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).evaluateLabelNeed(channelId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 获取视频通话的频道id
     *
     * @return
     */
    public static Flowable<BaseResult<String>> getVideoChannel(long id) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoChannel(id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getVideoPush(String channelId) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoPush(channelId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<List<RankBean>>> getAnchorCharts(String type) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getAnchorCharts(type)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<CallSettlementBean>> getVideoCallTime(String channelId) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoCallTime(channelId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }
    public static Flowable<BaseResult<Object>> getVideoPushReject(String channelId) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoPushReject(channelId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<PersonalCenterBean>> getPersonalCenter(long id, String userRole) {
        if (userRole.equals("1")) {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).getPersonalCenter(id)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
        }else return BaseApiClient.getInstance().create(IDiscoverApi.class).queryUserInfo(id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }
    public static Flowable<BaseResult<MomentDetailBean>> getMomentDetail(long id) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getMomentDetail(id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<MomentListBean>> getMomentDetailList (int current, int size, long id) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);
        request.put("userId", id);

        return BaseApiClient.getInstance().create(IDiscoverApi.class).getMomentDetailList(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }




    public static Flowable<BaseResult<Object>> removeAndFollowing(long UserId,int type,
                                                                  String userRole) {
        if (type == 1) {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).userSetFollow(UserId, userRole)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
        }else {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).userSetUnFollow(UserId, userRole)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
        }

    }

    public static Flowable<BaseResult<Object>> setBlack(long UserId, String userRole) {

        return BaseApiClient.getInstance().create(IDiscoverApi.class).setBlack(UserId, userRole)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> userBuyPhoto(long UserId
    ) {
            return BaseApiClient.getInstance().create(IDiscoverApi.class).userBuyPhoto(UserId)
                    .subscribeOn(RxSchedulers.INSTANCE.getIo())
                    .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    /**
     * 获取视频通话的token
     *.
     * @return
     */
    public static Flowable<BaseResult<String>> getVideoToken(String id, String userCode) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoToken(id,userCode)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    /**
     * 挂断视频通话
     *
     * @return
     */
    public static Flowable<BaseResult<String>> setVideoHangup(String id) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).setVideoHangup(id)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<Object>> publicRepoet(ReportBean bean) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).publicRepoet(bean)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> anchorFileSave(Map<String, Object> bean) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).anchorFileSave(bean)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<VideoHistoryBean>> getVideoHistory(int current, int size) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);
        return BaseApiClient.getInstance().create(IDiscoverApi.class).getVideoHistory(request)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> videoMissedCall(String channelId) {
        return BaseApiClient.getInstance().create(IDiscoverApi.class).videoMissedCall(channelId)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

}
