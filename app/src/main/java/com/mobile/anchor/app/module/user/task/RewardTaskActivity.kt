package com.mobile.anchor.app.module.user.task

import android.widget.ProgressBar
import com.angcyo.dsladapter.DslAdapterItem
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityRewardTaskBinding
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeVisible
import anchor.app.base.ext.toast
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import com.mobile.anchor.app.main.MainActivity
import com.mobile.anchor.app.module.user.album.AlbumManageActivity
import com.mobile.anchor.app.module.user.bean.RewardTaskItemBean
import com.mobile.anchor.app.module.user.main.UserViewModel
import com.mobile.anchor.app.util.RewardTaskTypeText

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/23 15:57
 * @description :奖励任务
 */
class RewardTaskActivity : BaseActivity<UserViewModel, ActivityRewardTaskBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_reward_task

    override fun initView() {
        bindingView?.activity = this
        bindingView?.lifecycleOwner = this
    }

    override fun loadData() {
        super.loadData()
        viewModel.getRewardList().observe(this) {
            showContentView()
            bindingView.recyclerView.clearAllItems()
            val list = buildList {
                addAll(it.oneTimeMissions)
                addAll(it.dailyMissions)
            }
            bindingView.recyclerView.append<DslAdapterItem>(list) {
                itemLayoutId = R.layout.user_item_reward_task
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as RewardTaskItemBean
                    val (taskTitle, taskDesc) = RewardTaskTypeText.get(
                        id = item.missionId, item.current, item.standardValue
                    )
                    itemHolder.tv(R.id.tv_name)?.text = taskTitle
                    itemHolder.tv(R.id.tv_description)?.text = taskDesc
                    itemHolder.tv(R.id.tv_diamond)?.text = "+${item.bonus}"
                    itemHolder.v<ProgressBar>(R.id.progress_bar)?.apply {
                        makeVisible(item.hasProgress == "1")
                        max = item.standardValue
                        progress = item.current
                    }

                    itemHolder.tv(R.id.tv_get)?.apply {
                        makeVisible(item.toType > 0)
                        text = if (item.canGet == "1") {
                            getString(R.string.task_get)
                        } else {
                            getString(R.string.task_go)
                        }

                        click {
                            if (item.canGet == "1") {
                                viewModel.claimTask(item.id).observe(this@RewardTaskActivity) {
                                    if (it) {
                                        toast(getString(R.string.received_successfully))
                                        loadData()
                                    }
                                }
                            } else {
                                when (item.toType) {
                                    3, 6 -> {
                                        jump(MainActivity::class.java)
                                        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_FIVE, 0)
                                    }

                                    7 -> {
                                        jump(AlbumManageActivity::class.java)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}