package com.mobile.anchor.app.module.user.setting.blacklist

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.View
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserItemBlacklistBinding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.dialog.OperationTipDialog
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CountryUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.databinding.ActivityBlackListBinding
import com.mobile.anchor.app.module.user.setting.blacklist.adapter.BlackListItemAdapter
import com.mobile.anchor.app.module.user.setting.blacklist.bean.BlackListBean

/**
 * 宝宝信息页面
 */
class BlackListActivity : BaseActivity<BlackListViewmodel?, ActivityBlackListBinding?>(),
    ItemClickPresenter<BlackListBean.RecordsDTO?>, ItemDecorator {
    val from get() = intent.getStringExtra("from")
    lateinit var adapter: BlackListItemAdapter
    var dataList: ObservableArrayList<BlackListBean.RecordsDTO>? = null
    var isMoreLoading = false
    var current = 0
    var size = 50
    override fun getLayoutId(): Int {
        return R.layout.activity_black_list
    }

    override fun initView() {
        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
        bindingView?.activity = this
        bindingView?.lifecycleOwner = this
        bindingView?.refreshLayout?.setColorSchemeColors(Color.parseColor("#FFB909"))
        bindingView?.refreshLayout?.setOnRefreshListener { loadData() }
        dataList = ObservableArrayList<BlackListBean.RecordsDTO>()
        adapter = BlackListItemAdapter(mContext, dataList!!)
        adapter.itemDecorator = this
        adapter.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView?.recyclerView?.layoutManager = gridLayoutManager
        bindingView?.recyclerView?.adapter = adapter
        bindingView?.recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val position = gridLayoutManager.findLastVisibleItemPosition()
                if (position > dataList!!.size - 10 && !isMoreLoading && dataList!!.size == current * size) {
                    isMoreLoading = true
                    current += 1
                    loadData()
                }
            }
        })
    }

    override fun loadData() {
        showLoading()
        /**  <AUTHOR>  Description : 加载兑换专区数据
         */
        viewModel?.getList(current, size)?.observe(
            this
        ) { bean: List<BlackListBean.RecordsDTO?> ->
            showContentView()
            if (bindingView?.refreshLayout?.isRefreshing == true) { //
                dataList?.clear()
                adapter.notifyDataSetChanged()
                bindingView?.refreshLayout?.isRefreshing = false
            } else {
                isMoreLoading = false
            }
            if (bean.size > 0) {
                dataList!!.addAll(bean)
            } else {
//                        showLoading();
//                viewModel?.error?.setValue(
//                    ResultException(
//                        BaseResult.NOT_DATA,
//                        getString(R.string.a0018)
//                    )
//                )
            }
        }
    }

    var operationTipDialog: OperationTipDialog? = null

    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: BlackListBean.RecordsDTO?) {
        operationTipDialog = OperationTipDialog {
            val bean = item as BlackListBean.RecordsDTO
            viewModel?.removeBlackList(bean.blackUserId.toLong())?.observe(this) {
                dataList?.clear()
                loadData()
            }
            operationTipDialog?.dismiss()

        }
        operationTipDialog?.content =
            getString(R.string.Are_you_sure_to_remove_that_user_from_your_blacklist)
        operationTipDialog?.show(supportFragmentManager, "BlackListActivity")


//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>,
        position: Int,
        viewType: Int
    ) {
        val binding01 = holder.binding
        if (binding01 is UserItemBlacklistBinding) {
            val bean = dataList!![position] as BlackListBean.RecordsDTO
            val binding = binding01

//            Glide.with(mContext).load(bean.blackHeadFileName).apply(RequestOptions.bitmapTransform(CircleCrop())).into(binding.head)

            binding01.head.setTag(R.id.head, position)
            bean.blackHeadFileName?.let { it.ifEmpty { null } }?.apply {
                Glide.with(mContext)
                    .asBitmap()
                    .load(bean.blackHeadFileName)
                    .into(object : CustomTarget<Bitmap?>() {
                        override fun onResourceReady(
                            @NonNull resource: Bitmap,
                            @Nullable transition: Transition<in Bitmap?>?
                        ) {
                            if (position == binding01.head.getTag(R.id.head)) {
                                binding01.head.setImageBitmap(resource)
                            }
                        }

                        override fun onLoadCleared(@Nullable placeholder: Drawable?) {}
                    })
            }

            Glide.with(mContext).load(bean.level)
                .apply(RequestOptions.bitmapTransform(CircleCrop()))
                .into(binding.level)
            binding.name.text = bean.blackNickName

            binding.country.setImageDrawable(
                CountryUtil.countryCodeToImage(
                    mContext,
                    bean?.country
                )
            )
            bean.country?.apply { binding.countryTV.text = bean.country }

//            bean.level?.let { it.ifEmpty { null } }?.apply {
////                    Glide.with(mContext).load(loginBean.level).apply(RequestOptions.bitmapTransform(CircleCrop())).into(bindingView!!.level)
//                if (this.toInt() > 0) {
//                    binding.level.setImageResource(resources.getIdentifier("base_ic_level_num_0$this", "mipmap", "mikchat.app"))
//                }
//            }


//        bindingView.country.setImageDrawable(mActivity.getDrawable(R.mipmap.user_ic_sex2));
//        bindingView.country.setText(loginBean.getLastCountry());
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        //        if (!"user".equals(from)) {
//            //跳转首页
//            ActivityUtil.getInstance().finishAllActivityExcept("mikchat.app.user.baby.BabyInfoActivity");
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            super.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarDarkFont(false, 0.2f).navigationBarColor(R.color.colorTheme).init()
    }

    companion object {
        const val TAG = "BlackListActivity"
    }
}