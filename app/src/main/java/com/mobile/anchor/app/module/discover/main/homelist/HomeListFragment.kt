package com.mobile.anchor.app.module.discover.main.homelist

import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeVisible
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.retrofit.RxSchedulers.ui
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseFragment
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.SharePreUtil
import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.core.text.isDigitsOnly
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.ColorUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverHomeFragmentListBinding
import com.mobile.anchor.app.databinding.DiscoverItemHomeNewBinding
import com.mobile.anchor.app.module.discover.main.center.PersonalCenterActivity
import com.mobile.anchor.app.module.discover.main.homelist.adapter.HomeListItemAdapter
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import com.uber.autodispose.AutoDispose

/**
 * 推荐、亲密
 */
class HomeListFragment() : BaseFragment<HomeListViewmodel, DiscoverHomeFragmentListBinding>(),
    ItemClickPresenter<AnchorItemBean.RecordsDTO>, ItemDecorator {
    public var pageType = 1
    var adapter: HomeListItemAdapter? = null
    var dataList: ObservableArrayList<Any?> = ObservableArrayList<Any?>()


    override fun getLayoutId(): Int {
        return R.layout.discover_home_fragment_list
    }

    override fun initView() {

        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
//        pageType = getArguments().getInt("pageType");
        bindingView!!.fragment = this
        bindingView!!.lifecycleOwner = this
//        bindingView!!.refreshLayout.setColorSchemeColors(Color.parseColor("#FFB909"))
        bindingView!!.refreshLayout.setOnRefreshListener {
            loadData2(true)
        }
        bindingView!!.refreshLayout.setOnLoadMoreListener {
            loadData2(false)
        }
        bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)

        adapter = HomeListItemAdapter(mContext, dataList)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this

        val spacing = resources.getDimensionPixelSize(R.dimen.dp_7)
        bindingView!!.recyclerView.addItemDecoration(
            GridSpacingItemDecoration(2, spacing, false)
        )

        val gridLayoutManager = GridLayoutManager(context, 2)
        //        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
//            @Override
//            public int getSpanSize(int position) {
//                int viewType = adapter.getItemViewType(position);
//                if (viewType == HomeListItemAdapter.ITEM_DESCRIPTION) {
//                    return 2;
//                } else {
//                    return 1;
//                }
//            }
//        });
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE02, Integer::class.java)
            .observeOn(ui).`as`(AutoDispose.autoDisposable(scopeProvider))
            .subscribe { index: Integer ->
                if (pageType == index.toInt()) {
                    loadData2(true)
                    //        通话结束或者其他都去刷新一下金币
                    RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)

                }
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_BLOCK, Long::class.java)
            .observeOn(ui).`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe { id: Long ->
                dataList.forEachIndexed { index, any ->
                    if (any is AnchorItemBean.RecordsDTO) {
                        val item = any as AnchorItemBean.RecordsDTO
                        if (item.id == id) {
                            adapter?.notifyItemRemoved(index)
                        }
                    }
                }
            }
    }

    var current = 1
    var beforeLoadPosition = 0
    var size = 20
    var isLoading = false

    /** <AUTHOR>
     * newInstance() Description : 构建当前fragment实例对象
     * @params [type] : 不同页面的参数类型(判断是进场特效,还是 头像框还是气泡等)
     * @return com.coolpi.mutter.mine.ui.decoratemyself.decorate.headdecorate.VarietyDecorateFragment : 实例对象
     */
    //    public static HomeListFragment newInstance(int type) {
    //        HomeListFragment varietyDecorateFragment = new HomeListFragment();
    //        Bundle bundle = new Bundle();
    //        bundle.putInt("pageType", type);
    //        varietyDecorateFragment.setArguments(bundle);
    //        return varietyDecorateFragment;
    //    }
    init {
        this.pageType = pageType
    }

    var country: String = ""
    override fun loadData() {
        isLoading = false
//        loadData2(true)
    }

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {
        if (isLoading) {
            return
        }
        if (reference) {
            current = 1
        }
        isLoading = true
        viewModel!!.getList(current, size, pageType.toString(), country)
            .observe(this) { bean: AnchorItemBean ->
                showContentView()
                bindingView!!.refreshLayout.finishLoadMore()
                bindingView!!.refreshLayout.finishRefresh()
                isLoading = false
                current += 1

                if (reference) { //
                    bindingView!!.refreshLayout.setNoMoreData(false)
                    dataList!!.clear()
                }
//            isMoreLoading = false
                if (bean.records.size > 0) {
                    dataList!!.addAll(bean.records)
                }
                if (bean.records.size < size) {
                    bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)
                    bindingView!!.refreshLayout.setNoMoreData(true)
                }

                if (dataList!!.size == 0) {
                    viewModel!!.error.value =
                        ResultException(BaseResult.NOT_DATA, getString(R.string.a0018))
                }
                adapter!!.notifyDataSetChanged()
            }
    }

    override fun onResume() {
        super.onResume()
//        refereData()
        // 销毁置空,防止IMCenter中阻断该id的消息,正常情况下不到到这里,只是防止异常情况下videoChatActivity销毁没有调用该函数
        SharePreUtil.setVideoChatID(null)
        loadData2(true)
    }

    fun selectCountry(country: String) {
        this.country = country
        loadData()
    }

    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: AnchorItemBean.RecordsDTO) {
//        if (UserInfoManager.UserAuditState(mActivity) != "1") {
//            return
//        }
        if (v.id == R.id.rl) {
            jump(PersonalCenterActivity::class.java, Bundle().apply {
                putLong("id", item.id ?: item.anchorId)
                putString("userRole", "2")
            })
        } else if (v.id == R.id.chat) {
            val hashMap: java.util.HashMap<String, String> = java.util.HashMap<String, String>()
            hashMap["headFileName"] = item.headFileName
            hashMap["nikeName"] = item.nickName
            hashMap["id"] = (item.id ?: item.anchorId).toString()
            RxBus.getDefault()
                .post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(hashMap))
        }
    }

    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
        val binding = holder.binding
        if (binding is DiscoverItemHomeNewBinding) {
            val bean = dataList[position] as AnchorItemBean.RecordsDTO
            when (bean.onlineStatus) {
                "0" -> {
                    binding.tvStateText.setText(R.string.offline)
                    binding.tvStateText.setTextColor(ColorUtils.getColor(R.color.color_99ffffff))
                    binding.tvState.setTextColor(ColorUtils.getColor(R.color.color_99ffffff))
                }

                "1" -> {
                    binding.tvStateText.setText(R.string.online)
                    binding.tvStateText.setTextColor(ColorUtils.getColor(R.color.color_21c76e))
                    binding.tvState.setTextColor(ColorUtils.getColor(R.color.color_21c76e))
                }

                "2" -> {
                    binding.tvStateText.setText(R.string.busy)
                    binding.tvStateText.setTextColor(ColorUtils.getColor(R.color.color_eb6239))
                    binding.tvState.setTextColor(ColorUtils.getColor(R.color.color_eb6239))
                }
            }

            binding.levelLabel.apply {
                makeVisible(bean.level.isDigitsOnly() && bean.level.toInt() > 0)
                if (bean.level.isDigitsOnly() && bean.level.toInt() > 0) {
                    current = bean.level.toInt()
                }
            }
            binding.vipLabel.apply {
                makeVisible(bean.isVIP)
            }

            binding.areaName.text = bean.country ?: ""
            binding.tvAge.apply {
                makeVisible(!TextUtils.isEmpty(bean.age))
                text = bean.age ?: ""
            }

            binding.anchorName.text = bean.nickName ?: ""
            bean.headFileName?.let {
                it.ifEmpty {
                    binding.imageViewHead.setImageDrawable(mActivity.getDrawable(R.mipmap.ic_default_image))
                }
            }?.apply {
                binding.imageViewHead.setTag(R.id.imageViewHead, position)
//                Glide.with(mContext).load(bean.headFileName).into(binding01.imageViewHead)

                Glide.with(mContext).asBitmap().load(bean.headFileName)
                    .into(object : CustomTarget<Bitmap?>() {
                        override fun onResourceReady(
                            @NonNull resource: Bitmap, @Nullable transition: Transition<in Bitmap?>?
                        ) {
                            if (position == binding.imageViewHead.getTag(R.id.imageViewHead)) {
                                binding.imageViewHead.setImageBitmap(resource)
                            }
                        }

                        override fun onLoadCleared(@Nullable placeholder: Drawable?) {}
                    })
            }
        }
    }

}