package com.mobile.anchor.app.module.user.bean;

public class GroupListBean {

    private String auditFlag;
    private String auditVideo;
    private String checkTime;
    private String connectSucRate;
    private Integer connectTimes;
    private Integer connectUsers;
    private String country;
    private String createTime;
    private String email;
    private String ensureDay;
    private Integer ensureSalary;
    private String expireDay;
    private String firebaseToken;
    private String gender;
    private String groundFileName;
    private String headFileName;
    private Long id;
    private Integer incomeDiamond;
    private String language;
    private String lastIp;
    private String level;
    private Integer newSource;
    private String nickName;
    private String normalGroundFileName;
    private String normalRoomTitle;
    private String onlineStatus;
    private String packageVersion;
    private String password;
    private String phone;
    private String phoneModel;
    private Integer popularSource;
    private String recommendLimit;
    private String remark;
    private String roomTitle;
    private String status;
    private Integer totalIncome;
    private Integer unionId;
    private String unionName;
    private String updateTime;
    private String userCode;
    private String username;
    private Integer videoPrice;
    private Integer warnTimes;

    public String getAuditFlag() {
        return auditFlag;
    }

    public void setAuditFlag(String auditFlag) {
        this.auditFlag = auditFlag;
    }

    public String getAuditVideo() {
        return auditVideo;
    }

    public void setAuditVideo(String auditVideo) {
        this.auditVideo = auditVideo;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getConnectSucRate() {
        return connectSucRate;
    }

    public void setConnectSucRate(String connectSucRate) {
        this.connectSucRate = connectSucRate;
    }

    public Integer getConnectTimes() {
        return connectTimes;
    }

    public void setConnectTimes(Integer connectTimes) {
        this.connectTimes = connectTimes;
    }

    public Integer getConnectUsers() {
        return connectUsers;
    }

    public void setConnectUsers(Integer connectUsers) {
        this.connectUsers = connectUsers;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEnsureDay() {
        return ensureDay;
    }

    public void setEnsureDay(String ensureDay) {
        this.ensureDay = ensureDay;
    }

    public Integer getEnsureSalary() {
        return ensureSalary;
    }

    public void setEnsureSalary(Integer ensureSalary) {
        this.ensureSalary = ensureSalary;
    }

    public String getExpireDay() {
        return expireDay;
    }

    public void setExpireDay(String expireDay) {
        this.expireDay = expireDay;
    }

    public String getFirebaseToken() {
        return firebaseToken;
    }

    public void setFirebaseToken(String firebaseToken) {
        this.firebaseToken = firebaseToken;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGroundFileName() {
        return groundFileName;
    }

    public void setGroundFileName(String groundFileName) {
        this.groundFileName = groundFileName;
    }

    public String getHeadFileName() {
        return headFileName;
    }

    public void setHeadFileName(String headFileName) {
        this.headFileName = headFileName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIncomeDiamond() {
        return incomeDiamond;
    }

    public void setIncomeDiamond(Integer incomeDiamond) {
        this.incomeDiamond = incomeDiamond;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Integer getNewSource() {
        return newSource;
    }

    public void setNewSource(Integer newSource) {
        this.newSource = newSource;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getNormalGroundFileName() {
        return normalGroundFileName;
    }

    public void setNormalGroundFileName(String normalGroundFileName) {
        this.normalGroundFileName = normalGroundFileName;
    }

    public String getNormalRoomTitle() {
        return normalRoomTitle;
    }

    public void setNormalRoomTitle(String normalRoomTitle) {
        this.normalRoomTitle = normalRoomTitle;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getPackageVersion() {
        return packageVersion;
    }

    public void setPackageVersion(String packageVersion) {
        this.packageVersion = packageVersion;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneModel() {
        return phoneModel;
    }

    public void setPhoneModel(String phoneModel) {
        this.phoneModel = phoneModel;
    }

    public Integer getPopularSource() {
        return popularSource;
    }

    public void setPopularSource(Integer popularSource) {
        this.popularSource = popularSource;
    }

    public String getRecommendLimit() {
        return recommendLimit;
    }

    public void setRecommendLimit(String recommendLimit) {
        this.recommendLimit = recommendLimit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRoomTitle() {
        return roomTitle;
    }

    public void setRoomTitle(String roomTitle) {
        this.roomTitle = roomTitle;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(Integer totalIncome) {
        this.totalIncome = totalIncome;
    }

    public Integer getUnionId() {
        return unionId;
    }

    public void setUnionId(Integer unionId) {
        this.unionId = unionId;
    }

    public String getUnionName() {
        return unionName;
    }

    public void setUnionName(String unionName) {
        this.unionName = unionName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getVideoPrice() {
        return videoPrice;
    }

    public void setVideoPrice(Integer videoPrice) {
        this.videoPrice = videoPrice;
    }

    public Integer getWarnTimes() {
        return warnTimes;
    }

    public void setWarnTimes(Integer warnTimes) {
        this.warnTimes = warnTimes;
    }
}
