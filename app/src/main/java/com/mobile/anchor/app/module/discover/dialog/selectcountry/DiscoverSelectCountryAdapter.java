package com.mobile.anchor.app.module.discover.dialog.selectcountry;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.discover.adapter.DiscoverBindingViewAdapter;


public class DiscoverSelectCountryAdapter extends DiscoverBindingViewAdapter {

    public static final int ITEM_CONTENT = 0;

    public DiscoverSelectCountryAdapter(@NotNull Context context,
                                        @NotNull ObservableArrayList<Object> list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_CONTENT, R.layout.discover_item_select_country);
    }

    @Override
    public int getViewType(@NotNull Object item) {
        return ITEM_CONTENT;
    }
}
