package com.mobile.anchor.app.module.user.record

import com.mobile.anchor.app.R
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeGone
import anchor.app.base.ext.makeVisible
import anchor.app.base.ext.toast
import anchor.app.base.ui.BaseFragment
import anchor.app.base.utils.ActivityUtil
import anchor.app.base.utils.camera.CameraControl
import anchor.app.base.utils.camera.CommonUtil
import anchor.app.base.utils.camera.FileUtils
import anchor.app.base.utils.camera.RecordStatus
import anchor.app.base.utils.camera.RecordVideoInterface
import com.mobile.anchor.app.databinding.FragmentRecordVideoBinding
import com.mobile.anchor.app.main.MainActivity
import com.mobile.anchor.app.module.user.editedInfo.EditInfoModel
import java.io.File
import java.text.DecimalFormat

class RecordVideoFragment : BaseFragment<EditInfoModel, FragmentRecordVideoBinding>(),
    RecordVideoInterface {
    private var record_status = RecordStatus.TYPE_DEFAULT
    private val mMinDuration = 10000
    private var recordFile: String? = null
    private var time = 0
    private var cameraControl: CameraControl? = null

    override fun getLayoutId(): Int = R.layout.fragment_record_video

    override fun initView() {
        showContentView()
        initDataFragment()
        bindingView.composeRecordBtn.click {
            switchRecord()
        }
        bindingView.tvUpload.click {
            cameraControl?.finishControl()
            bindingView.composeRecordBtn.releaseRecord()

            if (recordFile == null || !File(recordFile).exists())
                return@click
            LoadingDialog.getInstance(mContext).show()
            viewModel.uploadPicture(File(recordFile)).observe(this) {
                viewModel.uploadRecordVideo(it).observe(this) {
                    LoadingDialog.getInstance(mContext).dismiss()
                    if (it) {
                        toast(getString(R.string.upload_success))
                        ActivityUtil.getInstance().finishAllActivity()
                        jump(MainActivity::class.java)
                    }
                }
            }
        }
        bindingView.tvRecordAgain.click {
            recordAgain()
        }
        bindingView.recorderFacing.click {
            switchFacing()
        }
    }

    private fun initDataFragment() {
        cameraControl = CameraControl(activity, bindingView.textureView)
        cameraControl?.setRecordVideoInterface(this)
    }

    fun startRecord() {
        cameraControl?.prepareMediaRecorder()
        cameraControl?.startMediaRecorder() //开始录制
    }

    private fun stopRecord() {
        cameraControl?.stopRecording(true)
    }

    override fun startRecordRes() {
        updateRecordStatus(RecordStatus.TYPE_START_RECORD)
    }

    override fun onRecording(milliSecond: Long) {
        if (activity != null && !requireActivity().isFinishing) {
            requireActivity().runOnUiThread {
                bindingView.composeRecordBtn.setProgress(milliSecond.toInt())
                time = (milliSecond / 1000f).toInt()
                val fnum = DecimalFormat("##0")
                val timeFormat = fnum.format(time.toLong())
                bindingView.processTime.text = formatSecondsToTime(time)
                if (time < mMinDuration / 1000f) {
                    bindingView.composeRecordBtn.tag = "false"
                } else {
                    bindingView.composeRecordBtn.tag = "true"
                }
            }
        }
    }

    override fun onRecordFinish(videoPath: String?) {
        recordFile = videoPath
        bindingView.playerView.setDataBean(videoPath, time)
        updateRecordStatus(RecordStatus.TYPE_STOP_RECORD)
    }

    override fun onRecordError() {
        toast(getString(R.string.record_video_error))
        updateRecordStatus(RecordStatus.TYPE_STOP_RECORD)
    }

    private fun switchFacing() {
        if (!CommonUtil.fastClick()) {
            cameraControl?.switchCamera()
        }
    }

    private fun recordAgain() {
        FileUtils.deleteFile(recordFile)
        updateRecordStatus(RecordStatus.TYPE_DEFAULT)
        cameraControl?.resetCamera()
    }

    private fun switchRecord() {
        when (record_status) {
            RecordStatus.TYPE_DEFAULT, RecordStatus.TYPE_STOP_RECORD -> startRecord()
            RecordStatus.TYPE_START_RECORD -> if (bindingView.composeRecordBtn.tag == "false") {
                toast(getString(R.string.aleast_record_10s))
            } else {
                stopRecord()
            }
        }
    }

    private fun updateRecordStatus(type: Int) {
        record_status = type
        when (type) {
            RecordStatus.TYPE_DEFAULT -> {
                bindingView.textureView.makeVisible()
                bindingView.composeRecordBtn.makeVisible()
                bindingView.composeRecordBtn.releaseRecord()
                bindingView.playerView.makeGone()
    //            bindingView.recordLayout.setBackgroundResource(R.color.color_cardBackground)
                bindingView.rlRecordTip.makeGone()
                bindingView.rlResult.makeGone()
                bindingView.recorderFacing.makeVisible()
            }
            RecordStatus.TYPE_START_RECORD -> {
                bindingView.textureView.makeVisible()
                bindingView.composeRecordBtn.makeVisible()
                bindingView.composeRecordBtn.startRecord()
                bindingView.playerView.makeGone()
    //            record_layout!!.setBackgroundResource(R.drawable.tran)
    //            bindingView.recordLayout.setBackgroundResource(R.drawable.shape_card_background)
                bindingView.rlRecordTip.makeVisible()
                bindingView.rlResult.makeGone()
                bindingView.recorderFacing.makeGone()
            }
            RecordStatus.TYPE_STOP_RECORD -> {
                bindingView.textureView.makeGone()
                bindingView.composeRecordBtn.makeGone()
                bindingView.playerView.makeVisible()
    //            bindingView.recordLayout.setBackgroundResource(R.drawable.shape_card_background)
                bindingView.rlRecordTip.makeGone()
                bindingView.rlResult.makeVisible()
                bindingView.recorderFacing.makeGone()
            }
        }
    }

    fun formatSecondsToTime(seconds: Int): String {
        if (seconds < 0) return "00:00"
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }
}