package com.mobile.anchor.app.main.conversion

import anchor.app.base.ext.openLoadMore
import anchor.app.base.ui.BaseRecyclerViewFragment
import com.mobile.anchor.app.module.discover.main.homelist.bean.VideoHistoryBean

class HistoryCallFragment : BaseRecyclerViewFragment<ConversionViewmodel>() {

    private var loadPage = 1
    private val pageSize = 20

    override fun initView() {
        bindingView.refreshLayout.recyclerView.apply {
            setOnRefreshListener { b: Boolean, _: Int ->
                if (b) {
                    loadPage = 1
                }
                loadDataFromServer()
            }
            openLoadMore {
                loadPage++
                loadDataFromServer()
            }
        }
    }

    override fun loadData() {
        bindingView.refreshLayout.onRefresh()
    }

    override fun showError(message: String?) {
        super.showError(message)
        val tmpPage = loadPage--
        loadPage = if (tmpPage >= 1) tmpPage else 1
        bindingView.refreshLayout.loadFailedWithMore()
    }

    override fun showNetWorkError() {
        super.showNetWorkError()
        val tmpPage = loadPage--
        loadPage = if (tmpPage >= 1) tmpPage else 1
        bindingView.refreshLayout.loadFailedWithMore()
    }

    private fun loadDataFromServer() {
        viewModel?.getVideoHistory(loadPage, pageSize)?.observe(this) { bean: VideoHistoryBean ->
            showContentView()
            bindingView.refreshLayout.apply {
                if (loadPage == 1) {
                    clearAllItems()
                }
                append<VideoHistoryItem>(
                    items = bean.records,
                    isNoMore = bean.records.isNullOrEmpty()
                ) { data ->
                    recordInfo = data as VideoHistoryBean.RecordInfo
                }
            }.recyclerView.apply {
                if (loadPage == 1) {
                    smoothScrollToPosition(0)
                    layoutManager?.scrollToPosition(0)
                }

            }
        }
    }
}