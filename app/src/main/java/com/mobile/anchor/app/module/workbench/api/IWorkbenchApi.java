package com.mobile.anchor.app.module.workbench.api;

import com.mobile.anchor.app.module.workbench.bean.ConnectRateListBean;
import com.mobile.anchor.app.module.workbench.bean.MarqueeBean;
import com.mobile.anchor.app.module.workbench.bean.MatchProgressBean;
import com.mobile.anchor.app.module.workbench.bean.MemberListBean;
import com.mobile.anchor.app.module.workbench.bean.TndAgentListBean;
import com.mobile.anchor.app.module.workbench.bean.WorkbenchBean;
import com.mobile.anchor.app.module.workbench.bean.WorkbenchUnionBean;

import java.util.List;
import java.util.Map;

import anchor.app.base.bean.LoginBean;
import anchor.app.base.bean.WordEntity;
import anchor.app.base.core.Constants;
import anchor.app.base.retrofit.BaseResult;
import io.reactivex.Flowable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.api
 * @class describe
 * @time 2019/8/9 12:20
 * @class describe
 */
public interface IWorkbenchApi {

    @POST(Constants.HTTP_URL.query_anchor_work)
    Flowable<BaseResult<WorkbenchBean>> getWorkbench();

    @GET(Constants.HTTP_URL.query_union_anchor_data)
    Flowable<BaseResult<WorkbenchUnionBean>> getWorkbenchUnionData(@Query("unionId") int unionId);

    @GET(Constants.HTTP_URL.query_anchor_share_url)
    Flowable<BaseResult<String>> getWorkbenchAnchorShareUrl();

    @GET(Constants.HTTP_URL.query_union_anchor_work_child)
    Flowable<BaseResult<List<TndAgentListBean>>> getMemberChildList(@Query("unionId") int unionId);

    @POST(Constants.HTTP_URL.query_union_anchor_work)
//    Flowable<BaseResult<MemberListBean>> getMemberList(@Query("countId")int countId, @Query("country")String country, @Query("current")int current, @Query("size")int size, @Query("unionId")int unionId);
    Flowable<BaseResult<MemberListBean>> getMemberList(@Body Map<String, Object> map);


    @GET(Constants.HTTP_URL.query_video_list)
    Flowable<BaseResult<List<ConnectRateListBean>>> getVideoList();


    @POST(Constants.HTTP_URL.online_status)
    Flowable<BaseResult<Object>> onlineStatus(@Query("onlineStatus") String onlineStatus);

//    @GET(Constants.HTTP_URL.query_call_anchor)
//    Flowable<BaseResult<Object>> getCallAnchor(@Query("channelId") String channelId);

    @GET(Constants.HTTP_URL.anchor_pay_info)
    Flowable<BaseResult<Object>> anchorPayInfo();

    @POST(Constants.HTTP_URL.match_switch)
    Flowable<BaseResult<Object>> matchSwitch(@Query("isOpen") String isOpen);

    @GET(Constants.HTTP_URL.home_task)
    Flowable<BaseResult<MatchProgressBean>> homeTask();

    @GET(Constants.HTTP_URL.query_sensitive_words)
    Flowable<BaseResult<List<WordEntity>>> getSensitiveWords();

    @POST(Constants.HTTP_URL.anchor_addVideo)
    Flowable<BaseResult<Object>> anchorAddVideo(@Body Map<String, Object> map);

    @GET(Constants.HTTP_URL.marquee_list)
    Flowable<BaseResult<List<MarqueeBean>>> getMarqueeList();

    @GET(Constants.HTTP_URL.query_user_detail)
    Flowable<BaseResult<LoginBean>> getAnchorDetail(@Query("id") String id);

}
