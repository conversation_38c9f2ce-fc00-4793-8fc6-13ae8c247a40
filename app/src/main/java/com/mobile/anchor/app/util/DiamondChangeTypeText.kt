package com.mobile.anchor.app.util

import com.blankj.utilcode.util.StringUtils
import com.mobile.anchor.app.R

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/28 16:34
 * @description :钻石获取、消耗类型字符串
 */
object DiamondChangeType {

    fun get(type: String): String = when (type) {
        "0" -> StringUtils.getString(R.string.diamond_change_type_recharge)
        "1" -> StringUtils.getString(R.string.diamond_change_type_videochat)
        "2" -> StringUtils.getString(R.string.diamond_change_type_privatephoto)
        "3" -> StringUtils.getString(R.string.diamond_change_type_gift)
        "4" -> StringUtils.getString(R.string.diamond_change_type_extract)
        "5" -> StringUtils.getString(R.string.diamond_change_type_manualrecharge)
        "6" -> StringUtils.getString(R.string.diamond_change_type_rejectcash)
        "7" -> StringUtils.getString(R.string.diamond_change_type_subscribe)
        "8" -> StringUtils.getString(R.string.diamond_change_type_signin)
        "9" -> StringUtils.getString(R.string.diamond_change_type_advert)
        "10" -> StringUtils.getString(R.string.diamond_change_type_message)
        "11" -> StringUtils.getString(R.string.diamond_change_type_share)
        "12" -> StringUtils.getString(R.string.diamond_change_type_inviteanchor)
        "13" -> StringUtils.getString(R.string.diamond_change_type_rankreward)
        "14" -> StringUtils.getString(R.string.diamond_change_type_wishdeduct)
        "15" -> StringUtils.getString(R.string.diamond_change_type_admingive)
        "16" -> StringUtils.getString(R.string.diamond_change_type_taskcomplete)
        "17" -> StringUtils.getString(R.string.diamond_change_type_levelrecharge)
        "18" -> StringUtils.getString(R.string.diamond_change_type_levelweekbonus)
        "19" -> StringUtils.getString(R.string.diamond_change_type_inviteuser)
        "20" -> StringUtils.getString(R.string.diamond_change_type_reply_bonus)
        "21" -> StringUtils.getString(R.string.diamond_change_type_match_earning)
        "22" -> StringUtils.getString(R.string.diamond_change_type_match_chat)
        "23" -> StringUtils.getString(R.string.diamond_change_type_settlement_failure_refund)
        else -> ""
    }
}
