package com.mobile.anchor.app.module.user.wallet.problem;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.databinding.ActivityWalletDescriptionBinding;

import anchor.app.base.ui.BaseNoModelActivity;
import anchor.app.base.utils.immersionbar.standard.ImmersionBar;
import com.mobile.anchor.app.module.user.dialog.LogoutDialogFragment;

/**
 * 设置页面
 */
public class DescriptionActivity extends BaseNoModelActivity<ActivityWalletDescriptionBinding> {
    public static final String TAG = "SettingActivity";

    private LogoutDialogFragment mLogoutDialogFragment;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_wallet_description;
    }

//    private void openActivity(Context context, String msg) {
//        Intent intent = new Intent(context, this.getClass());
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
//        context.startActivity(intent);
//    }

    @Override
    protected void initView() {
        bindingView.setActivity(this);
        bindingView.setLifecycleOwner(this);
    }


    @Override
    protected void initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init();
    }
}
