package com.mobile.anchor.app.module.workbench.connectrate.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.workbench.adapter.WorkbenchBindingViewAdapter;


public class ConnectRateListItemAdapter extends WorkbenchBindingViewAdapter {

    public static final int ITEM_CONTENT = 0;

    public ConnectRateListItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_CONTENT, R.layout.workbench_item_connect_rate);
    }

    @Override
    public int getViewType(@NotNull Object item) {
        return ITEM_CONTENT;
    }
}
