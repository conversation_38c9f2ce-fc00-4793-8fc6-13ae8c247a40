package com.mobile.anchor.app.module.workbench.proxy.memberlist

import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import io.reactivex.Flowable
import com.mobile.anchor.app.module.workbench.api.WorkbenchApi
import com.mobile.anchor.app.module.workbench.bean.MemberListBean

class MemberListRepository : IRepository {



    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(countId: Int,country: String,current: Int,size: Int,unionId: Int): Flowable<BaseResult<MemberListBean>> = WorkbenchApi.getMemberList(countId,country,current,size,unionId)





}