package com.mobile.anchor.app.module.discover.main.rank

import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.scope
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.user.bean.RankBean

class RankListViewmodel (application: Application) : BaseViewModel<RankListRepository>(application) {

    override fun getRepository(): RankListRepository {

        return RankListRepository()
    }


    fun getAnchorCharts(activity: FragmentActivity?, type: String): MutableLiveData<List<RankBean>> {
        val data = MutableLiveData<List<RankBean>>()
        repository!!.getAnchorCharts(type)
            .map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(activity?.scope()))
            .subscribe({ result: List<RankBean> ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) {
//                        data.value = "";
                    }
                }
            }
        return data
    }


}