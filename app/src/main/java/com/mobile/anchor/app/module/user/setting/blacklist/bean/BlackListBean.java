package com.mobile.anchor.app.module.user.setting.blacklist.bean;

import java.util.List;

/**
 * 奖品Bean
 * Modified by syk on 2020/3/3
 */

public class BlackListBean {

    private Integer current;
    private Boolean hitCount;
    private Integer pages;
    private Boolean searchCount;
    private Integer size;
    private Integer total;
    private List<RecordsDTO> records;

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(Boolean hitCount) {
        this.hitCount = hitCount;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Boolean searchCount) {
        this.searchCount = searchCount;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<RecordsDTO> getRecords() {
        return records;
    }

    public void setRecords(List<RecordsDTO> records) {
        this.records = records;
    }

    public static class RecordsDTO {
        public String getBlackHeadFileName() {
            return blackHeadFileName;
        }

        public void setBlackHeadFileName(String blackHeadFileName) {
            this.blackHeadFileName = blackHeadFileName;
        }

        private String blackHeadFileName;
        private long id;
        private String level;
        private String blackUserId;
        private String blackNickName;

        private String country;

        public String getBlackUserId() {
            return blackUserId;
        }

        public void setBlackUserId(String blackUserId) {
            this.blackUserId = blackUserId;
        }

        public String getBlackNickName() {
            return blackNickName;
        }

        public void setBlackNickName(String blackNickName) {
            this.blackNickName = blackNickName;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }


        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }


    }
}
