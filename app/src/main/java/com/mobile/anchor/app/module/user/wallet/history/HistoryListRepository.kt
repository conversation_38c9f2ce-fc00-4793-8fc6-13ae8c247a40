package com.mobile.anchor.app.module.user.wallet.history

import com.mobile.anchor.app.module.user.api.UserApi
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean
import io.reactivex.Flowable

class HistoryListRepository : IRepository {



    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(current: Int,size: Int): Flowable<BaseResult<HistoryListBean>> = UserApi.getHistoryList(current,size)

    fun anchorExtract(extractNum: Int): Flowable<BaseResult<Any>> = UserApi.anchorExtract(extractNum)




}