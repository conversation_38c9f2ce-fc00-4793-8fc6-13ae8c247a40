package com.mobile.anchor.app.module.user.grouplist

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserItemGrouplistBinding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.ext.jump
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CountryUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.databinding.ActivityGroupListBinding
import com.mobile.anchor.app.module.discover.main.center.PersonalCenterActivity
import com.mobile.anchor.app.module.user.bean.GroupListBean
import com.mobile.anchor.app.module.user.grouplist.adapter.GroupListItemAdapter

class GroupListActivity : BaseActivity<GroupListViewmodel, ActivityGroupListBinding>(),
    ItemClickPresenter<Any?>, ItemDecorator {
    var adapter: GroupListItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var current = 1
    var size = 30
    var isLoading = false

    var unionId = 1
    override fun getLayoutId(): Int {
        return R.layout.activity_group_list
    }

    override fun initView() {
        unionId = intent.getIntExtra("unionId", 1)
        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
//        bindingView!!.refreshLayout.setColorSchemeColors(Color.parseColor("#FFB909"))
//        bindingView!!.refreshLayout.setOnRefreshListener { loadData() }

        bindingView!!.refreshLayout.setOnRefreshListener { loadData2(true) }
        bindingView!!.refreshLayout.setOnLoadMoreListener { loadData2(false) }
        bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)

        dataList = ObservableArrayList<Any>()
        adapter = GroupListItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter
    }
//

    override fun loadData() {
        showLoading()
        isLoading = false
        loadData2(false)
    }

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {
        if (isLoading) {
            return
        }
        if (reference) {
            current = 1;
        }
        isLoading = true
        viewModel!!.getList(unionId).observe(
            this
        ) { bean: List<GroupListBean> ->
            showContentView()
            bindingView!!.refreshLayout.finishLoadMore()
            bindingView!!.refreshLayout.finishRefresh()
            isLoading = false
            current += 1

            if (reference) { //
                bindingView!!.refreshLayout.setNoMoreData(false)
                dataList!!.clear()
            }
//            isMoreLoading = false
            if (bean.size > 0) {
                dataList!!.addAll(bean)
            }
            if (bean.size < size) {
                bindingView!!.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)
                bindingView!!.refreshLayout.setNoMoreData(true)
            }

            if (dataList!!.size == 0) {
                viewModel!!.error.value = ResultException(BaseResult.NOT_DATA, getString(R.string.a0018))
            }
            adapter!!.notifyDataSetChanged()

        }
    }

    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any?) {
        val bean = item as GroupListBean
//        if (v.id == R.id.head){
        jump(PersonalCenterActivity::class.java, Bundle().apply {
            putLong("id", bean.id.toLong())
            putString("userRole", "1")
        })
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>,
        position: Int,
        viewType: Int
    ) {
        val binding01 = holder.binding
        val bean = dataList!![position] as GroupListBean
        if (binding01 is UserItemGrouplistBinding) {
            val binding = binding01

            binding.head.setTag(R.id.head, position)

            bean.headFileName?.let { it.ifEmpty { null } }?.apply {
                Glide.with(mContext).asBitmap().load(bean.headFileName)
                    .apply(RequestOptions.bitmapTransform(CircleCrop())).into(object : CustomTarget<Bitmap?>() {
                        override fun onResourceReady(
                            @NonNull resource: Bitmap,
                            @Nullable transition: Transition<in Bitmap?>?
                        ) {
                            if (position == binding.head.getTag(R.id.head)) {
                                binding01.head.setImageBitmap(resource)
                            }
                        }

                        override fun onLoadCleared(@Nullable placeholder: Drawable?) {}
                    })
            }


            bean.nickName?.apply { binding.name.text = bean.nickName }


            binding!!.country.setImageDrawable(CountryUtil.countryCodeToImage(mContext, bean?.country))
            bean.country?.apply { binding.countryTV.text = bean.country }
            // 暂时全部都隐藏
            binding.btnSave.visibility = View.GONE
            binding.level.visibility = View.GONE
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        //        if (!"user".equals(from)) {
//            //跳转首页
//            ActivityUtil.getInstance().finishAllActivityExcept("mikchat.app.user.baby.BabyInfoActivity");
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            super.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "BlackListActivity"
    }
}