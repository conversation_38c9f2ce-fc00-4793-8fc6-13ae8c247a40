package com.mobile.anchor.app.module.discover.main.chat.videochat

import io.reactivex.Flowable
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.discover.api.DiscoverApi
import com.mobile.anchor.app.module.discover.bean.CallSettlementBean

class VideoChatRepository : IRepository {
    //    public Flowable<BaseResult<Object>> loginByPhone(String authId, String type, String username) {
    //        return DiscoverApi.queryExperienceCourse();
    //    }
    /**
     * 获取视频通话的频道id
     *
     * @return
     */
    fun getVideoChannel(id: Long): Flowable<BaseResult<String>> {
        return DiscoverApi.getVideoChannel(id)
    }

    fun getVideoPush(id: String): Flowable<BaseResult<Any>> {
        return DiscoverApi.getVideoPush(id)
    }
    fun getVideoPushReject(id: String): Flowable<BaseResult<Any>> {
        return DiscoverApi.getVideoPushReject(id)
    }
    fun getVideoCallTime(id: String): Flowable<BaseResult<CallSettlementBean>> {
        return DiscoverApi.getVideoCallTime(id)
    }

    /**
     * 扣除费用-每分钟
     *
     * @return
     */
    fun getVideoDeduct(channelId: String?, firstFlag: String?): Flowable<BaseResult<DeductBean>> {
        return DiscoverApi.getVideoDeduct(channelId, firstFlag)
    }

    fun evaluateLabelNeed(channelId: String): Flowable<BaseResult<Any>> {
        return DiscoverApi.evaluateLabelNeed(channelId)
    }

    fun removeAndFollowing(UserId: Long,type: Int,userRole: String ): Flowable<BaseResult<Any>> = DiscoverApi.removeAndFollowing(
        UserId,
        type,
        userRole
    )

    /**
     * 获取视频通话的token
     *
     * @return
     */
    fun getVideoToken(id: String?, userCode: String): Flowable<BaseResult<String>> {
        return DiscoverApi.getVideoToken(id,userCode)
    }

    /**
     * 挂断视频通话
     *
     * @return
     */
    fun setVideoHangup(id: String?): Flowable<BaseResult<String>> {
        return DiscoverApi.setVideoHangup(id)
    }

    /**
     * 主播端未接调用
     */
    fun videoMissedCall(channelId: String): Flowable<BaseResult<Any>> = DiscoverApi.videoMissedCall( channelId )
}