package com.mobile.anchor.app.module.user.setting.blacklist

import com.mobile.anchor.app.module.user.api.UserApi
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.user.setting.blacklist.bean.BlackListBean
import io.reactivex.Flowable

class BlackListRepository : IRepository {



    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(current: Int,size: Int): Flowable<BaseResult<BlackListBean>> = UserApi.getBlackList(current,size)



    /** <AUTHOR>
     * 从黑名单移除
    @return  对应列表数据:  */
    fun removeBlackList(blackUserId: Long): Flowable<BaseResult<Any>> = UserApi.removeBlackList(blackUserId)

}