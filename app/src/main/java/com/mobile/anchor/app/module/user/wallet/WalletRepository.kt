package com.mobile.anchor.app.module.user.wallet

import com.mobile.anchor.app.module.user.api.UserApi
import com.mobile.anchor.app.module.user.bean.AnchorPaySaveInfo
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean
import com.mobile.anchor.app.module.user.bean.PageBean
import com.mobile.anchor.app.module.user.bean.SettlementBean
import io.reactivex.Flowable
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean

class WalletRepository : IRepository {
    fun aa() {}

    fun anchorPayInfo(): Flowable<BaseResult<Any>> = UserApi.anchorPayInfo()

    fun anchorPayCountry(): Flowable<BaseResult<List<String>>> = UserApi.anchorPayCountry()
    fun orderPayOrg(country: String, paymentMethodType: String): Flowable<BaseResult<List<String>>> =
        UserApi.orderPayOrg(country, paymentMethodType)

    fun anchorPayMethod(country: String): Flowable<BaseResult<List<String>>> = UserApi.anchorPayMethod(country)

    fun anchorPayParam(country: String, paymentMethodType: String): Flowable<BaseResult<List<String>>> =
        UserApi.anchorPayParam(country, paymentMethodType)

    fun anchorPaySaveInfo(anchorPaySaveInfo: AnchorPaySaveInfo): Flowable<BaseResult<Any>> =
        UserApi.anchorPaySaveInfo(anchorPaySaveInfo)

    fun getDiamondList(startTime: String,endTime:String, current: Int, size: Int): Flowable<BaseResult<HistoryListBean>> =
        UserApi.getDiamondList(startTime,endTime, current, size)

    fun getIncomeStatementList(params: Map<String, String>): Flowable<BaseResult<PageBean<IncomeStatementBean>>> =
        UserApi.getIncomeStatementList(params)

    fun getSettlementList(params: Map<String, String>): Flowable<BaseResult<PageBean<SettlementBean>>> =
        UserApi.getSettlementList(params)
}