/*
 * Copyright 2016 czy1121
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.mobile.anchor.app.module.update.util;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import anchor.app.base.utils.Logger;

import androidx.core.content.FileProvider;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class UpdateUtil {
    private static final String TAG = "zile.update";
    private static final String PREFS = "zile.update.prefs";
    private static final String KEY_IGNORE = "zile.update.prefs.ignore";
    private static final String KEY_UPDATE = "zile.update.prefs.update";

    public static boolean DEBUG = true;

    public static void log(String content) {
        if (DEBUG) {
            Log.i(TAG, content);
        }
    }

    public static void clean(Context context) {
        SharedPreferences sp = context.getSharedPreferences(PREFS, 0);
        File file = new File(context.getExternalCacheDir(), sp.getString(KEY_UPDATE, "") + ".apk");
        UpdateUtil.log("apk ==> " + file.toString());
        if (file.exists()) {
            file.delete();
        }
        sp.edit().clear().apply();
    }

    public static void setUpdate(Context context, String md5) {
        if (TextUtils.isEmpty(md5)) {
            return;
        }
        SharedPreferences sp = context.getSharedPreferences(PREFS, 0);
        String old = sp.getString(KEY_UPDATE, "");
        if (md5.equals(old)) {
            UpdateUtil.log("same md5");
            return;
        }
        File oldFile = new File(context.getExternalCacheDir(), old);
        if (oldFile.exists()) {
            oldFile.delete();
        }
        sp.edit().putString(KEY_UPDATE, md5).apply();
        File file = new File(context.getExternalCacheDir(), md5);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void ensureExternalCacheDir(Context context) {
        File file = context.getExternalCacheDir();
        if (file == null) {
            file = new File(context.getExternalFilesDir("").getParentFile(), "cache");
        }
        if (file != null) {
            file.mkdirs();
        }
    }

    public static void setIgnore(Context context, String md5) {
        context.getSharedPreferences(PREFS, 0).edit().putString(KEY_IGNORE, md5).apply();
    }

    public static boolean isIgnore(Context context, String md5) {
        return !TextUtils.isEmpty(md5) && md5.equals(context.getSharedPreferences(PREFS, 0).getString(KEY_IGNORE, ""));
    }

    public static void install(Context context, boolean force) {
        String md5 = context.getSharedPreferences(PREFS, 0).getString(KEY_UPDATE, "");
        File apk = new File(context.getExternalCacheDir(), md5 + ".apk");
        if (UpdateUtil.verify(apk, md5)) {
            install(context, apk, force);
        }
    }

    public static void install(Context context, File file, boolean force) {
        try {
            Logger.d(TAG, "install file:" + file.getAbsolutePath() + " force:" + force);
            if (!file.exists()) {
                Logger.e(TAG, "APK 文件不存在！");
                return;
            }

            // Android 8.0 及以上需要检查未知来源权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                boolean canInstall = context.getPackageManager().canRequestPackageInstalls();
                if (!canInstall) {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, Uri.parse("package:" + context.getPackageName()));
                    context.startActivity(intent);
                    return;
                }
            }

            Intent intent = new Intent(Intent.ACTION_VIEW);
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
            } else {
                Uri uri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileprovider", file);
                intent.setDataAndType(uri, "application/vnd.android.package-archive");
                // 授权目标应用访问安装文件
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);

//        if (force) {
//            System.exit(0);
//        }
        } catch (Exception e) {
            e.printStackTrace();
            if (file.exists()) {
                file.delete();
            }
        }
    }


    public static boolean verify(File apk, String sha512) {
        Logger.d(TAG, "verify apk:" + apk.getAbsolutePath() + " sha512:" + sha512);
        if (!apk.exists()) {
            return false;
        }
        String _sha512 = calculateSHA512(apk);
        Logger.d(TAG, "_sha512:" + _sha512);

        if (TextUtils.isEmpty(_sha512)) {
            return false;
        }
        boolean result = ((_sha512 != null) && (_sha512.equalsIgnoreCase(sha512)));
        if (!result) {
            apk.delete();
        }
        return result;
    }


    public static String toCheckUrl(Context context, String url, String channel) {
        StringBuilder builder = new StringBuilder();
        builder.append(url);
        builder.append(url.indexOf("?") < 0 ? "?" : "&");
        builder.append("package=");
        builder.append(context.getPackageName());
        builder.append("&version=");
        builder.append(getVersionCode(context));
        builder.append("&channel=");
        builder.append(channel);
        return builder.toString();
    }

    public static int getVersionCode(Context context) {
        try {
            PackageInfo info = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            return info.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            return 0;
        }
    }

    public static boolean checkWifi(Context context) {
        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity == null) {
            return false;
        }
        NetworkInfo info = connectivity.getActiveNetworkInfo();
        return info != null && info.isConnected() && info.getType() == ConnectivityManager.TYPE_WIFI;
    }

    public static boolean checkNetwork(Context context) {
        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity == null) {
            return false;
        }
        NetworkInfo info = connectivity.getActiveNetworkInfo();
        return info != null && info.isConnected();
    }

    public static boolean isDebuggable(Context context) {
        try {
            return (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {

        }
        return false;
    }

    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static String toHexString(byte[] b) {
        // 预分配字符数组，避免重复装箱拆箱
        char[] hexChars = new char[b.length * 2];
        for (int j = 0; j < b.length; j++) {
            int v = b[j] & 0xFF;
            hexChars[j * 2] = HEX_DIGITS[v >>> 4];
            hexChars[j * 2 + 1] = HEX_DIGITS[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String calculateSHA512(File file) {
        try (DigestInputStream dis = new DigestInputStream(new FileInputStream(file), MessageDigest.getInstance("SHA-512"))) {
            byte[] buffer = new byte[8192];
            while (dis.read(buffer) != -1) ;
            return toHexString(dis.getMessageDigest().digest());
        } catch (Exception e) {
            Logger.e("FileHash", "Error calculating SHA-256", e);
            return null;
        }
    }

    public static String md5sum(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            Log.e("MD5", "Invalid file");
            return null;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            MessageDigest md5 = MessageDigest.getInstance("SHA-512");
            DigestInputStream dis = new DigestInputStream(fis, md5);
            byte[] buffer = new byte[8192];
            // 读取整个文件但不保存内容
            while (dis.read(buffer) != -1) ;
            return toHexString(dis.getMessageDigest().digest());
        } catch (NoSuchAlgorithmException e) {
            Log.e("MD5", "MD5 algorithm not available", e);
            return null;
        } catch (IOException e) {
            Log.e("MD5", "Error reading file: " + file.getName(), e);
            return null;
        }
    }

    public static String readString(InputStream input) throws IOException {
        if (input == null) {
            return "";
        }
        try (input; ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                return output.toString(StandardCharsets.UTF_8);
            } else {
                return output.toString("UTF-8");
            }
        }
    }

    public static void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}