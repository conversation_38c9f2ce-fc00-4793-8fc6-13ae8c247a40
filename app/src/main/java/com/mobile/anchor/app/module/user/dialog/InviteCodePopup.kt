package com.mobile.anchor.app.module.user.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import anchor.app.base.ext.click
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupInviteCodeBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/23 14:37
 * @description :
 */
class InviteCodePopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_invite_code

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupInviteCodeBinding.bind(popupImplView)
        binding.tvInviteCode.text = UserInfoManager.user()?.userCode
        binding.tvInviteCode.click {
            copyClipboard(context, binding.tvInviteCode.text.toString())
        }
        binding.tvInvite.click {
            QrCodeTipsDialog(activity, "").show()
        }
        binding.ivClose.click {
            dismiss()
        }
    }

    fun copyClipboard(context: Context, copyStr: String?): Boolean {
        return try {
            //获取剪贴板管理器：
            val cm = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            // 创建普通字符型ClipData
            val mClipData = ClipData.newPlainText("Label", copyStr)
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData)
            ToastUtil.show(getResources().getString(R.string.b27))
            true
        } catch (e: Exception) {
            false
        }
    }
}

fun showInviteCodePopup(context: Context) {
    XPopup.Builder(context).asCustom(InviteCodePopup(context)).show()
}