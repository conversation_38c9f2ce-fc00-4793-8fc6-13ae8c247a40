package com.mobile.anchor.app.module.user.editedInfo

import anchor.app.base.bean.LanguagesBean
import anchor.app.base.dialog.CommonInputDialog
import anchor.app.base.dialog.LanguageSelectDialogFragment
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.SingleTipDialog
import anchor.app.base.ext.click
import anchor.app.base.ext.setTextCompatColor
import anchor.app.base.ext.toast
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.ImageUtil
import anchor.app.base.utils.Logger
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.TimeUtil
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bigkoo.pickerview.view.TimePickerView
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.TimeUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityProfileBinding
import com.mobile.anchor.app.module.user.dialog.showCountryPopup
import java.io.File
import java.util.Calendar

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/7 19:27
 * @description :个人资料
 */
class ProfileActivity : BaseActivity<EditInfoModel, ActivityProfileBinding>() {

    private val params = JsonObject()
    private val mCameraUtil: CameraUtil by lazy { CameraUtil(this) }
    private var mOpenTimePicker: TimePickerView? = null
    private var birthdayString = ""
    private var languages = ArrayList<String>()
    private var languagesData = ArrayList<LanguagesBean>()
    private var isEditMode = true

    override fun getLayoutId(): Int = R.layout.activity_profile

    override fun initView() {
        bindingView.activity = this
        bindingView.llAvatar.click {
            if (!isEditMode) return@click
            mCameraUtil.getPic(this, callback = object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: java.util.ArrayList<LocalMedia>) {
                    if (result.isNotEmpty() && result.size > 0) {
                        val path = result[0].cutPath ?: result[0].realPath
                        Glide.with(this@ProfileActivity).load(path).apply(
                            RequestOptions.bitmapTransform(
                                CircleCrop()
                            )
                        ).into(bindingView.ivAvatar)
                        LoadingDialog.getInstance(this@ProfileActivity).show()
                        viewModel.uploadPicture(File(path)).observe(this@ProfileActivity) {
                            updateUserInfo(JsonObject().apply {
                                addProperty("headFileName", it)
                            })
                        }
                    }
                }

                override fun onCancel() {
                }
            })
        }

        bindingView.llNickmame.click {
            CommonInputDialog(
                context = this, config = CommonInputDialog.DialogConfig(
                    title = "修改昵称",
                    hint = "请输入昵称",
                    content = bindingView.etNickname?.text?.toString() ?: "",
                    maxLength = 12
                )
            ) { newNickname ->
                updateUserInfo(JsonObject().apply {
                    addProperty("nickName", newNickname)
                })
            }.show()
        }

        bindingView.llBirthday.click {
            if (!isEditMode) return@click
            setOpenTime()
        }

        bindingView.llCountry.click {
            if (!isEditMode) return@click
            showCountryPopup(this) {
                bindingView.tvCountry.text = it.countryNameEn
//                params.addProperty("country", it.countryCode)
                updateUserInfo(JsonObject().apply {
                    addProperty("country", it.countryCode)
                })
            }
        }

        bindingView.llLanguage.click {
            if (!isEditMode) return@click
            val dialog = LanguageSelectDialogFragment(languagesData) {
                languages.clear()
                languagesData.forEach {
                    if (it.isSelected) {
                        languages.add(it.languageName)
                    }
                }
//                params.add(
//                    "languages",
//                    Gson().toJsonTree(languagesData.filter { it.isSelected }
//                        ?.map { it.languageCode })
//                )

                updateUserInfo(JsonObject().apply {
                    add(
                        "languages",
                        Gson().toJsonTree(languagesData.filter { it.isSelected }
                            ?.map { it.languageCode })
                    )
                })

                if (languages.isNotEmpty()) {
                    bindingView.tvLanguage.text = TextUtils.join("、", languages)
                } else {
                    bindingView.tvLanguage.text = ""
                }
            }
            dialog.show(this.supportFragmentManager, "LanguageSelectDialogFragment")
        }

        bindingView.btnSave.click {
            if (TextUtils.equals(bindingView.btnSave.text, getString(R.string.edit_info))) {
                isEditMode = true
                bindingView.btnSave.text = getString(R.string.album_submit)
                bindingView.etNickname.isEnabled = true
//                bindingView.etEmail.isEnabled = true
                return@click
            }

            if (bindingView.etNickname.text.isNullOrEmpty()) {
                toast(getString(R.string.nickname_hint))
                return@click
            }
//            viewModel.modifyBasicInfo(params.apply {
//                addProperty("id", SharePreUtil.getUserId())
//                addProperty("nickName", bindingView.etNickname.text.toString())
//                addProperty("email", bindingView.etEmail.text.toString())
//            }).observe(this) {
//                toast(getString(R.string.modify_successful))
////                userInfo?.token = it.
//                finish()
//            }
        }
    }

    private fun updateUserInfo(extraParams: JsonObject) {
        viewModel.modifyBasicInfo(JsonObject().apply {
            addProperty("id", SharePreUtil.getUserId())
            for ((key, value) in extraParams.entrySet()) {
                add(key, value)
            }
        }).observe(this) {
            Logger.e("updateUserInfo $it")
            if (it) {
                toast(getString(R.string.modify_successful))
//                userInfo?.token = it.
                finish()
            }
            LoadingDialog.getInstance(this@ProfileActivity).dismiss()
        }
    }

    override fun loadData() {
        viewModel.queryUserInfo(this).observe(this) {
            showContentView()
            if (it != null && TextUtils.equals(it.infoStatus, "1")) {
                bindingView.btnSave.apply {
                    isEnabled = false
                    text = getString(R.string.in_review)
                    setTextCompatColor(R.color.color_9D97AC)
                }
            }

            viewModel?.checkProfileAuditStatus()?.observe(this) {
                if (it.isNotEmpty()) {
                    SingleTipDialog.showTips(this, it) {
                        bindingView.btnSave.apply {
                            isEnabled = true
                            text = getString(R.string.edit_info)
                            setTextCompatColor(R.color.colorWhite)
                        }
                    }
                }
            }
            ImageUtil.displayCircleImage(this, bindingView.ivAvatar, it.headFileName)
            bindingView.etNickname.setText(it.nickName)
            bindingView.tvBirthday.text = it.birthday
            bindingView.tvCountry.text = it.country
            bindingView.tvLanguage.text = it.languages?.map { it.languageName }?.joinToString()
            bindingView.tvGender.text =
                if (it.gender == "1") getString(R.string.gender_female) else getString(R.string.gender_male)
            bindingView.etEmail.setText(it.email)
        }

        viewModel.query_user_language(this).observe(this) {
            languagesData.clear()
            languagesData.addAll(it)
        }
    }

    fun setOpenTime() {
        bindingView.etNickname.clearFocus()
        val birth = if (birthdayString.isEmpty()) TimeUtils.getNowString() else birthdayString

        val date = Calendar.getInstance()
        date.time = TimeUtils.string2Date(birth, "yyyy-MM-dd")

        //时间选择器 ，自定义布局
        mOpenTimePicker = TimePickerBuilder(
            this
        ) { date, v ->
            date?.let {
                birthdayString = TimeUtil.yyyyMMdd(it.time)
            }
        }.setTextColorCenter(Color.WHITE) //设置选中项的颜色
            .setLineSpacingMultiplier(2.6f) //设置两横线之间的间隔倍数
            .setRangDate(Calendar.getInstance().apply {
                set(Calendar.YEAR, 1960)
            }, Calendar.getInstance().apply {
                set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR) - 18)
            }).setDate(date).setLayoutRes(R.layout.device_layout_select_time) { v: View ->
                val tv_cancel = v.findViewById<TextView>(R.id.screen_cancle)
                val tv_sure = v.findViewById<TextView>(R.id.screen_sure)

                val screen_title = v.findViewById<TextView>(R.id.screen_title)
                screen_title.setText(StringUtils.getString(R.string.age_select))
                tv_sure.setOnClickListener { v1: View? ->
                    mOpenTimePicker?.let {
                        bindingView.tvBirthday.setText(birthdayString)
                        updateUserInfo(JsonObject().apply {
                            addProperty("birthday", birthdayString)
                        })
                    }
                    mOpenTimePicker?.dismiss()
                }
                tv_cancel.setOnClickListener { v12: View? ->
                    mOpenTimePicker?.dismiss()
                }
            }.setTimeSelectChangeListener {
                birthdayString = TimeUtil.yyyyMMdd(it.time)
                bindingView.tvBirthday.setText(birthdayString)
//                updateUserInfo(JsonObject().apply {
//                    addProperty("birthday", birthdayString)
//                })
            }.setType(booleanArrayOf(true, true, true, false, false, false))
            .setDividerColor(Color.parseColor("#EBEBEB"))
//            .setDecorView(window.decorView as ViewGroup)
            .build()
        mOpenTimePicker?.show()
    }
}