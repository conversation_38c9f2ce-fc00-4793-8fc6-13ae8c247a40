package com.mobile.anchor.app.module.user.component;

import android.content.Context;

/**
 * 向外提供服务的接口实现类
 */
//@Route(path = ArouterPath.USER_SERVICE_USERINFOSERVICE, name = "个人中心Module提供信息服务")
public class UserInfoServiceImpl {//implements UserInfoService
    private Context mContext;

//    @Override
//    public void init(Context context) {
//        mContext = context;
//    }
//
//
//    @Override
//    public void addChildInfo() {
//        BabyInfo babyInfo = new BabyInfo();
//        babyInfo.setName("宝宝");
//        babyInfo.setGender(1);
//        babyInfo.setRelation("妈妈");
//        Calendar startDate = Calendar.getInstance();
//        startDate.set(startDate.get(Calendar.YEAR) - 4, 0, 1);
//        babyInfo.setBirthday(new SimpleDateFormat("yyyy-MM-dd").format(startDate.getTime()));
//        UserApi.addChildInfo(babyInfo).subscribe();
//    }
}
