package com.mobile.anchor.app.module.user.invitation

import android.view.View
import androidx.fragment.app.Fragment
import com.angcyo.dsladapter.DslAdapterItem
import com.flyco.tablayout.listener.OnTabSelectListener
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityInviteBinding
import anchor.app.base.ext.attach
import anchor.app.base.ext.click
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.ImageUtil
import com.mobile.anchor.app.module.user.bean.InvitationBean
import com.mobile.anchor.app.module.user.dialog.showInputInviteCodePopup
import com.mobile.anchor.app.module.user.dialog.showInviteCodePopup
import com.mobile.anchor.app.module.user.dialog.showRewardDescriptionPopup

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/23 11:40
 * @description :邀请好友
 */
class InviteActivity : BaseActivity<InvitationModel, ActivityInviteBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_invite

    override fun initView() {
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        bindingView.tvInviteCode.click {
            showInputInviteCodePopup(this)
        }
        bindingView.tvDiamond.click {
            showRewardDescriptionPopup(this)
        }
        bindingView.tvEarnNow.click {
            showInviteCodePopup(this)
        }

        bindingView.slidingTabLayout.apply {
            setTitles(arrayOf(getString(R.string.introduced_girls), getString(R.string.bonus_history)))
            setOnTabSelectListener(object : OnTabSelectListener {
                override fun onTabSelect(position: Int) {
                    if (position == 0) fetchUserList() else fetchBonusList()
                }

                override fun onTabReselect(position: Int) {
                }
            })
        }

        bindingView.viewPager.attach(supportFragmentManager, lifecycle, listOf<Fragment>(Fragment(), Fragment())) {}
        bindingView.slidingTabLayout.setViewPager(bindingView.viewPager)

        fetchUserList()
    }

    private fun fetchUserList() {
        viewModel.getInviteIntroducedList(1).observe(this) {
            showContentView()
            bindingView.recyclerView.clearAllItems()
            bindingView.recyclerView.append<DslAdapterItem>(it) {
                itemLayoutId = R.layout.user_item_invite_girl
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as InvitationBean.UserDataListDTO
                    itemHolder.img(R.id.iv_avatar)?.apply {
                        ImageUtil.displayCircleImage(itemHolder.itemView.context, this, item.anchorHeadFileName)
                    }
                    itemHolder.tv(R.id.tv_nickname)?.text = item.anchorNickName
                    itemHolder.tv(R.id.tv_last_time)?.text = item.lastSignInTime
                    itemHolder.v<View>(R.id.status)?.apply {
                        setBackgroundResource(
                            when (item.anchorOnlineStatus) {
                                "0" -> R.mipmap.user_ic_user_status_online
                                "1" -> R.mipmap.user_ic_user_status_online
                                else -> R.mipmap.user_ic_user_status_busy
                            }
                        )
                    }
                }
            }
        }
    }

    private fun fetchBonusList() {
        viewModel.getInviteIntroducedList(1).observe(
            this
        ) { bean ->
            showContentView()
            bindingView.recyclerView.clearAllItems()
            bindingView.recyclerView.append<DslAdapterItem>(bean) {
                itemLayoutId = R.layout.user_item_invite_bonus
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as InvitationBean.UserDataListDTO
                    itemHolder.img(R.id.iv_avatar)?.apply {
                        ImageUtil.displayCircleImage(itemHolder.itemView.context, this, item.anchorHeadFileName)
                    }
                    itemHolder.tv(R.id.tv_name)?.text = item.bonus
                    itemHolder.tv(R.id.tv_diamond)?.text = item.bonus
                    itemHolder.tv(R.id.tv_last_time)?.text = item.bonusTime
                }
            }
//            viewModel.getList(SharePreUtil.getUserId()).observe(
//                this
//            ) { bean: InvitationBean ->
//                showContentView()
//
//                bean.userDataList = bean.records
//
//                if (bean.userDataList.size > 0) {
//                    dataList!!.addAll(bean.userDataList)
//                }
//
//                if (dataList!!.size == 0) {
////                bindingView!!.LLEmpty.visibility = View.VISIBLE
//                }
//                adapter!!.notifyDataSetChanged()
//
//                bindingView!!.rewardsRecharge.text = "Up to ${bean.rechargeMax}%"
//                bindingView!!.rewardsGift.text = "Up to ${bean.giftMax}%"
//
//                bindingView!!.rewardsGift.text = "Up to ${bean.giftMax}%"
//
//                bindingView!!.day.text = bean.inviteStatic.todayInvites.toString()
//                bindingView!!.accumulated.text = bean.inviteStatic.totalInvites.toString()
//                bindingView!!.dayreward.text = bean.inviteStatic.todayReward
//                bindingView!!.accumulatedreward.text = bean.inviteStatic.totalReward
//                googleUrl = bean.googleUrl
//
//                val messages: MutableList<String> = ArrayList()
//                for (anchorDetailListDTO in bean.anchorDetailList) {
//                    messages.add("${anchorDetailListDTO.nickName} invites friends to earn ${anchorDetailListDTO.rewardAmount}  diamonds in revenue")
//                }
//                bindingView!!.marqueeView.startWithList(messages)
//            }
        }
    }
}