package com.mobile.anchor.app.module.user.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.fragment.app.DialogFragment;

import com.mobile.anchor.app.R;
import io.reactivex.annotations.Nullable;

/**
 * 退出登录提示页面
 */
public class LogoutDialogFragment extends DialogFragment {

    public static final String TAG = LogoutDialogFragment.class.getSimpleName();
    private View mRootView;
    private View.OnClickListener confirmClickListener;
    public String content="";

    public LogoutDialogFragment(View.OnClickListener confirmClickListener) {
        this.confirmClickListener = confirmClickListener;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        if (null == mRootView) {
            mRootView = inflater.inflate(R.layout.user_layout_logout, null);
        }


        TextView tv01 = mRootView.findViewById(R.id.content);
        if (!content.isEmpty()) {
            tv01.setText(content);
        }

        mRootView.findViewById(R.id.textViewConfirm).setOnClickListener(confirmClickListener);
        mRootView.findViewById(R.id.textViewConcle).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        getDialog().setCanceledOnTouchOutside(false);
        final Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
        return mRootView;
    }


}
