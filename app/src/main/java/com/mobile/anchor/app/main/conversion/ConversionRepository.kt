package com.mobile.anchor.app.main.conversion

import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.discover.api.DiscoverApi
import io.reactivex.Flowable
import com.mobile.anchor.app.module.discover.main.homelist.bean.VideoHistoryBean

class ConversionRepository : IRepository {
    fun getVideoHistory(current: Int, size: Int): Flowable<BaseResult<VideoHistoryBean>> = DiscoverApi.getVideoHistory(current,size)
}