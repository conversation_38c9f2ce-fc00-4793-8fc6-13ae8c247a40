package com.mobile.anchor.app.module.workbench.adapter

import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.MultiTypeAdapter
import com.mobile.anchor.app.BR
import android.content.Context
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding

/**
 * 和databinding结合的Reyclerview的adapter
 */
open abstract class WorkbenchBindingViewAdapter<T>(context: Context, list: ObservableArrayList<T>) : MultiTypeAdapter<T>(context, list) {

    override fun onBindViewHolder(holder: BindingViewHolder<ViewDataBinding>, position: Int) {
        val item = list[position]
        //pending binding itemModel
        holder.binding.setVariable(BR.workbenchinfo, item)
        //pending binding presenter
        holder.binding.setVariable(BR.presenter, itemPresenter)
        holder.binding.executePendingBindings()
        //set decorator
        itemDecorator?.decorator(holder, position, getItemViewType(position))

    }
}