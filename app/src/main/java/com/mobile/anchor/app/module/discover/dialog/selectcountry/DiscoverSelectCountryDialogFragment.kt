package com.mobile.anchor.app.module.discover.dialog.selectcountry

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverDialogLayoutSelectCountryBinding
import com.mobile.anchor.app.databinding.DiscoverItemSelectCountryBinding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.utils.CountryUtil
import com.mobile.anchor.app.module.discover.dialog.selectcountry.bean.CountryBean

/**
 * 头像选择提示页面
 */
class DiscoverSelectCountryDialogFragment(index: Int, var refer: (String, Int) -> Unit) : DialogFragment(),
    ItemClickPresenter<Any>, ItemDecorator {
    private var bindingView: DiscoverDialogLayoutSelectCountryBinding? = null

    interface referenceData {
        fun reference(country: String, index: Int)
    }

    var index = 0

    //    public DiscoverSelectCountryDialogFragment(ComponentActivity activity) {
    //        this.activity = activity;
    //    }
    var dataList: ObservableArrayList<Any> = ObservableArrayList<Any>()

    init {
        this.index = index
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (null == bindingView) {
            bindingView = DataBindingUtil.inflate(
                layoutInflater, R.layout.discover_dialog_layout_select_country, null, false
            )
        }
        bindingView!!.dialog = this
        bindingView!!.lifecycleOwner = this
        val adapter = DiscoverSelectCountryAdapter(requireContext(), dataList!!)
        adapter.itemDecorator = this
        adapter.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(context, 3)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter


//        RxViewUtils.setOnClickListeners(mRootView.close, view -> {});
//        RxViewUtils.setOnClickListeners(mRootView.submit, view -> {});
        dataList.addAll(initCountryMap())
        dialog!!.setCanceledOnTouchOutside(true)
        dialog!!.window!!.setWindowAnimations(R.style.AnimBottom)
        val window = dialog!!.window
        window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window.decorView.setPadding(0, 0, 0, 0)
        val wlp = window.attributes
        wlp.gravity = Gravity.BOTTOM
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.attributes = wlp
        return bindingView!!.root
    }

    private fun initCountryMap(): ArrayList<*> {
        val list = ArrayList<CountryBean>()
        val hashMap: HashMap<String, Int> = CountryUtil.getCountryMap02()
        val en: Set<Map.Entry<String, Int>> = hashMap.entries
        for ((key, value) in en) {
            val map = CountryBean()
            map.country = key
            map.icon = value
            map.name = CountryUtil.countryNameToCode(key)
            list.add(map)
        }
        return list
    }

    fun close() {
//        getParentFragmentManager().beginTransaction().hide(this);
        dialog!!.cancel()
    }

    fun submit() {}
    override fun onItemClick(v: View, item: Any) {
        var name = (item as CountryBean).country ?: ""
        if (name == "ALL") {
            name = ""
        }
        refer(name, dataList.indexOf(item))
        v.isSelected = true
        dialog!!.cancel()
    }

    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>,
        position: Int,
        viewType: Int
    ) {
        val binding01 = holder.binding
        val bean = dataList[position] as CountryBean
        if (binding01 is DiscoverItemSelectCountryBinding) {
            val binding = binding01
            Glide.with(requireContext()).load(requireContext().getDrawable(bean.icon))
                .apply(RequestOptions.bitmapTransform(CircleCrop())).into(binding.icon)
            binding.name.text = bean.country
            if (position == index) {
                binding.LL01.isSelected = true
            }
        }
    }

}