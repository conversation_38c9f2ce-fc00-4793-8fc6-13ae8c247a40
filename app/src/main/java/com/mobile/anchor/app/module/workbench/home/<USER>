package com.mobile.anchor.app.module.workbench.home

import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.mobile.anchor.app.module.workbench.bean.ConnectRateListBean
import com.mobile.anchor.app.module.workbench.bean.MarqueeBean
import com.mobile.anchor.app.module.workbench.bean.MatchProgressBean
import com.mobile.anchor.app.module.workbench.bean.WorkbenchBean
import com.mobile.anchor.app.module.workbench.bean.WorkbenchUnionBean

class WorkbenchViewmodel(application: Application) :
    BaseViewModel<WorkbenchRepository>(application) {

    override fun getRepository(): WorkbenchRepository {
        return WorkbenchRepository()
    }

    fun getWorkbench(): MutableLiveData<WorkbenchBean> {
        val info: MutableLiveData<WorkbenchBean> = MutableLiveData()
        dataStreamFliter.fliter(repository.getWorkbench(), { list ->
            info.value = list
        }) { throwable ->
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info;
    }

    fun getWorkbenchAnchorShareUrl(): MutableLiveData<String> {
        val info: MutableLiveData<String> = MutableLiveData()
        dataStreamFliter.fliter(repository.getWorkbenchAnchorShareUrl(), { st ->
            info.value = st
        }) { throwable ->
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info;
    }

    fun getWorkbenchUnionData(unionId: Int): MutableLiveData<WorkbenchUnionBean> {
        val info: MutableLiveData<WorkbenchUnionBean> = MutableLiveData()
        dataStreamFliter.fliter(repository.getWorkbenchUnionData(unionId), { list ->
            info.value = list
        }) { throwable ->
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info;
    }

    fun getVideoList(): MutableLiveData<List<ConnectRateListBean>> {
        val info: MutableLiveData<List<ConnectRateListBean>> = MutableLiveData()
        dataStreamFliter.fliter(repository.getVideoList(), { list ->
            info.value = list
        }) { throwable ->
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info;
    }

    fun onlineStatus(onlineStatus: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.onlineStatus(onlineStatus), { state ->
            info.value = state
        }) { throwable ->
//            error.value = throwable
            info.value = "000"
            ToastUtil.show(throwable.message)
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info
    }

    fun matchSwitch(isOpen: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.matchSwitch(isOpen), { state ->
            info.value = true
        }) { throwable ->
            ToastUtil.show(throwable.message)
        }
        return info
    }

    fun homeTask(): MutableLiveData<MatchProgressBean> {
        val info: MutableLiveData<MatchProgressBean> = MutableLiveData()
        dataStreamFliter.fliter(repository.homeTask(), { state ->
            info.value = state
        }) { throwable ->
            ToastUtil.show(throwable.message)
        }
        return info
    }


    fun marqueeList(): MutableLiveData<List<MarqueeBean>> {
        val info: MutableLiveData<List<MarqueeBean>> = MutableLiveData()
        dataStreamFliter.fliter(repository.marqueeList(), { state ->
            info.value = state
        }) { throwable ->
            ToastUtil.show(throwable.message)
        }
        return info
    }
}