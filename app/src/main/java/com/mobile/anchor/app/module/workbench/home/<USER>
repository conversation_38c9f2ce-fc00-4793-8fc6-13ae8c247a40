package com.mobile.anchor.app.module.workbench.home

import anchor.app.base.core.Constants
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.SelectTipDialog
import anchor.app.base.dialog.SingleTipDialog
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeGone
import anchor.app.base.ext.makeVisible
import anchor.app.base.ext.toast
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.manager.UserInfoManager.Companion.user
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseFragment
import anchor.app.base.utils.FileUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.Logger
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.camera.Camera2Util.getMinPreSize
import android.Manifest
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Context.CAMERA_SERVICE
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.SurfaceTexture
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraDevice
import android.hardware.camera2.CameraManager
import android.os.Bundle
import android.util.Size
import android.view.Gravity
import android.view.Surface
import android.view.TextureView
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.WorkbencHomeFragmentBinding
import com.mobile.anchor.app.module.discover.main.rank.RankListActivity
import com.mobile.anchor.app.module.user.agreement.AgreementActivity
import com.mobile.anchor.app.module.user.feedback.FeedbackActivity
import com.mobile.anchor.app.module.user.level.LevelActivity2
import com.mobile.anchor.app.module.user.rank.RankActivity
import com.mobile.anchor.app.module.workbench.bean.WorkbenchBean
import com.mobile.anchor.app.module.workbench.bean.WorkbenchUnionBean
import com.mobile.anchor.app.module.workbench.connectrate.ConnectRateActivity
import com.mobile.anchor.app.module.workbench.notification.NotificationActivity
import com.mobile.anchor.app.module.workbench.proxy.memberlist.MemberListActivity
import com.mobile.anchor.app.module.workbench.proxy.tndagentlist.TndAgentListActivity
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ObservableSubscribeProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable.isActive
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 工作统计页面
 */
class WorkbenchFragment : BaseFragment<WorkbenchViewmodel, WorkbencHomeFragmentBinding>() {

    private val cameraManager: CameraManager by lazy {
        requireActivity().getSystemService(
            CAMERA_SERVICE
        ) as CameraManager
    }
    private var cameraDevice: CameraDevice? = null
    private lateinit var previewSize: Size
    private lateinit var cameraId: String
    private var captureSession: CameraCaptureSession? = null

    private var refreshJob: Job? = null
    private val refreshScope = CoroutineScope(Dispatchers.Main + Job())

    private companion object {
        private const val REFRESH_INTERVAL = 60_000L // 1分钟的间隔
    }

    override fun getLayoutId(): Int = R.layout.workbenc_home_fragment

    override fun initView() {
        bindingView!!.activity = this
        bindingView!!.refreshLayout.setOnRefreshListener { loadData() }
        bindingView!!.refreshLayout.setEnableLoadMore(false)

        var selectTipDialog: SelectTipDialog? = null
        bindingView.switch01.setOnCheckedChangeListener { buttonView, isChecked ->
//            if (!buttonView.) return@setOnCheckedChangeListener
            if (isChecked && UserInfoManager.UserAuditState(mActivity) != "1") {
                bindingView.switch01.isChecked = false
                return@setOnCheckedChangeListener
            }

            val title =
                if (isChecked) getString(R.string.are_you_sure_you_want_to_open_the_order_button) else getString(
                    R.string.are_you_sure_you_want_to_turn_off_the_order_button
                )

//            if (selectTipDialog == null) {
            selectTipDialog = SelectTipDialog.showTips(
                <EMAIL>,
                this.getString(R.string.Done),
                resources.getString(R.string.Cancel),
                getString(R.string.Operation_Tips),
                title,
                object : SelectTipDialog.ISimpleTipsClickListener() {
                    override fun confirmClick() {
                        LoadingDialog.getInstance(mContext).show()
                        viewModel.onlineStatus(if (isChecked) "1" else "0")
                            .observe(this@WorkbenchFragment) {
                                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)
                                if (it.toString() == "000") {
                                    bindingView.switch01.isChecked = !bindingView.switch01.isChecked
                                }
                                if (!bindingView.switch01.isChecked) {
                                    bindingView.switch02.isChecked = false
                                    stopPreview()
                                }

                                OtherManager.manager?.onlineStatus?.value =
                                    if (bindingView.switch01.isChecked) "1" else "0"
                                LoadingDialog.getInstance(mContext).cancel()
                            }
                    }

                    override fun concelClick() {
                        bindingView.switch01.setCheckedNoEvent(bindingView.switch01.isChecked.not())
//                            bindingView.switch01.isChecked = bindingView.switch01.isChecked.not()
                        OtherManager.manager?.onlineStatus?.value =
                            if (bindingView.switch01.isChecked) "1" else "0"
                    }
                })
            selectTipDialog.setCanceledOnTouchOutside(false)
//            } else {
//                selectTipDialog.show()
//            }
        }

        bindingView.switch02.setOnCheckedChangeListener { buttonView, isChecked ->
            if (bindingView.switch01.isChecked) {
                viewModel.matchSwitch(if (bindingView.switch02.isChecked) "1" else "0")
                if (isChecked) {
                    startPreview()
                } else {
                    stopPreview()
                }
            } else {
                bindingView.switch02.isChecked = false
                stopPreview()
                toast(getString(R.string.please_open_work_mode_first))
            }
        }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_NOTIFICATION, String::class.java)
            .observeOn(RxSchedulers.ui).`as`<ObservableSubscribeProxy<String>>(
                AutoDispose.autoDisposable(scopeProvider)
            ).subscribe { hashMapStr: String ->
                val hashMap: HashMap<String, Any> =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, Any>
                hashMap["isShow"] = true
                hashMap["itemPath"] = PathUtils.getPathNotification() + "/" + user()?.id.toString()
                hashMap["itemName"] = System.currentTimeMillis().toString()
                FileUtil.writeFileToString(
                    hashMap["itemPath"].toString(),
                    hashMap["itemName"].toString(),
                    GsonUtil.GsonString(hashMap)
                )

                SharePreUtil.setReadMoment(if (SharePreUtil.getReadMoment() <= 0) 1 else SharePreUtil.getReadMoment() + 1)
                bindingView!!.badge.text =
                    (if (SharePreUtil.getReadMoment() <= 0) 0 else SharePreUtil.getReadMoment()).toString()
                bindingView!!.badge.visibility = View.VISIBLE

                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_BADGE, "0")
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_BADGE, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe {
                bindingView!!.badge.text =
                    (if (SharePreUtil.getReadMoment() <= 0) 0 else SharePreUtil.getReadMoment()).toString()
                if (SharePreUtil.getReadMoment() <= 0) bindingView!!.badge.visibility =
                    View.GONE else bindingView!!.badge.visibility = View.VISIBLE
            }
        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_WORKBENCH, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe {
                loadData()
            }

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_WARN_MSG, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(scopeProvider)
            ).subscribe { hashMapStr: String ->
                val hashMap: HashMap<String, Any> =
                    GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, Any>
                SingleTipDialog.showTips(mContext, hashMap["warnMsg"].toString(), null)
                loadData()
            }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_AUTOMATIC_ONLINE, Integer::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(scopeProvider)
            ).subscribe {
                if (user()?.payInfoStatus == "1") {
                    // Call onlineStatus and matchSwitch, then observe the results to refresh UI
                    viewModel.onlineStatus("1").observe(
                        viewLifecycleOwner
                    ) { onlineStatusResult ->
                        // Handle the result of onlineStatus if needed (e.g., error handling)
                        if (onlineStatusResult != "000") { // Assuming "000" is your error code
                            OtherManager.manager?.onlineStatus?.value = "1"
                            viewModel.matchSwitch("1").observe(
                                viewLifecycleOwner
                            ) { matchSwitchResult ->
                                // Handle the result of matchSwitch if needed
                                // Now refresh the workbench data to update switches
                                viewModel.getWorkbench().observe(viewLifecycleOwner) {
                                    updateSwitchStates(it)
                                } // This triggers the whole chain again
                            }
                        }
                    }
                }
            }

        if (bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
            startPreview()
        } else {
            stopPreview()
        }

        setupCamera()

        bindingView.tvMatchCallCompletionRateTitle.setOnClickListener {
            SingleTipDialog.showTips(
                mContext, getString(R.string.match_call_completion_rate_tips), null
            )
        }

        bindingView.tvVideoCallCompletionRateTitle.setOnClickListener {
            SingleTipDialog.showTips(
                mContext, getString(R.string.video_call_completion_rate_tips), null
            )
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                startPeriodicRefresh()
            }
        }
    }

    private fun setupCamera(autoPreview: Boolean = false) {
        fun initial(block: (Int, Int) -> Unit) {
            bindingView.textureView.surfaceTextureListener =
                object : TextureView.SurfaceTextureListener {
                    override fun onSurfaceTextureAvailable(
                        surface: SurfaceTexture, width: Int, height: Int
                    ) {
                        block.invoke(width, height)
                    }

                    override fun onSurfaceTextureSizeChanged(
                        surface: SurfaceTexture, width: Int, height: Int
                    ) {
                    }

                    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                    }

                    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean = true
                }
        }

        initial { width, height ->
            try {
                // 遍历摄像头列表，找到前置摄像头
                for (id in cameraManager.cameraIdList) {
                    val characteristics = cameraManager.getCameraCharacteristics(id)
                    val facing = characteristics.get(CameraCharacteristics.LENS_FACING)
                    if (facing == CameraCharacteristics.LENS_FACING_FRONT) {
                        cameraId = id
                        previewSize =
                            characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                                ?.let {
                                    getMinPreSize(
                                        it.getOutputSizes(SurfaceTexture::class.java),
                                        width,
                                        height,
                                        1000
                                    )
                                } ?: Size(1080, 1920)
                        break
                    }
                }
                if (autoPreview && bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
                    startPreview()
                }
            } catch (e: CameraAccessException) {
                e.printStackTrace()
            }
        }
    }

    private fun startPreview() {
        fun openCamera(block: () -> Unit) {
            try {
                bindingView.refreshLayout.alpha = 0.3f
                bindingView.textureView.makeVisible()

                if (cameraDevice != null) {
                    Logger.w("Camera already opened")
                    block.invoke()
                    return
                }

                if (ActivityCompat.checkSelfPermission(
                        requireActivity(), Manifest.permission.CAMERA
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    return
                }
                if (::cameraId.isInitialized) {
                    cameraManager.openCamera(cameraId, object : CameraDevice.StateCallback() {
                        override fun onOpened(camera: CameraDevice) {
                            cameraDevice = camera
                            if (bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
                                block.invoke()
                            }
                            Logger.e("Camera onOpened occurred: $camera")
                        }

                        override fun onDisconnected(camera: CameraDevice) {
                            camera.close()
                            cameraDevice = null
                            stopPreview()
                            Logger.e("Camera onDisconnected occurred: $camera")
                        }

                        override fun onError(camera: CameraDevice, error: Int) {
                            camera.close()
                            cameraDevice = null
                            stopPreview()
                            Logger.e("Camera onError occurred: $error")
                        }
                    }, null)
                } else {
                    Logger.e("cameraId not initialized")
                    setupCamera(true)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        openCamera {
            try {
                val surfaceTexture = bindingView.textureView.surfaceTexture
                    ?: throw IllegalStateException("SurfaceTexture is null")

                // 配置 Surface
                surfaceTexture.setDefaultBufferSize(previewSize.width, previewSize.height)
                val surface = Surface(surfaceTexture)

                // 创建预览请求
                val previewRequestBuilder =
                    cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
                previewRequestBuilder?.addTarget(surface)

                // 创建新的捕获会话
                cameraDevice?.createCaptureSession(
                    listOf(surface), object : CameraCaptureSession.StateCallback() {
                        override fun onConfigured(session: CameraCaptureSession) {
                            captureSession = session
                            previewRequestBuilder?.build()?.let {
                                runCatching {
                                    captureSession?.setRepeatingRequest(it, null, null)
                                    Logger.d("Camera Preview started")
                                }
                            }
                        }

                        override fun onConfigureFailed(session: CameraCaptureSession) {
                            Logger.e("Camera Failed to configure capture session")
                        }
                    }, null
                )
            } catch (e: Exception) {
                e.printStackTrace()
                Logger.e("Camera Error starting preview: ${e.message}")
            }
        }
    }

    private fun stopPreview() {
        try {
            // 停止捕获会话
            captureSession?.close()
            captureSession = null

            // 关闭摄像头设备
            cameraDevice?.close()
            cameraDevice = null

            bindingView.textureView.setOpaque(true) // 设置为不透明，避免留存残影
            bindingView.refreshLayout.alpha = 1f
            bindingView.textureView.makeGone()
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.e("Camera Error stopping preview: ${e.message}")
        }
    }

    var bean: WorkbenchBean? = null
    var bean02: WorkbenchUnionBean? = null
    var hasChild = "0"
    var InviteLink = ""

    @SuppressLint("SetTextI18n")
    override fun loadData() {
        super.loadData()
        val loginBean = user()
        if (loginBean?.power != "0" && loginBean?.power != null) {// 0普通主播1一级代理2二级代理
            bindingView.agentPartContainer.visibility = View.VISIBLE
            viewModel.getWorkbenchUnionData(loginBean!!.unionId.toInt()).observe(this) {
                hasChild = it.hasChild
                if (loginBean?.power == "1" && it.hasChild == "1") {
                } else {// 2
                    bindingView.ProxyLL05.visibility = View.GONE
                    bindingView.agencySkip.text = "Member list >>"
                    bindingView.agencySkip.setTextColor(Color.parseColor("#ffffb909"))
                }

                bindingView!!.refreshLayout.finishRefresh()
                bean02 = it
                bindingView?.apply {
                    Agent.text = "Agent Name : " + it.agentName
                    Invite.text = "Invite Code ：" + it.unionId
                    currentAmount.text = it.currentAmount
                    todayDiamond.text = it.todayDiamond
                    yesterdayDiamond.text = it.yesterdayDiamond
                    weekDiamond.text = it.weekDiamond
                    monthDiamond.text = it.monthDiamond
                    totalDiamond.text = it.totalDiamond
                }
            }
        }

        viewModel?.getWorkbench()?.observe(this) {
            showContentView()
            bindingView?.refreshLayout?.finishRefresh()
            bean = it
            bindingView?.apply {
                badge.text =
                    (if (SharePreUtil.getReadMoment() <= 0) 0 else SharePreUtil.getReadMoment()).toString()
                if (SharePreUtil.getReadMoment() <= 0) bindingView?.badge?.visibility =
                    View.GONE else bindingView!!.badge.visibility = View.VISIBLE
                bindingView.switch01.isChecked =
                    (it.onlineStatus != "0")// && (UserInfoManager.user.value?.coverVideoUrl?.length != 0 && UserInfoManager.user.value?.showVideoUrl?.length != 0)
                bindingView.switch02.isChecked = (it.matchStatus == "1")

                if (bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
                    startPreview()
                } else {
                    stopPreview()
                }
                time01.text = it.workDayTime
                time02.text = it.workWeekTime
                time03.text = it.avgWeekCallTime
                time04.text = "Today：${it.connectDayRate}"
                time05.text = "Week：${it.connectWeekRate}"
                OtherManager.manager?.onlineStatus?.value =
                    if (bindingView.switch01.isChecked) "1" else "0"

                tvMatchCallCompletionRate.text =
                    getString(R.string.today_rate, it.todayMatchCallCompletionRate)
                tvMatchCallCompletionRateTotal.text =
                    getString(R.string.total_rate, it.totalMatchCallCompletionRate)

                tvVideoCallCompletionRate.text =
                    getString(R.string.today_rate, it.todayVideoCallCompletionRate)
                tvVideoCallCompletionRateTotal.text =
                    getString(R.string.total_rate, it.totalVideoCallCompletionRate)
            }
        }
        updateTask()
    }

    private fun updateTask() {
        viewModel?.homeTask()?.observe(this) {
            bindingView.tvValidMatchCalls.text =
                getString(R.string.valid_match_calls_today, it.todayValidMatchTimes)
            bindingView.tvTask.apply {
                text = getString(
                    R.string.match_bonus_task_text,
                    it.matchBonus,
                    it.currentMatchTimes,
                    it.matchSettleValue
                )
                click {
                    val (effectiveMatchCallCoin, effectiveMatchCallDuration, anchorMatchingTimesSettle) = OtherManager.manager(
                        requireActivity()
                    ).sysParam.value?.let {
                        val triple = Triple<String, String, String>(
                            it["effectiveMatchCallCoin"].toString(),
                            it["effectiveMatchCallDuration"].toString(),
                            it["anchorMatchingTimesSettle"].toString()
                        )
                        triple
                    } ?: Triple("10", "15", "20")

                    SingleTipDialog.showTips(/* context = */ requireContext(),/* content = */
                        getString(
                            R.string.home_instruction_explain,
                            effectiveMatchCallCoin,
                            effectiveMatchCallDuration,
                            anchorMatchingTimesSettle
                        ),/* gravity = */
                        Gravity.START
                    ) { }
                }
            }
        }

        viewModel?.marqueeList()?.observe(this) {
            bindingView.marqueeView.makeVisible(it.isNotEmpty())
            bindingView.marqueeView.setData(
                it.map { it.notifications })
        }
    }

    private fun updateSwitchStates(workbenchBean: WorkbenchBean) {
        bindingView?.apply {
            switch01.setCheckedNoEvent(workbenchBean.onlineStatus != "0")
            switch02.isChecked = (workbenchBean.matchStatus == "1")
            if (switch01.isChecked && switch02.isChecked) {
                startPreview()
            } else {
                stopPreview()
            }
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            bindingView?.switch01?.isChecked = OtherManager.manager?.onlineStatus?.value == "1"
            loadData()
        }
    }

    override fun onPause() {
        super.onPause()
        if (bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
            stopPreview()
        }
    }

    override fun onResume() {
        super.onResume()
        if (bindingView.switch01.isChecked && bindingView.switch02.isChecked) {
            startPreview()
        } else {
            stopPreview()
        }
        loadData()
    }

    private fun startPeriodicRefresh() {
        refreshJob?.cancel() // 取消之前的任务
        refreshJob = refreshScope.launch {
            while (isActive) {
                loadData() // 加载数据
                delay(REFRESH_INTERVAL) // 延时1分钟
            }
        }
    }

    private fun stopPeriodicRefresh() {
        refreshJob?.cancel()
        refreshJob = null
    }


    fun rank() {
        jump(RankListActivity::class.java)
    }

    fun rank02() {
        jump(RankActivity::class.java)
    }

    fun copyID02() {
        val loginBean = user()
        val isCopy = copyClipboar(mActivity, loginBean?.unionId)
//        ToastUtil.show("Copy Success ~")

    }

    fun copyClipboar(context: Context, copyStr: String?): Boolean {
        return try {
            //获取剪贴板管理器：
            val cm = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            // 创建普通字符型ClipData
            val mClipData = ClipData.newPlainText("Label", copyStr)
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData)
            ToastUtil.show(getResources().getString(R.string.b27))
            true
        } catch (e: Exception) {
            false
        }
    }

    fun agencySkip() {
        val loginBean = user()
        loginBean?.let {

            if (loginBean.power == "1" && hasChild == "1") {
                jump(TndAgentListActivity::class.java, Bundle().apply {
                    putInt("unionId", loginBean.unionId.toInt())
                })
            } else {// 2
                jump(MemberListActivity::class.java, Bundle().apply {
                    putInt("unionId", loginBean.unionId.toInt())
                })
            }
        }
    }

    fun agencySkip02() {
        val loginBean = user()
        loginBean?.let {
            jump(MemberListActivity::class.java, Bundle().apply {
                putInt("unionId", loginBean.unionId.toInt())
            })
        }
    }

    fun feedback() {
        jump(FeedbackActivity::class.java)
    }

    fun dumpWeb(type: Int) {
        var url = ""
        var title = ""
        when (type) {
            0 -> {
//                url = bean?.anchorRule.toString();title = "AnchorRule"
                jump(LevelActivity2::class.java)
                return
            }

            1 -> {
                url = bean?.workingStandard.toString();title = "WorkingStandard"
            }

            2 -> {
                url = bean?.beginnerTutorial.toString();title = "BeginnerTutorial"
            }
        }

        jump(AgreementActivity::class.java, Bundle().apply {
            putString("url", url)
            putString("title", title)
            putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
        })
    }

    fun connectRateQuery() {
        jump(ConnectRateActivity::class.java)
    }

    fun notification() {
        jump(NotificationActivity::class.java)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopPeriodicRefresh()
        refreshScope.cancel()
    }
}
