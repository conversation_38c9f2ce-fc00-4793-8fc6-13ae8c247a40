package com.mobile.anchor.app.module.workbench.home

import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.workbench.api.WorkbenchApi
import com.mobile.anchor.app.module.workbench.bean.ConnectRateListBean
import com.mobile.anchor.app.module.workbench.bean.MarqueeBean
import com.mobile.anchor.app.module.workbench.bean.MatchProgressBean
import com.mobile.anchor.app.module.workbench.bean.WorkbenchBean
import com.mobile.anchor.app.module.workbench.bean.WorkbenchUnionBean
import io.reactivex.Flowable

class WorkbenchRepository : IRepository {


    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @params  goodsType :  数据类型(判断是进场特效,还是 头像框还是气泡等)
    @return  对应列表数据:  */
    fun getWorkbench(): Flowable<BaseResult<WorkbenchBean>> = WorkbenchApi.getWorkbench()

    fun getWorkbenchAnchorShareUrl(): Flowable<BaseResult<String>> =
        WorkbenchApi.getWorkbenchAnchorShareUrl()


    fun getWorkbenchUnionData(unionId: Int): Flowable<BaseResult<WorkbenchUnionBean>> =
        WorkbenchApi.getWorkbenchUnionData(unionId)

    fun getVideoList(): Flowable<BaseResult<List<ConnectRateListBean>>> =
        WorkbenchApi.getVideoList()

    fun onlineStatus(onlineStatus: String): Flowable<BaseResult<Any>> =
        WorkbenchApi.onlineStatus(onlineStatus)

    fun matchSwitch(isOpen: String): Flowable<BaseResult<Any>> = WorkbenchApi.matchSwitch(isOpen)

    fun homeTask(): Flowable<BaseResult<MatchProgressBean>> = WorkbenchApi.homeTask()

    fun marqueeList(): Flowable<BaseResult<List<MarqueeBean>>> = WorkbenchApi.marqueeList()
}