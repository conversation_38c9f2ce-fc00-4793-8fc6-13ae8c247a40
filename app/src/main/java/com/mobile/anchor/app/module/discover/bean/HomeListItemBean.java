package com.mobile.anchor.app.module.discover.bean;

public class HomeListItemBean {
    String id;
    String headFileName;
    String level;
    String nickName;
    String content;
    String contentTranslation = "";
    String ImageUrl = "";
    boolean isShowTranslation = false;

    public String getImageUrl() {
        return ImageUrl;
    }

    public void setImageUrl(String imageUrl) {
        ImageUrl = imageUrl;
    }

    public String getContentTranslation() {
        return contentTranslation;
    }

    public void setContentTranslation(String contentTranslation) {
        this.contentTranslation = contentTranslation;
    }

    public boolean isShowTranslation() {
        return isShowTranslation;
    }

    public void setShowTranslation(boolean showTranslation) {
        isShowTranslation = showTranslation;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHeadFileName() {
        return headFileName;
    }

    public void setHeadFileName(String headFileName) {
        this.headFileName = headFileName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
