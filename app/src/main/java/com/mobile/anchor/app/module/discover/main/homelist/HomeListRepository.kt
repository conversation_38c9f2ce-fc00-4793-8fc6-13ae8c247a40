package com.mobile.anchor.app.module.discover.main.homelist

import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.discover.api.DiscoverApi
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import io.reactivex.Flowable

class HomeListRepository : IRepository {



    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @params  goodsType :  数据类型(判断是进场特效,还是 头像框还是气泡等)
    @return  对应列表数据:  */
    fun getList(current: Int, size: Int, type: String, country: String): Flowable<BaseResult<AnchorItemBean>> = DiscoverApi.getHomeList(current,size,type, country)

    fun getCallAnchor(channelId: String, type: String): Flowable<BaseResult<Any>> = DiscoverApi.getCallAnchor(channelId,type)
}