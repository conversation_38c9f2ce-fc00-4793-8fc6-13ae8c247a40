package com.mobile.anchor.app.module.discover.main.center;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bigkoo.convenientbanner.utils.ScreenUtil;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.immersionbar.standard.ImmersionBar;
import com.mobile.anchor.app.R;
import anchor.app.base.databinding.BaseActivityPicViewLayBinding;
import anchor.app.base.ui.BaseNoModelActivity;
import anchor.app.base.utils.ActivityUtils;
import anchor.app.base.utils.ImageUtil;
import anchor.app.base.utils.RxViewUtils;
import anchor.app.base.view.CustomSideViewPager;
import anchor.app.base.view.MyBigImgView;
import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean;
import com.mobile.anchor.app.util.BlurTransformation;

import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.ImageViewState;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;


public class AlbumBigImagePreviewActivity extends BaseNoModelActivity<BaseActivityPicViewLayBinding> {

    private static final String DATA_IMAGE_URL = "DATA_IMAGE_URL";
    private static final String DATA_IMAGE_RESOURCE = "DATA_IMAGE_RESOURCE";
    private static final String DATA_IMAGE_URL_LIST = "DATA_IMAGE_URL_LIST";
    private static final String DATA_DEFAULT_SELECT = "DATA_DEFAULT_SELECT";
    private static final String DATA_RECT = "DATA_RECT";
    private static final String DATA_USER = "DATA_USER";

    private static final long ANIM_TIME = 300;


//    ImageView mBackground;
//    FrameLayout mLoading;
//    MyBigImgView mOnePic;
//    CustomSideViewPager mViewPager;
//    LinearLayout mPicPoint;
//    FrameLayout mFlViewPager;

    private RectF mThumbMaskRect;
    private Matrix mThumbImageMatrix;

    private ObjectAnimator mBackgroundAnimator;


    //本地资源
    private int imageResource;
    //一张照片
    private String imageUrl;
    //照片列表数据
    private List<String> imageUrlList;
    //照片列表Adapter
    private UserPicListAdapter picListAdapter;
    //照片列表当前选中的下标
    private int picSelectPosition;

    //选中的图片地址
    private String selectImageUrl;
    private boolean canJumpPage = true;


    /**
     * 指定Layout布局
     *
     * @return layout的R文件地址
     */
    @Override
    public int getLayoutId() {
        getWindow().setStatusBarColor(Color.BLACK);
//        Window window = getWindow();
//        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
//        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//        window.setStatusBarColor(Color.BLACK);
        return R.layout.base_activity_pic_view_lay;
    }

    /**
     * 无需重写Activity的onCreate，数据初始化在此方法执行
     */
    PersonalCenterModel personalCenterModel;

    @Override
    protected void initView() {

        personalCenterModel = new PersonalCenterModel(this.getApplication());
        personalCenterModel.initFliterAttribute(this);
        //获取参数
        imageUrl = getIntent().getStringExtra(DATA_IMAGE_URL);
        imageResource = getIntent().getIntExtra(DATA_IMAGE_RESOURCE, 0);
        imageUrlList = (List<String>) getIntent().getSerializableExtra(DATA_IMAGE_URL_LIST);


        //走单张照片逻辑
        if (!TextUtils.isEmpty(imageUrl)) {
            loadOnePic(imageUrl);
        }
        //走本地资源逻辑
        else if (imageResource != 0) {
            loadOnePic(imageResource);
        }
        //照片墙
        else {

            RxViewUtils.setOnClickListeners(bindingView.ivBack, new Consumer<View>() {
                @Override
                public void accept(View view) throws Exception {
                    finish();
                }
            });
            bindingView.mFlViewPager.setVisibility(View.VISIBLE);
            bindingView.mOnePic.setVisibility(View.GONE);
            bindingView.mLoading.setVisibility(View.GONE);
            picSelectPosition = getIntent().getIntExtra(DATA_DEFAULT_SELECT, 0);

            initPagePoint();
            picListAdapter = new UserPicListAdapter();
            bindingView.mViewPager.setAdapter(picListAdapter);
            //切换监听
            bindingView.mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int i, float v, int positionOffsetPixels) {
                }

                @Override
                public void onPageSelected(int position) {
                    View lastChild = bindingView.mPicPoint.getChildAt(picSelectPosition);
                    if (lastChild != null) {
                        lastChild.setSelected(false);
                    }
                    View childAt = bindingView.mPicPoint.getChildAt(position);
                    if (childAt != null) {
                        childAt.setSelected(true);
                        picSelectPosition = position;
                    }
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                }
            });

            bindingView.mViewPager.setOnSideListener(new CustomSideViewPager.onSideListener() {
                @Override
                public void onLeftSide() {

                }

                @Override
                public void onRightSide() {
                    if (canJumpPage) {
                        canJumpPage = false;
                    }
                }
            });

            bindingView.mViewPager.setCurrentItem(picSelectPosition);
            //背景动画
            mBackgroundAnimator = ObjectAnimator.ofFloat(bindingView.mBackground, "alpha", 0f, 1f);
            mBackgroundAnimator.setDuration(ANIM_TIME);
            mBackgroundAnimator.start();
            ObjectAnimator alpha = ObjectAnimator.ofFloat(bindingView.mFlViewPager, "alpha", 0f, 1f);
            alpha.setDuration(ANIM_TIME);
            alpha.start();
        }

    }


    @Override
    protected void onResume() {
        super.onResume();
        canJumpPage = true;
    }

    /**
     * 加载单张照片
     */
    private void loadOnePic(Object imageResource) {
        bindingView.mFlViewPager.setVisibility(View.GONE);
        bindingView.mOnePic.setVisibility(View.VISIBLE);
        bindingView.mLoading.setVisibility(View.VISIBLE);

        Rect rect = getIntent().getParcelableExtra(DATA_RECT);

        //计算之前压缩图的位置
        ViewGroup.LayoutParams layoutParams = bindingView.mLoading.getLayoutParams();
        layoutParams.width = Math.abs(rect.right - rect.left);
        layoutParams.height = Math.abs(rect.bottom - rect.top);
        bindingView.mLoading.setLayoutParams(layoutParams);
        bindingView.mLoading.setX(rect.left);
        bindingView.mLoading.setY(rect.top);

        //加载原图
        ImageUtil.displayImg(this, bindingView.mOnePic, imageResource, 0, new RequestListener<Drawable>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                bindingView.mOnePic.setImageResource(R.mipmap.base_ic_send_pic_default);
                return false;
            }

            @Override
            public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                //加载出来的图片尺寸
                float resourceWidth = resource.getIntrinsicWidth();
                float resourceHeight = resource.getIntrinsicHeight();
                //计算图片的比例
                float resourceScale = resourceHeight / resourceWidth;
                //计算屏幕比例
                float screenHeight = ScreenUtil.getScreenHeight(mContext);
                float screenWidth = ScreenUtil.getScreenWidth(mContext);
                float screenScale = screenHeight / screenWidth;

                //如果图片高宽比 > 屏幕高宽比，代表图片长度占满屏幕，宽度占不满
                if (resourceScale > screenScale) {
                    //原控件高度
                    float viewHeight = Math.abs(rect.bottom - rect.top);
                    //展示在屏幕中时，图片的宽度
                    float newImageWidth = screenHeight / resourceHeight * screenWidth;
                    //计算图片左右黑边大小，因为宽度未占满
                    float distance = (screenWidth - newImageWidth) / 2;
                    //拿到控件高度，计算缩放大小
                    float scaleY = viewHeight / screenHeight;
                    float scaleWidth = distance * scaleY;
                    rect.left = (int) (rect.left - scaleWidth);
                    rect.right = (int) (rect.right + scaleWidth);
                }
                //代表宽度占满，高度占不满
                else if (resourceScale < screenScale) {
                    //原控件宽度
                    float viewWidth = Math.abs(rect.right - rect.left);
                    //展示在屏幕中时，图片的高度
                    float newImageHeight = resourceScale * screenWidth;
                    //计算图片上下黑边大小，因为高度未占满
                    float distance = (screenHeight - newImageHeight) / 2;
                    //拿到控件宽度，计算缩放大小
                    float scaleX = viewWidth / screenWidth;
                    float scaleHeight = distance * scaleX;
                    rect.top = (int) (rect.top - scaleHeight);
                    rect.bottom = (int) (rect.bottom + scaleHeight);
                }
                mThumbMaskRect = new RectF(rect);


                //背景动画
                mBackgroundAnimator = ObjectAnimator.ofFloat(bindingView.mBackground, "alpha", 0f, 1f);
                mBackgroundAnimator.setDuration(ANIM_TIME);
                mBackgroundAnimator.start();

                //计算大图大小
                RectF bigMaskRect = new RectF(0, 0, bindingView.mOnePic.getWidth(), bindingView.mOnePic.getHeight());
                //直接将图片大小放大至小图大小
                bindingView.mOnePic.zoomMaskTo(mThumbMaskRect, 0);
                //从小图过渡到大图大小
                bindingView.mOnePic.zoomMaskTo(bigMaskRect, ANIM_TIME);


                //图片放大动画
                RectF thumbImageMatrixRect = new RectF();
                MyBigImgView.MathUtils.calculateScaledRectInContainer(new RectF(rect), 1, 1, ImageView.ScaleType.CENTER_CROP, thumbImageMatrixRect);
                RectF bigImageMatrixRect = new RectF();
                MyBigImgView.MathUtils.calculateScaledRectInContainer(new RectF(0, 0, bindingView.mOnePic.getWidth(), bindingView.mOnePic.getHeight()), 1, 1, ImageView.ScaleType.CENTER_CROP, bigImageMatrixRect);
                mThumbImageMatrix = new Matrix();
                MyBigImgView.MathUtils.calculateRectTranslateMatrix(bigImageMatrixRect, thumbImageMatrixRect, mThumbImageMatrix);
                bindingView.mOnePic.outerMatrixTo(mThumbImageMatrix, 0);
                bindingView.mOnePic.outerMatrixTo(new Matrix(), ANIM_TIME);
                bindingView.mLoading.setVisibility(View.GONE);
                return false;
            }
        });

        RxViewUtils.setOnClickListeners(bindingView.mOnePic, new Consumer<View>() {
            @Override
            public void accept(View view) throws Exception {
                finish();

            }
        });

        //长按事件
        bindingView.mOnePic.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {

                showBottomMenu(imageUrl);
                return false;
            }
        });
    }

    /**
     * 初始化照片墙指示点
     */
    private void initPagePoint() {
        if (imageUrlList == null || imageUrlList.size() == 0 || imageUrlList.size() == 1) {
            return;
        }
        bindingView.mPicPoint.setVisibility(View.VISIBLE);
        bindingView.mPicPoint.removeAllViews();
        for (String data : imageUrlList) {
            ImageView view = new ImageView(this);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ScreenUtil.dip2px(mContext, 6), ScreenUtil.dip2px(mContext, 6));
            layoutParams.leftMargin = ScreenUtil.dip2px(mContext, 4);
            layoutParams.rightMargin = ScreenUtil.dip2px(mContext, 4);
            view.setBackgroundResource(R.drawable.base_state_selected_oval_fff_fff_50);
            view.setLayoutParams(layoutParams);

            bindingView.mPicPoint.addView(view);
            if (bindingView.mPicPoint.getChildCount() == picSelectPosition + 1) {
                view.setSelected(true);
            }
        }
    }


    /**
     * 展示菜单
     */
    private void showBottomMenu(String imageUrl) {
//        this.selectImageUrl = imageUrl;
//        List<DialogList.Item> items = new ArrayList<>();
//        items.add(new DialogList.Item(AppUtils.getString(R.string.save_to_local_s), 111));
//
//        DialogList dialogList = new DialogList(BigImagePreviewActivity.this, AppUtils.getString(R.string.cancel_s), items, new DialogList.OnDialogItemClickListener() {
//            @Override
//            public void onDialogItemClick(DialogList.Item item, int dialogPosition) {
//                UCropEntity.Builder.create(BigImagePreviewActivity.this).putPermissin(Permission.Group.STORAGE).build().start(new UCropEntity.OnPermission() {
//                    @Override
//                    public void permissionSuccess() {
//                        downloadPic();
//                    }
//
//                    @Override
//                    public void permissionFailed(Throwable e) {
//
//                    }
//                });
//
//            }
//
//
//            @Override
//            public void onCancel() {
//
//            }
//        });
//        dialogList.show();
    }

    /**
     * 图片下载
     */
//    private void downloadPic() {
//        if (TextUtils.isEmpty(selectImageUrl)) {
//            ToastUtils.showShort(R.string.data_err);
//            return;
//        }
//
//        LoadingDialog.getInstance(BigImagePreviewActivity.this).show();
//
//        Disposable subscribe = Observable.create(new ObservableOnSubscribe<String>() {
//
//            @Override
//            public void subscribe(ObservableEmitter<String> emitter) throws Exception {
//                ImageUtil.downloadImage(BigImagePreviewActivity.this, UrlManager.getRealHeadPath(selectImageUrl), new ImageUtil.OnDownloadOkListener() {
//                    @Override
//                    public void onResultOk(Bitmap resource) {
//                        if (resource == null) {
//                            emitter.onError(null);
//                        } else {
////                            File file = FileUtil.saveImageToGallery(resource, String.valueOf(System.currentTimeMillis()));
////                            if (file == null) {
////                                emitter.onError(null);
////                            } else {
////                                emitter.onNext(file.getPath());
////                            }
//                           boolean re =FileUtils.StaticParams.addBitmapToAlbum(resource,System.currentTimeMillis()+".png","image/png",Bitmap.CompressFormat.PNG);
//                            if (!re) {
//                                emitter.onError(null);
//                            } else {
//                                emitter.onNext("");
//                            }
//                        }
//                    }
//
//                    @Override
//                    public void onResultError() {
//                        emitter.onError(null);
//
//                    }
//                });
//            }
//        })
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(new Consumer<String>() {
//                    @Override
//                    public void accept(String filePath) throws Exception {
//                        LoadingDialog.getInstance(BigImagePreviewActivity.this).dismiss();
//
//                        ToastUtils.showShort(R.string.save_success);
//                    }
//
//
//                }, new Consumer<Throwable>() {
//                    @Override
//                    public void accept(Throwable throwable) throws Exception {
//                        LoadingDialog.getInstance(BigImagePreviewActivity.this).dismiss();
//                        ToastUtils.showShort(R.string.save_failed);
//
//                    }
//                });
//    }
    @Override
    public void finish() {
        if ((mBackgroundAnimator != null && mBackgroundAnimator.isRunning())) {
            return;
        }
        if (bindingView.mBackground == null) {
            return;
        }

        //背景动画
        mBackgroundAnimator = ObjectAnimator.ofFloat(bindingView.mBackground, "alpha", bindingView.mBackground.getAlpha(), 0f);
        mBackgroundAnimator.setDuration(ANIM_TIME);
        mBackgroundAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                AlbumBigImagePreviewActivity.super.finish();
                overridePendingTransition(0, 0);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mBackgroundAnimator.start();


        if (imageUrlList != null && imageUrlList.size() > 0) {
            ObjectAnimator alpha = ObjectAnimator.ofFloat(bindingView.mFlViewPager, "alpha", bindingView.mBackground.getAlpha(), 0f);
            alpha.setDuration(ANIM_TIME);
            alpha.start();
        }
        //只有单张照片时，做结束缩放动画
        else {
            //mask动画
            bindingView.mOnePic.zoomMaskTo(mThumbMaskRect, ANIM_TIME);
            //图片缩小动画
            bindingView.mOnePic.outerMatrixTo(mThumbImageMatrix, ANIM_TIME);
        }

    }


    //--------------------------静态跳转--------------------------
    //--------------------------静态跳转--------------------------
    //--------------------------静态跳转--------------------------


    public static void router(Context context, View imageView, int resource) {
        Intent intent = new Intent(context, AlbumBigImagePreviewActivity.class);
        Rect rect = new Rect();
        imageView.getGlobalVisibleRect(rect);
        intent.putExtra(DATA_RECT, rect);
        intent.putExtra(DATA_IMAGE_RESOURCE, resource);
        context.startActivity(intent);
    }

    public static void router(Context context, View imageView, String imageUrl) {
        Intent intent = new Intent(context, AlbumBigImagePreviewActivity.class);
        Rect rect = new Rect();
        imageView.getGlobalVisibleRect(rect);
        intent.putExtra(DATA_RECT, rect);
        intent.putExtra(DATA_IMAGE_URL, imageUrl);
        context.startActivity(intent);
    }

    public static void router(Context context, View imageView, ArrayList<String> imageUrlList, int defaultSelect) {
        Intent intent = new Intent(context, AlbumBigImagePreviewActivity.class);
        Rect rect = new Rect();
        imageView.getGlobalVisibleRect(rect);
        intent.putExtra(DATA_RECT, rect);
        intent.putExtra(DATA_IMAGE_URL_LIST, imageUrlList);
        intent.putExtra(DATA_DEFAULT_SELECT, defaultSelect);
        context.startActivity(intent);
    }

    public static ArrayList<PersonalCenterBean.AnchorFileListDTO> listData;

    public static void router(Context context, View imageView, ArrayList<String> imageUrlList, int defaultSelect, ArrayList<PersonalCenterBean.AnchorFileListDTO> bean) {
        AlbumBigImagePreviewActivity.listData = bean;
        router(context, imageView, imageUrlList, defaultSelect);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        AlbumBigImagePreviewActivity.listData = null;
    }

    //--------------------------Adapter--------------------------
    //--------------------------Adapter--------------------------
    //--------------------------Adapter--------------------------
    public class UserPicListAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return imageUrlList == null ? 0 : imageUrlList.size();
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
            return view == o;
        }

        @SuppressLint("CheckResult")
        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {

            View contentView = LayoutInflater.from(container.getContext())
                    .inflate(R.layout.discover_imge_preview, container, false);
            MyBigImgView imageView = contentView.findViewById(R.id.preview_image);
            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
            SubsamplingScaleImageView longImg = contentView.findViewById(R.id.longImg);
            ImageView ivLong = contentView.findViewById(R.id.ivLong);
            ImageView gaosi = contentView.findViewById(R.id.gaosi);
            RelativeLayout rlvisi = contentView.findViewById(R.id.RLVISI);
            LinearLayout buy = contentView.findViewById(R.id.buy);
            TextView price = contentView.findViewById(R.id.price);

            Glide.with(imageView.getContext()).load(listData.get(position).getFileUrl()).into(imageView);

            gaosi.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    return true;
                }
            });
            //设置图片控件的高斯模糊效果
//            Glide.with(container.getContext())
//                    .load(listData.get(position).getFileUrl())
//                    .apply(bitmapTransform(new BlurTransformation(container.getContext(), 0, 10)))
//                            .into(gaosi);

            Glide.with(container.getContext())
                    .load(listData.get(position).getFileUrl())
                    .apply(RequestOptions.bitmapTransform(new BlurTransformation(container.getContext(), 8, 30)))
                    .into(gaosi);

            if (listData.get(position).getPurchaseFlag().equals("1")) {
                rlvisi.setVisibility(View.GONE);
                imageView.setVisibility(View.VISIBLE);
            } else {
                imageView.setVisibility(View.GONE);
                rlvisi.setVisibility(View.VISIBLE);
                price.setText(" pay " + listData.get(position).getFilePrice());

                buy.setOnClickListener(v -> {
                    personalCenterModel.userBuyPhoto(listData.get(position).getId()).observe(AlbumBigImagePreviewActivity.this, o -> {
                        rlvisi.setVisibility(View.GONE);
                        imageView.setVisibility(View.VISIBLE);
                        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_ALBUMIMAGE, position);
                    });
                });
            }

            ImageUtil.displayImg(AlbumBigImagePreviewActivity.this, imageView, imageUrlList.get(position), R.mipmap.base_ic_main_default, new RequestListener<Drawable>() {

                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                    return false;
                }

                @Override
                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                    if (ActivityUtils.isDestroy(AlbumBigImagePreviewActivity.this)) {
                        return false;
                    }
                    if (imageUrlList.get(position) != null && (imageUrlList.get(position).endsWith(".gif") || imageUrlList.get(position).endsWith(".GIF"))) {
                        return false;
                    }

                    //加载出来的图片尺寸
                    float resourceWidth = resource.getIntrinsicWidth();
                    float resourceHeight = resource.getIntrinsicHeight();
                    //计算图片的比例
                    float resourceScale = resourceHeight / resourceWidth;
                    //计算屏幕比例
                    float screenHeight = ScreenUtil.getScreenHeight(mContext);
                    float screenWidth = ScreenUtil.getScreenWidth(mContext);
                    float screenScale = screenHeight / screenWidth;

                    //如果图片高宽比 > 屏幕高宽比，代表图片长度占满屏幕，宽度占不满
                    if (resourceScale > screenScale) {
                        if (ActivityUtils.isDestroy(AlbumBigImagePreviewActivity.this)) {
                            return false;
                        }
//                        imageView.setImageResource(R.mipmap.ic_main_default);
                        imageView.setVisibility(View.GONE);
                        ivLong.setVisibility(View.VISIBLE);
                        longImg.setVisibility(View.VISIBLE);
                        longImg.setOnImageEventListener(new SubsamplingScaleImageView.DefaultOnImageEventListener() {
                            @Override
                            public void onReady() {
                                if (!ActivityUtils.isDestroy(AlbumBigImagePreviewActivity.this)) {
                                    imageView.setVisibility(View.GONE);
                                    ivLong.setVisibility(View.GONE);
                                    longImg.setVisibility(View.VISIBLE);
                                }
                            }

                        });
                        Glide.with(AlbumBigImagePreviewActivity.this)
                                .download(imageUrlList.get(position))
                                .into(new SimpleTarget<File>() {
                                    @Override
                                    public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                        super.onLoadFailed(errorDrawable);
                                    }

                                    @Override
                                    public void onResourceReady(File resource, Transition<? super File> transition) {
                                        if (!ActivityUtils.isDestroy(AlbumBigImagePreviewActivity.this)) {
                                            if (resource != null) {
                                                longImg.setQuickScaleEnabled(true);
                                                longImg.setZoomEnabled(true);
                                                longImg.setPanEnabled(true);
                                                longImg.setDoubleTapZoomDuration(100);
                                                longImg.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_CENTER_CROP);
                                                longImg.setDoubleTapZoomDpi(SubsamplingScaleImageView.ZOOM_FOCUS_CENTER);
                                                longImg.setImage(ImageSource.uri(resource.getAbsolutePath()), new ImageViewState(0, new PointF(0, 0), 0));
                                            }
                                        }

                                    }
                                });


                    }
                    return false;
                }
            });


            RxViewUtils.setOnClickListeners(longImg, new Consumer<View>() {
                @Override
                public void accept(View view) throws Exception {
                    finish();

                }
            });
            RxViewUtils.setOnClickListeners(ivLong, new Consumer<View>() {
                @Override
                public void accept(View view) throws Exception {
                    finish();

                }
            });
            RxViewUtils.setOnClickListeners(imageView, new Consumer<View>() {
                @Override
                public void accept(View view) throws Exception {
                    finish();

                }
            });

            //长按事件
            imageView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {

                    showBottomMenu(imageUrlList.get(position));
                    return false;
                }
            });
            //长按事件
            longImg.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {

                    showBottomMenu(imageUrlList.get(position));
                    return false;
                }
            });
            container.addView(contentView);
            return contentView;
        }


        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            container.removeView((View) object);
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }
    }

    @Override
    protected void initImmersionBar() {
        super.initImmersionBar();

        ImmersionBar.with(this)
                .statusBarDarkFont(true, 0.2f).init();
    }
}