package com.mobile.anchor.app.module.discover.main.rank

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverItemRankBinding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.ext.jump
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CountryUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.databinding.ActivityRankListBinding
import com.mobile.anchor.app.module.user.bean.RankBean
import com.mobile.anchor.app.module.discover.main.chat.videochat.CallVideoChatActivity
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import com.mobile.anchor.app.module.discover.main.rank.adapter.RankListItemAdapter

class RankListActivity : BaseActivity<RankListViewmodel, ActivityRankListBinding>(), ItemClickPresenter<Any>,
    ItemDecorator {
    val from: String? get() = intent.getStringExtra("from")
    var adapter: RankListItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var isMoreLoading = false
    var current = 0
    var size = 50
    override fun getLayoutId(): Int {
        return R.layout.activity_rank_list
    }

    override fun initView() {
        /**  <AUTHOR>  Description : 初始化数据和页面视图控件
         */
//        pageType = getArguments().getInt("pageType");
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        dataList = ObservableArrayList<Any>()
        adapter = RankListItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

//        for (int i = 0; i < 100; i++) {
//            dataList.add(new AnchorItemBean());
//        }
    }

    //    然后就想在
    override fun loadData() {
        showLoading()
        loadData2(false)
    }

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {
        if (reference) {
            current = 1;
        }
        viewModel!!.getAnchorCharts(this@RankListActivity, "1").observe(
            this
        ) { bean: List<RankBean?> ->
            showContentView()
            current += 1

            if (reference) { //
                dataList!!.clear()
            }
//            isMoreLoading = false
            if (bean.size > 0) {
                setTopThree(bean)
            }
            if (bean.size < size) {
            }

            if (dataList!!.size == 0) {
                viewModel!!.error.value = ResultException(BaseResult.NOT_DATA, getString(R.string.a0018))
            }
            adapter!!.notifyDataSetChanged()

        }
    }

    fun toVideo(type: Int) {
        var bean = dataList!![type] as RankBean


        val item1 = AnchorItemBean.RecordsDTO()
        bean.id = bean.getId()
        bean.country = bean.getCountry()
        bean.nickName = bean.getNickName()
        bean.headFileName = bean.getHeadFileName()
        bean.videoPrice = bean.getVideoPrice()
        bean.coverVideoUrl = bean.getCoverVideoUrl()
        bean.showVideoUrl = bean.getShowVideoUrl()

        jump(CallVideoChatActivity::class.java, Bundle().apply {
            putLong("id", bean.id)
            putString("userRole", "1")
            putString("sourceType", "1")
            putString("sourcePage", "10")
            putInt("type", 1)
            putSerializable("bean", item1)
        })
    }


    private fun setTopThree(bean: List<RankBean?>) {
        dataList!!.addAll(bean)
        var removeFirst = dataList!!.removeFirst() as RankBean
        var removeFirst02 = dataList!!.removeFirst() as RankBean
        var removeFirst03 = dataList!!.removeFirst() as RankBean


        Glide.with(mContext).load(removeFirst02.headFileName).into(bindingView.head)
        bindingView.name.text = removeFirst02.nickName
        bindingView.countryICON.setImageDrawable(
            CountryUtil.countryCodeToImage(
                this@RankListActivity, removeFirst02.country
            )
        )

        Glide.with(mContext).load(removeFirst.headFileName).into(bindingView.head02)
        bindingView.name02.text = removeFirst.nickName
        bindingView.countryICON02.setImageDrawable(
            CountryUtil.countryCodeToImage(
                this@RankListActivity, removeFirst.country
            )
        )

        Glide.with(mContext).load(removeFirst03.headFileName).into(bindingView.head03)
        bindingView.name03.text = removeFirst03.nickName
        bindingView.countryICON03.setImageDrawable(
            CountryUtil.countryCodeToImage(
                this@RankListActivity, removeFirst03.country
            )
        )
    }


    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any) {

        val bean = item as RankBean
        if (v.id == R.id.tryChat) {
            val hashMap: java.util.HashMap<String, String> = java.util.HashMap<String, String>()
            hashMap["headFileName"] = bean?.headFileName ?: ""
            hashMap["nikeName"] = bean!!.nickName
            hashMap["id"] = bean!!.id.toString()

            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(hashMap))

        } else if (v.id == R.id.tryVideo) {


            val item1 = AnchorItemBean.RecordsDTO()
            bean.id = bean.getId()
            bean.country = bean.getCountry()
            bean.nickName = bean.getNickName()
            bean.headFileName = bean.getHeadFileName()
            bean.videoPrice = bean.getVideoPrice()
            bean.coverVideoUrl = bean.getCoverVideoUrl()
            bean.showVideoUrl = bean.getShowVideoUrl()

            jump(CallVideoChatActivity::class.java, Bundle().apply {
                putLong("id", bean.id)
                putString("userRole", "1")
                putString("sourceType", "1")
                putString("sourcePage", "10")
                putInt("type", 1)
                putSerializable("bean", item1)
            })
        }

//        BlackListBean.RecordsDTO  bean = (BlackListBean.RecordsDTO) item;
//        viewModel.removeBlackList(bean.getId());

//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    @SuppressLint("SetTextI18n")
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
//        discover_item_rank
        val binding01 = holder.binding
        if (binding01 is DiscoverItemRankBinding) {
            var bean = dataList?.get(position) as RankBean
            Glide.with(mContext).load(bean.headFileName).into(binding01.head)
            binding01.num.text = (position + 4).toString()
            binding01.name.text = bean.nickName
            binding01.country.text = bean.country
            binding01.countryICON.setImageDrawable(CountryUtil.countryCodeToImage(this@RankListActivity, bean.country))

        }
    }

    fun query() {

    }

    override fun onBackPressed() {
        super.onBackPressed()
        //        if (!"user".equals(from)) {
//            //跳转首页
//            ActivityUtil.getInstance().finishAllActivityExcept("mikchat.app.user.baby.BabyInfoActivity");
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            super.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "BlackListActivity"
    }
}