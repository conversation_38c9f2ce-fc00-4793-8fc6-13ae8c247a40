package com.mobile.anchor.app.module.user.bean

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/11/23 16:19
 * @description :
 */
data class RewardTaskBean(val oneTimeMissions: List<RewardTaskItemBean>, val dailyMissions: List<RewardTaskItemBean>)

data class RewardTaskItemBean(
    val id: String,
    val missionId: String,
    val title: String,
    val type: Int,
    val bonus: String,
    val standardValue: Int,
    val current: Int,
    val canGet: String,
    val desc: String,
    val toType: Int,
    val hasProgress: String
)
