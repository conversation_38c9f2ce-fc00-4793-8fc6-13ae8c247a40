package com.mobile.anchor.app.module.login.register

import anchor.app.base.bean.LoginBean
import android.annotation.SuppressLint
import android.content.Intent
import android.database.Cursor
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.util.Patterns
import android.view.View
import android.widget.EditText
import androidx.recyclerview.widget.GridLayoutManager
import com.bigkoo.convenientbanner.utils.ScreenUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.entity.LocalMedia
import com.mobile.anchor.app.R
import com.uber.autodispose.AutoDispose
import me.drakeet.multitype.MultiTypeAdapter
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.OperationTipDialog
import anchor.app.base.ext.jump
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.ActivityUtil
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.Utils
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import anchor.app.base.view.BigImagePreviewActivity
import anchor.app.base.view.at.AtWrapper
import anchor.app.base.view.picbind.binder.OnItemChildClickListener
import anchor.app.base.view.picbind.binder.PublishAddBinder
import anchor.app.base.view.picbind.binder.PublishImageBinder
import anchor.app.base.view.video.CameraXVideoActivity
import com.mobile.anchor.app.databinding.ActivityModifyInfoBinding
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.util.Locale

/**
 * 宝宝信息页面
 */
class ModifyInfoActivity : BaseActivity<ModifyInfoModel, ActivityModifyInfoBinding>() {

    val hashMapStr: String get() = intent.getStringExtra("hashMapStr").toString()

    //拍照/相册选择工具
    private var mCameraUtil: CameraUtil? = null
    override fun getLayoutId(): Int {
        return R.layout.activity_modify_info
    }

    @SuppressLint("Range")
    override fun initView() {
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        bindingView!!.checkbox01.isSelected = false
        bindingView!!.checkbox02.isSelected = true
        bindingView!!.editTextName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                textChanged(bindingView!!.editTextName, s)
            }
        })
        bindingView.editPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                textChanged(bindingView!!.editPhone, s)
            }
        })
        bindingView.editEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                textChanged(bindingView!!.editEmail, s)
            }
        })

        initUploadImageView()

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_REFRESH_UPLOADVIDEURL, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(AutoDispose.autoDisposable(scopeProvider)).subscribe {
                ToastUtil.show("Uploading a video file...")
                LoadingDialog.getInstance(mContext).show()

                var path = getAbsolutePathFromUri(Uri.parse(it))


                val projection = arrayOf(MediaStore.Video.Media._ID)
                val selection = "${MediaStore.Video.Media.DATA} = ?"
                val selectionArgs = arrayOf(path)
                val cursor = <EMAIL>(
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI, projection, selection, selectionArgs, null
                )
                cursor?.use { cursor ->
                    if (cursor.moveToFirst()) {
                        val videoId = cursor.getLong(cursor.getColumnIndex(MediaStore.Video.Media._ID))
                        val sourceUri =
                            Uri.withAppendedPath(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, videoId.toString())


                        val destinationDirectory = File(PathUtils.getPathVideo())// 指定目标目录

                        val resolver = <EMAIL>
                        val sourceCursor = resolver.query(sourceUri, null, null, null)
                        sourceCursor?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                val displayName =
                                    cursor.getString(cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME))
                                val sourceStream = resolver.openInputStream(sourceUri)
                                val destinationFile = File(destinationDirectory, displayName)
                                destinationFile.outputStream().use { destinationStream ->
                                    sourceStream?.copyTo(destinationStream)
                                }

                                videoFileUri = destinationFile.toURI().toString()

//                            viewModel!!.uploadPicture(File(videoFileName)).observe(this@ModifyInfoActivity) { s: String ->
                                viewModel!!.uploadPicture(destinationFile).observe(this@ModifyInfoActivity)

                                { s: kotlin.String ->
                                    anchor.app.base.dialog.LoadingDialog.getInstance(mContext).dismiss()
                                    if (!android.text.TextUtils.isEmpty(s)) {
                                        videoFileUri = it
                                        videoFileName = s
                                        bindingView.ivDelete.visibility = android.view.View.VISIBLE
                                        bindingView!!.title02.text = "Upload cover video（1/1）";

                                        checkSave()

                                        val mmr = android.media.MediaMetadataRetriever() //实例化MediaMetadataRetriever对象



                                        mmr.setDataSource(destinationFile.absolutePath)
                                        val bitmap = mmr.getFrameAtTime(0) //0表示首帧图片
                                        bindingView.imgVideo.setImageBitmap(bitmap)
                                        mmr.release() //释放MediaMetadataRetriever对象
                                    } else {
                                        anchor.app.base.utils.ToastUtil.show("Failed to upload profile video")

                                    }
                                }
                            }
                        }
                    }
                }
//        var file = File(path)
//
//                viewModel!!.uploadPicture(file).observe(this) { s: String ->
//                    LoadingDialog.getInstance(mContext).dismiss()
//                    if (!TextUtils.isEmpty(s)) {
//                        videoFileUri = it
//                        videoFileName = s
//                        bindingView.ivDelete.visibility = View.VISIBLE
//                        bindingView!!.title02.text = "Upload cover video（1/1）";
//
//                        checkSave()
//
//                        val mmr = MediaMetadataRetriever() //实例化MediaMetadataRetriever对象
//
//
//                        if (!file.exists()) {
////            Toast.makeText(SubmitVideoActivity.this, "文件不存在", Toast.LENGTH_SHORT).show();
//                        }
//                        mmr.setDataSource(path)
//                        val bitmap = mmr.getFrameAtTime(0) //0表示首帧图片
//                        bindingView.imgVideo.setImageBitmap(bitmap)
//                        mmr.release() //释放MediaMetadataRetriever对象
//                    } else {
//                        ToastUtil.show("Failed to upload profile video")
//
//                    }
//                }
            }

    }


    private fun textChanged(editTextName: EditText, s: Editable) {
        if (s.toString().contains(" ")) {
            val str: List<String> = s.toString().split(" ")
            var str1 = ""
            for (i in str.indices) {
                str1 += str[i]
            }
            editTextName.setText(str1)
            editTextName.setSelection(str1.length)

        } else {
//            editTextName.setSelection(s.toString().length)
        }
        checkSave()
    }

    var sexual = 1
    fun checkbox(checkbox: View?, type: Int) {
        if (type == 0) {
            bindingView!!.checkbox01.isSelected = true
            bindingView!!.checkbox02.isSelected = false
        } else {
            bindingView!!.checkbox01.isSelected = false
            bindingView!!.checkbox02.isSelected = true
        }
        sexual = type
        checkSave()
    }

    override fun loadData() {
        showContentView()
        //        viewModel.getUserInfo(this).observe(this, result -> {
//            if (result) {
//            }
//        });
    }

    /**
     * 展示图片选择方式弹窗
     */
    fun showSelectAlert() {
        if (mCameraUtil == null) {
            mCameraUtil = CameraUtil(this)
        }
        mCameraUtil!!.getPic(this.supportFragmentManager)
        isHead = true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == CameraUtil.RESULT_CODE_SELECT_ALBUM && data != null) {
                if (isHead) {
                    //相册获取返回 剪裁
                    mCameraUtil!!.getPicture(data.data, false);
                    setHeadPic()
                } else {
                    onResultDeal(mCameraUtil?.getPicture(data.data, false))

                }
            } else if (requestCode == CameraUtil.RESULT_CODE_TAKE_PHOTO) {
                if (isHead) {
                    //从相机返回 剪裁
                    mCameraUtil!!.getPicture(mCameraUtil!!.imgUri, true)
                    setHeadPic()
                } else {
                    onResultDeal(mCameraUtil?.getPicture(mCameraUtil?.imgUri, true))
                }
            } else if (requestCode == CameraUtil.RESULT_CODE_CROP) {
                //剪裁返回
                setHeadPic()
            }
        }
    }

    var headFileName = ""
    var videoFileName = ""
    var videoFileUri = ""
    private fun setHeadPic() {


        LoadingDialog.getInstance(mContext).show()
//        ToastUtil.show("正在加载头像图片资源...")
        ToastUtil.show(getResources().getString(R.string.b29))

        if (mCameraUtil!!.cutFile == null) {
            LoadingDialog.getInstance(mContext).dismiss()
            ToastUtil.show(getResources().getString(R.string.b30))
            return
        }

        //因为上传图片大小后端有限制 gif 大于3m 的时候就压缩成普通 普通图片大于100kb就压缩
        var mLeastCompressSize = 100
        if (mCameraUtil!!.cutFile?.getPath()?.lowercase(Locale.getDefault())!!.endsWith(".gif")) {
            mLeastCompressSize = 1024 * 3
        }
        Luban.with(this@ModifyInfoActivity).load(mCameraUtil!!.cutFile?.getPath()).ignoreBy(mLeastCompressSize)
            .setTargetDir(PathUtils.getPathIMg()).filter { path: String? -> !TextUtils.isEmpty(path) }
            .setCompressListener(object : OnCompressListener {
                override fun onStart() {}
                override fun onSuccess(index: Int, f: File?) {
                    f?.let {
                        var file =
                            Glide.with(mContext).asFile().load(f).apply(RequestOptions.bitmapTransform(CircleCrop()))
                                .into(object : CustomTarget<File?>() {
                                    override fun onResourceReady(resource: File, transition: Transition<in File?>?) {
                                        uploadPicture(resource)
                                    }

                                    override fun onLoadCleared(placeholder: Drawable?) {}
                                })
                    }
                }

                override fun onError(index: Int, e: Throwable?) {

                }

            }).launch()
    }

    private fun uploadPicture(file: File) {
        Glide.with(mContext).load(file).apply(RequestOptions.bitmapTransform(CircleCrop()))
            .into(bindingView!!.imageViewHead)
        viewModel!!.uploadPicture(file).observe(this) { s: String ->
            LoadingDialog.getInstance(mContext).dismiss()
            if (!TextUtils.isEmpty(s)) {
                headFileName = s
                checkSave()
                Glide.with(mContext).load(s).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .into(bindingView!!.imageViewHeadBG)
            } else {
                ToastUtil.show(getResources().getString(R.string.b30))

            }
        }
    }

    fun checkSave() {
//        if (bindingView.editTextName.text != null && bindingView.editTextName.text.toString().length() !=0 && sexual != 0 && headFileName.length() != 0) {
        bindingView!!.btnSave.isSelected =
            bindingView!!.editTextName.text != null && bindingView!!.editTextName.text.toString().trim()
                .isNotEmpty() && bindingView.editPhone.text != null && bindingView.editPhone.text.toString().trim()
                .isNotEmpty() && bindingView.editEmail.text != null && bindingView.editEmail.text.toString().trim()
                .isNotEmpty() && Patterns.EMAIL_ADDRESS.matcher(bindingView.editEmail.text.toString())
                .matches() && headFileName.isNotEmpty()
//                && videoFileName.isNotEmpty()
    }

    var operationTipDialog: OperationTipDialog? = null
    fun save() {


        if (headFileName.isEmpty()) {
            ToastUtil.show(getResources().getString(R.string.b33))
            return
        }

        if (bindingView!!.editTextName.text == null || bindingView!!.editTextName.text.toString().trim().isEmpty()) {
            ToastUtil.show(getResources().getString(R.string.b31))

            return
        }

        if (bindingView.editPhone.text == null || bindingView.editPhone.text.toString().trim()
                .isEmpty() || bindingView.editPhone.text.toString().trim().length <= 6
        ) {
            ToastUtil.show(getResources().getString(R.string.b34))

            return
        }
        if (bindingView.editEmail.text == null || bindingView.editEmail.text.toString().trim()
                .isEmpty() || !Patterns.EMAIL_ADDRESS.matcher(bindingView.editEmail.text.toString().trim()).matches()
        ) {
            ToastUtil.show(getResources().getString(R.string.b35))

            return
        }
//        if(selectList.size == 0){
//            ToastUtil.show("Please upload the background wall picture")
//            return
//        }
//        if(videoFileName.isEmpty()){
//            ToastUtil.show("Please upload the video")
//            return
//        }

        operationTipDialog = OperationTipDialog {
            save2()
            operationTipDialog?.dismiss()
        }
        operationTipDialog?.content = getString(R.string.b98);
        operationTipDialog?.show(getSupportFragmentManager(), "VideoChatActivity");
//
    }

    fun save2() {


        if (bindingView!!.btnSave.isSelected) {
//            ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
//            finish();
//            SharePreUtil.setUserInfo(GsonUtil.GsonString(result));


            var hashMap: HashMap<String, String> =
                GsonUtil.GsonToBean(hashMapStr, HashMap::class.java) as HashMap<String, String>

//            val loginBean = UserInfoManager.user()
            val loginBean = LoginBean()
            //            ModifyUserInfoBean modifyUserInfoBean = new ModifyUserInfoBean();
            loginBean.nick = bindingView!!.editTextName.text.toString().trim()
            loginBean.headFileName = headFileName
            loginBean.email = bindingView.editEmail.text.toString().trim()
            loginBean.phone = bindingView.editPhone.text.toString().trim()
            loginBean.gender = sexual.toString() + ""
//            loginBean.groundFileName =  selectList[0]
            loginBean.groundFileName = ""
            loginBean.auditVideo = videoFileName

            loginBean.password = hashMap["password"]
            loginBean.unionId = hashMap["unionId"]
            loginBean.country = hashMap["country"]
            loginBean.username = hashMap["username"]
            loginBean.deviceId = Utils.getAndroidId()

            viewModel!!.save(this, hashMap).observe(
                this
            ) { result: Any ->
//                ToastUtil.show("保存信息成功")
                loginBean.token = result.toString()
                SharePreUtil.setAccessToken(result.toString())
                SharePreUtil.setUserInfo(GsonUtil.GsonString(loginBean))

                //跳转首页
                ActivityUtil.getInstance().finishAllActivity()
//                ARouter.getInstance().build(ArouterPath.PATH_LOGIN_SELECT).navigation()
                jump()
            }

        } else {
            ToastUtil.show("Please complete the information")
        }
    }

    fun ivDelete() {
        bindingView!!.title02.text = "Upload cover video（0/1）"
        bindingView.ivDelete.visibility = View.GONE
        videoFileName = ""
        videoFileUri = ""
        bindingView.imgVideo.setImageDrawable(getDrawable(R.mipmap.base_publish_add))
    }


    fun uploadVideo() {
        if (videoFileName.isNotEmpty()) {
            jump(CameraXVideoActivity::class.java, Bundle().apply {
                putString("videoFileName", videoFileUri)
            })
        } else {
            jump(CameraXVideoActivity::class.java)
        }
    }

    override fun onBackPressed() {
        val operationTipDialog =
            OperationTipDialog { //                ActivityUtil.getInstance().finishAllActivity();
                finish()
            }
        operationTipDialog.content = getString(R.string.Do_you_want_to_give_up_registration)
        operationTipDialog.show(supportFragmentManager, "ModifyInfoActivity")
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "BabyInfoActivity"
    }


    private var mAdpter: MultiTypeAdapter? = null
    private val list: MutableList<Any?> = ArrayList()
    private val selectList = ArrayList<String?>()
    private val selectListLocal: List<LocalMedia> = ArrayList()
    private val selectMap: MutableMap<String, String> = HashMap()
    private var atWrapper: AtWrapper? = null
    var isHead = false
    private fun initUploadImageView() {
        val gridLayoutManager = GridLayoutManager(this, 3)
        gridLayoutManager.isSmoothScrollbarEnabled = true
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.setHasFixedSize(true)
        bindingView!!.recyclerView.isNestedScrollingEnabled = false
        val spanCount = 3
        val spacing = ScreenUtil.dip2px(mContext, 0f)
        bindingView!!.recyclerView.addItemDecoration(
            GridSpacingItemDecoration(
                spanCount, spacing, false
            )
        )
        mAdpter = MultiTypeAdapter()
        val publishAddBinder = PublishAddBinder()
        publishAddBinder.onItemChildClickListener = onAddItemChildClickListenerAdd
        val publishImageBinder = PublishImageBinder()
        publishImageBinder.onItemChildClickListener = onItemChildClickListener
        mAdpter!!.register(String::class.java, publishImageBinder)
        mAdpter!!.register<Integer>(Integer::class.java, publishAddBinder)
        bindingView!!.recyclerView.adapter = mAdpter
        mAdpter!!.items = list
        initImg()
        atWrapper = AtWrapper
    }


    var onItemChildClickListener = OnItemChildClickListener { view: View, position: Int ->
        val id = view.id
        if (id == R.id.iv_head_id) {
            BigImagePreviewActivity.router(mContext, view, selectList, position)
        } else if (id == R.id.ivDelete) {
            selectList.removeAt(position)
//            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//                selectListLocal.removeAt(position)
//            }
            updateimg()
        }
    }
    var onAddItemChildClickListenerAdd = OnItemChildClickListener { view: View?, position: Int ->

        if (mCameraUtil == null) {
            mCameraUtil = CameraUtil(this)
        }
        isHead = false
        mCameraUtil?.getPic(this.supportFragmentManager)
    }

    fun initImg() {
        updateimg()
    }

    fun updateimg() {
        list.clear()
        if (selectList.size < 1) {
            val add = 0
            list.add(add)
        }
        list.addAll(selectList)
        mAdpter!!.notifyDataSetChanged()
        bindingView!!.title01.text = getString(R.string.b97) + "（${selectList.size}/1）";
    }

    fun updateVideo() {

        bindingView!!.title01.text = getString(R.string.b97) + "（${selectList.size}/1）";
    }

    private fun onResultDeal(file: File?) {
        LoadingDialog.getInstance(this@ModifyInfoActivity).show()
        if (file != null) {
            //因为上传图片大小后端有限制 gif 大于3m 的时候就压缩成普通 普通图片大于100kb就压缩
            var mLeastCompressSize = 100
            if (file.path.lowercase(Locale.getDefault()).endsWith(".gif")) {
                mLeastCompressSize = 1024 * 3
            }
            Luban.with(this@ModifyInfoActivity).load(file.path).ignoreBy(mLeastCompressSize)
                .setTargetDir(PathUtils.getPathIMg()).filter { path: String? -> !TextUtils.isEmpty(path) }
                .setCompressListener(object : OnCompressListener {
                    override fun onStart() {}
                    override fun onSuccess(index: Int, f: File?) {
                        f?.let {
                            selectMap[file.path] = f.path
                            viewModel?.uploadPicture(f)?.observe(this@ModifyInfoActivity) {
                                if (it != "000") {
                                    selectList.add(0, it)
                                    updateimg()
                                }
                                LoadingDialog.getInstance(this@ModifyInfoActivity).dismiss()

                            }
                        }
                    }

                    override fun onError(index: Int, e: Throwable?) {
                    }

                }).launch()
        }
    }


    private fun getAbsolutePathFromUri(contentUri: Uri): String? {
        var cursor: Cursor? = null
        return try {
            cursor = <EMAIL>(
                contentUri, arrayOf(MediaStore.Images.Media.DATA), null, null, null
            )
            if (cursor == null) {
                return null
            }
            val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            cursor.moveToFirst()
            cursor.getString(columnIndex)
        } catch (e: RuntimeException) {
            Log.e(
                "VideoViewerFragment", String.format(
                    "Failed in getting absolute path for Uri %s with Exception %s",
                    contentUri.toString(),
                    e.toString()
                )
            )
            null
        } finally {
            cursor?.close()
        }
    }


}