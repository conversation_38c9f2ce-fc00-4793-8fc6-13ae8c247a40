package com.mobile.anchor.app.module.login.login

import anchor.app.base.ui.BaseActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityLoginBinding
import com.mobile.anchor.app.module.login.login.fragment.EmailLoginFragment

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/27 17:26
 * @description :
 */
class LoginActivity : BaseActivity<LoginViewModel, ActivityLoginBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_login

    override fun initView() {
        bindingView.activity = this
        showContentView()
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, EmailLoginFragment(), "EmailLoginFragment")
            .commit()
    }
}