package com.mobile.anchor.app.module.user.bean;

import java.util.List;

public class RankBean {

    private String beginDate;
    private String endDate;
    private AnchorRankDTO anchorRank;
    private List<RankListDTO> rankList;
    private List<RankConfigListDTO> rankConfigList;

    private String country;
    private String coverVideoUrl;
    private String followFlag;
    private String gender;
    private String groundFileName;
    private String headFileName;
    private Long id;
    private Integer incomeDiamond;
    private String language;
    private String nickName;
    private String normalGroundFileName;
    private String normalRoomTitle;
    private String onlineStatus;
    private String roomTitle;
    private String showVideoUrl;
    private Integer unionId;
    private String userCode;
    private Integer videoPrice;
    private String vipFlag;

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public AnchorRankDTO getAnchorRank() {
        return anchorRank;
    }

    public void setAnchorRank(AnchorRankDTO anchorRank) {
        this.anchorRank = anchorRank;
    }

    public List<RankListDTO> getRankList() {
        return rankList;
    }

    public void setRankList(List<RankListDTO> rankList) {
        this.rankList = rankList;
    }

    public List<RankConfigListDTO> getRankConfigList() {
        return rankConfigList;
    }

    public void setRankConfigList(List<RankConfigListDTO> rankConfigList) {
        this.rankConfigList = rankConfigList;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCoverVideoUrl() {
        return coverVideoUrl;
    }

    public void setCoverVideoUrl(String coverVideoUrl) {
        this.coverVideoUrl = coverVideoUrl;
    }

    public String getFollowFlag() {
        return followFlag;
    }

    public void setFollowFlag(String followFlag) {
        this.followFlag = followFlag;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGroundFileName() {
        return groundFileName;
    }

    public void setGroundFileName(String groundFileName) {
        this.groundFileName = groundFileName;
    }

    public String getHeadFileName() {
        return headFileName;
    }

    public void setHeadFileName(String headFileName) {
        this.headFileName = headFileName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIncomeDiamond() {
        return incomeDiamond;
    }

    public void setIncomeDiamond(Integer incomeDiamond) {
        this.incomeDiamond = incomeDiamond;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getNormalGroundFileName() {
        return normalGroundFileName;
    }

    public void setNormalGroundFileName(String normalGroundFileName) {
        this.normalGroundFileName = normalGroundFileName;
    }

    public String getNormalRoomTitle() {
        return normalRoomTitle;
    }

    public void setNormalRoomTitle(String normalRoomTitle) {
        this.normalRoomTitle = normalRoomTitle;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getRoomTitle() {
        return roomTitle;
    }

    public void setRoomTitle(String roomTitle) {
        this.roomTitle = roomTitle;
    }

    public String getShowVideoUrl() {
        return showVideoUrl;
    }

    public void setShowVideoUrl(String showVideoUrl) {
        this.showVideoUrl = showVideoUrl;
    }

    public Integer getUnionId() {
        return unionId;
    }

    public void setUnionId(Integer unionId) {
        this.unionId = unionId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Integer getVideoPrice() {
        return videoPrice;
    }

    public void setVideoPrice(Integer videoPrice) {
        this.videoPrice = videoPrice;
    }

    public String getVipFlag() {
        return vipFlag;
    }

    public void setVipFlag(String vipFlag) {
        this.vipFlag = vipFlag;
    }

    public static class AnchorRankDTO {
        private Integer id;
        private String createTime;
        private String updateTime;
        private String headFileName;
        private String nickName;
        private String userCode;
        private Integer ranks;
        private Integer giftId;
        private Integer giftNumber;
        private String anchorId;
        private String beginTime;
        private String endTime;
        private String remark;
        private String giftIcon;
        private Integer status;

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getHeadFileName() {
            return headFileName;
        }

        public void setHeadFileName(String headFileName) {
            this.headFileName = headFileName;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getUserCode() {
            return userCode;
        }

        public void setUserCode(String userCode) {
            this.userCode = userCode;
        }

        public Integer getRanks() {
            return ranks;
        }

        public void setRanks(Integer ranks) {
            this.ranks = ranks;
        }

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Integer getGiftNumber() {
            return giftNumber;
        }

        public void setGiftNumber(Integer giftNumber) {
            this.giftNumber = giftNumber;
        }

        public String getAnchorId() {
            return anchorId;
        }

        public void setAnchorId(String anchorId) {
            this.anchorId = anchorId;
        }

        public String getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(String beginTime) {
            this.beginTime = beginTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public static class RankListDTO {
        private String id;
        private String createTime;
        private String updateTime;
        private String headFileName;
        private String nickName;
        private String userCode;
        private Integer ranks;
        private Integer giftId;
        private Integer giftNumber;
        private String anchorId;
        private String beginTime;
        private String endTime;
        private String remark;
        private String giftIcon;
        private Integer status;

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getHeadFileName() {
            return headFileName;
        }

        public void setHeadFileName(String headFileName) {
            this.headFileName = headFileName;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getUserCode() {
            return userCode;
        }

        public void setUserCode(String userCode) {
            this.userCode = userCode;
        }

        public Integer getRanks() {
            return ranks;
        }

        public void setRanks(Integer ranks) {
            this.ranks = ranks;
        }

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Integer getGiftNumber() {
            return giftNumber;
        }

        public void setGiftNumber(Integer giftNumber) {
            this.giftNumber = giftNumber;
        }

        public String getAnchorId() {
            return anchorId;
        }

        public void setAnchorId(String anchorId) {
            this.anchorId = anchorId;
        }

        public String getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(String beginTime) {
            this.beginTime = beginTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public static class RankConfigListDTO {
        private String id;
        private String createTime;
        private String updateTime;
        private Integer diamond;
        private Integer diamondNumber;
        private Integer giftId;
        private Integer giftNumber;
        private Integer status;
        private String beginTime;
        private String endTime;
        private String giftIcon;
        private String diamondIcon;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public Integer getDiamond() {
            return diamond;
        }

        public void setDiamond(Integer diamond) {
            this.diamond = diamond;
        }

        public Integer getDiamondNumber() {
            return diamondNumber;
        }

        public void setDiamondNumber(Integer diamondNumber) {
            this.diamondNumber = diamondNumber;
        }

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Integer getGiftNumber() {
            return giftNumber;
        }

        public void setGiftNumber(Integer giftNumber) {
            this.giftNumber = giftNumber;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(String beginTime) {
            this.beginTime = beginTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public String getDiamondIcon() {
            return diamondIcon;
        }

        public void setDiamondIcon(String diamondIcon) {
            this.diamondIcon = diamondIcon;
        }
    }
}
