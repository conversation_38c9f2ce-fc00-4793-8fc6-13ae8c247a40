package com.mobile.anchor.app.module.user.dialog

import android.content.Context
import anchor.app.base.ext.click
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupRewardDescriptionBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/11/23 14:37
 * @description :
 */
class RewardDescriptionPopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_reward_description

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupRewardDescriptionBinding.bind(popupImplView)
        binding.ivClose.click {
            dismiss()
        }
    }
}

fun showRewardDescriptionPopup(context: Context) {
    XPopup.Builder(context).asCustom(RewardDescriptionPopup(context)).show()
}