package com.mobile.anchor.app.module.user.feedback;


import java.io.File;

import io.reactivex.Flowable;
import anchor.app.base.api.BaseApi;
import anchor.app.base.repository.IRepository;
import anchor.app.base.retrofit.BaseResult;
import com.mobile.anchor.app.module.user.api.UserApi;
import com.mobile.anchor.app.module.user.bean.ReportBean;

public class FeedbackRepository implements IRepository {


    public void aa() {

    }



    public Flowable<BaseResult<Object>> publicRepoet(ReportBean bean) {
        return UserApi.publicRepoet(bean);
    }

    public Flowable<BaseResult> uploadPicture(File file) {
        return BaseApi.uploadPicture(file);
    }

    public FeedbackRepository() {
    }

}
