package com.mobile.anchor.app.module.discover.main.homelist.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;


import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.discover.adapter.DiscoverBindingViewAdapter;

public class HomeListItemAdapter extends DiscoverBindingViewAdapter {

    public static final int ITEM_DESCRIPTION = 0;
    public static final int ITEM_MATCH = 1;
    public static final int ITEM_CHAT = 2;

    public HomeListItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
//        addViewTypeToLayoutMap(ITEM_DESCRIPTION, R.layout.discover_item_home);
//        addViewTypeToLayoutMap(ITEM_MATCH, R.layout.discover_item_home);
//        addViewTypeToLayoutMap(ITEM_CHAT, R.layout.discover_item_home);

        addViewTypeToLayoutMap(ITEM_MATCH, R.layout.discover_item_home_new);
    }

    @Override
    public int getViewType(@NotNull Object item) {
//        if (item instanceof DescriptionItemBean) {
//            return ITEM_DESCRIPTION;
//        } else if (item instanceof ChatItemBean) {
//            return ITEM_CHAT;
//        }
        return ITEM_MATCH;
    }
}
