package com.mobile.anchor.app.module.login.login

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import com.mobile.anchor.app.R
import anchor.app.base.core.Constants
import anchor.app.base.core.Constants.HTTP_URL
import anchor.app.base.ext.jump
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.databinding.ActivityLoginBinding
import com.mobile.anchor.app.databinding.ActivityLoginWithPasswordBinding
import com.mobile.anchor.app.module.user.agreement.AgreementActivity

/**
 * 登录页面
 */
@Deprecated("This class is deprecated, please use LoginActivity2 instead.")
class LoginWithPasswordActivity : BaseActivity<LoginViewModel?, ActivityLoginWithPasswordBinding?>() {

    val loginType get() = intent.getIntExtra("loginType", 0) //0代表绑定，1代表登录
    val from get() = intent.getStringExtra("loginType") //retry代表是token失效需要登录重新获取token

    private val mAreaCode = "86"
    override fun getLayoutId(): Int {
        return R.layout.activity_login_with_password
    }

    override fun isDarkMode(): Boolean {
        return false
    }

    override fun initView() {
        viewModel!!.loginType = loginType
        viewModel!!.from = from
        showContentView()
        bindingView!!.viewModel = viewModel
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this

        bindingView!!.editAcount.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
//                    bindingView!!.close01.visibility = if (s.toString().isEmpty()) View.GONE else View.VISIBLE
            }
        })
        bindingView!!.editPassword.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
//                bindingView!!.close02.visibility = if (s.toString().isEmpty()) View.GONE else View.VISIBLE
                bindingView!!.show1.visibility = if (s.toString().isEmpty()) View.GONE else View.VISIBLE
            }
        })
    }

    var isShow1 = false

    fun show(type: Int) {
        when (type) {
            1 -> {
                if (isShow1.not()) {                    //显示密码
                    isShow1 = true
                    bindingView!!.editPassword.setTransformationMethod(
                        HideReturnsTransformationMethod.getInstance()
                    );
                    bindingView!!.show1.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_03))
                } else { //隐藏密码
                    isShow1 = false
                    bindingView!!.editPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                    bindingView!!.show1.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_04))
                }
                if (bindingView!!.editPassword != null && bindingView!!.editPassword.text != null && bindingView!!.editPassword.text.length > 0) {

                }
                bindingView!!.editPassword.setSelection(bindingView!!.editPassword.length())
            }
        }

    }

    fun treaty(type: Int) {
        if (type == 1) {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", HTTP_URL.Useragreement)
                putString("title", resources.getString(R.string.User_agreement_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        } else {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", HTTP_URL.PrivatePolicy)
                putString("title", resources.getString(R.string.Privacy_policy_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        }
    }

    fun checkbox(checkbox: View) {
        checkbox.isSelected = !checkbox.isSelected
    }

    fun login() {
        val acount = bindingView!!.editAcount.text.toString().trim { it <= ' ' }
        val password = bindingView!!.editPassword.text.toString().trim { it <= ' ' }
//        if (!RegexUtils.isEmail(acount)) {
//            ToastUtil.show(StringUtils.getString(R.string.register_email_err))
//            return
//        }

        if (bindingView!!.editPassword.text == null || bindingView!!.editPassword.text.toString().length == 0) {
            ToastUtil.show("Password is empty")
            return
        }

//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();
        viewModel?.login(this, password, "1", acount)
    }
}