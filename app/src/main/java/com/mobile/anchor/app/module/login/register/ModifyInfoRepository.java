package com.mobile.anchor.app.module.login.register;


import java.io.File;
import java.util.Map;

import anchor.app.base.api.BaseApi;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.repository.IRepository;
import anchor.app.base.retrofit.BaseResult;
import com.mobile.anchor.app.module.login.api.LoginApi;
import io.reactivex.Flowable;

public class ModifyInfoRepository implements IRepository {

    public ModifyInfoRepository() {
    }

    /**
     * 查询孩子
     *
     * @return
     */

    public Flowable<BaseResult<LoginBean>> modifyChildInfo(Map<String, String> bean) {
        return LoginApi.modifyChildInfo(bean);
    }
    public Flowable<BaseResult<Object>> auditUpdate(Map<String, Object> bean) {
        return LoginApi.auditUpdate(bean);
    }

    public Flowable<BaseResult> uploadPicture(File file) {
        return BaseApi.uploadPicture(file);
    }
}
