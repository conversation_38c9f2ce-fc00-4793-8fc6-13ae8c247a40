package com.mobile.anchor.app.module.discover.bean;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.bean
 * @class describe
 * @time 2019/8/9 12:23
 * @class describe
 */
public class CourseInfo {

    /**
     * courseType : 体验课
     * title : SoulPair爱英语体验课
     * description : 8月19号开课
     * originalPrice : 999
     * sellingPrice : 0.01
     * salesVolume : null
     * stage : 1
     * label : null
     * h5Url : https://h5.duyaya.com/dev-h5/app-h5/index.html#/xpcourseDetail
     * sellOut : false
     */

    private String courseType;
    private String title;
    private String description;
    private int originalPrice;
    private String sellingPrice;
    private String salesVolume;
    private int stage;
    private String label;
    private String h5Url;
    private boolean sellOut;

    public String getCourseType() {
        return courseType;
    }

    public void setCourseType(String courseType) {
        this.courseType = courseType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(int originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(String sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(String salesVolume) {
        this.salesVolume = salesVolume;
    }

    public int getStage() {
        return stage;
    }

    public void setStage(int stage) {
        this.stage = stage;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getH5Url() {
        return h5Url;
    }

    public void setH5Url(String h5Url) {
        this.h5Url = h5Url;
    }

    public boolean isSellOut() {
        return sellOut;
    }

    public void setSellOut(boolean sellOut) {
        this.sellOut = sellOut;
    }
}
