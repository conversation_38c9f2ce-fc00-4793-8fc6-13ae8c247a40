package com.mobile.anchor.app.module.discover.main.center

import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.uber.autodispose.AutoDispose
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean
import java.io.File

class PersonalCenterModel(application: Application) :
    BaseViewModel<PersonalCenterRepository>(application) {


    fun getPersonalCenter(
        activity: FragmentActivity?,
        id: Long,
        userRole: String
    ): MutableLiveData<PersonalCenterBean> {
        val data = MutableLiveData<PersonalCenterBean>()
        repository!!.getPersonalCenter(id, userRole)
            .map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(this))
            .subscribe({ result: PersonalCenterBean ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
//                error.value = throwable
            }
        return data
    }

    fun removeAndFollowing(UserId: Long, type: Int, userRole: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.removeAndFollowing(UserId, type, userRole), { bean ->
            info.value = ""

        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                    info.value = ""
                }
            }
        }
        return info;
    }

    fun setBlack(UserId: Long, userRole: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.setBlack(UserId, userRole), { bean ->
            info.value = ""

        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                    info.value = ""
                }
            }
        }
        return info;
    }

    fun userBuyPhoto(id: Long): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.userBuyPhoto(id), { bean ->
            info.value = ""
        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                    info.setValue("");
                } else ToastUtil.show(throwable.message)
            }
        }
        return info;
    }


    fun uploadPicture(file: File?): MutableLiveData<String> {
        val data = MutableLiveData<String>()
        repository.uploadPicture(file)!!.`as`(AutoDispose.autoDisposable(this))
            .subscribe({ baseResult: BaseResult<Any> ->
                data.setValue(baseResult.data.toString())
            }) { throwable: Throwable? ->
                ToastUtil.show(throwable?.message)
                data.setValue("000")
            }
        return data
    }

    fun uploadPictureAnchorFile(hashMap: Map<String, Any>): MutableLiveData<String> {
        val data = MutableLiveData<String>()
        repository.anchorFileSave(hashMap)!!.`as`(AutoDispose.autoDisposable(this))
            .subscribe({ baseResult: BaseResult<Any> ->
                data.setValue(baseResult.data.toString())
            }) { throwable: Throwable? ->
                ToastUtil.show(throwable?.message)
                data.setValue("000")
            }
        return data
    }

    public override fun getRepository(): PersonalCenterRepository {
        return PersonalCenterRepository()
    }
}