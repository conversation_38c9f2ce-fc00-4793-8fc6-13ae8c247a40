package com.mobile.anchor.app.module.user.wallet.history

import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean
import android.app.Application
import androidx.lifecycle.MutableLiveData
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants

class HistoryListViewmodel (application: Application) : BaseViewModel<HistoryListRepository>(application) {

    override fun getRepository(): HistoryListRepository {
        return HistoryListRepository()
    }


    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(current: Int,size: Int): MutableLiveData<List<HistoryListBean.RecordsDTO>>{
        val info: MutableLiveData<List<HistoryListBean.RecordsDTO>> = MutableLiveData()
        dataStreamFliter.fliter(repository.getList(current,size), { bean ->

                info.value = bean.records

//            val resultJson: Any = hashMap.get(goodsType.toString())!!
//            if (resultJson != null) {
//                val json = GsonUtil.GsonString(resultJson)
//
//            }
//            if(info.value == null) info.value = ArrayList()

        }) { throwable ->
            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }

    fun anchorExtract(extractNum: Int): MutableLiveData<Any>{
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.anchorExtract(extractNum), { bean ->
            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)
                info.value = ""
        }) { throwable ->
            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE, 0)
            info.value = "000"
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }


}