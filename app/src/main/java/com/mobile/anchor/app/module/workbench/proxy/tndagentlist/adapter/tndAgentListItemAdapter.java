package com.mobile.anchor.app.module.workbench.proxy.tndagentlist.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.workbench.adapter.WorkbenchBindingViewAdapter;


public class tndAgentListItemAdapter extends WorkbenchBindingViewAdapter {

    public static final int ITEM_CONTENT = 0;

    public tndAgentListItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_CONTENT, R.layout.item_tndagent_list);
    }

    @Override
    public int getViewType(@NotNull Object item) {
        return ITEM_CONTENT;
    }
}
