package com.mobile.anchor.app.module.user.main;

import static com.uber.autodispose.AutoDispose.autoDisposable;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.Map;

import anchor.app.base.bean.AlbumBean;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.ext.rxweaver.RxErrorUtil;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.retrofit.ResultMap;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.viewmodel.BaseViewModel;
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean;
import com.mobile.anchor.app.module.user.bean.RewardTaskBean;

public class UserViewModel extends BaseViewModel<UserRepository> {

    public UserViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 请求用户信息
     *
     * @return
     */
    public MutableLiveData<LoginBean> queryUserInfo(FragmentActivity activity) {
        MutableLiveData<LoginBean> loginBean = new MutableLiveData<>();
        repository.queryUserInfo().map(new ResultMap<>()).compose(RxErrorUtil.handleGlobalError(activity)).as(autoDisposable(this)).subscribe(result -> {
            loginBean.setValue(result);
        }, throwable -> {
            error.setValue(throwable);
            if (throwable instanceof ResultException) {
                ToastUtil.show("User : " + throwable.getMessage());
                if (((ResultException) throwable).getCode() == BaseResult.SUCCESS) loginBean.setValue(new LoginBean());
            }
        });
        return loginBean;
    }


    @Override
    protected UserRepository getRepository() {
        return new UserRepository();
    }

    public MutableLiveData<Boolean> updateOnlineStatus(String status) {
        MutableLiveData<Boolean> liveData = new MutableLiveData<>();
        repository.updateOnlineStats(status).as(autoDisposable(this)).subscribe(objectBaseResult -> liveData.setValue(true), throwable -> liveData.setValue(false));
        return liveData;
    }

    public MutableLiveData<List<AlbumBean>> getAlbumList(Map<String, String> params) {
        MutableLiveData<List<AlbumBean>> liveData = new MutableLiveData<>();
        repository.getAlbumList(params).as(autoDisposable(this)).subscribe(it -> liveData.setValue(it.getData()), throwable -> liveData.setValue(null));
        return liveData;
    }

    public MutableLiveData<Boolean> deleteAlbum(List<String> ids) {
        MutableLiveData<Boolean> liveData = new MutableLiveData<>();
        JsonObject params = new JsonObject();
        params.add("ids", new Gson().toJsonTree(ids));
        repository.albumDelete(ids).as(autoDisposable(this)).subscribe(it -> liveData.setValue(true), throwable -> liveData.setValue(false));
        return liveData;
    }

    public MutableLiveData<Boolean> updateAnchorAlbum(JsonObject params) {
        MutableLiveData<Boolean> liveData = new MutableLiveData<>();
        repository.updateAnchorAlbum(params).as(autoDisposable(this)).subscribe(it -> {
            liveData.setValue(it.getCode() == 200);
            ToastUtil.show(it.getMessage());
        }, throwable -> liveData.setValue(false));

        return liveData;
    }

    public MutableLiveData<IncomeStatementBean> getIncomeStatistics(Map<String, String> params) {
        MutableLiveData<IncomeStatementBean> liveData = new MutableLiveData<IncomeStatementBean>();
        repository.getIncomeStatistics(params).as(autoDisposable(this)).subscribe(it -> {
            liveData.setValue(it.getData());
        }, throwable -> ToastUtil.show(throwable.getMessage()));
        return liveData;
    }

    public MutableLiveData<List<IncomeStatementBean>> getIncomeStatementList(Map<String, String> params) {
        MutableLiveData<List<IncomeStatementBean>> liveData = new MutableLiveData<List<IncomeStatementBean>>();
        repository.getIncomeStatementList(params).as(autoDisposable(this)).subscribe(it -> {
            liveData.setValue(it.getData().getRecords());
        }, throwable -> ToastUtil.show(throwable.getMessage()));
        return liveData;
    }

    public MutableLiveData<RewardTaskBean> getRewardList() {
        MutableLiveData<RewardTaskBean> liveData = new MutableLiveData<RewardTaskBean>();
        repository.getRewardList().as(autoDisposable(this)).subscribe(it -> {
            liveData.setValue(it.getData());
        }, throwable -> {
            error.setValue(throwable);
            ToastUtil.show(throwable.getMessage());
        }); return liveData;
    }

    public MutableLiveData<Boolean> claimTask(String id) {
        MutableLiveData<Boolean> liveData = new MutableLiveData<Boolean>();
        repository.claimTask(id).as(autoDisposable(this)).subscribe(it -> {
            liveData.setValue(it.getData());
        }, throwable -> ToastUtil.show(throwable.getMessage()));
        return liveData;
    }
}
