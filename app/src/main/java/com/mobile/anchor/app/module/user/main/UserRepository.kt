package com.mobile.anchor.app.module.user.main

import anchor.app.base.bean.AlbumBean
import anchor.app.base.bean.LoginBean
import com.google.gson.JsonObject
import io.reactivex.Flowable
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.user.api.UserApi
import com.mobile.anchor.app.module.user.bean.GiftBean
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean
import com.mobile.anchor.app.module.user.bean.PageBean
import com.mobile.anchor.app.module.user.bean.RewardTaskBean

/**
 * 用户信息页面
 */
class UserRepository : IRepository {
    /**
     * 查询关注和粉丝
     *
     * @return
     */
    fun queryUserInfo(): Flowable<BaseResult<LoginBean>> = UserApi.queryUserInfo()


    /**
     * 查询孩子
     *
     * @return
     */
    //    public Flowable<BaseResult<QueryKidInfo>> queryChildInfo() {
    //        return BaseApi.queryChildInfo();
    //    }
    fun updateOnlineStats(status: String): Flowable<BaseResult<Any>> =
        UserApi.updateOnlineStatus(status)


    fun getAlbumList(params: Map<String, String>): Flowable<BaseResult<List<AlbumBean>>> =
        UserApi.getAlbumList(params)


    fun albumDelete(params: List<String>): Flowable<BaseResult<PageBean<AlbumBean>>> =
        UserApi.albumDelete(params)


    fun updateAnchorAlbum(params: JsonObject): Flowable<BaseResult<Any>> =
        UserApi.updateAnchorAlbum(params)


    fun getGiftList(params: Map<String, String>): Flowable<BaseResult<PageBean<GiftBean>>> =
        UserApi.getGiftList(params)


    val giftTotalValue: Flowable<BaseResult<Int>> = UserApi.giftTotalValue()

    fun modifyPassword(params: Map<String, String>): Flowable<BaseResult<Any>> =
        UserApi.modifyPassword(params)

    fun getEmailCode(email: String): Flowable<BaseResult<Any>> = UserApi.getEmailCode(email)

    fun getIncomeStatistics(params: Map<String, String>): Flowable<BaseResult<IncomeStatementBean>> = UserApi.getIncomeStatistics(params)

    fun getIncomeStatementList(params: Map<String, String>): Flowable<BaseResult<PageBean<IncomeStatementBean>>> = UserApi.getIncomeStatementList(params)

    fun getRewardList(): Flowable<BaseResult<RewardTaskBean>> = UserApi.getRewardList()

    fun claimTask(id: String): Flowable<BaseResult<Boolean>> = UserApi.claimTask(id)

    fun setupPrice(params:Map<String,String>): Flowable<BaseResult<Any>> = UserApi.setupPrice(params)
}
