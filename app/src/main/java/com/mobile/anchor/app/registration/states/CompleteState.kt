package com.mobile.anchor.app.registration.states

import anchor.app.base.utils.Logger
import com.mobile.anchor.app.registration.RegistrationContext
import com.mobile.anchor.app.registration.RegistrationState

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/26 17:21
 * @description : 完成状态
 * 处理注册流程完成后的逻辑
 */
class CompleteState : RegistrationState {
    override fun handle(context: RegistrationContext) {
        Logger.i("RegistrationState 注册流程完成")
    }

    override fun isMissing(): Boolean = false

    override fun isReviewing(): Boolean = false

    override fun isCompleted(): <PERSON><PERSON>an = true
}