/*
 * Copyright 2016 czy1121
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.mobile.anchor.app.module.update;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Notification;
import android.app.NotificationManager;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.core.app.NotificationCompat;
import androidx.core.content.FileProvider;

import com.mobile.anchor.app.R;

import java.io.File;

import anchor.app.base.dialog.LoadingDialog;
import anchor.app.base.rx.RxBus;
import anchor.app.base.rx.RxCodeConstants;
import anchor.app.base.utils.PathUtils;
import anchor.app.base.utils.ToastUtil;

import com.mobile.anchor.app.module.update.util.NotificationUtil;
import com.mobile.anchor.app.module.update.util.UpdateDialogUtils;
import com.mobile.anchor.app.module.update.util.UpdateUtil;

class UpdateAgent implements ICheckAgent, IUpdateAgent, IDownloadAgent {

    private static final String TAG = UpdateAgent.class.getSimpleName();

    private Context mContext;
    private String mUrl;
    private File mTmpFile;
    private File mApkFile;
    private boolean mIsManual = false;
    private boolean mIsWifiOnly = false;
    private boolean mIsUseInCourse = false;

    private UpdateInfo mInfo;
    private UpdateError mError = null;

    private IUpdateParser mParser = new DefaultUpdateParser();
    private IUpdateChecker mChecker;
    private IUpdateDownloader mDownloader;
    private IUpdatePrompter mPrompter;

    private OnFailureListener mOnFailureListener;

    private OnDownloadListener mOnDownloadListener;
    private OnDownloadListener mOnNotificationDownloadListener;

    public UpdateAgent(Context context, String url, boolean isManual, boolean isWifiOnly, int notifyId, boolean isUseInCourse) {
//        mContext = context.getApplicationContext();
        mContext = context;
        mUrl = url;
        mIsManual = isManual;
        mIsWifiOnly = isWifiOnly;
        mIsUseInCourse = isUseInCourse;
        mDownloader = new DefaultUpdateDownloader(mContext);
        mPrompter = new DefaultUpdatePrompter(context);
        mOnFailureListener = new DefaultFailureListener(context);
        mOnDownloadListener = new DefaultDialogDownloadListener(context);
        if (notifyId > 0) {
            mOnNotificationDownloadListener = new DefaultNotificationDownloadListener(mContext, notifyId, this);
        } else {
            mOnNotificationDownloadListener = new DefaultDownloadListener();
        }
    }

    public void setParser(IUpdateParser parser) {
        mParser = parser;
    }

    public void setChecker(IUpdateChecker checker) {
        mChecker = checker;
    }

    public void setDownloader(IUpdateDownloader downloader) {
        mDownloader = downloader;
    }

    public void setPrompter(IUpdatePrompter prompter) {
        mPrompter = prompter;
    }

    public void setOnNotificationDownloadListener(OnDownloadListener listener) {
        mOnNotificationDownloadListener = listener;
    }

    public void setOnDownloadListener(OnDownloadListener listener) {
        mOnDownloadListener = listener;
    }

    public void setOnFailureListener(OnFailureListener listener) {
        mOnFailureListener = listener;
    }

    public void setInfo(UpdateInfo info) {
        mInfo = info;
        if ((null != mInfo) && (null != mOnDownloadListener) && (mOnDownloadListener instanceof DefaultDialogDownloadListener)) {
            ((DefaultDialogDownloadListener) mOnDownloadListener).setForce(info.forceFlag.equals("1"));
        }
    }

    @Override
    public UpdateInfo getInfo() {
        return mInfo;
    }

    @Override
    public void setInfo(String source) {
        try {
            mInfo = mParser.parse(source);
            if ((null != mInfo) && (null != mOnDownloadListener) && (mOnDownloadListener instanceof DefaultDialogDownloadListener)) {
                ((DefaultDialogDownloadListener) mOnDownloadListener).setForce(mInfo.forceFlag.equals("1"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            setError(new UpdateError(UpdateError.CHECK_PARSE));
        }
        ((Activity) mContext).runOnUiThread(() -> {
            doCheckFinish(mInfo);
        });
    }

    @Override
    public void setError(UpdateError error) {
        mError = error;
    }

    @Override
    public void update() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> ToastUtil.show("Checking download, please wait..."), 500);

        new Handler(Looper.getMainLooper()).post(() -> {
            LoadingDialog.getInstance(mContext).show();
            mApkFile = new File(PathUtils.getPathApk(), mInfo.packageEncryption + ".apk");
            if (UpdateUtil.verify(mApkFile, mInfo.packageEncryption)) {
                // dialog 不消失
                LoadingDialog.getInstance(mContext).dismiss();
                doInstall();
            } else {
                doDownload();
            }
        });
    }

    @Override
    public void ignore() {
        UpdateUtil.setIgnore(mContext, getInfo().packageEncryption);
    }

    @Override
    public void onStart() {
        LoadingDialog.getInstance(mContext).dismiss();
        if (mInfo.showNotification) mOnNotificationDownloadListener.onStart();
        if (mInfo.silentFlag.equals("1")) {
            mOnNotificationDownloadListener.onStart();
        } else {
            mOnDownloadListener.onStart();
        }
    }

    @Override
    public void onProgress(int progress) {
        if (mInfo.showNotification) mOnNotificationDownloadListener.onProgress(progress);
        if (mInfo.silentFlag.equals("1")) {
            mOnNotificationDownloadListener.onProgress(progress);
        } else {
            mOnDownloadListener.onProgress(progress);
        }
    }

    @Override
    public void onFinish() {
        if (mInfo.showNotification) mOnNotificationDownloadListener.onFinish();
        if (mInfo.silentFlag.equals("1")) {
            mOnNotificationDownloadListener.onFinish();
        } else {
            mOnDownloadListener.onFinish();
        }
        if (mError != null) {
            mOnFailureListener.onFailure(mError);
        }
        if (UpdateUtil.verify(mTmpFile, mInfo.packageEncryption)) {
            mTmpFile.renameTo(mApkFile);
            if (mInfo.isAutoInstall) {
                doInstall();
            }
        }

        // 强制升级下载结束后，要继续显示升级dialog
//        if (mInfo.forceFlag.equals("1")) {
//            if ((mPrompter != null) && (mPrompter instanceof DefaultUpdatePrompter)) {
//                AlertDialog dialog = ((DefaultUpdatePrompter) mPrompter).getUpdateDialog();
//                if (dialog != null && (!dialog.isShowing()))
//                    DialogsManager.getInstance().requestShow(dialog);
//            }
//        }

    }


    public void check() {
        if (mIsWifiOnly) {
            if (UpdateUtil.checkWifi(mContext)) {
                doCheck();
            } else {
                doFailure(new UpdateError(UpdateError.CHECK_NO_WIFI));
            }
        } else {
            if (UpdateUtil.checkNetwork(mContext)) {
                doCheck();
            } else {
                doFailure(new UpdateError(UpdateError.CHECK_NO_NETWORK));
            }
        }
    }


    void doCheck() {

        if (mChecker == null) {
            mChecker = new UpdateChecker();
        }
        mChecker.check(UpdateAgent.this, mUrl);
//        new AsyncTask<String, Void, Void>() {
//            @Override
//            protected Void doInBackground(String... params) {
//                if (mChecker == null) {
//                    mChecker = new UpdateChecker();
//                }
//                mChecker.check(UpdateAgent.this, mUrl);
//                return null;
//            }
//
//            @Override
//            protected void onPostExecute(Void aVoid) {
//                doCheckFinish();
//            }
//        }.execute();
    }

    void doCheckFinish(UpdateInfo info) {
        UpdateError error = mError;
        if (error != null) {
            doFailure(error);
        } else {
//            info.setVersionCode(3);
            if (info == null) {
                // 空数据表示无更新
//                doFailure(new UpdateError(UpdateError.CHECK_UNKNOWN));
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_GET_COUPON, true);
            } else if (UpdateUtil.isIgnore(mContext, info.packageEncryption)) {
                doFailure(new UpdateError(UpdateError.UPDATE_IGNORED));
                RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_GET_COUPON, true);
            } else if (UpdateUtil.getVersionCode(mContext) >= info.versionCode) {
                System.out.println("doCheckFinish : 当前已经是最新版本 " + info.versionCode);
            } else {
                UpdateUtil.log("update md5 " + mInfo.packageEncryption);
                UpdateUtil.ensureExternalCacheDir(mContext);
                UpdateUtil.setUpdate(mContext, mInfo.packageEncryption);
                mTmpFile = new File(PathUtils.getPathApk(), info.packageEncryption);
                mApkFile = new File(PathUtils.getPathApk(), info.packageEncryption + ".apk");

                if (UpdateUtil.verify(mApkFile, mInfo.packageEncryption)) {
                    // 进行提示
//                    doInstall();
                    doPrompt();
                } else if (info.silentFlag.equals("1")) {
                    doDownload();
                } else {
                    doPrompt();
                }
            }
        }
    }

    private void doPrompt() {
        //在这儿进行对返回数据进行干预
        UpdateInfo info = getInfo();
        mPrompter.prompt(this);
    }

    private void doDownload() {
        mDownloader.download(this, mInfo.downloadAddress, mTmpFile);
    }

    private void doInstall() {
        UpdateUtil.install(mContext, mApkFile, mInfo.forceFlag.equals("1"));
    }

    private void doFailure(UpdateError error) {
        if (mIsManual || error.isError()) {
            mOnFailureListener.onFailure(error);
        }
    }

    private static class DefaultUpdateDownloader implements IUpdateDownloader {
        final Context mContext;

        public DefaultUpdateDownloader(Context context) {
            mContext = context;
        }

        @Override
        public void download(IDownloadAgent agent, String url, File temp) {
            new UpdateDownloader(agent, mContext, url, temp).execute();
        }
    }

    private static class DefaultUpdateParser implements IUpdateParser {
        @Override
        public UpdateInfo parse(String source) throws Exception {
            return UpdateInfo.parse(source);
        }
    }

    private static class DefaultUpdatePrompter implements IUpdatePrompter {

        private Context mContext;
        private AlertDialog updateDialog = null;

        public DefaultUpdatePrompter(Context context) {
            mContext = context;
        }

        public AlertDialog getUpdateDialog() {
            return updateDialog;
        }

        @Override
        public void prompt(IUpdateAgent agent) {
            if (mContext instanceof Activity && ((Activity) mContext).isFinishing()) {
                return;
            }

            updateDialog = UpdateDialogUtils.showDialog(mContext, agent.getInfo(), agent);

//            final UpdateInfo info = agent.getInfo();
//            String size = Formatter.formatShortFileSize(mContext, info.size);
//            String content = "最新版本：" + info.versionName + "\n\n更新内容\n" + info.updateContent;
//            String content = String.format("最新版本：%1$s\n新版本大小：%2$s\n\n更新内容\n%3$s", info.versionName, size, info.updateContent);

//            final AlertDialog dialog = new AlertDialog.Builder(mContext).create();
//
//            dialog.setCancelable(false);
//            dialog.setCanceledOnTouchOutside(false);
//
//            float density = mContext.getResources().getDisplayMetrics().density;
//            TextView tv = new TextView(mContext);
//            tv.setMovementMethod(new ScrollingMovementMethod());
//            tv.setVerticalScrollBarEnabled(true);
//            tv.setTextSize(14);
//            tv.setMaxHeight((int) (250 * density));
//
//            dialog.setView(tv, (int) (25 * density), (int) (15 * density), (int) (25 * density), 0);
//
//
//            DialogInterface.OnClickListener listener = new DefaultPromptClickListener(agent, true);
//
//            if (info.forceFlag.equals("1")) {
//                tv.setText("您需要更新应用才能继续使用\n\n" + content);
//                dialog.setButton(DialogInterface.BUTTON_POSITIVE, "确定", listener);
//            } else {
//                tv.setText(content);
//                dialog.setButton(DialogInterface.BUTTON_POSITIVE, "立即更新", listener);
//                dialog.setButton(DialogInterface.BUTTON_NEGATIVE, "以后再说", listener);
//                if (info.isIgnorable) {
//                    dialog.setButton(DialogInterface.BUTTON_NEUTRAL, "忽略该版", listener);
//                }
//            }
//            dialog.show();
        }
    }

    private static class DefaultFailureListener implements OnFailureListener {

        private Context mContext;

        public DefaultFailureListener(Context context) {
            mContext = context;
        }

        @Override
        public void onFailure(UpdateError error) {
            UpdateUtil.log(error.toString());
        }
    }

    private static class DefaultDialogDownloadListener implements OnDownloadListener {
        private Context mContext;
        //        private ProgressDialog mDialog;
        private AlertDialog progressDialog = null;
        private boolean isForce = false;

        public DefaultDialogDownloadListener(Context context) {
            mContext = context;
        }

        public void setForce(boolean force) {
            isForce = force;
        }

        @Override
        public void onStart() {
            if (mContext instanceof Activity && !((Activity) mContext).isFinishing()) {
//                ProgressDialog dialog = new ProgressDialog(mContext);
//                dialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
//                dialog.setMessage("下载中...");
//                dialog.setIndeterminate(false);
//                dialog.setCancelable(false);
//                dialog.show();
//                mDialog = dialog;

                progressDialog = UpdateDialogUtils.showProcessDialog(mContext, isForce);

            }
        }

        @Override
        public void onProgress(int i) {
//            if (mDialog != null) {
//                mDialog.setProgress(i);
//            }
            if (null == progressDialog) return;
            if (!progressDialog.isShowing()) return;
            if (null == progressDialog.getWindow()) return;
            View dialogRootView = progressDialog.getWindow().getDecorView();
            ProgressBar progressBar = dialogRootView.findViewById(R.id.progress_download);
            TextView tvPrgress = dialogRootView.findViewById(R.id.tv_process_download);
            progressBar.setProgress(i);
            tvPrgress.setText(i + "%");
        }

        @Override
        public void onFinish() {
//            if (mDialog != null) {
//                mDialog.dismiss();
//                mDialog = null;
//            }
            if (null == progressDialog) return;
            if (!progressDialog.isShowing()) return;
            progressDialog.dismiss();
            progressDialog = null;
        }
    }

    private Uri getInstallApkUri() {
        try {
            if (mApkFile == null || (!mApkFile.exists())) return null;
            Uri uri;
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                uri = Uri.fromFile(mApkFile);
            } else {
                uri = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileprovider", mApkFile);
            }
            return uri;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static class DefaultNotificationDownloadListener implements OnDownloadListener {
        private Context mContext;
        private int mNotifyId;
        private UpdateAgent agent;
        private NotificationCompat.Builder mBuilder;

        public DefaultNotificationDownloadListener(Context context, int notifyId, UpdateAgent agent) {
            mContext = context;
            mNotifyId = notifyId;
            this.agent = agent;
        }

        @Override
        public void onStart() {
            if (mBuilder == null) {
                String contentText = "下载中…";
//                mBuilder = new Notification.Builder(mContext);
//                mBuilder.setOngoing(true)
//                        .setAutoCancel(false)
//                        .setPriority(Notification.PRIORITY_MAX)
//                        .setDefaults(Notification.DEFAULT_VIBRATE)
//                        .setSmallIcon(mContext.getApplicationInfo().icon)
//                        .setTicker(title)
//                        .setContentTitle(title);
                mBuilder = NotificationUtil.startNotificationManager(mContext, contentText);
            }
            onProgress(0);
        }

        @Override
        public void onProgress(int progress) {
            if (mBuilder != null) {
                if (progress > 0) {
                    mBuilder.setPriority(Notification.PRIORITY_DEFAULT);
                    mBuilder.setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE);
                }
                mBuilder.setProgress(100, progress, false);
                mBuilder.setContentText(progress + "%");
                NotificationManager nm = (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);
                nm.notify(mNotifyId, mBuilder.build());
            }
        }

        @Override
        public void onFinish() {
            NotificationManager nm = (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);
            nm.cancel(mNotifyId);

//            Uri uri = agent.getInstallApkUri();
//            if (uri != null) {
//                NotificationUtil.showInstallNotification(mContext, uri);
//            }
        }
    }


}