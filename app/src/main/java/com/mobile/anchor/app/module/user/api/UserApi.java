package com.mobile.anchor.app.module.user.api;

import android.annotation.SuppressLint;

import com.google.gson.JsonObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import anchor.app.base.bean.AlbumBean;
import anchor.app.base.bean.LanguagesBean;
import anchor.app.base.bean.LoginBean;
import io.reactivex.Flowable;
import anchor.app.base.retrofit.AliApiClient;
import anchor.app.base.retrofit.BaseApiClient;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.RxSchedulers;
import anchor.app.base.utils.FileUtil;
import anchor.app.base.utils.OkHttpUtil;
import anchor.app.base.utils.SharePreUtil;

import com.mobile.anchor.app.module.user.bean.AnchorPaySaveInfo;
import com.mobile.anchor.app.module.user.bean.GiftBean;
import com.mobile.anchor.app.module.user.bean.GroupListBean;
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean;
import com.mobile.anchor.app.module.user.bean.InvitationBean;
import com.mobile.anchor.app.module.user.bean.InvitationBean02;
import com.mobile.anchor.app.module.user.bean.LevelBean;
import com.mobile.anchor.app.module.user.bean.PageBean;
import com.mobile.anchor.app.module.user.bean.RankBean;
import com.mobile.anchor.app.module.user.bean.ReportBean;
import com.mobile.anchor.app.module.user.bean.RewardTaskBean;
import com.mobile.anchor.app.module.user.bean.SettlementBean;
import com.mobile.anchor.app.module.user.follow.bean.FansListBean;
import com.mobile.anchor.app.module.user.setting.blacklist.bean.BlackListBean;
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean;

import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;

public class UserApi {


    public static Flowable<BaseResult<Object>> publicRepoet(ReportBean bean) {
        return BaseApiClient.getInstance().create(IUserApi.class).publicRepoet(bean).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 修改個人信息
     *
     * @return
     */
    @SuppressLint("CheckResult")
    public static Flowable<BaseResult<Object>> modifyChildInfo(LoginBean bean) {

        return BaseApiClient.getInstance().create(IUserApi.class).modifyChildInfo(bean).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<Object>> modifyAnchorUpdateNew(HashMap<String, Object> bean) {

        return BaseApiClient.getInstance().create(IUserApi.class).modifyAnchorUpdateNew(bean).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> modifyAnchorBasicInfo(JsonObject jsonObject) {
        return BaseApiClient.getInstance().create(IUserApi.class).updateAnchorBasicInfo(jsonObject).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }


    /**
     * 查询用户信息
     *
     * @return
     */

    public static Flowable<BaseResult<LoginBean>> queryUserInfo() {
        return BaseApiClient.getInstance().create(IUserApi.class).queryUserInfo(SharePreUtil.getUserId()).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }


    /**
     * 查询语言
     */
    public static Flowable<BaseResult<List<LanguagesBean>>> query_user_language() {
        return BaseApiClient.getInstance().create(IUserApi.class).query_user_language().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }


    /**
     * 上传家长头像
     *
     * @return
     */
    public static Flowable<BaseResult<Object>> uploadPicture() {
        JsonObject jsonObject = null;
        try {
            jsonObject = new JsonObject();
            jsonObject.addProperty("contentType", "image/png");
            jsonObject.addProperty("fileSuffix", ".png");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return BaseApiClient.getInstance().create(IUserApi.class).uploadPicture(jsonObject).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 上传img
     *
     * @param filePath
     * @return
     */
    public static void uploadImg(String url, String filePath, Callback callBack) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("image/png"), FileUtil.getFileBytes(filePath));
        Request request = new Request.Builder().url(OkHttpUtil.assembleUrlWithParams(url, null)).put(requestBody).build();
        AliApiClient.getInstance().getOkhttpClient().newCall(request).enqueue(callBack);
    }


    /**
     * 获取黑名单列表
     *
     * @return
     */
    public static Flowable<BaseResult<BlackListBean>> getBlackList(int current, int size) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);

        return BaseApiClient.getInstance().create(IUserApi.class).getBlackList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 获取粉丝和关注列表
     *
     * @return
     */
    public static Flowable<BaseResult<FansListBean>> getFansList(long UserId, int current, int size, int type) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);
//        request.put("id", 0);
        if (UserId != 0) {
            request.put("userId", UserId);
        }

        if (type == 1) {
            return BaseApiClient.getInstance().create(IUserApi.class).getFollowList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
        } else {
            return BaseApiClient.getInstance().create(IUserApi.class).getFansList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
        }
    }

    public static Flowable<BaseResult<List<GroupListBean>>> getGroupList(int unionId) {

        return BaseApiClient.getInstance().create(IUserApi.class).getGroupList(unionId).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<LevelBean.DataDTO>>> getLevelInfo() {
        return BaseApiClient.getInstance().create(IUserApi.class).getLevelInfo().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());

    }

    /**
     * 消费记录
     *
     * @return
     */
    public static Flowable<BaseResult<HistoryListBean>> getHistoryList(int current, int size) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("current", current);
        request.put("size", size);

        return BaseApiClient.getInstance().create(IUserApi.class).getHistoryList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<HistoryListBean>> getDiamondList(String startTime, String endTime, int current, int size) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("startTime", startTime);
        request.put("endTime", endTime);
        request.put("current", current);
        request.put("size", size);

        return BaseApiClient.getInstance().create(IUserApi.class).getDiamondList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<InvitationBean>> getInviteList(String anchorId) {
        return BaseApiClient.getInstance().create(IUserApi.class).getInviteList(anchorId).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<RankBean>> getRankList(String anchorId, String type) {
        return BaseApiClient.getInstance().create(IUserApi.class).getRankList(anchorId, type).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<RankBean>> getRankList02(String anchorId, String type) {
        return BaseApiClient.getInstance().create(IUserApi.class).getRankList02(anchorId, type).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }


    public static Flowable<BaseResult<InvitationBean02>> getUserInviteList(String anchorId) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("anchorId", anchorId);
//        request.put("anchorId", "1760235597271736322");
        request.put("current", 1);
        request.put("size", "100");

        return BaseApiClient.getInstance().create(IUserApi.class).getUserInviteList(request).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    /**
     * 从黑名单移除
     *
     * @return
     */
    public static Flowable<BaseResult<Object>> anchorExtract(int extractNum) {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorExtract(extractNum).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> anchorPayInfo() {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorPayInfo().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<String>>> anchorPayCountry() {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorPayCountry().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<String>>> orderPayOrg(String country, String paymentMethodType) {
        return BaseApiClient.getInstance().create(IUserApi.class).orderPayOrg(country, paymentMethodType).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<String>>> anchorPayMethod(String country) {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorPayMethod(country).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<String>>> anchorPayParam(String country, String paymentMethodType) {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorPayParam(country, paymentMethodType).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> anchorPaySaveInfo(AnchorPaySaveInfo anchorPaySaveInfo) {
        return BaseApiClient.getInstance().create(IUserApi.class).anchorPaySaveInfo(anchorPaySaveInfo).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> removeBlackList(long blackUserId) {
        return BaseApiClient.getInstance().create(IUserApi.class).removeBlackList(blackUserId, "1").subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> removeAndFollowing(long UserId, int type, String role) {
        if (type == 1) {
            return BaseApiClient.getInstance().create(IUserApi.class).userSetFollow(UserId, role).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
        } else {
            return BaseApiClient.getInstance().create(IUserApi.class).userSetUnFollow(UserId, role).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
        }

    }


    public static Flowable<BaseResult<Object>> updateOnlineStatus(String status) {
        return BaseApiClient.getInstance().create(IUserApi.class).setupOnlineStatus(status).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<List<AlbumBean>>> getAlbumList(Map<String, String> map) {
        return BaseApiClient.getInstance().create(IUserApi.class).getAlbumList(map).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<PageBean<AlbumBean>>> albumDelete(List<String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).albumDelete(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> updateAnchorAlbum(JsonObject params) {
        return BaseApiClient.getInstance().create(IUserApi.class).updateAnchorAlbum(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<PageBean<GiftBean>>> getGiftList(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).giftList(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Integer>> giftTotalValue() {
        return BaseApiClient.getInstance().create(IUserApi.class).giftTotalValue().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> modifyPassword(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).modifyPassword(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> getEmailCode(String email) {
        return BaseApiClient.getInstance().create(IUserApi.class).emailCode(email).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<IncomeStatementBean>> getIncomeStatistics(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).incomeStatistics(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<PageBean<IncomeStatementBean>>> getIncomeStatementList(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).incomeStatementList(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<PageBean<SettlementBean>>> getSettlementList(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).settlementList(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<RewardTaskBean>> getRewardList() {
        return BaseApiClient.getInstance().create(IUserApi.class).rewardList().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Boolean>> claimTask(String id) {
        return BaseApiClient.getInstance().create(IUserApi.class).claimTask(id).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Object>> setupPrice(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).setupPrice(params)
                .subscribeOn(RxSchedulers.INSTANCE.getIo())
                .observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<PageBean<InvitationBean.UserDataListDTO>>> inviteIntroducedList(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).inviteIntroducedList(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Boolean>> fillInviteCode(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).fillInviteCode(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<Boolean>> uploadRecordVideo(Map<String, String> params) {
        return BaseApiClient.getInstance().create(IUserApi.class).uploadRecordVideo(params).subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }

    public static Flowable<BaseResult<String>> checkProfileAuditStatus() {
        return BaseApiClient.getInstance().create(IUserApi.class).checkProfileAuditStatus().subscribeOn(RxSchedulers.INSTANCE.getIo()).observeOn(RxSchedulers.INSTANCE.getUi());
    }
}

