package com.mobile.anchor.app.module.user.setting.safe

import android.content.Intent
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityAccountSafeBinding
import anchor.app.base.ui.BaseActivity

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/22 14:43
 * @description :帐号与安全
 */
class AccountSafeActivity : BaseActivity<AccountSafeViewModel, ActivityAccountSafeBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_account_safe

    override fun initView() {
        showContentView()
        setTitleText(R.string.account_safe)

        bindingView?.tvModifyPwd?.setOnClickListener { startActivity(Intent(this, ValidateMethodActivity::class.java)) }
        bindingView?.tvModifyEmail?.setOnClickListener { startActivity(Intent(this, ModifyEmailActivity::class.java)) }
    }
}