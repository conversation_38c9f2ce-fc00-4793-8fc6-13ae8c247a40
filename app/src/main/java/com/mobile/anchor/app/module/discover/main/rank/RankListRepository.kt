package com.mobile.anchor.app.module.discover.main.rank

import io.reactivex.Flowable
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.discover.api.DiscoverApi
import com.mobile.anchor.app.module.user.bean.RankBean

class RankListRepository : IRepository {


    fun getAnchorCharts(type: String): Flowable<BaseResult<List<RankBean>>> {
        return DiscoverApi.getAnchorCharts(type)
    }



}