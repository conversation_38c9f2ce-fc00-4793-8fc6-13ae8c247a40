package com.mobile.anchor.app.module.user.wallet

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.mobile.anchor.app.module.user.bean.AnchorPaySaveInfo
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean
import com.mobile.anchor.app.module.user.bean.SettlementBean
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean

class WalletModel(application: Application) : BaseViewModel<WalletRepository?>(application) {
    fun aa() {
        repository!!.aa()
    }

    public override fun getRepository(): WalletRepository {
        return WalletRepository()
    }

    fun anchorPayInfo(): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorPayInfo(), { bean ->
            info.value = bean
        }) { throwable ->
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;
    }

    fun anchorPayCountry(): MutableLiveData<List<String>> {
        val info: MutableLiveData<List<String>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorPayCountry(), { bean ->
            info.value = bean
        }) { throwable ->
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }

    fun orderPayOrg(country: String, paymentMethodType: String): MutableLiveData<List<String>> {
        val info: MutableLiveData<List<String>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.orderPayOrg(country, paymentMethodType), { bean ->
            info.value = bean
        }) { throwable ->
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }

    fun anchorPayMethod(country: String): MutableLiveData<List<String>> {
        val info: MutableLiveData<List<String>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorPayMethod(country), { bean ->
            info.value = bean
        }) { throwable ->
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }

    fun anchorPayParam(country: String, paymentMethodType: String): MutableLiveData<List<String>> {
        val info: MutableLiveData<List<String>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorPayParam(country, paymentMethodType), { bean ->
            info.value = bean
        }) { throwable ->
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;

    }

    fun anchorPaySaveInfo(anchorPaySaveInfo: AnchorPaySaveInfo): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorPaySaveInfo(anchorPaySaveInfo), { bean ->
            info.value = ""
        }) { throwable ->
            info.value = "000"
//            error.value = throwable
            ToastUtil.show("" + throwable.message)
        }
        return info;
    }

    fun getDiamondList(
        startTime: String,
        endTime: String,
        current: Int,
        size: Int = 20
    ): MutableLiveData<List<HistoryListBean.RecordsDTO>> {
        val info: MutableLiveData<List<HistoryListBean.RecordsDTO>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.getDiamondList(startTime, endTime, current, size), { bean ->
            info.value = bean.records
        }) { throwable ->
            ToastUtil.show("" + throwable.message)
        }
        return info;
    }

    fun getIncomeStatementList(params: Map<String, String>): MutableLiveData<List<IncomeStatementBean>> {
        val liveData = MutableLiveData<List<IncomeStatementBean>>()
        dataStreamFliter.fliter(repository!!.getIncomeStatementList(params), {
            liveData.value = it.records
        }) {}
        return liveData
    }

    fun getSettlementList(params: Map<String, String>): MutableLiveData<List<SettlementBean>> {
        val liveData = MutableLiveData<List<SettlementBean>>()
        dataStreamFliter.fliter(repository!!.getSettlementList(params), {
            liveData.value = it.records
        }) {}
        return liveData
    }
}