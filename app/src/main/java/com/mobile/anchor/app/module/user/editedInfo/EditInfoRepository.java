package com.mobile.anchor.app.module.user.editedInfo;


import com.google.gson.JsonObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Flowable;
import anchor.app.base.api.BaseApi;
import anchor.app.base.bean.LanguagesBean;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.repository.IRepository;
import anchor.app.base.retrofit.BaseResult;
import com.mobile.anchor.app.module.user.api.UserApi;

public class EditInfoRepository implements IRepository {

    public EditInfoRepository() {
    }

    /**
     * 查询孩子
     *
     * @return
     */

    public Flowable<BaseResult<Object>> modifyChildInfo(LoginBean bean) {
        return UserApi.modifyChildInfo(bean);
    }

    public Flowable<BaseResult<Object>> modifyAnchorUpdateNew(HashMap<String, Object> bean) {
        return UserApi.modifyAnchorUpdateNew(bean);
    }

    public Flowable<BaseResult<LoginBean>> queryUserInfo() {
        return UserApi.queryUserInfo();
    }

    public Flowable<BaseResult<List<LanguagesBean>>> query_user_language() {
        return UserApi.query_user_language();
    }

    public Flowable<BaseResult> uploadPicture(File file) {
        return BaseApi.uploadPicture(file);
    }

    public Flowable<BaseResult> uploadPicture(byte[] content, String fileName) {
        return BaseApi.uploadPicture(content, fileName);
    }

    public Flowable<BaseResult<Object>> modifyAnchorBasicInfo(JsonObject jsonObject) {
        return UserApi.modifyAnchorBasicInfo(jsonObject);
    }

    public Flowable<BaseResult<Boolean>> uploadRecordVideo(Map<String,String> params) {
        return UserApi.uploadRecordVideo(params);
    }
    public Flowable<BaseResult<String>> checkProfileAuditStatus() {
        return UserApi.checkProfileAuditStatus();
    }

}
