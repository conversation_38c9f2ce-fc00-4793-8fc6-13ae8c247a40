package com.mobile.anchor.app.module.user.grouplist

import anchor.app.base.viewmodel.BaseViewModel
import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.mobile.anchor.app.module.user.bean.GroupListBean

class GroupListViewmodel (application: Application) : BaseViewModel<GroupListRepository>(application) {

    override fun getRepository(): GroupListRepository {

        return GroupListRepository()
    }

    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(unionId: Int): MutableLiveData<List<GroupListBean>>{
        val info: MutableLiveData<List<GroupListBean>> = MutableLiveData()
        dataStreamFliter.fliter(repository.getList(unionId), { bean ->
                info.value = bean
        }) { throwable ->
            error.value = throwable
        }
        return info;
        }

}