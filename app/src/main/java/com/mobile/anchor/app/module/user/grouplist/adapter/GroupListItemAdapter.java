package com.mobile.anchor.app.module.user.grouplist.adapter;

import android.content.Context;

import androidx.databinding.ObservableArrayList;

import org.jetbrains.annotations.NotNull;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.module.user.adapter.UserBindingViewAdapter;

public class GroupListItemAdapter extends UserBindingViewAdapter {

    public static final int ITEM_CONTENT = 0;

    public GroupListItemAdapter(@NotNull Context context, @NotNull ObservableArrayList list) {
        super(context, list);
        addViewTypeToLayoutMap(ITEM_CONTENT, R.layout.user_item_grouplist);
    }

    @Override
    public int getViewType(@NotNull Object item) {
        return ITEM_CONTENT;
    }
}
