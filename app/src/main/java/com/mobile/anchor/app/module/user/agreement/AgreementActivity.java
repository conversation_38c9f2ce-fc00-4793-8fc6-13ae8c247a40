package com.mobile.anchor.app.module.user.agreement;

import android.webkit.WebView;
import android.webkit.WebViewClient;

import anchor.app.base.web.BaseJsAPI;
import anchor.app.base.web.BaseWebViewActivity;

public class AgreementActivity extends BaseWebViewActivity {

    private static final String TAG = "DyyAgreementActivity";

    @Override
    protected void initView() {
        super.initView();
        loadUrl(getIntent().getStringExtra("url"));
        bindingView.tvTitle.setText(getIntent().getStringExtra("title"));
        mWebView.setBackgroundColor(0xFF101321);

        mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                String js = "document.body.style.color = 'white';" +
                        "document.body.style.fontSize = '20px';";
                view.evaluateJavascript(js, null);
            }
        });
    }

    /**
     * 子类如果有js bridge方法，复写该方法进行返回
     */
    protected BaseJsAPI getJsAPI() {
        return new BaseJsAPI(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mWebView.callHandler("stopAudio", new Object[]{});
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void startWorkWall() {
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

}
