package com.mobile.anchor.app.module.user.wallet

import com.angcyo.dsladapter.DslAdapterItem
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityChooseCurrencyBinding
import anchor.app.base.ext.backgroundTint
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CountryUtil

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/28 17:23
 * @description :选择货币
 */
class ChooseCurrencyActivity : BaseActivity<WalletModel, ActivityChooseCurrencyBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_choose_currency

    override fun initView() {
        bindingView.activity = this
        showContentView()
        bindingView.recyclerView.apply {
            applySingleSelectorModel(0)
            append<DslAdapterItem>(
                listOf(
                    Pair<String, String>("US", "USD($)"),
                    Pair<String, String>("CN", "CNY(￥)"),
                    Pair<String, String>("JP", "JPY(¥)"),
                    Pair<String, String>("EU", "EUR(€)"),
                    Pair<String, String>("ID", "IDR(Rp)"),
                    Pair<String, String>("KR", "KRW(₩)")
                )
            ) {
                itemLayoutId = R.layout.user_item_choose_currency
                itemBindOverride = { itemHolder, _, _, _ ->
                    val item = itemData as Pair<String, String>
                    itemHolder.img(R.id.iv_flag)
                        ?.setBackgroundDrawable(CountryUtil.countryCodeToImage(itemHolder.context, item.first))
                    itemHolder.tv(R.id.tv_currency)?.text = item.second
                    itemHolder.itemView.backgroundTint(if (itemIsSelected) R.color.colorAccent else R.color.color_cardBackground)
                    itemHolder.clickItem {
                        updateItemSelect(true)
                    }
                }
            }
        }
    }
}