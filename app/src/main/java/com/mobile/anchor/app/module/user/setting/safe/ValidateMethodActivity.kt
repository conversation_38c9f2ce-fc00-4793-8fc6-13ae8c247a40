package com.mobile.anchor.app.module.user.setting.safe

import android.content.Intent
import anchor.app.base.ui.BaseNoModelActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityValidateMethodBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/8/22 16:01
 * @description :密码修改验证方式
 */
class ValidateMethodActivity : BaseNoModelActivity<ActivityValidateMethodBinding>() {
    override fun getLayoutId(): Int = R.layout.activity_validate_method

    override fun initView() {
        setTitleText(R.string.modify_password)

        bindingView?.tvValidatePwd?.setOnClickListener {
            startActivity(Intent(this,ModifyPasswordActivity::class.java).apply {
                putExtra("type",ValidateType.PASSWORD.value)
            })
        }

        bindingView?.tvValidateEmail?.setOnClickListener {
            startActivity(Intent(this,ValidateEmailActivity::class.java))
        }
    }
}