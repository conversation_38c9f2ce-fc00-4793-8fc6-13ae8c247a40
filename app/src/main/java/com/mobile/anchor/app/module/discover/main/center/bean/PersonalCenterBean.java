package com.mobile.anchor.app.module.discover.main.center.bean;

import android.os.Parcelable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class PersonalCenterBean implements Serializable{

    private List<AnchorFileListDTO> anchorFileList;
    private String onlineStatus;
    private String auditFlag;
    private String country;
    private String email;
    private int fansNum;
    private int followNum;
    private String followFlag;
    private String gender;
    private String groundFileName="";
    private String headFileName;
    private Long id;
    private int incomeDiamond;
    private String language;
    private String lastIp;
    private String nickName;
    private String phone;
    private String roomTitle;
    private String status;
    private List<TrendsDTO> trends;
    private int trendsNum;
    private int imageNum;
    private int unionId;
    private String userCode;
    private String username;
    private String userRole;
    private String level;
    private int videoPrice;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public int getImageNum() {
        return imageNum;
    }

    public void setImageNum(int imageNum) {
        this.imageNum = imageNum;
    }

    public void setFollowFlag(String followFlag) {
        this.followFlag = followFlag;
    }

    public String getFollowFlag() {
        return followFlag;
    }

    public List<AnchorFileListDTO> getAnchorFileList() {
        return anchorFileList;
    }

    public void setAnchorFileList(List<AnchorFileListDTO> anchorFileList) {
        this.anchorFileList = anchorFileList;
    }


    public String getAuditFlag() {
        return auditFlag;
    }

    public void setAuditFlag(String auditFlag) {
        this.auditFlag = auditFlag;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getFansNum() {
        return fansNum;
    }

    public void setFansNum(int fansNum) {
        this.fansNum = fansNum;
    }

    public int getFollowNum() {
        return followNum;
    }

    public void setFollowNum(int followNum) {
        this.followNum = followNum;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGroundFileName() {
        return groundFileName;
    }

    public void setGroundFileName(String groundFileName) {
        this.groundFileName = groundFileName;
    }

    public String getHeadFileName() {
        return headFileName;
    }

    public void setHeadFileName(String headFileName) {
        this.headFileName = headFileName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getIncomeDiamond() {
        return incomeDiamond;
    }

    public void setIncomeDiamond(int incomeDiamond) {
        this.incomeDiamond = incomeDiamond;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRoomTitle() {
        return roomTitle;
    }

    public void setRoomTitle(String roomTitle) {
        this.roomTitle = roomTitle;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<TrendsDTO> getTrends() {
        return trends;
    }

    public void setTrends(List<TrendsDTO> trends) {
        this.trends = trends;
    }

    public int getTrendsNum() {
        return trendsNum;
    }

    public void setTrendsNum(int trendsNum) {
        this.trendsNum = trendsNum;
    }

    public int getUnionId() {
        return unionId;
    }

    public void setUnionId(int unionId) {
        this.unionId = unionId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public int getVideoPrice() {
        return videoPrice;
    }

    public void setVideoPrice(int videoPrice) {
        this.videoPrice = videoPrice;
    }

    public static class AnchorFileListDTO implements Serializable {
        private long anchorId;
        private String createTime;
        private String fileCategory;
        private String fileName;
        private String purchaseFlag;
        private int filePrice;
        private String fileType;
        private String fileUrl;
        private long id;
        private String updateTime;

        public String getPurchaseFlag() {
            return purchaseFlag;
        }

        public void setPurchaseFlag(String purchaseFlag) {
            this.purchaseFlag = purchaseFlag;
        }

        public long getAnchorId() {
            return anchorId;
        }

        public void setAnchorId(long anchorId) {
            this.anchorId = anchorId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getFileCategory() {
            return fileCategory;
        }

        public void setFileCategory(String fileCategory) {
            this.fileCategory = fileCategory;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public int getFilePrice() {
            return filePrice;
        }

        public void setFilePrice(int filePrice) {
            this.filePrice = filePrice;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }

    public static class TrendsDTO implements Serializable {
        private String content;
        private String createTime;
        private long id;
        private String status;
        private String trendsUrl;
        private String updateTime;
        private ArrayList<String> fileNames;
        private long userId;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getTrendsUrl() {
            return trendsUrl;
        }

        public void setTrendsUrl(String trendsUrl) {
            this.trendsUrl = trendsUrl;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public ArrayList<String> getFileNames() {
            return fileNames;
        }

        public void setFileNames(ArrayList<String> fileNames) {
            this.fileNames = fileNames;
        }

        public long getUserId() {
            return userId;
        }

        public void setUserId(long userId) {
            this.userId = userId;
        }
    }
}
