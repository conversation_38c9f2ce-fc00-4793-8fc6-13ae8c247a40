package com.mobile.anchor.app.module.user.dialog

import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import anchor.app.base.ext.click
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupInputInviteCodeBinding
import com.mobile.anchor.app.module.user.invitation.InvitationModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/23 14:37
 * @description :
 */
class InputInviteCodePopup(context: AppCompatActivity) : CenterPopupView(context) {

    private val viewModel by context.viewModels<InvitationModel>()
    override fun getImplLayoutId(): Int = R.layout.popup_input_invite_code

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupInputInviteCodeBinding.bind(popupImplView)
        binding.tvInvite.click {
            if (binding.etInviteCode.text?.isEmpty() == true) {
                ToastUtil.show(context.getString(R.string.please_input_invite_code))
                return@click
            }
            viewModel.fillInviteCode(binding.etInviteCode.text.toString()).observe(this) {
                if (it) {
                    ToastUtil.show(context.getString(R.string.invite_code_confirm))
                    dismiss()
                }
            }
        }
        binding.ivClose.click {
            dismiss()
        }
    }
}

fun showInputInviteCodePopup(context: AppCompatActivity) {
    XPopup.Builder(context).asCustom(InputInviteCodePopup(context)).show()
}