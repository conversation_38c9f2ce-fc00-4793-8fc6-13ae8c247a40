package com.mobile.anchor.app.module.user.album.adapter

import anchor.app.base.bean.AlbumBean
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.UserItemAlbumUploadBinding
import anchor.app.base.ext.makeVisible

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/19 17:13
 * @description :相册上传Adapter
 */
class AlbumUploadAdapter(
    private val items: List<AlbumBean?>,
    private val onAdditionBlock: (Int) -> Unit,
    private val onDeleteBlock: (Int) -> Unit
) : RecyclerView.Adapter<AlbumUploadAdapter.ViewHolder>() {

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.user_item_album_upload, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val binding = DataBindingUtil.bind<UserItemAlbumUploadBinding>(holder.itemView)
        val fileBean = items[position]
        binding?.ivDel?.let {
            it.makeVisible(holder.layoutPosition != items.lastIndex)
            it.setOnClickListener { onDeleteBlock.invoke(holder.layoutPosition) }
        }

        binding?.ivVideo?.makeVisible(fileBean?.isVideo == true)

        binding?.ivImage?.let {
            if (holder.layoutPosition == items.lastIndex || fileBean == null) {
                it.setImageResource(R.mipmap.user_album_upload_placeholder)
                it.setOnClickListener {
                    onAdditionBlock.invoke(holder.layoutPosition)
                }
            } else {
                Glide.with(it).load(if (fileBean.isPhoto) fileBean.fileUrl else fileBean.thumbnail).transform(
                    CenterCrop(), RoundedCorners(
                        holder.itemView.context.resources.getDimension(R.dimen.dp_8).toInt()
                    )
                ).into(it)
                it.setOnClickListener {}
            }
        }
    }
}