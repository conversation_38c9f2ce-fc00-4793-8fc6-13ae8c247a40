package com.mobile.anchor.app.module.user.album

import anchor.app.base.bean.AlbumBean
import anchor.app.base.bean.AlbumType
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeVisible
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseFragment
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.ImageUtil
import anchor.app.base.utils.ToastUtil
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import com.angcyo.dsladapter.DragCallbackHelper
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter._dslAdapter
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.SmartGlideImageLoader
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.FragmentAlbumManageBinding
import com.mobile.anchor.app.module.user.dialog.showTestVideoRemindPopup
import com.mobile.anchor.app.module.user.editedInfo.EditInfoModel
import com.mobile.anchor.app.module.user.main.UserViewModel

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/16 14:02
 * @description :相册维护
 */
class AlbumManageFragment : BaseFragment<UserViewModel, FragmentAlbumManageBinding>() {
    companion object {
        fun newInstance(type: AlbumType): AlbumManageFragment {
            return AlbumManageFragment().apply {
                arguments = Bundle().apply {
                    putInt("type", type.value)
                }
            }
        }
    }

    private val editViewModel by viewModels<EditInfoModel>()
    private val MIN_ALBUM_COUNT = 5
    private var pageIndex = 1
    private val cameraUtil: CameraUtil by lazy { CameraUtil(requireActivity()) }
    private val isEditMode: MutableLiveData<Boolean> = MutableLiveData(false)
    private val dragCallbackHelper = DragCallbackHelper()
    private val items = mutableListOf<AlbumBean>()

    override fun getLayoutId(): Int = R.layout.fragment_album_manage

    override fun initView() {
        bindingView.refreshLayout.setEnableRefresh(false)
        bindingView.refreshLayout.setEnableLoadMore(false)
        bindingView.tvAddition.click {
            cameraUtil.getPic(
                activity = requireActivity() as AppCompatActivity,
                isCroup = false,
                maxSelectNum = 9,
                chooseMode = SelectMimeType.ofAll(),
                callback = object : OnResultCallbackListener<LocalMedia> {
                    override fun onResult(result: ArrayList<LocalMedia>) {
                        if (result.isNotEmpty()) {
                            onResultDeal(result)
                        }
                    }

                    override fun onCancel() {
                    }
                })
        }

        bindingView.tvSubmit.click {
            if (bindingView.recyclerView.dslAdapter?.dataItems?.isNullOrEmpty() == true) {
                return@click
            }

            val params = JsonObject()
            params.add(
                "album",
                Gson().toJsonTree(bindingView.recyclerView.dslAdapter.dataItems.map { it.itemData as? AlbumBean }
                    ?.mapIndexed { index, it ->
                        it?.apply { sort = index }
                    })
            )
            viewModel.updateAnchorAlbum(params.apply {
                addProperty("id", UserInfoManager.user()?.id)
                addProperty("isLock", arguments?.getInt("type"))
            }).observe(this) {
                isEditMode.value = false
                if (it) {
                    ToastUtil.show("upload success")
                    bindingView.recyclerView.notifyUpdateItem()
                    fetch(true)
                    UserInfoManager.user()?.apply {
                        isAlbumUpload = "1"
                        albumStatus = 0
                    }?.let {
                        UserInfoManager.setUser(it)

                        //新主播，还未上传过人脸录制视频
                        if (it.isUploadTestUrl == "0") {
                            showTestVideoRemindPopup(requireActivity())
                        }
                    }
                }
            }
        }

        dragCallbackHelper.apply {
            attachToRecyclerView(bindingView.recyclerView)
            enableLongPressDrag = false
            onItemMoveChanged = { fromList, toList, fromPosition, toPosition ->
                bindingView.recyclerView.dslAdapter.updateAllItem()
            }
        }

        isEditMode.observe(this) {
            bindingView.tvAddition.makeVisible(!it)
            bindingView.tvSubmit.makeVisible(it)
        }
    }

    override fun loadData() {
        super.loadData()
        fetch(true)
    }

    private fun fetch(refresh: Boolean = true) {
        pageIndex = if (refresh) 1 else pageIndex + 1
        viewModel?.getAlbumList(buildMap {
            put("current", pageIndex.toString())
            put("isLock", arguments?.getInt("type").toString())
            put("fileType", arguments?.getInt("type").toString())
            put("size", "50")
        })?.observe(this) {
            showContentView()
            if (refresh) {
                items.clear()
                bindingView.recyclerView.clearAllItems()
            }
            it?.let { beans ->
                items.addAll(beans)
                if (items.isNullOrEmpty()) {
                    isEditMode.value = false
                }
                append(items)
                bindingView.refreshLayout.finishRefresh()
                bindingView.refreshLayout.setNoMoreData(it.isNullOrEmpty() || items.size < 50)
            }
        }
    }

    private fun append(items: List<AlbumBean>) {
        bindingView.recyclerView.clearAllItems()
        bindingView.recyclerView.append<DslAdapterItem>(items) {
            itemLayoutId = R.layout.user_item_album_manage
            itemBindOverride = { itemHolder, itemPosition, _, _ ->
                val item = itemData as AlbumBean
                itemHolder.img(R.id.iv_video)?.makeVisible(item.fileType == 2)
                itemHolder.tv(R.id.tv_refused)?.makeVisible(item.status == 2)
                itemHolder.tv(R.id.tv_reviewing)
                    ?.makeVisible(item.id.isNotEmpty() && item.status == 0)
                itemHolder.img(R.id.iv_del)?.apply {
                    makeVisible(isEditMode.value!!)
                    click {
                        if (arguments?.getInt("type") == 0 && item.id.isNotEmpty() && (bindingView.recyclerView._dslAdapter?.dataItems?.filter { bean -> (bean.itemData as AlbumBean).id.isNotEmpty() }?.size
                                ?: 0) <= MIN_ALBUM_COUNT
                        ) {
                            ToastUtil.show(
                                getString(
                                    R.string.album_delete_limit_tips, MIN_ALBUM_COUNT, 3
                                )
                            )
                            return@click
                        }

                        if (arguments?.getInt("type") == 1 && item.id.isNotEmpty() && (bindingView.recyclerView._dslAdapter?.dataItems?.filter { bean -> (bean.itemData as AlbumBean).id.isNotEmpty() }?.size
                                ?: 0) <= 3
                        ) {
                            ToastUtil.show(
                                getString(
                                    R.string.album_delete_limit_tips, MIN_ALBUM_COUNT, 3
                                )
                            )
                            return@click
                        }

                        item.id.takeIf { it.isNotEmpty() }?.let {
                            viewModel.deleteAlbum(listOf(it)).observe(this@AlbumManageFragment) {
//                                fetch(true)
                                removeAdapterItemJust()
                                updateItemDepend()
                            }
                        } ?: run {
                            removeAdapterItemJust()
                            updateItemDepend()

                            //未绑卡，表明正处于进行入驻流程中，必须上传审核通过满5张
                            if (UserInfoManager.user()?.payInfoStatus == "0" && (bindingView.recyclerView._dslAdapter?.dataItems?.size
                                    ?: 0) < MIN_ALBUM_COUNT
                            ) {
                                isEditMode.value = false
                            }
                        }
                    }
                }
                itemHolder.v<ImageView>(R.id.iv_album)?.apply {
                    ImageUtil.displayImage(this.context, this, item.fileUrl)
                }
                itemHolder.clickItem {
                    if (item.fileType == AlbumType.VIDEO.value) {
                        jump(VideoPlaybackActivity::class.java, Bundle().apply {
                            putString("url", item.fileUrl)
                        })
                    } else {
                        XPopup.Builder(itemHolder.itemView.context).isDestroyOnDismiss(true)
                            .asImageViewer(
                                itemHolder.v<ImageView>(R.id.iv_album),
                                itemPosition,
                                items.map { it.fileUrl },
                                { _, _ -> },
                                object : SmartGlideImageLoader() {}).show()
                    }
                }

                itemHolder.longClickItem {
                    isEditMode.value = true
                    dragCallbackHelper.startDrag(itemHolder)
                    bindingView.recyclerView.dslAdapter.updateAllItem()
                }
            }
        }
    }

    private fun onResultDeal(result: ArrayList<LocalMedia>) {
        LoadingDialog.getInstance(requireContext()).show()
        ToastUtil.show(getResources().getString(R.string.b29))
        if (result.first().mimeType == PictureMimeType.MIME_TYPE_VIDEO) {
            editViewModel.uploadVideo(result)?.observe(this) {
                it?.let {
                    items.add(
                        AlbumBean(
                            fileUrl = it.url,
                            thumbnail = it.thumbnailUrl,
                            fileType = AlbumType.VIDEO.value
                        )
                    )
                    append(items)
                    //上传完毕
                    LoadingDialog.getInstance(requireContext()).dismiss()
                } ?: run {
                    ToastUtil.show(getString(R.string.upload_failure_please_try_again))
                    LoadingDialog.getInstance(requireContext()).dismiss()
                }

                //未绑卡，表明正处于进行入驻流程中，必须上传审核通过满5张 改变提交按钮状态，如果是未上传过相册，判断是否达到5张
                isEditMode.value =
                    UserInfoManager.user()?.payInfoStatus == "1" || UserInfoManager.user()?.payInfoStatus == "0" && items.size >= MIN_ALBUM_COUNT
            }
        } else {
            editViewModel.uploadPictures(result)?.observe(this) {
                if (it.isNotEmpty()) {
                    ToastUtil.show(getString(R.string.upload_success))
                    items.add(AlbumBean(fileUrl = it, fileType = AlbumType.PHOTO.value))
                    append(items)
                } else {
                    ToastUtil.show(getString(R.string.upload_failure_please_try_again))
                }
                isEditMode.value =
                    UserInfoManager.user()?.payInfoStatus == "1" || UserInfoManager.user()?.payInfoStatus == "0" && items.size >= MIN_ALBUM_COUNT
                LoadingDialog.getInstance(requireContext()).dismiss()
            }
        }
    }
}