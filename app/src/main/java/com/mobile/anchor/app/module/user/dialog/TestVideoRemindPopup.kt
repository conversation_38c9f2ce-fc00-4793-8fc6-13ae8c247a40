package com.mobile.anchor.app.module.user.dialog

import android.app.Activity
import android.content.Context
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupTestVideoBinding
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import com.mobile.anchor.app.module.user.record.RecordVideoActivity

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/27 20:21
 * @description :
 */
class TestVideoRemindPopup(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId(): Int = R.layout.popup_test_video

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupTestVideoBinding.bind(popupImplView)

        binding.btnRecord.click {
            jump(RecordVideoActivity::class.java)
            dismiss()
        }
    }
}

fun showTestVideoRemindPopup(activity: Activity) {
    XPopup.Builder(activity)
        .dismissOnBackPressed(false)
        .dismissOnTouchOutside(false)
        .asCustom(TestVideoRemindPopup(activity))
        .show()
}
