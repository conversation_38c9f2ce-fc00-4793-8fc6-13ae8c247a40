package com.mobile.anchor.app.module.user.bean;

import java.io.Serializable;
import java.util.List;

public class LevelBean {
    private List<DataDTO> data;

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO implements Serializable {
        private Integer basicNum;
        private String createTime;
        private String describeJson;
        private Integer grade;
        private Integer id;
        private String updateTime;

        public Integer getBasicNum() {
            return basicNum;
        }

        public void setBasicNum(Integer basicNum) {
            this.basicNum = basicNum;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getDescribeJson() {
            return describeJson;
        }

        public void setDescribeJson(String describeJson) {
            this.describeJson = describeJson;
        }

        public Integer getGrade() {
            return grade;
        }

        public void setGrade(Integer grade) {
            this.grade = grade;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }
}
