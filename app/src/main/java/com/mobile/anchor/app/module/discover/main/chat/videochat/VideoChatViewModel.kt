package com.mobile.anchor.app.module.discover.main.chat.videochat

import android.app.Application
import android.util.Log
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.scope
import io.reactivex.disposables.Disposable
import anchor.app.base.ext.rxweaver.RxErrorUtil
import anchor.app.base.manager.ResourceManager
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.retrofit.ResultMap
import anchor.app.base.utils.Logger
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.discover.bean.CallSettlementBean

class VideoChatViewModel(application: Application) :
    BaseViewModel<VideoChatRepository>(application) {
    private val disposable: Disposable? = null

    /**
     * 获取视频通话的频道id
     */
    fun getVideoChannel(activity: FragmentActivity?, id: Long): MutableLiveData<String> {
        val data = MutableLiveData<String>()
        repository!!.getVideoChannel(id).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity)).`as`(AutoDispose.autoDisposable(this))
            .subscribe({ result: String ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
            }
        return data
    }

    fun getVideoPush(activity: CallVideoChatActivity?, id: String): MutableLiveData<Any> {
        val data = MutableLiveData<Any>()
        repository!!.getVideoPush(id).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(activity?.scope())).subscribe({ result: Any ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) {
                        data.value = "";
                    }
                }
            }
        return data
    }

    fun getVideoCallTime(
        activity: FragmentActivity?, id: String
    ): MutableLiveData<CallSettlementBean?> {
        val data = MutableLiveData<CallSettlementBean?>()
        repository?.apply {
            getVideoCallTime(id).map(ResultMap()).compose(RxErrorUtil.handleGlobalError(activity))
                .`as`(AutoDispose.autoDisposable(activity?.scope()))
                .subscribe({ result: CallSettlementBean ->
//                    experienceCourse.setValue(result);
                    data.setValue(result)
                }) { throwable: Throwable ->
                    // error.value = throwable
                    if (throwable is ResultException) {
                        if (throwable.getCode() == BaseResult.SUCCESS) {
                            data.value = null;
                        }
                    }
                }
        }
        return data
    }

    fun getVideoPushReject(activity: CallVideoChatActivity?, id: String): MutableLiveData<Any> {
//        println("VideoPushReject")
//        ToastUtil.show("VideoPushReject")
        val data = MutableLiveData<Any>()
        repository!!.getVideoPushReject(id).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(activity?.scope())).subscribe({ result: Any ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
                println("VideoPushReject..")
//                ToastUtil.show("VideoPushReject..")
            }) { throwable: Throwable ->
                println("VideoPushReject....")
                // error.value = throwable
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) {
                        data.value = "";
                    }
                } else {
                    ToastUtil.show(throwable.message)
                }
            }
        return data
    }

    fun removeAndFollowing(UserId: Long, type: Int, userRole: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.removeAndFollowing(UserId, type, userRole), { bean ->
            info.value = ""

        }) { throwable ->
            // error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                    info.value = ""
                }
            }
        }
        return info;
    }

    fun videoMissedCall(channelId: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.videoMissedCall(channelId), { bean ->
            info.value = ""

        }) { throwable ->
            // error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                    info.value = ""
                }
            }
        }
        return info;
    }


    /**
     * 扣除费用-每分钟
     */
    fun getVideoDeduct(
        activity: FragmentActivity?, channelId: String?, firstFlag: String?
    ): MutableLiveData<DeductBean> {
        val data = MutableLiveData<DeductBean>()
        repository!!.getVideoDeduct(channelId, firstFlag).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity)).`as`(AutoDispose.autoDisposable(this))
            .subscribe({ result: DeductBean ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
            }
        return data
    }

    fun evaluateLabelNeed(activity: FragmentActivity?, channelId: String): MutableLiveData<Any> {
        val data = MutableLiveData<Any>()
        repository!!.evaluateLabelNeed(channelId).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity)).`as`(AutoDispose.autoDisposable(this))
            .subscribe({ result: Any ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
            }
        return data
    }

    /**
     * 获取视频通话的token
     */
    fun getVideoToken(
        activity: FragmentActivity?, id: String?, userCode: String
    ): MutableLiveData<String> {
        val data = MutableLiveData<String>()
        repository!!.getVideoToken(id, userCode).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity)).`as`(AutoDispose.autoDisposable(this))
            .subscribe({ result: String ->
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
                throwable.printStackTrace()
                Logger.e("error ${throwable.message}")
            }
        return data
    }

    /**
     * 挂断视频通话
     */
    fun setVideoHangup(activity: CallVideoChatActivity, id: String?): MutableLiveData<String> {
        val data = MutableLiveData<String>()
        repository!!.setVideoHangup(id).map(ResultMap())
            .compose(RxErrorUtil.handleGlobalError(activity))
            .`as`(AutoDispose.autoDisposable(ResourceManager.activity?.scope()))
            .subscribe({ result: String ->
//                    experienceCourse.setValue(result);
                data.setValue(result)
            }) { throwable: Throwable ->
                // error.value = throwable
                if (throwable is ResultException) {
                    if (throwable.getCode() == BaseResult.SUCCESS) data.value = ""
                }
            }
        return data
    }

    public override fun getRepository(): VideoChatRepository {
        return VideoChatRepository()
    }
}