package com.mobile.anchor.app.main

import anchor.app.base.BaseApp
import anchor.app.base.bean.LoginBean
import anchor.app.base.bean.WordEntity
import anchor.app.base.core.Constants
import anchor.app.base.db.AppDatabase
import anchor.app.base.manager.OtherManager.Companion.manager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException
import anchor.app.base.utils.Logger
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import anchor.app.base.word.WordFilter
import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import io.rong.imkit.IMCenter
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageBlockType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainViewModel(application: Application) : BaseViewModel<MainRepository?>(application) {
    var checkStatus: MutableLiveData<Int> = MutableLiveData()
    var unReadCount: MutableLiveData<Int> = MutableLiveData()


    init {
        RongCoreClient.getInstance().setMessageBlockListener {//处理消息拦截违规
            if (it.type == MessageBlockType.BLOCK_THIRD_PATY) {//三方数美判定照片违规
                RongCoreClient.getInstance().getMessageByUid(
                    it.blockMsgUId, object : IRongCoreCallback.ResultCallback<Message?>() {
                        override fun onSuccess(t: Message?) {
                            t?.let {
                                val expansion =
                                    hashMapOf<String, String>(Constants.KEY.MESSAGE_EXPEND_KEY_BLOCK to "1")
                                RongIMClient.getInstance().updateMessageExpansion(
                                    expansion, it.uId, object : RongIMClient.OperationCallback() {
                                        override fun onSuccess() {
                                            Logger.e("updateMessageExpansion success")
                                            it.setExpansion(expansion)
                                            IMCenter.getInstance().refreshMessage(t)
                                        }

                                        override fun onError(errorCode: RongIMClient.ErrorCode?) {
                                            Logger.e("updateMessageExpansion error ${errorCode}")
                                        }

                                    })
                            }
                        }

                        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        }
                    })
            }
        }
    }

    /**
     * 第一次登陆成功上报数据
     */
    fun reportLoginSuccess() {
        SharePreUtil.setIsFirstLogin(false)
    }

    fun check() {
        repository!!.queryChildInfo()

        //        modequeryChildInfo
    }


    public override fun getRepository(): MainRepository {
        return MainRepository()
    }


    fun anchorPayInfo(): MutableLiveData<Any> {
        val info = MutableLiveData<Any>()
        dataStreamFliter.fliter(
            repository!!.anchorPayInfo(),
            { value: Any -> info.setValue(value) },
            { throwable: Throwable -> ToastUtil.show(throwable.message) })
        return info
    }

    fun anchorScenarioScript(activity: FragmentActivity?, userId: String?) {
        manager(activity!!).anchorScenarioScript(userId!!)
    }

    fun getAndInsertFilterWords() {
        viewModelScope.launch(Dispatchers.IO) {
            val wordDao = AppDatabase.getInstance(BaseApp.getAppContext()).wordDao()
            Logger.e("fetchAndInsertWords ${wordDao.getWordCount()}")
            if (wordDao.getWordCount() > 0) {
                WordFilter.getInstance().initializeTrie()
            } else {
                withContext(Dispatchers.Main) { //observeForever 需要在主线程注册
                    getSensitiveWords().observeForever { list ->
                        if (list.isNullOrEmpty()) {
                            Logger.e("getAndInsertFilterWords word is null or empty")
                        } else if (list.isNotEmpty()) {
                            viewModelScope.launch(Dispatchers.IO) {
                                Logger.e("fetchAndInsertWords response: ${list.size}")
                                runCatching {
                                    wordDao.insertWords(list)
                                    WordFilter.getInstance().initializeTrie(list)
                                }
                            }
                        }
                    }
                }

            }
        }
    }

    private fun getSensitiveWords(): MutableLiveData<MutableList<WordEntity>> {
        val info: MutableLiveData<MutableList<WordEntity>> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.getSensitiveWords(), { state ->
            info.value = state
        }) { throwable ->
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info
    }

    fun anchorAddVideo(anchorId: Long, url: String, channelId: String): MutableLiveData<Any> {
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.anchorAddVideo(anchorId, url, channelId), { state ->
            info.value = state
        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS) {
                } //{ info.value = "" }
            }
        }
        return info;
    }

    fun getAnchorDetail(): MutableLiveData<LoginBean> {
        val info: MutableLiveData<LoginBean> = MutableLiveData()
        dataStreamFliter.fliter(repository!!.getAnchorDetail(), { state ->
            UserInfoManager.setUser(state)
            info.value = state
        }) { throwable ->
        }
        return info
    }
}
