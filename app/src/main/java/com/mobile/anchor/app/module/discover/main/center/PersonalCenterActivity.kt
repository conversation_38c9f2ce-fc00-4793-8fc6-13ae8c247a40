package com.mobile.anchor.app.module.discover.main.center

import anchor.app.base.dialog.OperationTipDialog
import anchor.app.base.dialog.ReportDialogFragment
import anchor.app.base.ext.jump
import anchor.app.base.manager.OtherManager
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CountryUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import anchor.app.base.view.BigImagePreviewActivity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import com.bumptech.glide.Glide
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverHomeCenterBinding
import com.mobile.anchor.app.module.discover.main.center.adapter.PersonalAdapter
import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean
import com.mobile.anchor.app.module.discover.main.center.report.ReportActivity
import com.mobile.anchor.app.module.user.follow.FansListActivity
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ObservableSubscribeProxy
import io.rong.imlib.model.UserInfo

/**
 * 用户详情
 */
class PersonalCenterActivity : BaseActivity<PersonalCenterModel, DiscoverHomeCenterBinding?>() {
    private var fragments: Array<Fragment?> = arrayOfNulls(2)
    private var mFragmentAdapter: PersonalAdapter? = null
    var id: Long = 0


    var userRole = "0"
    var bean: PersonalCenterBean? = null
    override fun getLayoutId(): Int {
        return R.layout.discover_home_center
    }

    override fun initView() {
        id = intent.getLongExtra("id", 0)
        userRole = intent.getStringExtra("userRole").toString()

        bindingView?.activity = this
        bindingView?.lifecycleOwner = this
        setRoleVisi()
    }

    private fun setRoleVisi() {
        if (id == UserInfoManager.user()?.id) {
            bindingView?.isFollow?.visibility = View.GONE
            bindingView?.rlSc?.visibility = View.GONE
            bindingView?.mToolbarMenu?.visibility = View.GONE
            bindingView?.mToolbarMenu2?.visibility = View.GONE
        }
    }


    override fun loadData() {
        super.loadData()
        showContentView()
        viewModel.getPersonalCenter(this, id, userRole).observe(
            this
        ) { bean: PersonalCenterBean ->
            showContentView()
            <EMAIL> = bean
            bindingView?.areaImg?.setImageDrawable(
                CountryUtil.countryCodeToImage(
                    mContext, bean.country
                )
            )
            bindingView?.mToolBarTitle?.text = bean.nickName ?: "unknown"
            bindingView?.anchorName?.text = bean.nickName ?: "unknown"
            bindingView?.id?.text = bean.userCode ?: "unknown"
            bindingView?.followers?.text = bean.fansNum.toString() ?: "unknown"
            bindingView?.following?.text = bean.followNum.toString() ?: "unknown"
//            bindingView!!.price01.text = bean!!.videoPrice?.toString()?:"unknown"

            bean.level?.let { it.ifEmpty { null } }?.apply {
//                    Glide.with(mContext).load(loginBean.level).apply(RequestOptions.bitmapTransform(CircleCrop())).into(bindingView!!.level)
                if (this.toInt() > 0) {
                    bindingView!!.level.setImageResource(
                        resources.getIdentifier(
                            "base_ic_level_num_0$${this.toInt() - 1}", "mipmap", "anchormikchat.app"
                        )
                    )
                }
            }

            //用户信息有修改 更新数据IM用户信息
            val userInfo = UserInfo(
                bean.id.toString(), bean.nickName, Uri.parse(bean.headFileName)
            )
            val simpleBean = PersonalCenterBean().apply {
                nickName = bean.nickName
                headFileName = bean.headFileName
                id = bean.id
                level = bean.level
                userCode = bean.userRole
                username = bean.username
            };
            userInfo.extra = GsonUtil.GsonString(simpleBean)
            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_RONGYUN_CACHE, userInfo)
//            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)


//            if (bean?.followFlag == "1") {
//                bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_05))
//            } else {
//                bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_01))
//            }

            OtherManager.Companion.manager(this).getFollowFlag(id.toString()).observe(this) {
                bean?.followFlag = it
                if (it == "1") {
                    bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_05))
                } else {
                    bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_01))
                }
            }

            bean.headFileName?.let { it.ifEmpty { null } }
                ?.apply { Glide.with(mContext).load(bean.headFileName).into(bindingView!!.head) }
//            bindingView!!.head.setPicAndStaticHeadgear(bean!!.headFileName,"01",0,138,100)
            if (bean?.groundFileName != null && bean?.groundFileName!!.isNotEmpty()) {
                Glide.with(mContext).load(bean.groundFileName).into(bindingView!!.groundBG)
            } else if (bean?.headFileName != null && bean?.headFileName!!.isNotEmpty()) {
                Glide.with(mContext).load(bean.headFileName).into(bindingView!!.groundBG)
            } else {
                Glide.with(mContext).load(mContext.getDrawable(R.mipmap.discover_home_center_bg_09))
                    .into(bindingView!!.groundBG)
            }


            when (bean!!.onlineStatus) {
                "0" -> bindingView!!.state.setImageDrawable(getDrawable(R.mipmap.offline))
                "1" -> bindingView!!.state.setImageDrawable(getDrawable(R.mipmap.online))
                "2" -> bindingView!!.state.setImageDrawable(getDrawable(R.mipmap.busy))
            }


            if (userRole == "1") {
//                fragments[0] = ARouter.getInstance().build(ArouterPath.PATH_MOMENT_MOMENTFRAGMENT03).withBoolean("isShowNotification",false).withString("pageType", "0").withLong("id",id).navigation() as Fragment
//                fragments[0] = ARouter.getInstance().build(ArouterPath.PATH_MOMENT_MOMENTFRAGMENT).withBoolean("isShowNotification",false).withString("pageType", "0").withLong("id",id).navigation() as Fragment
//                fragments[1] = ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_PRIVATE_ALBUM).withLong("id",id).withSerializable("bean",bean ).navigation() as Fragment
                mFragmentAdapter = PersonalAdapter(
                    supportFragmentManager,
                    FragmentPagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT,
                    fragments
                )

//                mFragmentAdapter!!.setTitles(arrayOf("${getString(R.string.Moment)}("+bean!!.trendsNum+")", "${getString(R.string.Private_Album)}("+bean!!.imageNum+")"))
//                mFragmentAdapter!!.setTitles(arrayOf("Video("+bean!!.trendsNum+")", "${getString(R.string.Private_Album)}("+bean!!.imageNum+")"))
                mFragmentAdapter!!.setTitles(
                    arrayOf(
                        "Video", "${getString(R.string.Private_Album)}(" + bean!!.imageNum + ")"
                    )
                )
                bindingView!!.viewpager.offscreenPageLimit = 1

            } else {
//                fragments = arrayOfNulls(1)
//                fragments[0] = ARouter.getInstance().build(ArouterPath.PATH_MOMENT_MOMENTFRAGMENT)
//                    .withBoolean("isShowNotification", false).withString("pageType", "0").withLong("id", id)
//                    .navigation() as Fragment
//                mFragmentAdapter = PersonalAdapter(
//                    supportFragmentManager, FragmentPagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT, fragments
//                )
//                bindingView!!.mTabLayout.visibility = View.GONE
//                mFragmentAdapter!!.setTitles(arrayOf("${getString(R.string.Moment)}("+bean!!.trendsNum+")"))
                mFragmentAdapter?.setTitles(arrayOf("Video"))
            }
            bindingView?.viewpager?.adapter = mFragmentAdapter
            bindingView?.mTabLayout?.setupWithViewPager(bindingView?.viewpager)
            bindingView?.mAppBarLayout?.addOnOffsetChangedListener(OnOffsetChangedListener { appBarLayout: AppBarLayout, verticalOffset: Int ->
                if (this@PersonalCenterActivity != null && !isDestroyed) {
                    val alpha =
                        (appBarLayout.totalScrollRange - Math.abs(verticalOffset)).toFloat() / appBarLayout.totalScrollRange
                    setHeadViewAlpha(alpha)
                }
            })

        }

        RxBus.getDefault()
            .toObservable(RxCodeConstants.JUMP_TYPE_TO_MOMENTFRAGMENT_ITEM, String::class.java)
            .`as`<ObservableSubscribeProxy<String>>(
                AutoDispose.autoDisposable(scopeProvider)
            ).subscribe { str: String ->
                var hashMap = GsonUtil.GsonToBean(str, HashMap::class.java)
                if (hashMap["followFlag"] == "1") {
                    bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_05))
                } else {
                    bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_01))
                }

            }
    }

    private fun setHeadViewAlpha(alpha: Float) {
        if (bindingView!!.mToolBarBg == null) {
            return
        }
        println("setHeadViewAlpha:$alpha")
        bindingView!!.mToolBarBg.alpha = 1 - alpha
        bindingView!!.mToolBarTitle.alpha = 1 - alpha
        bindingView!!.mToolbarMenu2.alpha = 1 - alpha
        bindingView!!.mBackBtn2.alpha = 1 - alpha
        bindingView!!.mToolbarMenu.alpha = alpha
        bindingView!!.mBackBtn.alpha = alpha
    }


//    override fun onDestroy() {
//        super.onDestroy()
//        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_PAGE02, position)
//    }

    fun showHead(v: View) {
        bean?.headFileName?.let { it.ifEmpty { null } }?.apply {
            BigImagePreviewActivity.router(mContext, v, this)
        }
    }

    fun copyID() {
        val isCopy = copyClipboard(this, UserInfoManager.user()?.userCode.toString() + "")
    }


    fun copyClipboard(context: Context, copyStr: String?): Boolean {
        return try {
            //获取剪贴板管理器：
            val cm = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            // 创建普通字符型ClipData
            val mClipData = ClipData.newPlainText("Label", copyStr)
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData)
            ToastUtil.show(getResources().getString(R.string.b27))
            true
        } catch (e: Exception) {
            false
        }
    }


    /**
     * @return
     * @description 粉丝
     */
    fun isFollow() {
        if (bean?.followFlag == "1") {
            bean?.followFlag = "0"
            viewModel!!.removeAndFollowing(id, 0, "2")
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_01))
            bindingView!!.followers.text =
                (bindingView!!.followers.text.toString().toInt() - 1).toString()
        } else {
            bean?.followFlag = "1"
            viewModel!!.removeAndFollowing(id, 1, "2")
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_home_center_bg_05))
            bindingView!!.followers.text =
                (bindingView!!.followers.text.toString().toInt() + 1).toString()
        }

        var hashMap = HashMap<String, String>()
        hashMap["userId"] = id.toString()
        hashMap["followFlag"] = bean?.followFlag.toString()
        RxBus.getDefault()
            .post(RxCodeConstants.JUMP_TYPE_TO_MOMENTFRAGMENT_ITEM, GsonUtil.GsonString(hashMap))

    }

    fun follow(type: Int) {
        jump(FansListActivity::class.java, Bundle().apply {
            putInt("type", type)
            putLong("id", bean?.id ?: 0)
        })
    }

    fun tryVideo() {
//        var bean1 = AnchorItemBean.RecordsDTO()
//        bean1.nickName = bean?.nickName
//        bean1.headFileName = bean?.headFileName
//        bean1.videoPrice = bean?.videoPrice
//        bean1.groundFileName = bean?.groundFileName
//        bean1.country = bean?.country
//        bean1.onlineStatus = bean?.onlineStatus
//        bean1.id = bean?.id
//        bean1.followFlag = bean?.followFlag
//
//
//        if (DiamondManager.diamond()< bean1.videoPrice && UserInfoManager.user().freeVideoCall<=0) {
//            ToastUtil.show(getString(R.string.Your_current_balance_is_insufficient_please_recharge))
//        } else if (bean1.onlineStatus != "1"){
//            ToastUtil.show(getResources().getString(R.string.b28))
//
//        } else {
//            ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT)
//                .withString("userRole", userRole)
//                .withLong("id", bean1.getId())
//                .withInt("type", 1)
//                .withString("sourceType", "1")
//                .withSerializable("bean", bean1)
//                .navigation()
//        }

    }

    fun tryChat() {

        val hashMap: java.util.HashMap<String, String> = java.util.HashMap<String, String>()
        hashMap["headFileName"] = bean?.headFileName ?: ""
        hashMap["nikeName"] = bean!!.nickName
        hashMap["id"] = bean!!.id.toString()

        RxBus.getDefault()
            .post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(hashMap))
//        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, id.toString())


//        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_TO_REFRESH_UNREAD_COUNT, id.toString())
//        RouteUtils.routeToConversationActivity(this, Conversation.ConversationType.PRIVATE, id.toString(), false);
//
//        ARouter.getInstance().build(ArouterPath.PATH_IMKIT_CONVERSATION)
//            .withString("targetId", id)
//            .navigation()
    }

    var operationTipDialog: OperationTipDialog? = null
    fun report() {
        ReportDialogFragment { index ->
            if (index == 0) {
                operationTipDialog = OperationTipDialog {
                    viewModel!!.setBlack(id, userRole).observe(this) {
                        ToastUtil.show("Success")
                        // 空数据表示无更新
//                doFailure(new UpdateError(UpdateError.CHECK_UNKNOWN));
                        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_BLOCK, id)
                        finish()
                    }
                    operationTipDialog?.dismiss()

                }
                operationTipDialog?.content = getString(R.string.b90)
                operationTipDialog?.show(supportFragmentManager, "PersonalCenterActivity")

            } else if (index == 1) {
                jump(ReportActivity::class.java, Bundle().apply {
                    putString("reportUserId", id.toString())
                    putString("reportUserRole", userRole)
                })
            }

        }.show(<EMAIL>, "PersonalCenterActivity")


//        ARouter.getInstance().build(ArouterPath.PATH_USER_FEEDBACK).navigation()

    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(false, 0.2f).init()
    }

    companion object {
        const val TAG = "SettingActivity"
    }
}