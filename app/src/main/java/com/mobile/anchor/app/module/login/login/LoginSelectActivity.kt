package com.mobile.anchor.app.module.login.login

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.blankj.utilcode.util.SPUtils
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.R
import anchor.app.base.core.Constants
import anchor.app.base.core.Constants.HTTP_URL
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.makeGone
import anchor.app.base.ext.makeVisible
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.Logger
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.databinding.ActivityLoginSelectBinding
import com.mobile.anchor.app.util.FirebaseAuthManager
import com.mobile.anchor.app.module.update.impl.ZlUpdateHelper
import com.mobile.anchor.app.module.user.agreement.AgreementActivity


/**
 * 登录页面
 */
class LoginSelectActivity : BaseActivity<LoginViewModel?, ActivityLoginSelectBinding>() {

    val loginType get() = intent.getIntExtra("loginType", 0)//0代表绑定，1代表登录

    val from get() = intent.getStringExtra("from")//retry代表是token失效需要登录重新获取token

    private val mAreaCode = "86"
    private lateinit var signInLauncher: ActivityResultLauncher<Intent>

    override fun getLayoutId(): Int {
        return R.layout.activity_login_select
    }

    override fun fitsSystemWindows(): Boolean {
        return false
    }

    override fun isDarkMode(): Boolean {
        return false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        signInLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            Logger.i("google login result: ${result}")
            if (result.resultCode == Activity.RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                Logger.e("login result: ${task.result.id}")
                viewModel?.loginByThird(this, "1", task.result.idToken)
            }
        }
    }

    override fun initView() {
        ZlUpdateHelper.update(mContext)

        viewModel?.loginType = loginType
        viewModel?.from = from
        showContentView()
        bindingView?.viewModel = viewModel
        bindingView?.activity = this
        bindingView?.lifecycleOwner = this

        //获取上次是否同意了协议
        val isSelected = SPUtils.getInstance().getBoolean("lastLoginChecked", false)
        bindingView?.checkbox?.isSelected = isSelected

        bindingView.tvSignIn.click {
            bindingView.LL00.makeGone()
            bindingView.LL01.makeVisible()
        }
        bindingView.tvRegister.click {
            select(1)
        }

        bindingView.tvBack.click {
            bindingView.LL00.makeVisible()
            bindingView.LL01.makeGone()
        }

        buttonStateCheck()

        bindingView.tvCheckboxText1.setOnClickListener {
            checkbox(bindingView!!.checkbox)
        }

        bindingView.llGoogleLogin.click {
            if (!bindingView!!.checkbox.isSelected) {
                ToastUtil.show(getString(R.string.a001))
                return@click
            }
            SPUtils.getInstance().put("lastLoginMethod", "google")
            buttonStateCheck()
            signInLauncher.launch(FirebaseAuthManager.authGoogle(this))
        }

        bindingView.llFacebookLogin.click {
            if (!bindingView!!.checkbox.isSelected) {
                ToastUtil.show(getString(R.string.a001))
                return@click
            }
            SPUtils.getInstance().put("lastLoginMethod", "facebook")
            buttonStateCheck()
        }
        bindingView.llEmailLogin.click {
            select(2)
        }
        if (BuildConfig.DEBUG) {
            bindingView.llGoogleLogin.makeGone()
        }
    }

    fun buttonStateCheck() {
        //获取上次登录方式
        var loginLast = SPUtils.getInstance().getString("lastLoginMethod", "")

        bindingView.llGoogleLogin.isSelected = loginLast == "google"
        bindingView.llFacebookLogin.isSelected = loginLast == "facebook"
        bindingView.llEmailLogin.isSelected = loginLast == "email"
    }

    fun select(type: Int) {
        if (!bindingView!!.checkbox.isSelected) {
            ToastUtil.show(getString(R.string.a001))
            return
        }

        SPUtils.getInstance().put("lastLoginMethod", "email")

        when (type) {
            1 -> {
                jump(LoginActivity::class.java)
            }

            2 -> {
                jump(LoginActivity::class.java)
                buttonStateCheck()
            }
        }
    }

    fun treaty(type: Int) {
        if (type == 1) {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", HTTP_URL.Useragreement)
                putString("title", resources.getString(R.string.User_agreement_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        } else {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", HTTP_URL.PrivatePolicy)
                putString("title", resources.getString(R.string.Privacy_policy_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        }
    }

    fun checkbox(checkbox: View) {
        checkbox.isSelected = !checkbox.isSelected
        SPUtils.getInstance().put("lastLoginChecked", checkbox.isSelected)
    }
}