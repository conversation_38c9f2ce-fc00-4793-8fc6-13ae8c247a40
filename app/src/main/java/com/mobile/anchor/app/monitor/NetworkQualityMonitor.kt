package com.mobile.anchor.app.monitor

import anchor.app.base.R
import anchor.app.base.utils.Logger
import anchor.app.base.utils.SharePreUtil
import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.graphics.Color
import android.media.RingtoneManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.telephony.TelephonyManager
import androidx.annotation.RequiresPermission
import androidx.core.app.NotificationCompat
import io.rong.imkit.RongIM
import io.rong.imlib.RongIMClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/5/22 15:40
 * @description: 增强版网络环境质量监控
 */
class NetworkQualityMonitor private constructor(private val context: Context) {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var monitorJob: Job? = null
    private var checkIntervalMillis: Long = 60_000L // 默认每 60 秒检测一次

    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val telephonyManager =
        context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

    // 网络质量检测配置
    private val networkConfig = NetworkQualityConfig()

    // 网络状态历史记录
    private val networkHistory = mutableListOf<NetworkQualityResult>()
    private val maxHistorySize = 10

    // 上次通知时间，避免频繁通知
    private var lastNotificationTime = 0L
    private val notificationCooldown = 1 * 60 * 1000L // 1分钟冷却时间

    companion object {
        private const val TAG = "NetworkQualityMonitor"
        private const val NOTIFICATION_CHANNEL_ID = "network_quality_monitor"
        private const val NOTIFICATION_ID = 1001

        @Volatile
        private var INSTANCE: NetworkQualityMonitor? = null

        fun getInstance(context: Context): NetworkQualityMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkQualityMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * 网络质量配置类
     */
    data class NetworkQualityConfig(
        val pingTimeout: Int = 3000,
        val httpTimeout: Int = 5000,
        val weakNetworkThreshold: Long = 1000L, // 1秒
        val poorNetworkThreshold: Long = 500L,  // 500ms
        val testUrls: List<String> = listOf(
            "https://www.baidu.com",
            "https://www.google.com",
            "https://api.mindmate.vip",
            "http://www.msftconnecttest.com/connecttest.txt"
        ),
        val pingHosts: List<String> = listOf(
            "*******", "***************", "www.baidu.com", "api.mindmate.vip"
        )
    )

    /**
     * 网络质量检测结果
     */
    data class NetworkQualityResult(
        val timestamp: Long,
        val networkType: NetworkType,
        val isConnected: Boolean,
        val pingLatency: Long, // -1表示ping失败
        val httpLatency: Long, // -1表示HTTP测试失败
        val qualityLevel: NetworkQuality,
        val signalStrength: Int = -1, // 移动网络信号强度
        val linkSpeed: Int = -1 // WiFi连接速度 Mbps
    )

    enum class NetworkType {
        WIFI, CELLULAR_5G, CELLULAR_4G, CELLULAR_3G, CELLULAR_2G, ETHERNET, UNKNOWN, NONE
    }

    enum class NetworkQuality {
        EXCELLENT, GOOD, FAIR, POOR, VERY_POOR, NO_CONNECTION
    }

    private val connectionStatusListener = RongIMClient.ConnectionStatusListener { status ->
        when (status) {
            RongIMClient.ConnectionStatusListener.ConnectionStatus.NETWORK_UNAVAILABLE,
            RongIMClient.ConnectionStatusListener.ConnectionStatus.UNCONNECTED,
            RongIMClient.ConnectionStatusListener.ConnectionStatus.SUSPEND,
            RongIMClient.ConnectionStatusListener.ConnectionStatus.TIMEOUT -> {
                Logger.w(TAG, "RongIM connection issue: $status")
                // 检查网络是否可用，如果网络可用但融云未连接，则尝试重连
                if (isNetworkAvailable()) {
                    reconnectRongIM()
                }
            }
            else -> {
                Logger.d(TAG, "RongIM connection status: $status")
            }
        }
    }

    fun startMonitoring(intervalMillis: Long = checkIntervalMillis) {
        checkIntervalMillis = intervalMillis
        if (monitorJob?.isActive == true) return

        RongIM.setConnectionStatusListener(connectionStatusListener)
        createNotificationChannel()

        monitorJob = scope.launch {
            while (isActive) {
                val result = performNetworkQualityCheck()
                handleNetworkQualityResult(result)

                // 检查融云连接状态
                if (result.isConnected) {
                    checkRongIMConnection()
                }

                delay(checkIntervalMillis)
            }
        }
        Logger.i(TAG, "Network quality monitoring started with interval: ${intervalMillis}ms")
    }

    fun stopMonitoring() {
        monitorJob?.cancel()
        Logger.i(TAG, "Network quality monitoring stopped")
    }

    /**
     * 执行综合网络质量检测
     */
    private suspend fun performNetworkQualityCheck(): NetworkQualityResult {
        val timestamp = System.currentTimeMillis()
        val networkType = getDetailedNetworkType()
        val isConnected = isNetworkAvailable()

        if (!isConnected) {
            return NetworkQualityResult(
                timestamp = timestamp,
                networkType = NetworkType.NONE,
                isConnected = false,
                pingLatency = -1,
                httpLatency = -1,
                qualityLevel = NetworkQuality.NO_CONNECTION
            )
        }

        // 并行执行ping和HTTP测试
        val pingLatency = measureBestPingLatency()
        val httpLatency = measureBestHttpLatency()
        val qualityLevel = calculateNetworkQuality(networkType, pingLatency, httpLatency)

        return NetworkQualityResult(
            timestamp = timestamp,
            networkType = networkType,
            isConnected = true,
            pingLatency = pingLatency,
            httpLatency = httpLatency,
            qualityLevel = qualityLevel,
            signalStrength = getSignalStrength(),
            linkSpeed = getLinkSpeed()
        )
    }

    /**
     * 获取详细的网络类型
     */
    private fun getDetailedNetworkType(): NetworkType {
        val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
        val capabilities =
            connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE

        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                getCellularNetworkType()
            }
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
            else -> NetworkType.UNKNOWN
        }
    }

    /**
     * 获取移动网络类型
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    private fun getCellularNetworkType(): NetworkType {
        return try {
            when (telephonyManager.networkType) {
                TelephonyManager.NETWORK_TYPE_GPRS, TelephonyManager.NETWORK_TYPE_EDGE,
                TelephonyManager.NETWORK_TYPE_CDMA, TelephonyManager.NETWORK_TYPE_1xRTT,
                TelephonyManager.NETWORK_TYPE_IDEN -> NetworkType.CELLULAR_2G

                TelephonyManager.NETWORK_TYPE_UMTS, TelephonyManager.NETWORK_TYPE_EVDO_0,
                TelephonyManager.NETWORK_TYPE_EVDO_A, TelephonyManager.NETWORK_TYPE_HSDPA,
                TelephonyManager.NETWORK_TYPE_HSUPA, TelephonyManager.NETWORK_TYPE_HSPA,
                TelephonyManager.NETWORK_TYPE_EVDO_B, TelephonyManager.NETWORK_TYPE_EHRPD,
                TelephonyManager.NETWORK_TYPE_HSPAP -> NetworkType.CELLULAR_3G

                TelephonyManager.NETWORK_TYPE_LTE -> NetworkType.CELLULAR_4G
                TelephonyManager.NETWORK_TYPE_NR -> NetworkType.CELLULAR_5G
                else -> NetworkType.CELLULAR_4G // 默认4G
            }
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to get cellular network type: ${e.message}")
            NetworkType.CELLULAR_4G
        }
    }

    /**
     * 测试多个主机的ping延迟，返回最佳结果
     */
    private suspend fun measureBestPingLatency(): Long = withContext(Dispatchers.IO) {
        var bestLatency = Long.MAX_VALUE

        networkConfig.pingHosts.forEach { host ->
            val latency = ping(host)
            if (latency != null && latency < bestLatency) {
                bestLatency = latency
            }
        }

        if (bestLatency == Long.MAX_VALUE) -1L else bestLatency
    }

    /**
     * 测试多个URL的HTTP延迟，返回最佳结果
     */
    private suspend fun measureBestHttpLatency(): Long = withContext(Dispatchers.IO) {
        var bestLatency = Long.MAX_VALUE

        networkConfig.testUrls.forEach { url ->
            val latency = httpConnectTest(url)
            if (latency != null && latency < bestLatency) {
                bestLatency = latency
            }
        }

        if (bestLatency == Long.MAX_VALUE) -1L else bestLatency
    }

    /**
     * HTTP连接测试
     */
    private suspend fun httpConnectTest(urlString: String): Long? = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            val url = URL(urlString)
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = networkConfig.httpTimeout
            connection.readTimeout = networkConfig.httpTimeout
            connection.requestMethod = "HEAD"
            connection.useCaches = false

            val responseCode = connection.responseCode
            val endTime = System.currentTimeMillis()
            connection.disconnect()

            if (responseCode in 200..299) {
                endTime - startTime
            } else {
                null
            }
        } catch (e: IOException) {
            Logger.d(TAG, "HTTP test failed for $urlString: ${e.message}")
            null
        }
    }

    /**
     * 计算网络质量等级
     */
    private fun calculateNetworkQuality(
        networkType: NetworkType, pingLatency: Long, httpLatency: Long
    ): NetworkQuality {
        // 基于网络类型的基础判断
        val baseQuality = when (networkType) {
            NetworkType.CELLULAR_2G -> NetworkQuality.POOR
            NetworkType.CELLULAR_3G -> NetworkQuality.FAIR
            NetworkType.NONE -> NetworkQuality.NO_CONNECTION
            else -> NetworkQuality.GOOD
        }

        // 如果ping和HTTP都失败，网络质量很差
        if (pingLatency == -1L && httpLatency == -1L) {
            return NetworkQuality.VERY_POOR
        }

        // 使用最好的延迟结果进行判断
        val bestLatency = when {
            pingLatency != -1L && httpLatency != -1L -> minOf(pingLatency, httpLatency)
            pingLatency != -1L -> pingLatency
            httpLatency != -1L -> httpLatency
            else -> Long.MAX_VALUE
        }

        val latencyQuality = when {
            bestLatency <= 100 -> NetworkQuality.EXCELLENT
            bestLatency <= 300 -> NetworkQuality.GOOD
            bestLatency <= networkConfig.poorNetworkThreshold -> NetworkQuality.FAIR
            bestLatency <= networkConfig.weakNetworkThreshold -> NetworkQuality.POOR
            else -> NetworkQuality.VERY_POOR
        }

        // 返回基础质量和延迟质量中较差的一个
        return if (baseQuality.ordinal > latencyQuality.ordinal) baseQuality else latencyQuality
    }

    /**
     * 获取信号强度（移动网络）
     */
    private fun getSignalStrength(): Int {
        return try {
            // 这里需要根据实际需求实现信号强度获取
            // 可能需要额外的权限和API调用
            -1
        } catch (e: Exception) {
            -1
        }
    }

    /**
     * 获取WiFi连接速度
     */
    private fun getLinkSpeed(): Int {
        return try {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    capabilities.linkUpstreamBandwidthKbps / 1000 // 转换为Mbps
                } else {
                    -1
                }
            } else {
                -1
            }
        } catch (e: Exception) {
            -1
        }
    }

    /**
     * 处理网络质量检测结果
     */
    private fun handleNetworkQualityResult(result: NetworkQualityResult) {
        // 添加到历史记录
        addToHistory(result)

        Logger.d(TAG, "Network quality check result: $result")

        // 简化通知逻辑，确保通知能够弹出
        when (result.qualityLevel) {
            NetworkQuality.NO_CONNECTION -> {
                // 无网络连接时立即通知
                showNotification(
                    context.getString(R.string.network_unavailable),
                    context.getString(R.string.please_check_your_network_connection),
                    NotificationCompat.PRIORITY_HIGH
                )
            }
            NetworkQuality.VERY_POOR, NetworkQuality.POOR -> {
                // 网络质量差时通知（简化条件判断）
                val message = buildNetworkQualityMessage(result)
                showNotification(
                    context.getString(R.string.poor_network_quality),
                    message,
                    NotificationCompat.PRIORITY_HIGH
                )
            }
            else -> {
                // 网络质量良好，无需通知
                Logger.d(TAG, "Network quality is good: ${result.qualityLevel}")
            }
        }
    }

    /**
     * 构建网络质量消息
     */
    private fun buildNetworkQualityMessage(result: NetworkQualityResult): String {
        val sb = StringBuilder()
        sb.append("Network Type: ${getNetworkTypeDescription(result.networkType)}")

        if (result.pingLatency > 0) {
            sb.append("\nPing Latency: ${result.pingLatency}ms")
        }
        if (result.httpLatency > 0) {
            sb.append("\nHTTP Latency: ${result.httpLatency}ms")
        }

        sb.append("\nSuggestion: ${getNetworkSuggestion(result.qualityLevel)}")

        return sb.toString()
    }

        private fun getNetworkTypeDescription(type: NetworkType): String {
            return when (type) {
                NetworkType.WIFI -> context.getString(com.mobile.anchor.app.R.string.wifi)
                NetworkType.CELLULAR_5G -> context.getString(com.mobile.anchor.app.R.string._5g_mobile_network)
                NetworkType.CELLULAR_4G -> context.getString(com.mobile.anchor.app.R.string._4g_mobile_network)
                NetworkType.CELLULAR_3G -> context.getString(com.mobile.anchor.app.R.string._3g_mobile_network)
                NetworkType.CELLULAR_2G -> context.getString(com.mobile.anchor.app.R.string._2g_mobile_network)
                NetworkType.ETHERNET -> context.getString(com.mobile.anchor.app.R.string.ethernet)
                NetworkType.UNKNOWN -> context.getString(com.mobile.anchor.app.R.string.unknown_network)
                NetworkType.NONE -> context.getString(com.mobile.anchor.app.R.string.no_network_connection)
            }
        }

        private fun getNetworkSuggestion(quality: NetworkQuality): String {
            return when (quality) {
                NetworkQuality.NO_CONNECTION -> context.getString(com.mobile.anchor.app.R.string.please_check_your_network_connection)
                NetworkQuality.VERY_POOR -> context.getString(com.mobile.anchor.app.R.string.it_is_recommended_to_switch_to_wifi_or_a_location_with_better_signal)
                NetworkQuality.POOR -> context.getString(com.mobile.anchor.app.R.string.the_current_network_is_slow_and_may_affect_your_experience)
                NetworkQuality.FAIR -> context.getString(com.mobile.anchor.app.R.string.the_network_is_average_consider_optimizing_your_network_environment)
                NetworkQuality.GOOD -> context.getString(com.mobile.anchor.app.R.string.the_network_is_good)
                NetworkQuality.EXCELLENT -> context.getString(com.mobile.anchor.app.R.string.the_network_is_excellent)
            }
        }

    /**
     * 添加到历史记录
     */
    private fun addToHistory(result: NetworkQualityResult) {
        networkHistory.add(result)
        if (networkHistory.size > maxHistorySize) {
            networkHistory.removeAt(0)
        }
    }

    /**
     * 获取网络质量趋势
     */
    fun getNetworkQualityTrend(): String {
        if (networkHistory.isEmpty()) return "暂无数据"

        val recent = networkHistory.takeLast(5)
        val avgPing = recent.filter { it.pingLatency > 0 }.map { it.pingLatency }.average()
        val avgHttp = recent.filter { it.httpLatency > 0 }.map { it.httpLatency }.average()

        return "近期平均Ping: ${avgPing.toInt()}ms, HTTP: ${avgHttp.toInt()}ms"
    }

    private fun checkRongIMConnection() {
        val currentStatus = RongIM.getInstance().currentConnectionStatus
        Logger.d(TAG, "checkRongIMConnection current status: $currentStatus")

        if (currentStatus != RongIMClient.ConnectionStatusListener.ConnectionStatus.CONNECTED) {
            Logger.e(TAG, "RongIMClient is not connected, try to reconnect.")
            reconnectRongIM()
        } else {
            Logger.d(TAG, "RongIMClient is connected")
        }
    }

    private fun reconnectRongIM() {
        val currentStatus = RongIM.getInstance().currentConnectionStatus
        if (currentStatus == RongIMClient.ConnectionStatusListener.ConnectionStatus.CONNECTED) {
            Logger.d(TAG, "RongIMClient 已连接，无需重连。")
            return
        }

        val token = SharePreUtil.getRongToken()
        if (!SharePreUtil.isLogin() || token.isNullOrEmpty()) {
            Logger.e(TAG, "RongIMClient Token 无效，用户未登录或token为空。")
            return
        }

        Logger.d(TAG, "开始重新连接 RongIMClient...")
        RongIM.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onDatabaseOpened(status: RongIMClient.DatabaseOpenStatus) {
                Logger.d(TAG, "RongIMClient onDatabaseOpened: $status")
            }

            override fun onSuccess(userId: String) {
                Logger.d(TAG, "RongIMClient 融云连接已恢复。userId: $userId")
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode) {
                when (errorCode) {
                    RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT -> {
                        Logger.e(TAG, "RongIMClient Token 已过期，请重新登录。")
                    }
                    RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT -> {
                        Logger.e(TAG, "RongIMClient 连接超时，请检查网络设置。")
                    }
                    else -> {
                        Logger.e(TAG, "RongIMClient 连接失败：${errorCode.name}")
                    }
                }
            }
        })
    }

    private fun isNetworkAvailable(): Boolean {
        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } catch (e: Exception) {
            Logger.e(TAG, "Error checking network availability: ${e.message}")
            false
        }
    }

    private fun ping(host: String): Long? {
        return try {
            val start = System.currentTimeMillis()
            val process = Runtime.getRuntime().exec("ping -c 1 -W ${networkConfig.pingTimeout / 1000} $host")

            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val output = reader.readText()
            reader.close()

            val exitCode = process.waitFor()

            Logger.d(TAG, "Ping $host - Exit code: $exitCode")

            if (exitCode == 0 && (output.contains("1 received") ||
                        output.contains("64 bytes from") ||
                        output.contains("1 packets transmitted, 1 received"))) {
                val latency = System.currentTimeMillis() - start
                Logger.d(TAG, "Ping $host successful: ${latency}ms")
                latency
            } else {
                Logger.d(TAG, "Ping $host failed")
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, "Ping $host error: ${e.message}")
            null
        }
    }

    private fun createNotificationChannel() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            val channel = NotificationChannel(
//                NOTIFICATION_CHANNEL_ID, "网络质量监控", NotificationManager.IMPORTANCE_HIGH
//            ).apply {
//                description = "通知网络不可用或质量差的情况"
//            }
//            val notificationManager =
//                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//            notificationManager.createNotificationChannel(channel)
//        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                context.getString(R.string.network_quality_monitor),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = context.getString(R.string.notifications_for_network_unavailability)
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
                // 关键设置：允许通知悬浮显示
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                setBypassDnd(true)  // 绕过勿扰模式
                // 设置声音
                setSound(
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION),
                    Notification.AUDIO_ATTRIBUTES_DEFAULT
                )
                // 设置震动模式
                vibrationPattern = longArrayOf(0, 500, 1000)
            }
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Logger.d(TAG, "Notification channel created")
        }
    }

    private fun showNotification(
        title: String,
        message: String,
        priority: Int = NotificationCompat.PRIORITY_HIGH
    ) {
        try {
//            val notificationManager =
//                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//
//            val notification = NotificationCompat.Builder(context,
//                NOTIFICATION_CHANNEL_ID
//            )
//                .setSmallIcon(android.R.drawable.stat_notify_error).setContentTitle(title)
//                .setContentText(message).setPriority(NotificationCompat.PRIORITY_HIGH)
//                .setAutoCancel(true).build()
//
//            notificationManager.notify(NOTIFICATION_ID, notification)

            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_notify_error)
                .setContentTitle(title)
                .setContentText(message)
                .setStyle(NotificationCompat.BigTextStyle().bigText(message))
                .setPriority(NotificationCompat.PRIORITY_HIGH)  // 强制使用最高优先级
                .setCategory(NotificationCompat.CATEGORY_ALARM)  // 设置为警报类别
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                // 关键设置：让通知能够悬浮显示
                .setFullScreenIntent(null, true)  // 全屏意图（可以为null）
                .setTimeoutAfter(10000)  // 10秒后自动消失
                // 增加震动和声音
                .setVibrate(longArrayOf(0, 500, 1000))
                .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
                // 设置LED灯
                .setLights(Color.RED, 1000, 1000)
                // 显示时间戳
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .build()

            notificationManager.notify(NOTIFICATION_ID, notification)
            Logger.d(TAG, "Notification shown successfully")
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to show notification: ${e.message}")
        }
    }
}