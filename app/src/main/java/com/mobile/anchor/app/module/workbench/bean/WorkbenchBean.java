package com.mobile.anchor.app.module.workbench.bean;

public class WorkbenchBean {


    private String anchorRule;
    private String avgMonthCallTime;
    private String beginnerTutorial;
    private String connectDayRate;
    private String connectMonthRate;
    private String helpMessage;
    private String onlineStatus;
    private String matchStatus;
    private String workDayTime;
    private String workMonthTime;
    private String workingStandard;
    private String workWeekTime;
    private String avgWeekCallTime;
    private String connectWeekRate;

    private String totalMatchCallCompletionRate;
    private String todayMatchCallCompletionRate;
    private String totalVideoCallCompletionRate;
    private String todayVideoCallCompletionRate;

    public String getAnchorRule() {
        return anchorRule;
    }

    public void setAnchorRule(String anchorRule) {
        this.anchorRule = anchorRule;
    }

    public String getAvgMonthCallTime() {
        return avgMonthCallTime;
    }

    public void setAvgMonthCallTime(String avgMonthCallTime) {
        this.avgMonthCallTime = avgMonthCallTime;
    }

    public String getBeginnerTutorial() {
        return beginnerTutorial;
    }

    public void setBeginnerTutorial(String beginnerTutorial) {
        this.beginnerTutorial = beginnerTutorial;
    }

    public String getConnectDayRate() {
        return connectDayRate;
    }

    public void setConnectDayRate(String connectDayRate) {
        this.connectDayRate = connectDayRate;
    }

    public String getConnectMonthRate() {
        return connectMonthRate;
    }

    public void setConnectMonthRate(String connectMonthRate) {
        this.connectMonthRate = connectMonthRate;
    }

    public String getHelpMessage() {
        return helpMessage;
    }

    public void setHelpMessage(String helpMessage) {
        this.helpMessage = helpMessage;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getWorkDayTime() {
        return workDayTime;
    }

    public void setWorkDayTime(String workDayTime) {
        this.workDayTime = workDayTime;
    }

    public String getWorkMonthTime() {
        return workMonthTime;
    }

    public void setWorkMonthTime(String workMonthTime) {
        this.workMonthTime = workMonthTime;
    }

    public String getWorkingStandard() {
        return workingStandard;
    }

    public void setWorkingStandard(String workingStandard) {
        this.workingStandard = workingStandard;
    }

    public String getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(String matchStatus) {
        this.matchStatus = matchStatus;
    }

    public String getWorkWeekTime() {
        return workWeekTime;
    }

    public void setWorkWeekTime(String workWeekTime) {
        this.workWeekTime = workWeekTime;
    }

    public String getAvgWeekCallTime() {
        return avgWeekCallTime;
    }

    public void setAvgWeekCallTime(String avgWeekCallTime) {
        this.avgWeekCallTime = avgWeekCallTime;
    }

    public String getConnectWeekRate() {
        return connectWeekRate;
    }

    public void setConnectWeekRate(String connectWeekRate) {
        this.connectWeekRate = connectWeekRate;
    }

    public String getTotalMatchCallCompletionRate() {
        return totalMatchCallCompletionRate;
    }

    public void setTotalMatchCallCompletionRate(String totalMatchCallCompletionRate) {
        this.totalMatchCallCompletionRate = totalMatchCallCompletionRate;
    }

    public String getTodayMatchCallCompletionRate() {
        return todayMatchCallCompletionRate;
    }

    public void setTodayMatchCallCompletionRate(String todayMatchCallCompletionRate) {
        this.todayMatchCallCompletionRate = todayMatchCallCompletionRate;
    }

    public String getTotalVideoCallCompletionRate() {
        return totalVideoCallCompletionRate;
    }

    public void setTotalVideoCallCompletionRate(String totalVideoCallCompletionRate) {
        this.totalVideoCallCompletionRate = totalVideoCallCompletionRate;
    }

    public String getTodayVideoCallCompletionRate() {
        return todayVideoCallCompletionRate;
    }

    public void setTodayVideoCallCompletionRate(String todayVideoCallCompletionRate) {
        this.todayVideoCallCompletionRate = todayVideoCallCompletionRate;
    }
}
