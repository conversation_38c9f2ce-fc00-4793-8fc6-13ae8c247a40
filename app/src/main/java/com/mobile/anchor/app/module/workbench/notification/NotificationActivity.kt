package com.mobile.anchor.app.module.workbench.notification

import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.retrofit.RxSchedulers
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.FileUtil
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityNotificationListBinding
import com.mobile.anchor.app.module.workbench.adapter.NotificationAdapter
import com.mobile.anchor.app.module.workbench.bean.NotificationBean
import com.uber.autodispose.AutoDispose
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

class NotificationActivity : BaseActivity<NotificationModel, ActivityNotificationListBinding>(),
    ItemClickPresenter<NotificationBean>, ItemDecorator {

    var adapter: NotificationAdapter? = null
    var dataList = ObservableArrayList<NotificationBean>()

    val id: Long? get() = intent?.getLongExtra("id", 0)

    var current = 1
    var size = 5

    override fun getLayoutId(): Int {
        return R.layout.activity_notification_list
    }

    override fun initView() {
        //        pageType = getArguments().getInt("pageType");
        bindingView.activity = this
        bindingView.lifecycleOwner = this

        adapter = NotificationAdapter(mContext, dataList)

        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this

        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView.recyclerView.layoutManager = gridLayoutManager
        bindingView.recyclerView.adapter = adapter

        RxBus.getDefault().toObservable(RxCodeConstants.JUMP_TYPE_NOTIFICATION, String::class.java)
            .observeOn(RxSchedulers.ui).`as`(
                AutoDispose.autoDisposable(scopeProvider)
            ).subscribe { hashMapStr: String ->
                loadData()
            }
        clearMessage()
    }

    var nameList = ArrayList<Long>()

    override fun loadData() {
        super.loadData()
        nameList.clear()
        dataList.clear()
        val file = File(PathUtils.getPathNotification(), UserInfoManager.user()?.id.toString())
        val listFiles = file.listFiles()
        if (listFiles != null && listFiles.size > 0) {
            listFiles.forEach {
                if (it.isFile) {
                    nameList.add(it.name.toLong())
                }
            }
        }

        if (nameList.size > 0) {
            nameList.sortWith(Comparator.reverseOrder())
            nameList.forEach {
                val file = File(
                    PathUtils.getPathNotification() + "/" + UserInfoManager.user()?.id.toString(),
                    it.toString()
                )
                val readFileToString = FileUtil.readFileToString(file.absolutePath)
                val bean = GsonUtil.GsonToBean(readFileToString, NotificationBean::class.java)
                bean?.let {
                    dataList.add(it)
                }
                adapter?.notifyDataSetChanged()
            }
        }
        showContentView()
    }

    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: NotificationBean) {
        var bean = item
        bean.isShow = false
        FileUtil.writeFileToString(bean.itemPath, bean.itemName, GsonUtil.GsonString(bean))

        //        刷新小红点
        val readMoment = SharePreUtil.getReadMoment()

        SharePreUtil.setReadMoment((if (readMoment <= 0) 0 else readMoment - 1))
        RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_BADGE, "0")
        adapter?.notifyDataSetChanged()
    }

    private fun clearMessage(delay: Long = 1200) {
        lifecycleScope.launch {
            delay(delay)
            SharePreUtil.setReadMoment(0)
            dataList.forEach {
                it.isShow = false
                FileUtil.writeFileToString(it.itemPath, it.itemName, GsonUtil.GsonString(it))
            }
            RxBus.getDefault().post(RxCodeConstants.JUMP_TYPE_REFRESH_BADGE, "0")
            adapter?.notifyDataSetChanged()
        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理
     */
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(false, 0.2f)
            .navigationBarColor(R.color.colorTheme).init()
    }

    override fun onStop() {
        super.onStop()
        clearMessage(0)
    }
}