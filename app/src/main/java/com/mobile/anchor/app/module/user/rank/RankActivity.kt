package com.mobile.anchor.app.module.user.rank

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.View
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityRankBinding
import com.mobile.anchor.app.databinding.UserItemRank01Binding
import com.mobile.anchor.app.databinding.UserItemRank02Binding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.module.user.bean.RankBean
import com.mobile.anchor.app.module.user.dialog.RankRuleDialogFragment
import com.mobile.anchor.app.module.user.rank.adapter.RankItemAdapter


/**
 * 设置页面
 */
class RankActivity : BaseActivity<RankModel?, ActivityRankBinding?>(), ItemClickPresenter<Any>, ItemDecorator {

    var adapter: RankItemAdapter? = null
    var adapter01: RankItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var dataList01: ObservableArrayList<Any>? = null

    override fun getLayoutId(): Int {
        return R.layout.activity_rank
    }

    override fun initView() {
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        showContentView()

        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this

        dataList = ObservableArrayList<Any>()
        dataList01 = ObservableArrayList<Any>()

        adapter = RankItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter

        adapter01 = RankItemAdapter(mContext, dataList01!!)
        adapter01!!.itemDecorator = this
        adapter01!!.itemPresenter = this
        val gridLayoutManager01 = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView01.layoutManager = gridLayoutManager01
        bindingView!!.recyclerView01.adapter = adapter01
    }


    override fun loadData() {
        showLoading()
        loadData2("0")
        loadData3("0")
    }

    @SuppressLint("RestrictedApi", "NotifyDataSetChanged")
    private fun loadData2(type: String) {
        viewModel!!.getList(UserInfoManager.user()?.id.toString(), type).observe(
            this
        ) { bean: RankBean ->
            showContentView()
            dataList?.clear()

            initTopThree()
            if (bean.rankList.size > 0) {
                setTopThree(bean.rankList)
            }

            adapter!!.notifyDataSetChanged()

            setTitle(type)
//            bindingView!!.marqueeView.startWithList(messages)
            if (bean.anchorRank.anchorId != null) {
                bindingView!!.CLSelf.visibility = View.VISIBLE

                bindingView!!.consumptionType.text = bean.anchorRank.nickName
                bindingView!!.nu.text = bean.anchorRank.ranks.toString()
                bindingView!!.idSelf.text = bean.anchorRank.userCode.toString()
                bindingView!!.giftSelfCount.text = "x" + bean.anchorRank.giftNumber.toString()
                Glide.with(mContext).load(bean.anchorRank.headFileName)
                    .apply(RequestOptions.bitmapTransform(CircleCrop())).into(bindingView!!.headSelf)
                Glide.with(mContext).load(bean.anchorRank.giftIcon).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .into(bindingView!!.giftSelf)
            } else {
                bindingView!!.CLSelf.visibility = View.GONE
            }


            bindingView!!.data.text = bean.beginDate + " ~ " + bean.endDate


//            Glide.with(mContext).load(bean.anchorRank.headFileName).into(bindingView!!.giftSelf)


        }
    }

    @SuppressLint("RestrictedApi", "NotifyDataSetChanged")
    private fun loadData3(type: String) {
        viewModel!!.getRankList02(UserInfoManager.user()?.id.toString(), type).observe(
            this
        ) { bean: RankBean ->
            showContentView()
            dataList01?.clear()

            if (bean.rankConfigList.size > 0) {
                dataList01!!.addAll(bean.rankConfigList)
            }

            adapter01!!.notifyDataSetChanged()

            setTitle02(type)
        }
    }

    private fun setTitle(type: String) {
        when (type) {
            "0" -> {
                bindingView!!.rank01.setTextColor(Color.parseColor("#3082FD"))
                bindingView!!.rank01bg.visibility = View.VISIBLE
                bindingView!!.rank02.setTextColor(Color.parseColor("#FFFFFF"))
                bindingView!!.rank02bg.visibility = View.GONE
            }

            else -> {

                bindingView!!.rank01.setTextColor(Color.parseColor("#FFFFFF"))
                bindingView!!.rank01bg.visibility = View.GONE
                bindingView!!.rank02.setTextColor(Color.parseColor("#3082FD"))
                bindingView!!.rank02bg.visibility = View.VISIBLE
            }
        }
    }

    private fun setTitle02(type: String) {
        when (type) {
            "0" -> {
                bindingView!!.rank03.setTextColor(Color.parseColor("#3082FD"))
                bindingView!!.rank03bg.visibility = View.VISIBLE
                bindingView!!.rank04.setTextColor(Color.parseColor("#CCCCCC"))
                bindingView!!.rank04bg.visibility = View.GONE
            }

            else -> {

                bindingView!!.rank03.setTextColor(Color.parseColor("#CCCCCC"))
                bindingView!!.rank03bg.visibility = View.GONE
                bindingView!!.rank04.setTextColor(Color.parseColor("#3082FD"))
                bindingView!!.rank04bg.visibility = View.VISIBLE

            }
        }
    }

    private fun initTopThree() {
        bindingView!!.headbg02.setImageDrawable(null)
        bindingView!!.name02.text = ""
        bindingView!!.id02.text = ""
        bindingView!!.gift02.setImageDrawable(null)
        bindingView!!.giftCount02.text = ""

        bindingView!!.headbg03.setImageDrawable(null)
        bindingView!!.name03.text = ""
        bindingView!!.id03.text = ""
        bindingView!!.gift03.setImageDrawable(null)
        bindingView!!.giftCount03.text = ""

        bindingView!!.headbg.setImageDrawable(null)
        bindingView!!.name.text = ""
        bindingView!!.id.text = ""
        bindingView!!.gift.setImageDrawable(null)
        bindingView!!.giftCount.text = ""
    }

    private fun setTopThree(rankList: MutableList<RankBean.RankListDTO>) {
        dataList!!.addAll(rankList)
        var removeFirst = dataList!!.removeFirst() as RankBean.RankListDTO

        Glide.with(mContext).load(removeFirst.headFileName).apply(RequestOptions.bitmapTransform(CircleCrop()))
            .into(bindingView!!.headbg02)
        bindingView!!.name02.text = removeFirst.nickName
        bindingView!!.id02.text = "ID:" + removeFirst.userCode
        Glide.with(mContext).load(removeFirst.giftIcon).into(bindingView!!.gift02)
        bindingView!!.giftCount02.text = "x" + removeFirst.giftNumber.toString()



        if (rankList.size > 1) {
            var removeFirst02 = dataList!!.removeFirst() as RankBean.RankListDTO
            Glide.with(mContext).load(removeFirst02.headFileName).apply(RequestOptions.bitmapTransform(CircleCrop()))
                .into(bindingView!!.headbg)
            bindingView!!.name.text = removeFirst02.nickName
            bindingView!!.id.text = "ID:" + removeFirst02.userCode
            Glide.with(mContext).load(removeFirst02.giftIcon).into(bindingView!!.gift)
            bindingView!!.giftCount.text = "x" + removeFirst02.giftNumber.toString()

        }
        if (rankList.size > 2) {
            var removeFirst03 = dataList!!.removeFirst() as RankBean.RankListDTO
            Glide.with(mContext).load(removeFirst03.headFileName).apply(RequestOptions.bitmapTransform(CircleCrop()))
                .into(bindingView!!.headbg03)
            bindingView!!.name03.text = removeFirst03.nickName
            bindingView!!.id03.text = "ID:" + removeFirst03.userCode
            Glide.with(mContext).load(removeFirst03.giftIcon).into(bindingView!!.gift03)
            bindingView!!.giftCount03.text = "x" + removeFirst03.giftNumber.toString()
        }
    }


    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any) {

//        val bean = item as HistoryListBean.RecordsDTO
//
//        if (v.id == R.id.head && bean.userId.toLong()!= 0L){
//            ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_CENTER)
//                .withLong("id", bean.userId.toLong())
//                .withString("userRole", "2")
//                .navigation()
//        }

//        BlackListBean.RecordsDTO  bean = (BlackListBean.RecordsDTO) item;
//        viewModel.removeBlackList(bean.getId());

//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    @SuppressLint("SetTextI18n")
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>,
        position: Int,
        viewType: Int
    ) {
        val binding01 = holder.binding
        if (binding01 is UserItemRank01Binding) {
            val bean = dataList!![position] as RankBean.RankListDTO
            val binding = binding01

            binding01.consumptionType.text = bean.nickName
//            binding01.nu.text = (position+1).toString()
            binding01.nu.text = bean.ranks.toString()
            binding01.id.text = bean.userCode.toString()
            binding01.count.text = "x" + bean.giftNumber.toString()
            Glide.with(mContext).load(bean.giftIcon).into(binding01.gift)

            binding01.head.setTag(R.id.head, position)
            bean.headFileName?.let { it.ifEmpty { null } }?.apply {
                Glide.with(mContext).asBitmap().load(bean.headFileName)
                    .apply(RequestOptions.bitmapTransform(CircleCrop())).into(object : CustomTarget<Bitmap?>() {
                        override fun onResourceReady(
                            @NonNull resource: Bitmap,
                            @Nullable transition: Transition<in Bitmap?>?
                        ) {
                            if (position == binding01.head.getTag(R.id.head)) {
                                binding01.head.setImageBitmap(resource)
                            }
                        }

                        override fun onLoadCleared(@Nullable placeholder: Drawable?) {}
                    })
            }
        } else if (binding01 is UserItemRank02Binding) {
            val bean = dataList01!![position] as RankBean.RankConfigListDTO
            Glide.with(mContext).load(bean.giftIcon).into(binding01.gift)
            binding01.giftCount.text = "x" + bean.giftNumber.toString()

//            Glide.with(mContext).load(bean.diamondIcon).into(binding01.diamond)
            binding01.diamondCount.text = "x" + bean.diamond.toString()


        }
    }

    fun rank01(type: Int) {
        setTitle(type.toString())

        loadData2(type.toString())
    }

    fun rank02(type: Int) {
        setTitle02(type.toString())

        loadData3(type.toString())
    }

    fun query() {
        RankRuleDialogFragment().show(getSupportFragmentManager(), "RankRuleDialogFragment")
    }


    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarDarkFont(true, 0.2f).init()
    }

    companion object {
        const val TAG = "SettingActivity"
    }
}