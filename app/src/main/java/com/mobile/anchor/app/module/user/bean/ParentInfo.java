package com.mobile.anchor.app.module.user.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.user.bean
 * @class describe
 * @time 2019/8/7 17:28
 * @class describe
 */
public class ParentInfo implements Serializable {


    /**
     * uid : 197
     * nickName : Wait。
     * avatarUrl : https://wx.qlogo.cn/mmopen/vi_32/kwFDiaWnAZ4d9UayphYCWVq58KUJoIzCscqfcPhExGdJfkSQsL9WVUENfylcQm3AtrbPISCDdhSdyLibsNSlVPzA/132
     * gender : 1
     * city : Qingdao
     * country : China
     * province : Shandong
     * phone : 18354285393
     * relation : 爸爸
     * isBindWechat : true
     */

    private int uid;
    private String nickName;
    private String avatarUrl;
    private String gender;
    private String city;
    private String country;
    private String province;
    private String phone;
    private String relation;
    private boolean isBindWechat;
    private String kidId;

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public boolean isIsBindWechat() {
        return isBindWechat;
    }

    public void setIsBindWechat(boolean isBindWechat) {
        this.isBindWechat = isBindWechat;
    }

    public String getKidId() {
        return kidId;
    }

    public void setKidId(String kidId) {
        this.kidId = kidId;
    }
}
