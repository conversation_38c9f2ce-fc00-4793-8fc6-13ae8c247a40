package com.mobile.anchor.app.module.discover.main.center

import io.reactivex.Flowable
import anchor.app.base.api.BaseApi
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.discover.api.DiscoverApi
import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean
import java.io.File

class PersonalCenterRepository : IRepository{



    fun getPersonalCenter(id: Long, userRole: String): Flowable<BaseResult<PersonalCenterBean>> {
        return DiscoverApi.getPersonalCenter(id,userRole)
    }
    fun removeAndFollowing(UserId: Long, type: Int, userRole: String): Flowable<BaseResult<Any>> = DiscoverApi.removeAndFollowing(UserId, type, userRole)
    fun setBlack(UserId: Long, userRole: String): Flowable<BaseResult<Any>> = DiscoverApi.setBlack(UserId, userRole)

    fun userBuyPhoto(id: Long ): Flowable<BaseResult<Any>> = DiscoverApi.userBuyPhoto(id)

    fun anchorFileSave(id: Map<String, Any>  ): Flowable<BaseResult<Any>> = DiscoverApi.anchorFileSave(id)


    fun uploadPicture(file: File?): Flowable<BaseResult<Any>> {
        return BaseApi.uploadPicture(file)
    }
}