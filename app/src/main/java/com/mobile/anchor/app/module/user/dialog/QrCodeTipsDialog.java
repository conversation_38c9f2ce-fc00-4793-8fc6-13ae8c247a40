package com.mobile.anchor.app.module.user.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.bigkoo.convenientbanner.utils.ScreenUtil;

import anchor.app.base.utils.QRCodeUtil;
import anchor.app.base.utils.StoreToPhotoUtil;
import anchor.app.base.utils.ToastUtil;
import com.mobile.anchor.app.R;


/**
 * 二维码扫描提示页面
 */
public class QrCodeTipsDialog extends Dialog {

    public static final String TAG = QrCodeTipsDialog.class.getSimpleName();
    private View mRootView;
    private String CodeStr;

    public QrCodeTipsDialog(@NonNull Context context,String CodeStr) {
        super(context);
        this.CodeStr = CodeStr;
        init(context);
    }

    public QrCodeTipsDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    private void init(Context context) {
        if (null == mRootView) {
            mRootView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_tips, null);
        }
        mRootView.findViewById(R.id.cancel).setOnClickListener(v -> {
            dismiss();
        });

        mRootView.findViewById(R.id.copy).setOnClickListener(v -> {
            copyClipboar(context,CodeStr);
        });

        mRootView.findViewById(R.id.save).setOnClickListener(v -> {
            StoreToPhotoUtil.INSTANCE.storeToPhoto((Activity) context,context.getExternalCacheDir().toString() + "007",1);
            ToastUtil.show("Save Success");
        });

        ImageView QRCode = mRootView.findViewById(R.id.QRCode);


        Bitmap qrImage = QRCodeUtil.createQRImage(CodeStr, ScreenUtil.px2dip(context, 400), ScreenUtil.px2dip(context, 400), BitmapFactory.decodeResource(context.getResources(), R.mipmap.ic_launcher), context.getExternalCacheDir().toString() + "007");
        QRCode.setImageBitmap(qrImage);

        setContentView(mRootView);
        setCanceledOnTouchOutside(true);
        final Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.CENTER;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(wlp);
    }
    public boolean copyClipboar(Context context, String copyStr) {
        try {
            //获取剪贴板管理器：
            ClipboardManager cm = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            // 创建普通字符型ClipData
            ClipData mClipData = ClipData.newPlainText("Label", copyStr);
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData);
            ToastUtil.show(context.getResources().getString(R.string.b27));

            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
