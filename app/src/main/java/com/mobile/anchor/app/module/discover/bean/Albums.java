package com.mobile.anchor.app.module.discover.bean;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.bean
 * @class describe
 * @time 2019/8/22 10:44
 * @class describe
 */
public class Albums {
    /**
     * albumId : 1813
     * createTime : 1542090548000
     * feedId : 36
     * feedInfo : 《Honey English甜心英语 》第一辑是一套专门针对零基础宝宝的英语启蒙绘本，全套共20册，分为故事朗读、复读和说唱三种英语原版音频。绘本中的每个单词、句子都是孩子每天说的话，随时用得上！并把日常生活编成各种脑洞，孩子在学到很多小知识的同时，还能养成各种好习惯！
     * feedName : Honey English 甜心英语（第一辑）
     * feedTypeId : 2
     * imageUrl : http://static.duyaya.com/image/feed/e6a3a1590aa030aa264ca2e66d75aa51.png
     * stick : 1
     */

    private int albumId;
    private long createTime;
    private int feedId;
    private String feedInfo;
    private String feedName;
    private int feedTypeId;
    private String imageUrl;
    private int stick;

    public int getAlbumId() {
        return albumId;
    }

    public void setAlbumId(int albumId) {
        this.albumId = albumId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getFeedId() {
        return feedId;
    }

    public void setFeedId(int feedId) {
        this.feedId = feedId;
    }

    public String getFeedInfo() {
        return feedInfo;
    }

    public void setFeedInfo(String feedInfo) {
        this.feedInfo = feedInfo;
    }

    public String getFeedName() {
        return feedName;
    }

    public void setFeedName(String feedName) {
        this.feedName = feedName;
    }

    public int getFeedTypeId() {
        return feedTypeId;
    }

    public void setFeedTypeId(int feedTypeId) {
        this.feedTypeId = feedTypeId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getStick() {
        return stick;
    }

    public void setStick(int stick) {
        this.stick = stick;
    }
}
