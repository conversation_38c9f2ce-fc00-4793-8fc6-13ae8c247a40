package com.mobile.anchor.app.module.login.register

import anchor.app.base.core.Constants
import anchor.app.base.ext.jump
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.ActivityUtil
import anchor.app.base.utils.RxTimerUtil
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.Utils
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import com.blankj.utilcode.constant.RegexConstants
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.StringUtils
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityRegisterBinding
import com.mobile.anchor.app.module.login.login.LoginActivity
import com.mobile.anchor.app.module.login.login.LoginViewModel
import com.mobile.anchor.app.module.user.agreement.AgreementActivity
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 注册页面
 */
class RegisterActivity : BaseActivity<LoginViewModel?, ActivityRegisterBinding?>() {
    override fun getLayoutId(): Int {

        return R.layout.activity_register
    }

    override fun onDestroy() {
        super.onDestroy()
        RxTimerUtil.cancel(this)
    }

    var timeCount = 60


    override fun initView() {
        showContentView()

        bindingView!!.viewModel = viewModel
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this


        //获取上次是否同意了协议
        var isSelected = SPUtils.getInstance().getBoolean("lastLoginChecked", false)
        bindingView!!.checkbox.isSelected = isSelected


        bindingView!!.tvCheckboxText1.setOnClickListener {
            checkbox(bindingView!!.checkbox)
        }

        bindingView!!.nation.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                checkSubmitBG()
            }
        })
        bindingView!!.account.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
//                var str1 = ""
//                if (s.toString().contains(" ")) {
//                    val str: List<String> = s.toString().split(" ")
//                    for (i in str.indices) {
//                        str1 += str[i]
//                    }
//                }
                // 只允许字母、数字和汉字其余的还可以随时添加比如下划线什么的，但是注意引文符号和中文符号区别
                val regEx = "[^a-z0-9]@\\." //正则表达式
                val p: Pattern = Pattern.compile(RegexConstants.REGEX_EMAIL)
                val m: Matcher = p.matcher(s.toString())

                println("beforeTextChanged 01")
                if (m.matches().not()) {
                    bindingView!!.account.removeTextChangedListener(this)
                    var trim = m.replaceAll("").trim { it <= ' ' }
                    println("beforeTextChanged 02" + trim)
                    bindingView!!.account.setText(trim)
                    bindingView!!.account.addTextChangedListener(this)
                    if (trim != null) {
                        bindingView!!.account.setSelection(trim.length)
                    }
                }


//                var stringFilter = stringFilter(str1)
//                bindingView!!.account.setText(stringFilter)
//                if (stringFilter != null) {
//                    bindingView!!.account.setSelection(stringFilter.length)
//                }

//                if (s != null && s.toString().isNotEmpty()) {
//                    bindingView!!.close01.visibility = View.VISIBLE
//                } else {
//                    bindingView!!.close01.visibility = View.GONE
//                }
                checkSubmitBG()
            }
        })
        bindingView!!.verCode.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                checkSubmitBG()
            }
        })

        bindingView!!.password.setTransformationMethod(PasswordTransformationMethod.getInstance());
        bindingView!!.password.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if (s != null && s.toString().isNotEmpty()) {
//                    bindingView!!.close03.visibility = View.VISIBLE
                    bindingView!!.show2.visibility = View.VISIBLE
                } else {
//                    bindingView!!.close03.visibility = View.GONE
                    bindingView!!.show2.visibility = View.GONE
                }
                checkSubmitBG()
            }
        })

        bindingView!!.code.setInputType(InputType.TYPE_CLASS_NUMBER);
        bindingView!!.code.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
//                if (s != null && s.toString().isNotEmpty()) {
//                    bindingView!!.close04.visibility = View.VISIBLE
//                } else {
//                    bindingView!!.close04.visibility = View.GONE
//                }
                checkSubmitBG()
            }
        })

        if (BuildConfig.DEBUG) {
            bindingView!!.code.setText("604594")
        }
    }

    fun stringFilter(str: String?): String? {
        // 只允许字母、数字和汉字其余的还可以随时添加比如下划线什么的，但是注意引文符号和中文符号区别
        val regEx = "[^a-z0-9]" //正则表达式
        val p: Pattern = Pattern.compile(regEx)
        val m: Matcher = p.matcher(str)
        return m.replaceAll("").trim { it <= ' ' }
    }


    fun onGetCheckCode() {

        var email = bindingView!!.account.text.toString().trim()
        if (!RegexUtils.isEmail(email)) {
            ToastUtil.show(StringUtils.getString(R.string.register_email_err))
            return
        }
        viewModel?.anchorEmailCode(this, email)?.observe(this) {

            bindingView!!.tvGetCheckCode.text = "${timeCount} s"

            bindingView!!.tvGetCheckCode.isEnabled = false
            RxTimerUtil.interval(this@RegisterActivity, 1) {
                bindingView!!.tvGetCheckCode.text = "${timeCount - it - 1} s"
                if (it.toInt() == timeCount - 1) {
                    bindingView!!.tvGetCheckCode.isEnabled = true
                    //时间到了停止
                    RxTimerUtil.cancel(this@RegisterActivity)
                    bindingView!!.tvGetCheckCode.text =
                        StringUtils.getString(R.string.login_send_captcha)
                }
            }
        }
    }


    fun close(type: Int) {
        when (type) {
            1 -> {
                bindingView!!.account.setText("")
            }

            2 -> {
                bindingView!!.password.setText("")
            }

            3 -> {
                bindingView!!.verCode.setText("")
            }

            4 -> {
                bindingView!!.code.setText("")
            }

            else -> {}
        }

    }

    var isShow1 = false
    var isShow2 = false

    fun show(type: Int) {
        when (type) {
            1 -> {
                if (isShow1.not()) {                    //显示密码
                    isShow1 = true
                    bindingView!!.password.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
//                    bindingView!!.show1.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_03))
                    bindingView!!.password.setSelection(bindingView!!.password.length())

                } else { //隐藏密码
                    isShow1 = false
                    bindingView!!.password.setTransformationMethod(PasswordTransformationMethod.getInstance());
//                    bindingView!!.show1.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_04))
                    bindingView!!.password.setSelection(bindingView!!.password.length())

                }
            }

            2 -> {
                if (isShow2.not()) {                    //显示密码
                    isShow2 = true
                    bindingView!!.password.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                    bindingView!!.show2.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_03))
                    bindingView!!.password.setSelection(bindingView!!.password.length())

                } else { //隐藏密码
                    isShow2 = false
                    bindingView!!.password.setTransformationMethod(PasswordTransformationMethod.getInstance());
                    bindingView!!.show2.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_04))
                    bindingView!!.password.setSelection(bindingView!!.password.length())
                }
            }
        }

    }

    var isShow2Select = false

    fun select() {
        if (isShow2Select.not()) {
            isShow2Select = true
            bindingView!!.select.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_02))
            bindingView!!.cd.visibility = View.VISIBLE

        } else {
            isShow2Select = false
            bindingView!!.select.setImageDrawable(getDrawable(R.mipmap.login_activity_signup_bg_01))
            bindingView!!.cd.visibility = View.GONE
        }
    }

    var country = ""
    fun country(type: Int) {
        when (type) {
            1 -> {
                country = "BR"
            }

            2 -> {
                country = "VN"
            }

            3 -> {
                country = "PH"
            }

            4 -> {
                country = "MA"
            }

            5 -> {
                country = "IN"
            }

            6 -> {
                country = "CL"
            }

            7 -> {
                country = "PE"
            }

            8 -> {
                country = "VE"
            }

            9 -> {
                country = "OO"
            }
        }
        bindingView!!.nation.text = country
        bindingView!!.cd.visibility = View.GONE
    }

    fun checkSubmitBG() {

//        if (bindingView!!.nation.text == null || bindingView!!.nation.text.isEmpty()) {
//            bindingView!!.submit.background =
//                getDrawable(R.drawable.login_register_state_selected_03_false)
//            return
//        }


        if (bindingView!!.account.text == null || bindingView!!.account.text.isEmpty()) {
            bindingView!!.submit.background =
                getDrawable(R.drawable.login_register_state_selected_03_false)
            return
        }
        if (bindingView!!.verCode.text == null || bindingView!!.verCode.text.isEmpty()) {
            bindingView!!.submit.background =
                getDrawable(R.drawable.login_register_state_selected_03_false)
            return
        }
        if (bindingView!!.password.text == null || bindingView!!.password.text.isEmpty()) {
            bindingView!!.submit.background =
                getDrawable(R.drawable.login_register_state_selected_03_false)
            return
        }
        if (bindingView!!.code.text == null || bindingView!!.code.text.isEmpty()) {
            bindingView!!.submit.background =
                getDrawable(R.drawable.login_register_state_selected_03_false)
            return
        }

        bindingView!!.submit.background = getDrawable(R.drawable.bg_button_them)
    }

    fun submit() {

//        ARouter.getInstance().build(ArouterPath.PATH_LOGIN_MODIFY_INFO).withString("hashMapStr",GsonUtil.GsonString("")).navigation()

//        if (bindingView!!.nation.text == null || bindingView!!.nation.text.isEmpty()) {
//            ToastUtil.show("Please fill in the nation")
//            return
//        }

        if (bindingView!!.account.text == null || bindingView!!.account.text.isEmpty()) {
            ToastUtil.show(StringUtils.getString(R.string.b111))
            return
        }
        if (bindingView!!.verCode.text == null || bindingView!!.verCode.text.isEmpty()) {
            ToastUtil.show(StringUtils.getString(R.string.b104))
            return
        }

        if (bindingView!!.password.text == null || bindingView!!.password.text.isEmpty()) {
            ToastUtil.show(StringUtils.getString(R.string.b103))
            return
        }

        if (bindingView!!.code.text == null || bindingView!!.code.text.isEmpty()) {
            ToastUtil.show(StringUtils.getString(R.string.b105))
            return
        }

        if (bindingView!!.code.text == null || bindingView!!.code.text.isEmpty() || bindingView!!.code.text.toString().length != 6) {
            ToastUtil.show("Please enter a 6-digit recommendation code")
            return
        }

        var hashMap = HashMap<String, String>()
//        hashMap["country"] = bindingView!!.nation.text.toString()
        hashMap["username"] = bindingView!!.account.text.toString()
        hashMap["password"] = bindingView!!.password.text.toString()
        hashMap["unionId"] = bindingView!!.code.text.toString()
        hashMap["code"] = bindingView!!.verCode.text.toString()
        viewModel?.unionCheck(this, bindingView!!.code.text.toString())?.observe(this) {
            if (it != "000") {

                hashMap["deviceId"] = Utils.getAndroidId()
                viewModel!!.save(this, hashMap).observe(
                    this
                ) { result: Any ->
//                ToastUtil.show("保存信息成功")
//                    loginBean.token = result.toString()
//                    SharePreUtil.setAccessToken(result.toString())
//                    SharePreUtil.setUserInfo(GsonUtil.GsonString(loginBean))

                    //跳转首页
                    //退出所有Activity
                    ActivityUtil.getInstance().finishAllActivityExcept(this@RegisterActivity)
                    jump(LoginActivity::class.java)
                    finish()
                }

//                ARouter.getInstance().build(ArouterPath.PATH_LOGIN_MODIFY_INFO)
//                    .withString("hashMapStr", GsonUtil.GsonString(hashMap)).navigation()
            }
        }
    }


    fun treaty(type: Int) {
        if (type == 1) {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", Constants.HTTP_URL.Useragreement)
                putString("title", resources.getString(R.string.User_agreement_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        } else {
            jump(AgreementActivity::class.java, Bundle().apply {
                putString("url", Constants.HTTP_URL.PrivatePolicy)
                putString("title", resources.getString(R.string.Privacy_policy_title))
                putBoolean(Constants.KEY.TOOLBARDARKMODE, true)
            })
        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(false, 0.2f).init()
    }

    fun checkbox(checkbox: View) {
        checkbox.isSelected = !checkbox.isSelected

        SPUtils.getInstance().put("lastLoginChecked", checkbox.isSelected)
    }


    companion object {
        private const val TAG = "LoginActivity"
    }
}