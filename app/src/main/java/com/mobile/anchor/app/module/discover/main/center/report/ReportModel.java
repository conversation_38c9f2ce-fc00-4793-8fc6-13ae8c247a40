package com.mobile.anchor.app.module.discover.main.center.report;

import static com.uber.autodispose.AutoDispose.autoDisposable;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;

import java.io.File;

import anchor.app.base.ext.rxweaver.RxErrorUtil;
import anchor.app.base.retrofit.BaseResult;
import anchor.app.base.retrofit.ResultException;
import anchor.app.base.retrofit.ResultMap;
import anchor.app.base.utils.Logger;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.viewmodel.BaseViewModel;
import com.mobile.anchor.app.module.discover.main.center.bean.ReportBean;

public class ReportModel extends BaseViewModel<ReportRepository> {


    public ReportModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public ReportRepository getRepository() {
        return new ReportRepository();
    }

    public MutableLiveData<Object> publicRepoet(FragmentActivity activity, ReportBean bean) {
        MutableLiveData<Object> data = new MutableLiveData<>();
        repository.publicRepoet(bean)
                .map(new ResultMap<>())
                .compose(RxErrorUtil.handleGlobalError(activity))
                .as(autoDisposable(this))
                .subscribe(result -> {
                    data.setValue("");
                }, throwable -> {
                    if (throwable instanceof ResultException) {
                        if (((ResultException) throwable).getCode() == BaseResult.SUCCESS) data.setValue("");
                        else ToastUtil.show(throwable.getMessage());
                    }

//                    data.setValue(false);
                });
        return data;
    }

    public MutableLiveData<String> uploadPicture(File file) {
        MutableLiveData<String> data = new MutableLiveData<>();
        repository.uploadPicture(file).as(autoDisposable(this))
                .subscribe(baseResult -> {
                    Gson gson = new Gson();
                    Logger.d(gson.toJson(baseResult.getData()));
//                    JSONObject jsonObject = JSONObject.parseObject(gson.toJson(baseResult.getData()));
//                    String strPath = jsonObject.getString("imageUrl");
//                    data.setValue(strPath);
                    data.setValue(baseResult.getData().toString());
                }, throwable -> {
                    data.setValue(null);
                });
        return data;
    }
}
