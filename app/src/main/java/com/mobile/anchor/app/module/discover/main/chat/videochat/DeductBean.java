package com.mobile.anchor.app.module.discover.main.chat.videochat;

public class DeductBean {

    private int diamond;
    private String enoughFlag;

    public int getDiamond() {
        return diamond;
    }

    public void setDiamond(int diamond) {
        this.diamond = diamond;
    }

    public String getEnoughFlag() {
        return enoughFlag;
    }

    public void setEnoughFlag(String enoughFlag) {
        this.enoughFlag = enoughFlag;
    }
}
