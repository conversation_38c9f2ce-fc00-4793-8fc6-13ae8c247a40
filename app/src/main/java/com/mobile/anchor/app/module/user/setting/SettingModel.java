package com.mobile.anchor.app.module.user.setting;

import android.app.Application;

import androidx.annotation.NonNull;

import anchor.app.base.utils.Utils;
import anchor.app.base.viewmodel.BaseViewModel;

public class SettingModel extends BaseViewModel<SettingRepository> {

    public SettingModel(@NonNull Application application) {
        super(application);
    }

    public void logOut() {
        Utils.logOut();
        //退出所有Activity
    }

    @Override
    public SettingRepository getRepository() {
        return new SettingRepository();
    }

}
