package com.mobile.anchor.app.module.user.setting.safe

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.uber.autodispose.AutoDispose
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.utils.Logger
import anchor.app.base.utils.ToastUtil
import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.user.main.UserRepository

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/8/22 15:58
 * @description :帐号安全ViewModel
 */
class AccountSafeViewModel(application: Application) : BaseViewModel<UserRepository>(application) {
    override fun getRepository(): UserRepository = UserRepository()

    fun modifyPassword(params: Map<String, String>): MutableLiveData<Boolean> {
        val liveData = MutableLiveData<Boolean>()
        repository.modifyPassword(params)
            .`as`(AutoDispose.autoDisposable(this@AccountSafeViewModel)).subscribe({
                if (it.code == BaseResult.SUCCESS) {
                    liveData.value = true
                } else {
                    ToastUtil.show(it.message)
                }
            }, { Logger.e(it.message) })
        return liveData
    }

    fun getEmailCaptcha(email: String): MutableLiveData<Boolean> {
        val liveData = MutableLiveData<Boolean>()
        repository.getEmailCode(email).`as`(AutoDispose.autoDisposable(this@AccountSafeViewModel))
            .subscribe({
                liveData.value = it.code == BaseResult.SUCCESS
                if (it.code != BaseResult.SUCCESS) {
                    ToastUtil.show(it.message)
                }
            }, { ToastUtil.show(it.message) })
        return liveData
    }
}