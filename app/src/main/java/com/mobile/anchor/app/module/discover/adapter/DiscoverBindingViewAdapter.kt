package com.mobile.anchor.app.module.discover.adapter

import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.MultiTypeAdapter
import com.mobile.anchor.app.BR
import android.content.Context
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding

/**
 * 和databinding结合的Reyclerview的adapter
 */
abstract class DiscoverBindingViewAdapter<T>(context: Context, list: ObservableArrayList<T>) : MultiTypeAdapter<T>(context, list) {

    override fun onBindViewHolder(holder: BindingViewHolder<ViewDataBinding>, position: Int) {
        val item = list[position]
        //pending binding itemModel
        holder.binding.setVariable(BR.courseinfo, item)
        //pending binding presenter
        itemPresenter?.let {  holder.binding.setVariable(BR.presenter, itemPresenter)}
        itemPresenter3?.let {  holder.binding.setVariable(BR.presenter, itemPresenter3)}

        holder.binding.executePendingBindings()
        //set decorator
        itemDecorator?.decorator(holder, position, getItemViewType(position))
    }
}