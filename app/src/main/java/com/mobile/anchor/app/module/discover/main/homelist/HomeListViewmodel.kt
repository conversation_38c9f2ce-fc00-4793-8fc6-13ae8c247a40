package com.mobile.anchor.app.module.discover.main.homelist

import anchor.app.base.viewmodel.BaseViewModel
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean
import android.app.Application
import androidx.lifecycle.MutableLiveData
import anchor.app.base.retrofit.BaseResult
import anchor.app.base.retrofit.ResultException

class HomeListViewmodel (application: Application) : BaseViewModel<HomeListRepository>(application) {

    override fun getRepository(): HomeListRepository {

        return HomeListRepository()
    }


    /** <AUTHOR>
     * getRollShopList() Description : 获取 全部 的 列表数据
    @return  对应列表数据:  */
    fun getList(current: Int, size: Int, type: String, country: String): MutableLiveData<AnchorItemBean>{
        val info: MutableLiveData<AnchorItemBean> = MutableLiveData()
        dataStreamFliter.fliter(repository.getList(current,size,type,country), { list ->
                info.value = list

//            val resultJson: Any = hashMap.get(goodsType.toString())!!
//            if (resultJson != null) {
//                val json = GsonUtil.GsonString(resultJson)
//
//            }
//            if(info.value == null) info.value = ArrayList()

        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS){} //{ info.value = "" }
            }
        }
        return info;
    }

    fun getCallAnchor(channelId:String,type:String): MutableLiveData<Any>{
        val info: MutableLiveData<Any> = MutableLiveData()
        dataStreamFliter.fliter(repository.getCallAnchor(channelId,type), { state ->
            info.value = state
        }) { throwable ->
            error.value = throwable
            if (throwable is ResultException) {
                if (throwable.getCode() == BaseResult.SUCCESS){} //{ info.value = "" }
            }
        }
        return info;
    }




}