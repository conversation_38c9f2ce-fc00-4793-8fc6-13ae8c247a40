package com.mobile.anchor.app.module.discover.main.center.moment.bean;

import java.util.List;

import com.mobile.anchor.app.module.discover.main.center.bean.PersonalCenterBean;

public class MomentListBean {

    private int current;
    private boolean hitCount;
    private int pages;
    private List<PersonalCenterBean.TrendsDTO> records;
    private boolean searchCount;
    private int size;
    private int total;

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(boolean hitCount) {
        this.hitCount = hitCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<PersonalCenterBean.TrendsDTO> getRecords() {
        return records;
    }

    public void setRecords(List<PersonalCenterBean.TrendsDTO> records) {
        this.records = records;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

}
