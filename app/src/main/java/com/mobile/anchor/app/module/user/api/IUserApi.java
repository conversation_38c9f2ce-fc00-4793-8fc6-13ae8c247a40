package com.mobile.anchor.app.module.user.api;

import com.google.gson.JsonObject;
import com.mobile.anchor.app.module.user.bean.AnchorPaySaveInfo;
import com.mobile.anchor.app.module.user.bean.CountryBean;
import com.mobile.anchor.app.module.user.bean.GiftBean;
import com.mobile.anchor.app.module.user.bean.GroupListBean;
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean;
import com.mobile.anchor.app.module.user.bean.InvitationBean;
import com.mobile.anchor.app.module.user.bean.InvitationBean02;
import com.mobile.anchor.app.module.user.bean.LevelBean;
import com.mobile.anchor.app.module.user.bean.PageBean;
import com.mobile.anchor.app.module.user.bean.RankBean;
import com.mobile.anchor.app.module.user.bean.ReportBean;
import com.mobile.anchor.app.module.user.bean.RewardTaskBean;
import com.mobile.anchor.app.module.user.bean.SettlementBean;
import com.mobile.anchor.app.module.user.follow.bean.FansListBean;
import com.mobile.anchor.app.module.user.setting.blacklist.bean.BlackListBean;
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import anchor.app.base.bean.AlbumBean;
import anchor.app.base.bean.LanguagesBean;
import anchor.app.base.bean.LoginBean;
import anchor.app.base.core.Constants;
import anchor.app.base.retrofit.BaseResult;
import io.reactivex.Flowable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface IUserApi {


    @POST(Constants.HTTP_URL.publish_feedback)
    Flowable<BaseResult<Object>> publicRepoet(@Body ReportBean map);

    /**
     * 修改個人信息
     */
    @POST(Constants.HTTP_URL.modifyChildInfo)
    Flowable<BaseResult<Object>> modifyChildInfo(@Body LoginBean map);

    @POST(Constants.HTTP_URL.modifyAnchorUpdateNew)
    Flowable<BaseResult<Object>> modifyAnchorUpdateNew(@Body HashMap<String, Object> map);

    @POST(Constants.HTTP_URL.groundFileName)
    Flowable<BaseResult<Object>> groundFileName(@Body LoginBean map);

    @POST(Constants.HTTP_URL.headFileName)
    Flowable<BaseResult<Object>> headFileName(@Body LoginBean map);

    @POST(Constants.HTTP_URL.nickName)
    Flowable<BaseResult<Object>> nickName(@Body LoginBean map);

    @POST(Constants.HTTP_URL.showVideoUrl)
    Flowable<BaseResult<Object>> showVideoUrl(@Body LoginBean map);

    @POST(Constants.HTTP_URL.coverVideoUrl)
    Flowable<BaseResult<Object>> coverVideoUrl(@Body LoginBean map);

    @POST(Constants.HTTP_URL.highGroundFileName)
    Flowable<BaseResult<Object>> highGroundFileName(@Body LoginBean map);

    /**
     * 查询个人主页信息
     */
    @GET(Constants.HTTP_URL.query_user_detail)
    Flowable<BaseResult<LoginBean>> queryUserInfo(@Query("id") String id);


    @GET(Constants.HTTP_URL.query_user_language)
    Flowable<BaseResult<List<LanguagesBean>>> query_user_language();

    /**
     * 查询关注
     */
    @GET(Constants.HTTP_URL.query_user_fans)
    Flowable<BaseResult<Object>> query_user_follower(@Query("id") Long id);

    /**
     * 上传图片
     */
    @POST(Constants.HTTP_URL.user_upload_picture)
    Flowable<BaseResult<Object>> uploadPicture(@Body JsonObject jsonBody);


    /**
     * 消费记录列表
     */
    @POST(Constants.HTTP_URL.get_history_list_anchor)
    Flowable<BaseResult<HistoryListBean>> getHistoryList(@Body Map<String, Object> map);

    @POST(Constants.HTTP_URL.get_diamond_list_anchor)
    Flowable<BaseResult<HistoryListBean>> getDiamondList(@Body Map<String, Object> map);

    @POST(Constants.HTTP_URL.get_invite_list_anchor)
    Flowable<BaseResult<InvitationBean>> getInviteList(@Query("anchorId") String anchorId);

    @POST(Constants.HTTP_URL.get_rank_list_anchor)
    Flowable<BaseResult<RankBean>> getRankList(@Query("anchorId") String anchorId, @Query("type") String type);

    @POST(Constants.HTTP_URL.get_config_list_anchor)
    Flowable<BaseResult<RankBean>> getRankList02(@Query("anchorId") String anchorId, @Query("type") String type);

    @POST(Constants.HTTP_URL.get_invite_user_list_anchor)
//    Flowable<BaseResult<InvitationBean02>> getUserInviteList(@Query("anchorId")String anchorId,@Query("current")int current,@Query("size")String size);
    Flowable<BaseResult<InvitationBean02>> getUserInviteList(@Body Map<String, Object> map);


    @GET(Constants.HTTP_URL.anchorExtract)
    Flowable<BaseResult<Object>> anchorExtract(@Query("extractNum") int extractNum);

    @GET(Constants.HTTP_URL.anchor_pay_info)
    Flowable<BaseResult<Object>> anchorPayInfo();

    @GET(Constants.HTTP_URL.anchor_pay_country)
    Flowable<BaseResult<List<String>>> anchorPayCountry();

    @GET(Constants.HTTP_URL.order_pay_org)
    Flowable<BaseResult<List<String>>> orderPayOrg(@Query("country") String country, @Query("paymentMethodType") String paymentMethodType);

    @GET(Constants.HTTP_URL.anchor_pay_method)
    Flowable<BaseResult<List<String>>> anchorPayMethod(@Query("country") String country);

    @GET(Constants.HTTP_URL.anchor_pay_param)
    Flowable<BaseResult<List<String>>> anchorPayParam(@Query("country") String country, @Query("paymentMethodType") String paymentMethodType);

    @POST(Constants.HTTP_URL.anchor_pay_saveInfo)
    Flowable<BaseResult<Object>> anchorPaySaveInfo(@Body AnchorPaySaveInfo anchorPaySaveInfo);


    /**
     * 获取黑名单列表
     */
    @POST(Constants.HTTP_URL.get_black_list)
    Flowable<BaseResult<BlackListBean>> getBlackList(@Body Map<String, Object> map);

    /**
     * 粉丝
     */
    @POST(Constants.HTTP_URL.get_fans_list)
    Flowable<BaseResult<FansListBean>> getFansList(@Body Map<String, Object> map);

    @GET(Constants.HTTP_URL.get_user_list)
    Flowable<BaseResult<List<GroupListBean>>> getGroupList(@Query("unionId") int unionId);

    /**
     * 等级信息
     */
    @POST(Constants.HTTP_URL.get_level_info)
    Flowable<BaseResult<List<LevelBean.DataDTO>>> getLevelInfo();

    /**
     * 关注
     */
    @POST(Constants.HTTP_URL.get_follow_list)
    Flowable<BaseResult<FansListBean>> getFollowList(@Body Map<String, Object> map);

    /**
     * 从黑名单移除
     */
    @POST(Constants.HTTP_URL.remove_black_list)
    Flowable<BaseResult<Object>> removeBlackList(@Query("blackUserId") long blackUserId, @Query("role") String role);


    @POST(Constants.HTTP_URL.user_set_follow)
    Flowable<BaseResult<Object>> userSetFollow(@Query("id") long UserId, @Query("role") String role);


    @POST(Constants.HTTP_URL.user_set_unfollow)
    Flowable<BaseResult<Object>> userSetUnFollow(@Query("id") long UserId, @Query("role") String role);

    @GET(Constants.HTTP_URL.country_list)
    Flowable<BaseResult<List<CountryBean>>> getCountryList();

    @POST(Constants.HTTP_URL.modifyAnchorUpdateInfo)
    Flowable<BaseResult<Object>> updateAnchorBasicInfo(@Body JsonObject params);

    @POST(Constants.HTTP_URL.modifyAnchorUpdateFiles)
    Flowable<BaseResult<Object>> updateAnchorAlbum(@Body JsonObject map);

    @POST(Constants.HTTP_URL.modifyOnlineStatus)
    Flowable<BaseResult<Object>> setupOnlineStatus(@Query("onlineStatus") String status);

    @GET(Constants.HTTP_URL.get_online_status)
    Flowable<BaseResult<Object>> getOnlineStatus();

    @POST(Constants.HTTP_URL.album_list)
    Flowable<BaseResult<List<AlbumBean>>> getAlbumList(@Body Map<String, String> map);

    @POST(Constants.HTTP_URL.album_delete)
    Flowable<BaseResult<PageBean<AlbumBean>>> albumDelete(@Body List<String> params);

    @POST(Constants.HTTP_URL.gift_list)
    Flowable<BaseResult<PageBean<GiftBean>>> giftList(@Body Map<String, String> params);

    @GET(Constants.HTTP_URL.gift_total_value)
    Flowable<BaseResult<Integer>> giftTotalValue();

    @POST(Constants.HTTP_URL.modify_pwd)
    Flowable<BaseResult<Object>> modifyPassword(@Body Map<String, String> params);

    @GET(Constants.HTTP_URL.anchorEmailCode)
    Flowable<BaseResult<Object>> emailCode(@Query("email") String email);

    @POST(Constants.HTTP_URL.incomeStatistics)
    Flowable<BaseResult<IncomeStatementBean>> incomeStatistics(@Body Map<String, String> params);

    @POST(Constants.HTTP_URL.incomeStatementList)
    Flowable<BaseResult<PageBean<IncomeStatementBean>>> incomeStatementList(@Body Map<String, String> params);

    @POST(Constants.HTTP_URL.settlementList)
    Flowable<BaseResult<PageBean<SettlementBean>>> settlementList(@Body Map<String, String> params);

    @GET(Constants.HTTP_URL.rewardTaskList)
    Flowable<BaseResult<RewardTaskBean>> rewardList();

    @GET(Constants.HTTP_URL.claimTask)
    Flowable<BaseResult<Boolean>> claimTask(@Path("id") String id);

    @POST(Constants.HTTP_URL.setupCallPrice)
    Flowable<BaseResult<Object>> setupPrice(@Body Map<String, String> params);

    @POST(Constants.HTTP_URL.inviteIntroducedList)
    Flowable<BaseResult<PageBean<InvitationBean.UserDataListDTO>>> inviteIntroducedList(@Body Map<String, String> params);

    @POST(Constants.HTTP_URL.fillInviteCode)
    Flowable<BaseResult<Boolean>> fillInviteCode(@Body Map<String, String> params);

    @POST(Constants.HTTP_URL.uploadRecordVideo)
    Flowable<BaseResult<Boolean>> uploadRecordVideo(@Body Map<String, String> params);

    @GET(Constants.HTTP_URL.profileAuditStatus)
    Flowable<BaseResult<String>> checkProfileAuditStatus();
}
