package com.mobile.anchor.app.module.discover.main.chat.videochat.result

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import com.bumptech.glide.Glide
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.DiscoverHomeVideoChatResultBinding
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.GsonUtil
import anchor.app.base.utils.Logger
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.module.discover.main.chat.videochat.VideoChatViewModel
import com.mobile.anchor.app.module.discover.main.homelist.bean.AnchorItemBean


class VideoResultActivity : BaseActivity<VideoChatViewModel, DiscoverHomeVideoChatResultBinding>() {

    var bean: AnchorItemBean.RecordsDTO? = null
    val userRole get() = intent.getStringExtra("userRole")

    var id = 0L

    override fun getLayoutId(): Int {
        return R.layout.discover_home_video_chat_result
    }

    override fun initView() {
//        id = bean?.id?:0L
        //        pageType = getArguments().getInt("pageType");
//        bean = intent.getSerializableExtra("bean") as? AnchorItemBean.RecordsDTO
        intent.getStringExtra("bean")?.let { string ->
            bean = GsonUtil.GsonToBean<AnchorItemBean.RecordsDTO>(
                string, AnchorItemBean.RecordsDTO::class.java
            )
        }

        bindingView.activity = this
        bindingView.lifecycleOwner = this


        bean?.headFileName?.let {
            if ((bean?.headFileName?.length ?: 0) > 0) {
                Glide.with(mContext).load(bean?.headFileName).into(bindingView!!.head)
            }
        }

        bean?.nickName?.let { bindingView!!.anchorName.text = bean?.nickName }

        bean?.duration?.let { bindingView!!.duration.text = bean?.duration }

        if (bean?.followFlag == "1") {
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_ic_video_10))
        } else {
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_ic_video_09))
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        intent?.getSerializableExtra("bean")?.also { bean = it as? AnchorItemBean.RecordsDTO }
            ?.let {
                loadData()
            }
    }

//    var bean: MomentListBean? = null

    @SuppressLint("DefaultLocale")
    override fun loadData() {
        super.loadData()
        showContentView()
        bean?.channelID?.let {
            viewModel?.getVideoCallTime(this@VideoResultActivity, it)
                ?.observe(this) {
                    showContentView()
                    it?.apply {
                        val totalSeconds = time.toLong()
                        val hours = totalSeconds / 3600
                        val remainderSeconds = totalSeconds % 3600
                        val minutes = remainderSeconds / 60
                        val seconds = remainderSeconds % 60

                        // 格式化输出，确保时、分、秒都保持两位数
                        val formattedTime = if (hours > 0) {
                            java.lang.String.format("%02d:%02d:%02d", hours, minutes, seconds)
                        } else {
                            java.lang.String.format("%02d:%02d", minutes, seconds)
                        }
                        bindingView?.duration?.text = formattedTime
//                bindingView?.income?.text = it.incoming.toString()
//            bean?.duration?.let {  bindingView!!.duration.text = DateTimeUtils.millis2String(it.toLong()*1000) }
                    }
                }
        }
    }

    fun isFollow() {
        if (bean?.followFlag == "1") {
            bean?.followFlag = "0"
            viewModel!!.removeAndFollowing(id, 0, userRole.toString())
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_ic_video_09))
        } else {
            bean?.followFlag = "1"
            viewModel!!.removeAndFollowing(id, 1, userRole.toString())
            bindingView!!.isFollow.setImageDrawable(getDrawable(R.mipmap.discover_ic_video_10))
        }
    }

    var strList = ArrayList<String>()

    /**  <AUTHOR>  Description : Item点击事件
     */


    fun commit() {
        finish()
    }


    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f).init()
    }

}