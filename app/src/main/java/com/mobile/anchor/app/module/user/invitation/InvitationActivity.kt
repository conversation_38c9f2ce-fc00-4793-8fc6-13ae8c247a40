package com.mobile.anchor.app.module.user.invitation

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.databinding.ObservableArrayList
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityInvitationBinding
import com.mobile.anchor.app.databinding.UserItemInvotationBinding
import anchor.app.base.adapter.BindingViewHolder
import anchor.app.base.adapter.ItemClickPresenter
import anchor.app.base.adapter.ItemDecorator
import anchor.app.base.ext.jump
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.module.discover.main.center.PersonalCenterActivity
import com.mobile.anchor.app.module.user.bean.InvitationBean
import com.mobile.anchor.app.module.user.bean.InvitationBean02
import com.mobile.anchor.app.module.user.dialog.QrCodeTipsDialog
import com.mobile.anchor.app.module.user.invitation.adapter.InvitationListItemAdapter
import com.mobile.anchor.app.module.user.invitation.problem.DescriptionActivity
import com.mobile.anchor.app.module.user.wallet.WithdrawActivity
import com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean


/**
 * 设置页面
 */
@Deprecated(
    "This class is deprecated, please use InviteActivity instead.",
)
//@Route(path = ArouterPath.PATH_USER_INVITATION)
class InvitationActivity : BaseActivity<InvitationModel?, ActivityInvitationBinding?>(),
    ItemClickPresenter<Any>, ItemDecorator {

    var adapter: InvitationListItemAdapter? = null
    var dataList: ObservableArrayList<Any>? = null
    var isMoreLoading = false

    override fun getLayoutId(): Int {
        return R.layout.activity_invitation
    }

    override fun initView() {
        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        showContentView()

        bindingView!!.activity = this
        bindingView!!.lifecycleOwner = this
        dataList = ObservableArrayList<Any>()
        adapter = InvitationListItemAdapter(mContext, dataList!!)
        adapter!!.itemDecorator = this
        adapter!!.itemPresenter = this
        val gridLayoutManager = GridLayoutManager(mContext, 1)
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.adapter = adapter
    }


    var googleUrl = ""
    fun imgInvite() {
        QrCodeTipsDialog(this, googleUrl).show()
    }

    fun save() {
        jump(WithdrawActivity::class.java)
        finish()
    }


    override fun loadData() {
        showLoading()
        loadData2(false)
    }

    @SuppressLint("RestrictedApi")
    private fun loadData2(reference: Boolean) {

        viewModel!!.getUserInviteList(SharePreUtil.getUserId()).observe(
            this
        ) { bean01: InvitationBean02 ->


            viewModel!!.getList(SharePreUtil.getUserId()).observe(
                this
            ) { bean: InvitationBean ->
                showContentView()

                bean.userDataList = bean01.records

                if (bean.userDataList.size > 0) {
                    dataList!!.addAll(bean.userDataList)
                }

                if (dataList!!.size == 0) {
//                bindingView!!.LLEmpty.visibility = View.VISIBLE
                }
                adapter!!.notifyDataSetChanged()

                bindingView!!.rewardsRecharge.text = "Up to ${bean.rechargeMax}%"
                bindingView!!.rewardsGift.text = "Up to ${bean.giftMax}%"

                bindingView!!.rewardsGift.text = "Up to ${bean.giftMax}%"

                bindingView!!.day.text = bean.inviteStatic.todayInvites.toString()
                bindingView!!.accumulated.text = bean.inviteStatic.totalInvites.toString()
                bindingView!!.dayreward.text = bean.inviteStatic.todayReward
                bindingView!!.accumulatedreward.text = bean.inviteStatic.totalReward
                googleUrl = bean.googleUrl


                val messages: MutableList<String> = ArrayList()
                for (anchorDetailListDTO in bean.anchorDetailList) {
                    messages.add("${anchorDetailListDTO.nickName} invites friends to earn ${anchorDetailListDTO.rewardAmount}  diamonds in revenue")
                }
                bindingView!!.marqueeView.startWithList(messages)


            }
        }
    }


    /**  <AUTHOR>  Description : Item点击事件
     */
    override fun onItemClick(v: View, item: Any) {

        val bean = item as HistoryListBean.RecordsDTO

        if (v.id == R.id.head && bean.userId.toLong() != 0L) {
            jump(PersonalCenterActivity::class.java, Bundle().apply {
                putLong("id", bean.userId.toLong())
                putString("userRole", "2")
            })
        }

//        BlackListBean.RecordsDTO  bean = (BlackListBean.RecordsDTO) item;
//        viewModel.removeBlackList(bean.getId());

//        ARouter.getInstance().build(ArouterPath.PATH_DISCOVER_VIDEOCHAT).navigation();

//        if (item instanceof MatchItemBean) {
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }else  if (item instanceof ChatItemBean){
//            if(getActivity() instanceof ShopHomePurActivity){
//                ((ShopHomePurActivity)getActivity()).buyDialog((ShopInfoPurBean) item);
//            }
//        }
    }

    /**  <AUTHOR>  Description : 各个样式条目业务逻辑处理 user_item_blacklist
     */
    @SuppressLint("SetTextI18n")
    override fun decorator(
        holder: BindingViewHolder<ViewDataBinding>, position: Int, viewType: Int
    ) {
        val binding01 = holder.binding
        if (binding01 is UserItemInvotationBinding) {
            val bean = dataList!![position] as InvitationBean.UserDataListDTO
            val binding = binding01

            binding01.consumptionType.text = bean.nickName
            binding01.nu.text = (position + 1).toString()
            binding01.head.setTag(R.id.head, position)
            bean.headFileName?.let { it.ifEmpty { null } }?.apply {
                Glide.with(mContext).asBitmap().load(bean.headFileName)
                    .apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .into(object : CustomTarget<Bitmap?>() {
                        override fun onResourceReady(
                            @NonNull resource: Bitmap, @Nullable transition: Transition<in Bitmap?>?
                        ) {
                            if (position == binding01.head.getTag(R.id.head)) {
                                binding01.head.setImageBitmap(resource)
                            }
                        }

                        override fun onLoadCleared(@Nullable placeholder: Drawable?) {}
                    })
            }
        }
    }

    fun query() {
        jump(DescriptionActivity::class.java)
    }


    override fun initImmersionBar() {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f)
            .statusBarColor(R.color.color_FF4B20)
            .navigationBarColor(R.color.color_FB6324)
            .init()
    }

    companion object {
        const val TAG = "SettingActivity"
    }
}