package com.mobile.anchor.app.main.conversion

import android.os.Bundle
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.mobile.anchor.app.R
import anchor.app.base.ext.click
import anchor.app.base.ext.jump
import anchor.app.base.ext.loadAvatar
import anchor.app.base.ext.makeGone
import anchor.app.base.ext.makeVisible
import anchor.app.base.rx.RxBus
import anchor.app.base.rx.RxCodeConstants
import anchor.app.base.utils.GsonUtil
import com.mobile.anchor.app.module.discover.main.center.PersonalCenterActivity
import com.mobile.anchor.app.module.user.follow.bean.FansListBean

class FollowItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.follow_item
    }

    var recordsInfo: FansListBean.RecordsDTO? = null
    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            clickItem {
                jump(PersonalCenterActivity::class.java, Bundle().apply {
                    putLong("id", recordsInfo?.anchorId ?: 0)
                    putString("userRole", "2")
                })
            }
            recordsInfo?.apply {
                img(R.id.iv_avatar)?.loadAvatar(context, headFileName)
                tv(R.id.tv_nickname)?.text = nickName
                tv(R.id.tv_age)?.text = "$age"
                tv(R.id.tv_country)?.text = country
                img(R.id.iv_send_msg)?.click {
                    val hashMap: java.util.HashMap<String, String> =
                        java.util.HashMap<String, String>()
                    hashMap["headFileName"] = headFileName
                    hashMap["nikeName"] = nickName
                    hashMap["id"] = anchorId.toString()

                    RxBus.getDefault().post(
                        RxCodeConstants.JUMP_TYPE_TO_CONVERSATION, GsonUtil.GsonString(hashMap)
                    )
                }
                when (onlineStatus) {
                    "1" -> {
                        view(R.id.fl_online_status)?.makeVisible()
                    }

                    else -> {
                        view(R.id.fl_online_status)?.makeGone()
                    }
                }
            }
        }
    }
}