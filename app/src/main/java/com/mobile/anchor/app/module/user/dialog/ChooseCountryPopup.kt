package com.mobile.anchor.app.module.user.dialog

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.uber.autodispose.android.lifecycle.autoDisposable
import anchor.app.base.retrofit.BaseApiClient
import anchor.app.base.retrofit.RxSchedulers.io
import anchor.app.base.retrofit.RxSchedulers.ui
import anchor.app.base.utils.CountryUtil
import anchor.app.base.view.SideBar
import com.mobile.anchor.app.R
import com.mobile.anchor.app.module.user.api.IUserApi
import com.mobile.anchor.app.module.user.bean.CountryBean
import com.mobile.anchor.app.databinding.PopupChooseCountryBinding
import com.mobile.anchor.app.util.PinyinUtil
import java.util.Locale


class ChooseCountryPopup(activity: Activity, private val onClick: (CountryBean) -> Unit) :
    BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_choose_country

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = DataBindingUtil.bind<PopupChooseCountryBinding>(popupImplView)

        val items = ArrayList<CountryBean>()

        binding?.tvCancel?.setOnClickListener {
            dismiss()
        }

        binding?.ivClose?.setOnClickListener {
            dismiss()
        }

        val adapter = CountryAdapter(items) {
            onClick.invoke(it)
            dismiss()
        }

        BaseApiClient.getInstance().create(IUserApi::class.java).countryList.subscribeOn(io)
            .observeOn(ui).autoDisposable(this, null).subscribe {
                it.data?.let {
                    it.forEach {
                        val pinyin = PinyinUtil.getPingYin(it.countryNameEn)
                        val sortLetter = pinyin.substring(0, 1).uppercase(Locale.getDefault())
                        it.letter = if (sortLetter.matches("[A-Z]".toRegex())) sortLetter.uppercase(
                            Locale.getDefault()
                        ) else "#"
                    }
                    it
                }?.let {
                    items.addAll(it)
                    items.sortWith(Comparator { o1, o2 ->
                        if (o1.letter == "@" || o2.letter == "#") {
                            -1
                        } else if (o1.letter == "#" || o2.letter == "@") {
                            1
                        } else {
                            o1.letter.compareTo(o2.letter)
                        }
                    })
                    adapter.notifyDataSetChanged()
                }
            }

        val manager = binding?.recyclerView?.layoutManager as LinearLayoutManager
        binding.sideBar.setOnTouchingLetterChangedListener(object :
            SideBar.OnTouchingLetterChangedListener {
            override fun onTouchingLetterChanged(s: String) {
                val position: Int = adapter.getPositionForSection(s[0].code)
                if (position != -1) {
                    manager.scrollToPositionWithOffset(position, 0)
                }
            }
        })

        binding.recyclerView.apply {
            this.adapter = adapter
        }
    }
}

class CountryAdapter(
    private var items: List<CountryBean>, private val onClick: (CountryBean) -> Unit
) : RecyclerView.Adapter<CountryAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.user_item_choose_country, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        val section = getSectionForPosition(position)
        if (position == getPositionForSection(section)) {
            holder.tvTag.visibility = View.VISIBLE
            holder.tvTag.text = item.letter
        } else {
            holder.tvTag.visibility = View.GONE
        }
        holder.ivFlag.setBackgroundResource(CountryUtil.getDrawableByName(item.countryCode))
        holder.tvName.text = item.countryNameEn
        holder.itemView.setOnClickListener {
            onClick.invoke(item)
        }
    }

    override fun getItemCount(): Int = items.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvTag: TextView = itemView.findViewById(R.id.tag)
        val tvName: TextView = itemView.findViewById(R.id.name)
        val ivFlag: ImageView = itemView.findViewById(R.id.iv_flag)
    }

    private fun getSectionForPosition(position: Int): Int = items[position].letter[0].code

    fun getPositionForSection(section: Int): Int {
        for (i in 0 until itemCount) {
            val sortStr = items[i].letter
            val firstChar = sortStr.uppercase()[0]
            if (firstChar.toInt() == section) {
                return i
            }
        }
        return -1
    }
}

fun showCountryPopup(activity: Activity, block: (CountryBean) -> Unit) {
    XPopup.Builder(activity).asCustom(ChooseCountryPopup(activity, block)).show()
}
