package com.mobile.anchor.app.module.user.feedback

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.bigkoo.convenientbanner.utils.ScreenUtil
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.entity.LocalMedia
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityFeedbackBinding
import com.mobile.anchor.app.module.user.bean.ReportBean
import me.drakeet.multitype.MultiTypeAdapter
import anchor.app.base.dialog.LoadingDialog
import anchor.app.base.dialog.PicSelectDialogFragment
import anchor.app.base.manager.UserInfoManager
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.KeyboardUtils
import anchor.app.base.utils.PathUtils
import anchor.app.base.utils.RxViewUtils
import anchor.app.base.utils.StringUtils
import anchor.app.base.utils.ToastUtil
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import anchor.app.base.view.at.AtWrapper
import anchor.app.base.view.picbind.binder.OnItemChildClickListener
import anchor.app.base.view.picbind.binder.PublishAddBinder
import anchor.app.base.view.picbind.binder.PublishImageBinder
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.util.Locale

/**
 * 问题反馈
 */
class FeedbackActivity : BaseActivity<FeedbackModel?, ActivityFeedbackBinding?>() {
    private val REQUEST_AT_FRIEND = 1109
    private var mAdpter: MultiTypeAdapter? = null
    private val list: MutableList<Any?> = ArrayList()
    private val selectList: MutableList<String?>? = ArrayList()
    private val selectListLocal: ArrayList<LocalMedia> = ArrayList()
    private val selectMap: MutableMap<String, String> = HashMap()
    private var atWrapper: AtWrapper? = null
    private val locationCity: String? = null

    var reportUserId = ""

    var reportUserRole = ""


    //    public static void startActivity(Context context, TopicInfo topicInfo) {topicInfo
    //        Intent intent = new Intent(context, FeedbackActivity.class);
    //        intent.putExtra(INTENT_DATA, topicInfo);
    //        context.startActivity(intent);
    //    }
    var count = 0
    public override fun getLayoutId(): Int {
        return R.layout.activity_feedback
    }

    override fun initView() {
        setTitleText(R.string.Contact_us)
        showContentView()
        //        setStatusBarStyle(Constant.BarStyle.TRAN_BLACK);
//        setAllowAutoHideKeyboard(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            bindingView!!.et.requestFocus()
        }
        bindingView!!.vPlace.setOnTouchListener { v: View?, event: MotionEvent? ->
            KeyboardUtils.hideSoftInput(
                bindingView!!.et
            )
            false
        }
        RxViewUtils.setOnClickListeners(bindingView!!.tvPublish) { view: View? ->
            val editable = bindingView!!.et.text
            if (TextUtils.isEmpty(editable.toString())) {
                ToastUtil.show("Please enter your feedback content~")
                return@setOnClickListeners
            }
//            if (selectList == null || selectList?.size == 0) {
//                ToastUtil.show("Please upload pictures~")
//                return@setOnClickListeners
//            }
            LoadingDialog.getInstance(this@FeedbackActivity).show()
            val publishDynamicBean = ReportBean()
            publishDynamicBean.reportContent = bindingView!!.et.text.toString()
            publishDynamicBean.reportUserId = UserInfoManager.user()?.id.toString()
            publishDynamicBean.reportUserRole = "1";
            publishDynamicBean.userId = UserInfoManager.user()?.id.toString();
            val upList = ArrayList<String?>()
            if (selectList != null && selectList.size > 0) {
                for (i in selectList.indices) {
                    println("selectList: $i")
                    viewModel!!.uploadPicture(File(selectList[i])).observe(this) { s: String? ->
                        println("selectList  ed: " + selectList[i])
                        if (!TextUtils.isEmpty(s)) {
                            count = count + 1
                            if (count == selectList.size) {
                                extracted(publishDynamicBean)
                            }
                        } else {
                            ToastUtil.show("图片上传失败")
                            LoadingDialog.getInstance(this@FeedbackActivity).dismiss()
                        }
                        upList.add(s)
                    }
                }
                publishDynamicBean.fileUrlList = upList
            } else {
                extracted(publishDynamicBean)
            }
        }
        val gridLayoutManager = GridLayoutManager(this, 3)
        gridLayoutManager.isSmoothScrollbarEnabled = true
        bindingView!!.recyclerView.layoutManager = gridLayoutManager
        bindingView!!.recyclerView.setHasFixedSize(true)
        bindingView!!.recyclerView.isNestedScrollingEnabled = false
        val spanCount = 3
        val spacing = ScreenUtil.dip2px(mContext, 0f)
        bindingView!!.recyclerView.addItemDecoration(
            GridSpacingItemDecoration(
                spanCount,
                spacing,
                false
            )
        )
        mAdpter = MultiTypeAdapter()
        val publishAddBinder = PublishAddBinder()
        publishAddBinder.onItemChildClickListener = onAddItemChildClickListenerAdd
        val publishImageBinder = PublishImageBinder()
        publishImageBinder.onItemChildClickListener = onItemChildClickListener
        mAdpter!!.register(String::class.java, publishImageBinder)
        mAdpter!!.register(Integer::class.java, publishAddBinder)
        bindingView!!.recyclerView.adapter = mAdpter
        mAdpter!!.items = list
        intEt()
        initImg()
        atWrapper = AtWrapper
        atWrapper!!.init(bindingView!!.et)
    }

    private fun extracted(publishDynamicBean: ReportBean) {
        viewModel!!.publicRepoet(this, publishDynamicBean).observe(this) { s1: Any? ->
            ToastUtil.show(getString(R.string.b92))
            LoadingDialog.getInstance(this@FeedbackActivity).dismiss()
            finish()
        }
    }

    //监听输入字数
    private fun intEt() {
        bindingView!!.et.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                temp = s
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                val selectionEnd = bindingView!!.et.selectionEnd
                val selectionStart = bindingView!!.et.selectionStart
                if (selectionEnd == selectionStart && selectionEnd > 0) {
                    val c = s[selectionEnd - 1]
                    if (count == 1 && c == '@') {
                        bindingView!!.et.text.replace(selectionEnd - 1, selectionEnd, "")
                    }
                }
            }

            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable) {
                val editStart = bindingView!!.et.selectionStart
                val editEnd = bindingView!!.et.selectionEnd
                if (temp!!.length > 1000) {
                    s.delete(editStart - 1, editEnd)
                    bindingView!!.et.text = s
                    bindingView!!.et.setSelection(s.length)
                    ToastUtil.show(getString(R.string.b106))

                } else if (temp.toString().endsWith("\n\n")) {
                    s.delete(editStart - 1, editEnd)
                    bindingView!!.et.text = s
                    bindingView!!.et.setSelection(s.length)
                } else if (StringUtils.countStr(temp.toString(), "\n") > 19) {
                    s.delete(editStart - 1, editEnd)
                    bindingView!!.et.text = s
                    bindingView!!.et.setSelection(s.length)
                } else {
                    bindingView!!.tvNum.text = (temp!!.length).toString() + "/1000"
                }
                updataPublishText()
            }
        })
    }

    fun updataPublishText() {
        if (!TextUtils.isEmpty(bindingView!!.et.text.toString())) {
//            bindingView.tvPublish.setBackgroundResource(R.drawable.rectangle_7a2cf6_r15);
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        saveInfo()
    }

    /**
     * 保存信息
     */
    fun saveInfo() {
//        publishDynamicBean = new PublishDynamicBean();
//        publishDynamicBean.setTime(System.currentTimeMillis());
//        if (!TextUtils.isEmpty(bindingView.et.getText().toString())) {
//            publishDynamicBean.setContent(bindingView.et.getText().toString());
//            if (selectList != null && selectList.size() > 0) {
//                publishDynamicBean.setImgPaths(selectList);
//            }
//        } else if (selectList != null && selectList.size() > 0) {
//            publishDynamicBean.setImgPaths(selectList);
//        } else {
//            publishDynamicBean = null;
//        }
    }

    private val mPicSelectDialogFragment: PicSelectDialogFragment? = null
    var onItemChildClickListener = OnItemChildClickListener { view: View, position: Int ->
        val id = view.id
        if (id == R.id.iv_head_id) {
        } else if (id == R.id.ivDelete) {
            selectList!!.removeAt(position)
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                selectListLocal.removeAt(position)
            }
            updateimg()
        }
    }
    private val mCameraUtil = CameraUtil(this)
    var onAddItemChildClickListenerAdd = OnItemChildClickListener { view: View?, position: Int ->

        //小于
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//            PictureSelector.create(FeedbackActivity.this)
//                    .openGallery(PictureMimeType.ofImage())
//                    .theme(R.style.pictureWhiteStyle)
//                    .imageEngine(GlideEngine.createGlideEngine()) // 请参考Demo GlideEngine.java
//                    .isGif(true)
//                    .isCamera(false)
//                    .maxSelectNum(9)// 最大图片选择数量
//                    .minSelectNum(1)// 最小选择数量
//                    .isCompress(true)
//                    .selectionData(selectListLocal)
//                    .forResult(PictureConfig.CHOOSE_REQUEST);
//        } else{
        mCameraUtil.getPic(supportFragmentManager)
    }

    fun initImg() {
        updateimg()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == CameraUtil.RESULT_CODE_SELECT_ALBUM && data != null) {
                //相册获取返回
                onResultDeal(mCameraUtil.getPicture(data.data, false))
            } else if (requestCode == CameraUtil.RESULT_CODE_TAKE_PHOTO) {
                //从相机返回
                onResultDeal(mCameraUtil.getPicture(mCameraUtil.imgUri, true))
            }
        }
    }

    private fun onResultDeal(file: File?) {
        if (file != null) {
            selectList!!.add(file.path)
            updateimg()
            //因为上传图片大小后端有限制 gif 大于3m 的时候就压缩成普通 普通图片大于100kb就压缩
            var mLeastCompressSize = 100
            if (file.path.lowercase(Locale.getDefault()).endsWith(".gif")) {
                mLeastCompressSize = 1024 * 3
            }
            Luban.with(this@FeedbackActivity)
                .load(file.path)
                .ignoreBy(mLeastCompressSize)
                .setTargetDir(PathUtils.getPathIMg())
                .filter { path: String? -> !TextUtils.isEmpty(path) }
                .setCompressListener(object : OnCompressListener {
                    override fun onStart() {}
                    override fun onSuccess(index: Int, f: File?) {
                        f?.let {
                            selectMap[file.path] = f.path
                        }
                    }

                    override fun onError(index: Int, e: Throwable?) {
                        TODO("Not yet implemented")
                    }

                }).launch()
        }
    }

    /**
     * 压缩gif
     */
    fun imgCompress(path: String) {
        Luban.with(this@FeedbackActivity)
            .load(path)
            .ignoreBy(1024 * 3)
            .setTargetDir(PathUtils.getPathIMg())
            .filter { path1: String? -> !TextUtils.isEmpty(path1) }
            .setCompressListener(object : OnCompressListener {
                override fun onStart() {}
                override fun onSuccess(index: Int, f: File?) {
                    if (f != null) {
                        selectMap[path] = f.path
                    }
                }

                override fun onError(index: Int, e: Throwable?) {

                }

            }).launch()
    }

    /**
     * 刷新图片
     */
    fun updateimg() {
        list.clear()
        list.addAll(selectList!!)
        if (selectList.size < 2) {
            val add = 0
            list.add(add)
        }
        bindingView?.ds2?.text = getString(R.string.b88) + "（${selectList.size}/2）"
        mAdpter!!.notifyDataSetChanged()
        updataPublishText()
    }

    //    @Override
    //    public void publishSuccess() {
    //        if (!ActivityUtils.isDestroy(this)) {
    //            EventBus.getDefault().post(new PublishDynamicEvent());
    //            LoadingDialog.getInstance(FeedbackActivity.this).dismiss();
    //            ToastUtil.show(getString(R.string.publish_success));
    //            finish();
    //        }
    //
    //    }
    //
    //    @Override
    //    public void publishFailed(String msg) {
    //        if (!ActivityUtils.isDestroy(this)) {
    //            LoadingDialog.getInstance(FeedbackActivity.this).dismiss();
    //            ToastUtil.show(msg);
    //        }
    //    }
    //
    //    @Override
    //    public void publishNoRight() {
    //        if (!ActivityUtils.isDestroy(this)) {
    //            LoadingDialog.getInstance(FeedbackActivity.this).dismiss();
    //            ToastUtil.show(R.string.msg_dynamic_publish_no_right);
    //        }
    //    }
    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarDarkFont(false, 0.2f).navigationBarColor(R.color.colorTheme).init()
    }

    companion object {
        private const val INTENT_DATA = "intent_topicInfo"
    }
}