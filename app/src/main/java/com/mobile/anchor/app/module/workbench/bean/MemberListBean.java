package com.mobile.anchor.app.module.workbench.bean;

import java.util.List;

/**
 * 奖品Bean
 * Modified by syk on 2020/3/3
 */

public class MemberListBean {

    private Integer current;
    private Boolean hitCount;
    private Integer pages;
    private Boolean searchCount;
    private Integer size;
    private Integer total;
    private List<RecordsDTO> records;

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(Boolean hitCount) {
        this.hitCount = hitCount;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Boolean searchCount) {
        this.searchCount = searchCount;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<RecordsDTO> getRecords() {
        return records;
    }

    public void setRecords(List<RecordsDTO> records) {
        this.records = records;
    }

    public static class RecordsDTO {
        private String checkTime;
        private String connectRate;
        private String country;
        private Integer followFlag;
        private String headFileName;
        private Integer id;
        private Integer incomeDiamond;
        private Double incomeDiamondDecimal;
        private String level;
        private String nickName;
        private String onlineStatus;
        private String onlineTimes;
        private String role;
        private String showVideoUrl;
        private String totalIncome;
        private Integer unionId;
        private String userCode;
        private Integer videoPrice;

        public String getCheckTime() {
            return checkTime;
        }

        public void setCheckTime(String checkTime) {
            this.checkTime = checkTime;
        }

        public String getConnectRate() {
            return connectRate;
        }

        public void setConnectRate(String connectRate) {
            this.connectRate = connectRate;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public Integer getFollowFlag() {
            return followFlag;
        }

        public void setFollowFlag(Integer followFlag) {
            this.followFlag = followFlag;
        }

        public String getHeadFileName() {
            return headFileName;
        }

        public void setHeadFileName(String headFileName) {
            this.headFileName = headFileName;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public Integer getIncomeDiamond() {
            return incomeDiamond;
        }

        public void setIncomeDiamond(Integer incomeDiamond) {
            this.incomeDiamond = incomeDiamond;
        }

        public Double getIncomeDiamondDecimal() {
            return incomeDiamondDecimal;
        }

        public void setIncomeDiamondDecimal(Double incomeDiamondDecimal) {
            this.incomeDiamondDecimal = incomeDiamondDecimal;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getOnlineStatus() {
            return onlineStatus;
        }

        public void setOnlineStatus(String onlineStatus) {
            this.onlineStatus = onlineStatus;
        }

        public String getOnlineTimes() {
            return onlineTimes;
        }

        public void setOnlineTimes(String onlineTimes) {
            this.onlineTimes = onlineTimes;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getShowVideoUrl() {
            return showVideoUrl;
        }

        public void setShowVideoUrl(String showVideoUrl) {
            this.showVideoUrl = showVideoUrl;
        }

        public String getTotalIncome() {
            return totalIncome;
        }

        public void setTotalIncome(String totalIncome) {
            this.totalIncome = totalIncome;
        }

        public Integer getUnionId() {
            return unionId;
        }

        public void setUnionId(Integer unionId) {
            this.unionId = unionId;
        }

        public String getUserCode() {
            return userCode;
        }

        public void setUserCode(String userCode) {
            this.userCode = userCode;
        }

        public Integer getVideoPrice() {
            return videoPrice;
        }

        public void setVideoPrice(Integer videoPrice) {
            this.videoPrice = videoPrice;
        }
    }
}
