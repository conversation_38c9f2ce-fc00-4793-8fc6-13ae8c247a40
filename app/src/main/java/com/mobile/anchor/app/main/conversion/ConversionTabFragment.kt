package com.mobile.anchor.app.main.conversion

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import io.rong.imkit.conversationlist.ConversationListFragment
import com.mobile.anchor.app.R
import anchor.app.base.ext.viewpager2.attach
import anchor.app.base.ext.viewpager2.checkSelected
import anchor.app.base.ui.BaseNoModelFragment
import com.mobile.anchor.app.databinding.FragmentMessageBinding

class ConversionTabFragment : BaseNoModelFragment<FragmentMessageBinding>() {
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val titles = resources.getStringArray(R.array.conversion_tabs)
        bindingView.tab.setTitles(titles)
        setupViewPager()
        bindingView.tab.setViewPager(bindingView.viewPager)
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_message
    }

    private fun setupViewPager() {
        val fragments = mutableListOf<Fragment>()
        fragments.add(ConversationListFragment())
        fragments.add(HistoryCallFragment())
        fragments.add(FragmentFollow())
        bindingView.viewPager.attach(childFragmentManager, lifecycle, fragments) { position ->
            bindingView.tab.setCurrentTab(position)
        }
        bindingView.viewPager.checkSelected(0)
    }
}