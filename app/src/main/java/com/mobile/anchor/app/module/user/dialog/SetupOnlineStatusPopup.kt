package com.mobile.anchor.app.module.user.dialog

import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import anchor.app.base.dialog.SingleTipDialog
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupSetupOnlineStatusBinding

enum class OnlineStatus(val value: String) {
    INVISIBILITY("0"), ONLINE("1"), BUSY("2")
}

class SetupOnlineStatusPopup(
    activity: ComponentActivity,
    private val status: OnlineStatus,
    private val block: (OnlineStatus) -> Unit
) : BottomPopupView(activity) {

    override fun getImplLayoutId(): Int = R.layout.popup_setup_online_status

    override fun initPopupContent() {
        super.initPopupContent()
        var currentStatus = status
        val binding = DataBindingUtil.bind<PopupSetupOnlineStatusBinding>(popupImplView)

        binding?.tvCancel?.setOnClickListener { dismiss() }
        binding?.tvInvisibility?.setOnClickListener {
            currentStatus = OnlineStatus.INVISIBILITY
            changeCheckedStatus(binding.tvInvisibility, binding.tvOnline, binding.tvBusy)
        }
        binding?.ivInvisibilityTips?.setOnClickListener {
            SingleTipDialog.showTips(activity, context.getString(R.string.invisibility_tips), null)
        }
        binding?.tvOnline?.setOnClickListener {
            currentStatus = OnlineStatus.ONLINE
            changeCheckedStatus(binding.tvOnline, binding.tvInvisibility, binding.tvBusy)
        }
        binding?.tvBusy?.setOnClickListener {
            currentStatus = OnlineStatus.BUSY
            changeCheckedStatus(binding.tvBusy, binding.tvOnline, binding.tvInvisibility)
        }
        binding?.tvConfirm?.setOnClickListener {
            dismiss()
            block.invoke(currentStatus)
        }
    }

    private fun changeCheckedStatus(target: TextView, vararg others: TextView) {
        target.setTextColor(
            ContextCompat.getColor(
                activity, R.color.color_333333
            )
        )
        others.forEach {
            it.setTextColor(
                ContextCompat.getColor(
                    activity, R.color.color_999999
                )
            )
        }
    }
}

fun showSetupOnlineStatusPopup(
    activity: ComponentActivity, status: OnlineStatus, block: (OnlineStatus) -> Unit
) {
    XPopup.Builder(activity).asCustom(SetupOnlineStatusPopup(activity, status, block)).show()
}