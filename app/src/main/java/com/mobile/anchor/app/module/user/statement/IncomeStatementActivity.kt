package com.mobile.anchor.app.module.user.statement

import androidx.databinding.ObservableArrayList
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.ActivityIncomeStatementBinding
import com.mobile.anchor.app.module.user.bean.IncomeStatementBean
import anchor.app.base.dialog.CalendarDialog
import anchor.app.base.ui.BaseActivity
import anchor.app.base.utils.immersionbar.standard.ImmersionBar
import com.mobile.anchor.app.module.user.main.UserViewModel
import com.mobile.anchor.app.module.user.statement.adapter.IncomeStatementAdapter
import java.util.Calendar

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/24 9:14
 * @description :主播收益报表
 */
class IncomeStatementActivity : BaseActivity<UserViewModel, ActivityIncomeStatementBinding>() {

    private var pageIndex = 1
    private val params = hashMapOf<String, String>().apply {
        put("startTime", formatCalendar(Calendar.getInstance()))
        put("endTime", formatCalendar(Calendar.getInstance()))
        put("size", "20")
    }
    private var items = ObservableArrayList<IncomeStatementBean>()

    override fun getLayoutId(): Int = R.layout.activity_income_statement

    override fun initImmersionBar() {
        super.initImmersionBar()
        pageIndex.toFloat()
        ImmersionBar.with(this).navigationBarColor(R.color.colorWhite).init()
    }

    override fun initView() {
        showContentView()
        setTitleText(R.string.income_statement)
        setActionImage(R.mipmap.user_ic_calendar)
        setOnActionListener {
            showCalendarDialog()
        }
        val tabs = resources.getStringArray(R.array.income_statement_tabs)
        tabs.forEach {
            bindingView.tabLayout.addTab(bindingView.tabLayout.newTab().apply {
//                TabLayout.Tab.setText = it
                TabLayout.Tab().text = it
            })
        }

        var calendar = Calendar.getInstance()
        bindingView.tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                when (tab?.position) {
                    0 -> calendar = Calendar.getInstance()

                    1 -> {
                        calendar = Calendar.getInstance()
                        calendar.set(
                            Calendar.DAY_OF_MONTH,
                            Calendar.getInstance().get(Calendar.DAY_OF_MONTH) - 2
                        )
                    }

                    2 -> {
                        calendar = Calendar.getInstance()
                        calendar.set(
                            Calendar.DAY_OF_MONTH,
                            Calendar.getInstance().get(Calendar.DAY_OF_MONTH) - 6
                        )
                    }

                    3 -> {
                        calendar = Calendar.getInstance()
                        calendar.set(
                            Calendar.DAY_OF_MONTH,
                            Calendar.getInstance().get(Calendar.DAY_OF_MONTH) - 14
                        )
                    }
                }
                params["startTime"] = formatCalendar(calendar)
                params["endTime"] = formatCalendar(Calendar.getInstance())
                fetchData()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })

        bindingView.refreshLayout.setOnRefreshListener { fetchList(true) }
        bindingView.refreshLayout.setOnLoadMoreListener { fetchList(false) }
        bindingView.refreshLayout.setEnableFooterFollowWhenNoMoreData(true)

        bindingView.recyclerView.adapter = IncomeStatementAdapter(mContext, items)
    }

    private fun showCalendarDialog() {
        val dialog: CalendarDialog = CalendarDialog(this)
        dialog.setOnCalendarListener { left_date, right_date ->
            params["startTime"] = left_date
            params["endTime"] = right_date
            fetchData()
        }
        dialog.show()
    }

    override fun loadData() {
        super.loadData()
        fetchData()
    }

    private fun fetchData() {
        viewModel.getIncomeStatistics(params).observe(this) {
            bindingView.bean = it
        }
        fetchList(true)
    }

    private fun fetchList(refresh: Boolean) {
        pageIndex = if (refresh) 1 else pageIndex + 1
        viewModel?.getIncomeStatementList(params.apply { put("current", pageIndex.toString()) })
            ?.observe(this) {
                if (refresh) {
                    items.clear()
                }
                items.addAll(it)
                bindingView.refreshLayout.finishRefresh()
                bindingView.refreshLayout.setNoMoreData(it.isNullOrEmpty() || it.size < 20)
            }
    }

    private fun formatCalendar(calendar: Calendar): String {
        return "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}-${
            calendar.get(
                Calendar.DAY_OF_MONTH
            )
        }"
    }
}