package com.mobile.anchor.app.module.discover.faceunity;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class authpack {
	public static int sha1_32(byte[] buf){int ret=0;try{byte[] digest=MessageDigest.getInstance("SHA1").digest(buf);return ((int)(digest[0]&0xff)<<24)+((int)(digest[1]&0xff)<<16)+((int)(digest[2]&0xff)<<8)+((int)(digest[3]&0xff)<<0);}catch(Exception e){}return ret;}
	public static byte[] A(){
		byte[] buf=new byte[1227];
		int i=0;
		for(i=70;i<86;i++){ buf[0]=(byte)i; if(sha1_32(buf)==826557923){break;} }
		for(i=-121;i<-98;i++){ buf[1]=(byte)i; if(sha1_32(buf)==1489511496){break;} }
		for(i=3;i<19;i++){ buf[2]=(byte)i; if(sha1_32(buf)==501716881){break;} }
		for(i=4;i<25;i++){ buf[3]=(byte)i; if(sha1_32(buf)==1643410444){break;} }
		for(i=-50;i<-36;i++){ buf[4]=(byte)i; if(sha1_32(buf)==-1690466278){break;} }
		for(i=55;i<82;i++){ buf[5]=(byte)i; if(sha1_32(buf)==2107073842){break;} }
		for(i=110;i<126;i++){ buf[6]=(byte)i; if(sha1_32(buf)==-589683700){break;} }
		for(i=27;i<42;i++){ buf[7]=(byte)i; if(sha1_32(buf)==839229837){break;} }
		for(i=2;i<24;i++){ buf[8]=(byte)i; if(sha1_32(buf)==894875034){break;} }
		for(i=111;i<128;i++){ buf[9]=(byte)i; if(sha1_32(buf)==-1696866979){break;} }
		for(i=30;i<47;i++){ buf[10]=(byte)i; if(sha1_32(buf)==1669727477){break;} }
		for(i=-33;i<-9;i++){ buf[11]=(byte)i; if(sha1_32(buf)==1397265472){break;} }
		for(i=53;i<66;i++){ buf[12]=(byte)i; if(sha1_32(buf)==-365154838){break;} }
		for(i=24;i<34;i++){ buf[13]=(byte)i; if(sha1_32(buf)==-47650971){break;} }
		for(i=100;i<111;i++){ buf[14]=(byte)i; if(sha1_32(buf)==-2124663644){break;} }
		for(i=-34;i<-26;i++){ buf[15]=(byte)i; if(sha1_32(buf)==-1622186294){break;} }
		for(i=-10;i<20;i++){ buf[16]=(byte)i; if(sha1_32(buf)==-222832693){break;} }
		for(i=35;i<51;i++){ buf[17]=(byte)i; if(sha1_32(buf)==-2105287056){break;} }
		for(i=6;i<23;i++){ buf[18]=(byte)i; if(sha1_32(buf)==1438728025){break;} }
		for(i=-127;i<-113;i++){ buf[19]=(byte)i; if(sha1_32(buf)==833385389){break;} }
		for(i=30;i<51;i++){ buf[20]=(byte)i; if(sha1_32(buf)==-1062162537){break;} }
		for(i=-87;i<-71;i++){ buf[21]=(byte)i; if(sha1_32(buf)==2030976852){break;} }
		for(i=-122;i<-96;i++){ buf[22]=(byte)i; if(sha1_32(buf)==536452821){break;} }
		for(i=-54;i<-51;i++){ buf[23]=(byte)i; if(sha1_32(buf)==-998765751){break;} }
		for(i=94;i<114;i++){ buf[24]=(byte)i; if(sha1_32(buf)==1108905631){break;} }
		for(i=-39;i<-18;i++){ buf[25]=(byte)i; if(sha1_32(buf)==-2039447196){break;} }
		for(i=-59;i<-50;i++){ buf[26]=(byte)i; if(sha1_32(buf)==-1325460426){break;} }
		for(i=98;i<102;i++){ buf[27]=(byte)i; if(sha1_32(buf)==211145357){break;} }
		for(i=29;i<33;i++){ buf[28]=(byte)i; if(sha1_32(buf)==-950894033){break;} }
		for(i=-68;i<-54;i++){ buf[29]=(byte)i; if(sha1_32(buf)==-519136126){break;} }
		for(i=-72;i<-45;i++){ buf[30]=(byte)i; if(sha1_32(buf)==24336339){break;} }
		for(i=0;i<16;i++){ buf[31]=(byte)i; if(sha1_32(buf)==-2038262790){break;} }
		for(i=-57;i<-50;i++){ buf[32]=(byte)i; if(sha1_32(buf)==-891462245){break;} }
		for(i=-35;i<-8;i++){ buf[33]=(byte)i; if(sha1_32(buf)==-1501677955){break;} }
		for(i=-3;i<1;i++){ buf[34]=(byte)i; if(sha1_32(buf)==-51043939){break;} }
		for(i=-35;i<-30;i++){ buf[35]=(byte)i; if(sha1_32(buf)==-610372774){break;} }
		for(i=79;i<93;i++){ buf[36]=(byte)i; if(sha1_32(buf)==1736930295){break;} }
		for(i=72;i<86;i++){ buf[37]=(byte)i; if(sha1_32(buf)==-836023575){break;} }
		for(i=-41;i<-27;i++){ buf[38]=(byte)i; if(sha1_32(buf)==-1582898333){break;} }
		for(i=-103;i<-98;i++){ buf[39]=(byte)i; if(sha1_32(buf)==2143847265){break;} }
		for(i=6;i<26;i++){ buf[40]=(byte)i; if(sha1_32(buf)==-1552929816){break;} }
		for(i=-113;i<-105;i++){ buf[41]=(byte)i; if(sha1_32(buf)==1308692628){break;} }
		for(i=-125;i<-111;i++){ buf[42]=(byte)i; if(sha1_32(buf)==399340008){break;} }
		for(i=29;i<42;i++){ buf[43]=(byte)i; if(sha1_32(buf)==-2086767822){break;} }
		for(i=58;i<66;i++){ buf[44]=(byte)i; if(sha1_32(buf)==-913610172){break;} }
		for(i=81;i<101;i++){ buf[45]=(byte)i; if(sha1_32(buf)==180804059){break;} }
		for(i=-33;i<-19;i++){ buf[46]=(byte)i; if(sha1_32(buf)==1834507990){break;} }
		for(i=-63;i<-50;i++){ buf[47]=(byte)i; if(sha1_32(buf)==1027085317){break;} }
		for(i=-54;i<-31;i++){ buf[48]=(byte)i; if(sha1_32(buf)==1830284367){break;} }
		for(i=-126;i<-98;i++){ buf[49]=(byte)i; if(sha1_32(buf)==954306338){break;} }
		for(i=-117;i<-95;i++){ buf[50]=(byte)i; if(sha1_32(buf)==-1215392645){break;} }
		for(i=37;i<43;i++){ buf[51]=(byte)i; if(sha1_32(buf)==-1796664285){break;} }
		for(i=64;i<79;i++){ buf[52]=(byte)i; if(sha1_32(buf)==161424918){break;} }
		for(i=32;i<42;i++){ buf[53]=(byte)i; if(sha1_32(buf)==1594258018){break;} }
		for(i=-61;i<-46;i++){ buf[54]=(byte)i; if(sha1_32(buf)==1162087016){break;} }
		for(i=-71;i<-67;i++){ buf[55]=(byte)i; if(sha1_32(buf)==-1067749459){break;} }
		for(i=22;i<27;i++){ buf[56]=(byte)i; if(sha1_32(buf)==-1744827129){break;} }
		for(i=-46;i<-36;i++){ buf[57]=(byte)i; if(sha1_32(buf)==-21443180){break;} }
		for(i=-89;i<-71;i++){ buf[58]=(byte)i; if(sha1_32(buf)==-810698084){break;} }
		for(i=-128;i<-110;i++){ buf[59]=(byte)i; if(sha1_32(buf)==-1701544045){break;} }
		for(i=-1;i<9;i++){ buf[60]=(byte)i; if(sha1_32(buf)==-2114387924){break;} }
		for(i=-53;i<-40;i++){ buf[61]=(byte)i; if(sha1_32(buf)==-601597526){break;} }
		for(i=69;i<85;i++){ buf[62]=(byte)i; if(sha1_32(buf)==-739739551){break;} }
		for(i=-93;i<-64;i++){ buf[63]=(byte)i; if(sha1_32(buf)==-1192652793){break;} }
		for(i=43;i<55;i++){ buf[64]=(byte)i; if(sha1_32(buf)==-1649252299){break;} }
		for(i=-19;i<-11;i++){ buf[65]=(byte)i; if(sha1_32(buf)==2028758548){break;} }
		for(i=-8;i<14;i++){ buf[66]=(byte)i; if(sha1_32(buf)==-184167291){break;} }
		for(i=-33;i<-18;i++){ buf[67]=(byte)i; if(sha1_32(buf)==-1599511839){break;} }
		for(i=13;i<32;i++){ buf[68]=(byte)i; if(sha1_32(buf)==-1804577803){break;} }
		for(i=11;i<28;i++){ buf[69]=(byte)i; if(sha1_32(buf)==1212384380){break;} }
		for(i=-4;i<-1;i++){ buf[70]=(byte)i; if(sha1_32(buf)==-1745795608){break;} }
		for(i=-62;i<-48;i++){ buf[71]=(byte)i; if(sha1_32(buf)==-1259198411){break;} }
		for(i=-59;i<-47;i++){ buf[72]=(byte)i; if(sha1_32(buf)==593573793){break;} }
		for(i=-52;i<-32;i++){ buf[73]=(byte)i; if(sha1_32(buf)==442246473){break;} }
		for(i=88;i<106;i++){ buf[74]=(byte)i; if(sha1_32(buf)==-1922829559){break;} }
		for(i=-128;i<-110;i++){ buf[75]=(byte)i; if(sha1_32(buf)==-1557278832){break;} }
		for(i=64;i<77;i++){ buf[76]=(byte)i; if(sha1_32(buf)==857095285){break;} }
		for(i=-116;i<-97;i++){ buf[77]=(byte)i; if(sha1_32(buf)==1241796414){break;} }
		for(i=72;i<92;i++){ buf[78]=(byte)i; if(sha1_32(buf)==2093898722){break;} }
		for(i=-64;i<-56;i++){ buf[79]=(byte)i; if(sha1_32(buf)==1216736013){break;} }
		for(i=-13;i<10;i++){ buf[80]=(byte)i; if(sha1_32(buf)==-1591733210){break;} }
		for(i=-86;i<-76;i++){ buf[81]=(byte)i; if(sha1_32(buf)==-1396680206){break;} }
		for(i=-11;i<3;i++){ buf[82]=(byte)i; if(sha1_32(buf)==-940160476){break;} }
		for(i=75;i<97;i++){ buf[83]=(byte)i; if(sha1_32(buf)==-903920955){break;} }
		for(i=-86;i<-62;i++){ buf[84]=(byte)i; if(sha1_32(buf)==1154520508){break;} }
		for(i=-98;i<-79;i++){ buf[85]=(byte)i; if(sha1_32(buf)==1834172611){break;} }
		for(i=-53;i<-33;i++){ buf[86]=(byte)i; if(sha1_32(buf)==-867009877){break;} }
		for(i=-121;i<-96;i++){ buf[87]=(byte)i; if(sha1_32(buf)==-883721928){break;} }
		for(i=58;i<82;i++){ buf[88]=(byte)i; if(sha1_32(buf)==-1590969816){break;} }
		for(i=-3;i<13;i++){ buf[89]=(byte)i; if(sha1_32(buf)==-243401857){break;} }
		for(i=-40;i<-32;i++){ buf[90]=(byte)i; if(sha1_32(buf)==-730858836){break;} }
		for(i=67;i<80;i++){ buf[91]=(byte)i; if(sha1_32(buf)==1764832563){break;} }
		for(i=105;i<121;i++){ buf[92]=(byte)i; if(sha1_32(buf)==-1184135767){break;} }
		for(i=109;i<127;i++){ buf[93]=(byte)i; if(sha1_32(buf)==-1067477766){break;} }
		for(i=-13;i<11;i++){ buf[94]=(byte)i; if(sha1_32(buf)==-2104703680){break;} }
		for(i=-42;i<-20;i++){ buf[95]=(byte)i; if(sha1_32(buf)==-64171329){break;} }
		for(i=-5;i<-1;i++){ buf[96]=(byte)i; if(sha1_32(buf)==910682743){break;} }
		for(i=-67;i<-46;i++){ buf[97]=(byte)i; if(sha1_32(buf)==1554123231){break;} }
		for(i=83;i<98;i++){ buf[98]=(byte)i; if(sha1_32(buf)==-819904513){break;} }
		for(i=84;i<107;i++){ buf[99]=(byte)i; if(sha1_32(buf)==1044367065){break;} }
		for(i=-126;i<-121;i++){ buf[100]=(byte)i; if(sha1_32(buf)==1036889165){break;} }
		for(i=-65;i<-45;i++){ buf[101]=(byte)i; if(sha1_32(buf)==-835554944){break;} }
		for(i=-19;i<-10;i++){ buf[102]=(byte)i; if(sha1_32(buf)==-453079540){break;} }
		for(i=-77;i<-68;i++){ buf[103]=(byte)i; if(sha1_32(buf)==-922296706){break;} }
		for(i=-104;i<-91;i++){ buf[104]=(byte)i; if(sha1_32(buf)==-226968036){break;} }
		for(i=-55;i<-32;i++){ buf[105]=(byte)i; if(sha1_32(buf)==-1914601395){break;} }
		for(i=-15;i<10;i++){ buf[106]=(byte)i; if(sha1_32(buf)==1703652789){break;} }
		for(i=-22;i<-3;i++){ buf[107]=(byte)i; if(sha1_32(buf)==-183577217){break;} }
		for(i=78;i<94;i++){ buf[108]=(byte)i; if(sha1_32(buf)==126291417){break;} }
		for(i=-121;i<-95;i++){ buf[109]=(byte)i; if(sha1_32(buf)==-524981961){break;} }
		for(i=-54;i<-40;i++){ buf[110]=(byte)i; if(sha1_32(buf)==1825849835){break;} }
		for(i=-120;i<-115;i++){ buf[111]=(byte)i; if(sha1_32(buf)==786932070){break;} }
		for(i=125;i<128;i++){ buf[112]=(byte)i; if(sha1_32(buf)==418447396){break;} }
		for(i=47;i<65;i++){ buf[113]=(byte)i; if(sha1_32(buf)==784377653){break;} }
		for(i=90;i<100;i++){ buf[114]=(byte)i; if(sha1_32(buf)==-452094832){break;} }
		for(i=38;i<51;i++){ buf[115]=(byte)i; if(sha1_32(buf)==-782796783){break;} }
		for(i=67;i<92;i++){ buf[116]=(byte)i; if(sha1_32(buf)==-178490156){break;} }
		for(i=104;i<125;i++){ buf[117]=(byte)i; if(sha1_32(buf)==762592986){break;} }
		for(i=16;i<37;i++){ buf[118]=(byte)i; if(sha1_32(buf)==-45508319){break;} }
		for(i=-37;i<-18;i++){ buf[119]=(byte)i; if(sha1_32(buf)==-1928706550){break;} }
		for(i=111;i<121;i++){ buf[120]=(byte)i; if(sha1_32(buf)==1579875454){break;} }
		for(i=-118;i<-107;i++){ buf[121]=(byte)i; if(sha1_32(buf)==-1368408655){break;} }
		for(i=-128;i<-111;i++){ buf[122]=(byte)i; if(sha1_32(buf)==1150683786){break;} }
		for(i=-54;i<-44;i++){ buf[123]=(byte)i; if(sha1_32(buf)==223364356){break;} }
		for(i=-91;i<-78;i++){ buf[124]=(byte)i; if(sha1_32(buf)==158383625){break;} }
		for(i=47;i<60;i++){ buf[125]=(byte)i; if(sha1_32(buf)==1174005701){break;} }
		for(i=-57;i<-37;i++){ buf[126]=(byte)i; if(sha1_32(buf)==-1217827096){break;} }
		for(i=-13;i<12;i++){ buf[127]=(byte)i; if(sha1_32(buf)==2024977663){break;} }
		for(i=-128;i<-116;i++){ buf[128]=(byte)i; if(sha1_32(buf)==742823647){break;} }
		for(i=15;i<37;i++){ buf[129]=(byte)i; if(sha1_32(buf)==1241681439){break;} }
		for(i=-95;i<-85;i++){ buf[130]=(byte)i; if(sha1_32(buf)==-1161462048){break;} }
		for(i=29;i<52;i++){ buf[131]=(byte)i; if(sha1_32(buf)==1256123060){break;} }
		for(i=108;i<123;i++){ buf[132]=(byte)i; if(sha1_32(buf)==693770088){break;} }
		for(i=112;i<128;i++){ buf[133]=(byte)i; if(sha1_32(buf)==-1209637168){break;} }
		for(i=-35;i<-33;i++){ buf[134]=(byte)i; if(sha1_32(buf)==-1955531276){break;} }
		for(i=113;i<118;i++){ buf[135]=(byte)i; if(sha1_32(buf)==2092476330){break;} }
		for(i=-43;i<-22;i++){ buf[136]=(byte)i; if(sha1_32(buf)==-357475643){break;} }
		for(i=-68;i<-49;i++){ buf[137]=(byte)i; if(sha1_32(buf)==-702615){break;} }
		for(i=49;i<63;i++){ buf[138]=(byte)i; if(sha1_32(buf)==1785568436){break;} }
		for(i=-87;i<-74;i++){ buf[139]=(byte)i; if(sha1_32(buf)==1905400936){break;} }
		for(i=104;i<122;i++){ buf[140]=(byte)i; if(sha1_32(buf)==1072385057){break;} }
		for(i=-67;i<-58;i++){ buf[141]=(byte)i; if(sha1_32(buf)==-952924772){break;} }
		for(i=-53;i<-43;i++){ buf[142]=(byte)i; if(sha1_32(buf)==1246426524){break;} }
		for(i=-128;i<-113;i++){ buf[143]=(byte)i; if(sha1_32(buf)==-1558530673){break;} }
		for(i=-112;i<-86;i++){ buf[144]=(byte)i; if(sha1_32(buf)==-494262400){break;} }
		for(i=56;i<73;i++){ buf[145]=(byte)i; if(sha1_32(buf)==475891155){break;} }
		for(i=-127;i<-113;i++){ buf[146]=(byte)i; if(sha1_32(buf)==89464599){break;} }
		for(i=-77;i<-69;i++){ buf[147]=(byte)i; if(sha1_32(buf)==1195215154){break;} }
		for(i=74;i<93;i++){ buf[148]=(byte)i; if(sha1_32(buf)==-513539011){break;} }
		for(i=-94;i<-77;i++){ buf[149]=(byte)i; if(sha1_32(buf)==-416720217){break;} }
		for(i=-73;i<-60;i++){ buf[150]=(byte)i; if(sha1_32(buf)==-948074780){break;} }
		for(i=7;i<23;i++){ buf[151]=(byte)i; if(sha1_32(buf)==-793483843){break;} }
		for(i=24;i<30;i++){ buf[152]=(byte)i; if(sha1_32(buf)==1927773038){break;} }
		for(i=111;i<118;i++){ buf[153]=(byte)i; if(sha1_32(buf)==-1461214800){break;} }
		for(i=-49;i<-41;i++){ buf[154]=(byte)i; if(sha1_32(buf)==-1449178835){break;} }
		for(i=0;i<20;i++){ buf[155]=(byte)i; if(sha1_32(buf)==1151409345){break;} }
		for(i=88;i<98;i++){ buf[156]=(byte)i; if(sha1_32(buf)==-1848783781){break;} }
		for(i=-39;i<-28;i++){ buf[157]=(byte)i; if(sha1_32(buf)==-1050194910){break;} }
		for(i=-32;i<-19;i++){ buf[158]=(byte)i; if(sha1_32(buf)==-261782033){break;} }
		for(i=122;i<128;i++){ buf[159]=(byte)i; if(sha1_32(buf)==-682693581){break;} }
		for(i=-59;i<-36;i++){ buf[160]=(byte)i; if(sha1_32(buf)==-1340643198){break;} }
		for(i=-78;i<-62;i++){ buf[161]=(byte)i; if(sha1_32(buf)==-444467744){break;} }
		for(i=-21;i<-5;i++){ buf[162]=(byte)i; if(sha1_32(buf)==-498986269){break;} }
		for(i=1;i<27;i++){ buf[163]=(byte)i; if(sha1_32(buf)==-1210659555){break;} }
		for(i=22;i<44;i++){ buf[164]=(byte)i; if(sha1_32(buf)==2048899374){break;} }
		for(i=5;i<15;i++){ buf[165]=(byte)i; if(sha1_32(buf)==809630311){break;} }
		for(i=55;i<73;i++){ buf[166]=(byte)i; if(sha1_32(buf)==-1409832970){break;} }
		for(i=31;i<49;i++){ buf[167]=(byte)i; if(sha1_32(buf)==-1014530076){break;} }
		for(i=-75;i<-52;i++){ buf[168]=(byte)i; if(sha1_32(buf)==1291597582){break;} }
		for(i=61;i<66;i++){ buf[169]=(byte)i; if(sha1_32(buf)==-1400914123){break;} }
		for(i=121;i<126;i++){ buf[170]=(byte)i; if(sha1_32(buf)==-95154124){break;} }
		for(i=-5;i<19;i++){ buf[171]=(byte)i; if(sha1_32(buf)==1523433584){break;} }
		for(i=119;i<125;i++){ buf[172]=(byte)i; if(sha1_32(buf)==662531787){break;} }
		for(i=23;i<35;i++){ buf[173]=(byte)i; if(sha1_32(buf)==-858932198){break;} }
		for(i=120;i<128;i++){ buf[174]=(byte)i; if(sha1_32(buf)==-1775434586){break;} }
		for(i=50;i<59;i++){ buf[175]=(byte)i; if(sha1_32(buf)==2074020387){break;} }
		for(i=-106;i<-84;i++){ buf[176]=(byte)i; if(sha1_32(buf)==-1534508733){break;} }
		for(i=108;i<117;i++){ buf[177]=(byte)i; if(sha1_32(buf)==-49321460){break;} }
		for(i=41;i<53;i++){ buf[178]=(byte)i; if(sha1_32(buf)==-1726971172){break;} }
		for(i=-51;i<-36;i++){ buf[179]=(byte)i; if(sha1_32(buf)==-1041474276){break;} }
		for(i=-19;i<7;i++){ buf[180]=(byte)i; if(sha1_32(buf)==-330693953){break;} }
		for(i=-95;i<-72;i++){ buf[181]=(byte)i; if(sha1_32(buf)==-1490284687){break;} }
		for(i=-29;i<-15;i++){ buf[182]=(byte)i; if(sha1_32(buf)==-1324628028){break;} }
		for(i=-9;i<18;i++){ buf[183]=(byte)i; if(sha1_32(buf)==-1744471562){break;} }
		for(i=-123;i<-98;i++){ buf[184]=(byte)i; if(sha1_32(buf)==-1862761927){break;} }
		for(i=21;i<39;i++){ buf[185]=(byte)i; if(sha1_32(buf)==1259852744){break;} }
		for(i=-20;i<4;i++){ buf[186]=(byte)i; if(sha1_32(buf)==1752109152){break;} }
		for(i=55;i<80;i++){ buf[187]=(byte)i; if(sha1_32(buf)==-1454017950){break;} }
		for(i=65;i<83;i++){ buf[188]=(byte)i; if(sha1_32(buf)==2098851096){break;} }
		for(i=-28;i<-14;i++){ buf[189]=(byte)i; if(sha1_32(buf)==-1536010455){break;} }
		for(i=91;i<109;i++){ buf[190]=(byte)i; if(sha1_32(buf)==-327132871){break;} }
		for(i=111;i<122;i++){ buf[191]=(byte)i; if(sha1_32(buf)==1202428948){break;} }
		for(i=-117;i<-99;i++){ buf[192]=(byte)i; if(sha1_32(buf)==1992382754){break;} }
		for(i=-128;i<-109;i++){ buf[193]=(byte)i; if(sha1_32(buf)==-1401165367){break;} }
		for(i=27;i<52;i++){ buf[194]=(byte)i; if(sha1_32(buf)==-860304327){break;} }
		for(i=76;i<88;i++){ buf[195]=(byte)i; if(sha1_32(buf)==-16322280){break;} }
		for(i=59;i<74;i++){ buf[196]=(byte)i; if(sha1_32(buf)==-1103188348){break;} }
		for(i=55;i<65;i++){ buf[197]=(byte)i; if(sha1_32(buf)==1923655933){break;} }
		for(i=31;i<57;i++){ buf[198]=(byte)i; if(sha1_32(buf)==-1672579746){break;} }
		for(i=31;i<41;i++){ buf[199]=(byte)i; if(sha1_32(buf)==770804234){break;} }
		for(i=-109;i<-94;i++){ buf[200]=(byte)i; if(sha1_32(buf)==-726471599){break;} }
		for(i=-51;i<-34;i++){ buf[201]=(byte)i; if(sha1_32(buf)==-1765477651){break;} }
		for(i=16;i<31;i++){ buf[202]=(byte)i; if(sha1_32(buf)==-101193968){break;} }
		for(i=-128;i<-111;i++){ buf[203]=(byte)i; if(sha1_32(buf)==43531134){break;} }
		for(i=-83;i<-69;i++){ buf[204]=(byte)i; if(sha1_32(buf)==-578831003){break;} }
		for(i=-54;i<-36;i++){ buf[205]=(byte)i; if(sha1_32(buf)==-1645107688){break;} }
		for(i=88;i<106;i++){ buf[206]=(byte)i; if(sha1_32(buf)==563382648){break;} }
		for(i=106;i<111;i++){ buf[207]=(byte)i; if(sha1_32(buf)==-2119592669){break;} }
		for(i=-128;i<-112;i++){ buf[208]=(byte)i; if(sha1_32(buf)==676397558){break;} }
		for(i=43;i<60;i++){ buf[209]=(byte)i; if(sha1_32(buf)==-2103688165){break;} }
		for(i=-32;i<-16;i++){ buf[210]=(byte)i; if(sha1_32(buf)==1873071286){break;} }
		for(i=41;i<53;i++){ buf[211]=(byte)i; if(sha1_32(buf)==-362372259){break;} }
		for(i=35;i<53;i++){ buf[212]=(byte)i; if(sha1_32(buf)==2091730717){break;} }
		for(i=24;i<48;i++){ buf[213]=(byte)i; if(sha1_32(buf)==573758067){break;} }
		for(i=75;i<105;i++){ buf[214]=(byte)i; if(sha1_32(buf)==-822733138){break;} }
		for(i=25;i<37;i++){ buf[215]=(byte)i; if(sha1_32(buf)==416624078){break;} }
		for(i=-107;i<-79;i++){ buf[216]=(byte)i; if(sha1_32(buf)==-1928168029){break;} }
		for(i=-126;i<-108;i++){ buf[217]=(byte)i; if(sha1_32(buf)==-1596471012){break;} }
		for(i=11;i<26;i++){ buf[218]=(byte)i; if(sha1_32(buf)==1331015508){break;} }
		for(i=-12;i<3;i++){ buf[219]=(byte)i; if(sha1_32(buf)==2101437592){break;} }
		for(i=34;i<53;i++){ buf[220]=(byte)i; if(sha1_32(buf)==1292223792){break;} }
		for(i=-4;i<4;i++){ buf[221]=(byte)i; if(sha1_32(buf)==1292223792){break;} }
		for(i=-36;i<-16;i++){ buf[222]=(byte)i; if(sha1_32(buf)==127779644){break;} }
		for(i=95;i<101;i++){ buf[223]=(byte)i; if(sha1_32(buf)==806969990){break;} }
		for(i=16;i<40;i++){ buf[224]=(byte)i; if(sha1_32(buf)==206018389){break;} }
		for(i=-122;i<-116;i++){ buf[225]=(byte)i; if(sha1_32(buf)==-1859076683){break;} }
		for(i=31;i<46;i++){ buf[226]=(byte)i; if(sha1_32(buf)==-1373363459){break;} }
		for(i=-128;i<-117;i++){ buf[227]=(byte)i; if(sha1_32(buf)==144536965){break;} }
		for(i=12;i<14;i++){ buf[228]=(byte)i; if(sha1_32(buf)==-1351811456){break;} }
		for(i=-123;i<-115;i++){ buf[229]=(byte)i; if(sha1_32(buf)==-831179388){break;} }
		for(i=10;i<24;i++){ buf[230]=(byte)i; if(sha1_32(buf)==-488554967){break;} }
		for(i=18;i<35;i++){ buf[231]=(byte)i; if(sha1_32(buf)==-1156411638){break;} }
		for(i=-9;i<6;i++){ buf[232]=(byte)i; if(sha1_32(buf)==-1489554287){break;} }
		for(i=-54;i<-40;i++){ buf[233]=(byte)i; if(sha1_32(buf)==1133715177){break;} }
		for(i=-17;i<3;i++){ buf[234]=(byte)i; if(sha1_32(buf)==-651823633){break;} }
		for(i=53;i<66;i++){ buf[235]=(byte)i; if(sha1_32(buf)==1103314222){break;} }
		for(i=-116;i<-97;i++){ buf[236]=(byte)i; if(sha1_32(buf)==764155194){break;} }
		for(i=32;i<52;i++){ buf[237]=(byte)i; if(sha1_32(buf)==644962028){break;} }
		for(i=-34;i<-17;i++){ buf[238]=(byte)i; if(sha1_32(buf)==517065714){break;} }
		for(i=-61;i<-40;i++){ buf[239]=(byte)i; if(sha1_32(buf)==1939058042){break;} }
		for(i=-54;i<-31;i++){ buf[240]=(byte)i; if(sha1_32(buf)==-1892922880){break;} }
		for(i=82;i<86;i++){ buf[241]=(byte)i; if(sha1_32(buf)==2030847019){break;} }
		for(i=-70;i<-60;i++){ buf[242]=(byte)i; if(sha1_32(buf)==-192224844){break;} }
		for(i=-121;i<-116;i++){ buf[243]=(byte)i; if(sha1_32(buf)==-707370752){break;} }
		for(i=-112;i<-101;i++){ buf[244]=(byte)i; if(sha1_32(buf)==-2145189207){break;} }
		for(i=6;i<29;i++){ buf[245]=(byte)i; if(sha1_32(buf)==-1362136314){break;} }
		for(i=-2;i<9;i++){ buf[246]=(byte)i; if(sha1_32(buf)==-515934100){break;} }
		for(i=-98;i<-91;i++){ buf[247]=(byte)i; if(sha1_32(buf)==1894019912){break;} }
		for(i=121;i<128;i++){ buf[248]=(byte)i; if(sha1_32(buf)==578487842){break;} }
		for(i=7;i<13;i++){ buf[249]=(byte)i; if(sha1_32(buf)==284725994){break;} }
		for(i=70;i<83;i++){ buf[250]=(byte)i; if(sha1_32(buf)==1107545883){break;} }
		for(i=43;i<57;i++){ buf[251]=(byte)i; if(sha1_32(buf)==-440932021){break;} }
		for(i=-95;i<-73;i++){ buf[252]=(byte)i; if(sha1_32(buf)==-2134084904){break;} }
		for(i=-46;i<-39;i++){ buf[253]=(byte)i; if(sha1_32(buf)==-2035042300){break;} }
		for(i=-50;i<-35;i++){ buf[254]=(byte)i; if(sha1_32(buf)==-734269641){break;} }
		for(i=-50;i<-29;i++){ buf[255]=(byte)i; if(sha1_32(buf)==-1818813010){break;} }
		for(i=116;i<128;i++){ buf[256]=(byte)i; if(sha1_32(buf)==-1576956142){break;} }
		for(i=6;i<10;i++){ buf[257]=(byte)i; if(sha1_32(buf)==-1568228251){break;} }
		for(i=110;i<121;i++){ buf[258]=(byte)i; if(sha1_32(buf)==-1404769024){break;} }
		for(i=-128;i<-116;i++){ buf[259]=(byte)i; if(sha1_32(buf)==588107435){break;} }
		for(i=36;i<50;i++){ buf[260]=(byte)i; if(sha1_32(buf)==-2129291173){break;} }
		for(i=78;i<98;i++){ buf[261]=(byte)i; if(sha1_32(buf)==-577680440){break;} }
		for(i=8;i<37;i++){ buf[262]=(byte)i; if(sha1_32(buf)==1691436040){break;} }
		for(i=114;i<128;i++){ buf[263]=(byte)i; if(sha1_32(buf)==-1397377600){break;} }
		for(i=-101;i<-81;i++){ buf[264]=(byte)i; if(sha1_32(buf)==-1294998805){break;} }
		for(i=8;i<23;i++){ buf[265]=(byte)i; if(sha1_32(buf)==678533224){break;} }
		for(i=81;i<93;i++){ buf[266]=(byte)i; if(sha1_32(buf)==-38876626){break;} }
		for(i=-14;i<9;i++){ buf[267]=(byte)i; if(sha1_32(buf)==2064368272){break;} }
		for(i=-128;i<-117;i++){ buf[268]=(byte)i; if(sha1_32(buf)==1176093289){break;} }
		for(i=21;i<47;i++){ buf[269]=(byte)i; if(sha1_32(buf)==-774244865){break;} }
		for(i=-107;i<-103;i++){ buf[270]=(byte)i; if(sha1_32(buf)==1460474109){break;} }
		for(i=-120;i<-92;i++){ buf[271]=(byte)i; if(sha1_32(buf)==-1041503668){break;} }
		for(i=29;i<55;i++){ buf[272]=(byte)i; if(sha1_32(buf)==-219176990){break;} }
		for(i=-103;i<-96;i++){ buf[273]=(byte)i; if(sha1_32(buf)==-504133708){break;} }
		for(i=98;i<115;i++){ buf[274]=(byte)i; if(sha1_32(buf)==-1462500662){break;} }
		for(i=-42;i<-19;i++){ buf[275]=(byte)i; if(sha1_32(buf)==1896065094){break;} }
		for(i=31;i<50;i++){ buf[276]=(byte)i; if(sha1_32(buf)==-1307521968){break;} }
		for(i=-42;i<-29;i++){ buf[277]=(byte)i; if(sha1_32(buf)==-451354306){break;} }
		for(i=-47;i<-31;i++){ buf[278]=(byte)i; if(sha1_32(buf)==-228017211){break;} }
		for(i=85;i<104;i++){ buf[279]=(byte)i; if(sha1_32(buf)==1730948719){break;} }
		for(i=-17;i<-13;i++){ buf[280]=(byte)i; if(sha1_32(buf)==1438061176){break;} }
		for(i=-124;i<-111;i++){ buf[281]=(byte)i; if(sha1_32(buf)==-94942266){break;} }
		for(i=117;i<128;i++){ buf[282]=(byte)i; if(sha1_32(buf)==1818638269){break;} }
		for(i=-4;i<3;i++){ buf[283]=(byte)i; if(sha1_32(buf)==393299409){break;} }
		for(i=-36;i<-29;i++){ buf[284]=(byte)i; if(sha1_32(buf)==786740510){break;} }
		for(i=96;i<104;i++){ buf[285]=(byte)i; if(sha1_32(buf)==1189625472){break;} }
		for(i=-76;i<-52;i++){ buf[286]=(byte)i; if(sha1_32(buf)==1286578309){break;} }
		for(i=-101;i<-73;i++){ buf[287]=(byte)i; if(sha1_32(buf)==1849392913){break;} }
		for(i=40;i<55;i++){ buf[288]=(byte)i; if(sha1_32(buf)==2022747524){break;} }
		for(i=114;i<123;i++){ buf[289]=(byte)i; if(sha1_32(buf)==-2128971023){break;} }
		for(i=-123;i<-102;i++){ buf[290]=(byte)i; if(sha1_32(buf)==-683722493){break;} }
		for(i=53;i<71;i++){ buf[291]=(byte)i; if(sha1_32(buf)==1922204989){break;} }
		for(i=-8;i<18;i++){ buf[292]=(byte)i; if(sha1_32(buf)==-101730788){break;} }
		for(i=116;i<128;i++){ buf[293]=(byte)i; if(sha1_32(buf)==-1829080905){break;} }
		for(i=98;i<126;i++){ buf[294]=(byte)i; if(sha1_32(buf)==-452564545){break;} }
		for(i=38;i<53;i++){ buf[295]=(byte)i; if(sha1_32(buf)==-748481498){break;} }
		for(i=-128;i<-118;i++){ buf[296]=(byte)i; if(sha1_32(buf)==1093349069){break;} }
		for(i=-68;i<-51;i++){ buf[297]=(byte)i; if(sha1_32(buf)==402368055){break;} }
		for(i=-73;i<-62;i++){ buf[298]=(byte)i; if(sha1_32(buf)==-1403728879){break;} }
		for(i=-84;i<-70;i++){ buf[299]=(byte)i; if(sha1_32(buf)==-107097347){break;} }
		for(i=16;i<46;i++){ buf[300]=(byte)i; if(sha1_32(buf)==-1104303284){break;} }
		for(i=60;i<69;i++){ buf[301]=(byte)i; if(sha1_32(buf)==-1503794444){break;} }
		for(i=-43;i<-28;i++){ buf[302]=(byte)i; if(sha1_32(buf)==-919802437){break;} }
		for(i=118;i<125;i++){ buf[303]=(byte)i; if(sha1_32(buf)==1397116054){break;} }
		for(i=17;i<39;i++){ buf[304]=(byte)i; if(sha1_32(buf)==-700067040){break;} }
		for(i=80;i<90;i++){ buf[305]=(byte)i; if(sha1_32(buf)==1865088458){break;} }
		for(i=65;i<82;i++){ buf[306]=(byte)i; if(sha1_32(buf)==371991455){break;} }
		for(i=53;i<81;i++){ buf[307]=(byte)i; if(sha1_32(buf)==1768007288){break;} }
		for(i=-128;i<-118;i++){ buf[308]=(byte)i; if(sha1_32(buf)==-1410862289){break;} }
		for(i=-52;i<-42;i++){ buf[309]=(byte)i; if(sha1_32(buf)==121403610){break;} }
		for(i=-30;i<-3;i++){ buf[310]=(byte)i; if(sha1_32(buf)==-726315320){break;} }
		for(i=-60;i<-42;i++){ buf[311]=(byte)i; if(sha1_32(buf)==1234301879){break;} }
		for(i=-59;i<-36;i++){ buf[312]=(byte)i; if(sha1_32(buf)==1422634646){break;} }
		for(i=-51;i<-30;i++){ buf[313]=(byte)i; if(sha1_32(buf)==392874903){break;} }
		for(i=-113;i<-94;i++){ buf[314]=(byte)i; if(sha1_32(buf)==126438732){break;} }
		for(i=-108;i<-93;i++){ buf[315]=(byte)i; if(sha1_32(buf)==-84611329){break;} }
		for(i=-11;i<5;i++){ buf[316]=(byte)i; if(sha1_32(buf)==-857041299){break;} }
		for(i=74;i<95;i++){ buf[317]=(byte)i; if(sha1_32(buf)==2019205746){break;} }
		for(i=-114;i<-96;i++){ buf[318]=(byte)i; if(sha1_32(buf)==782616314){break;} }
		for(i=-7;i<18;i++){ buf[319]=(byte)i; if(sha1_32(buf)==-1348394891){break;} }
		for(i=-112;i<-96;i++){ buf[320]=(byte)i; if(sha1_32(buf)==861797643){break;} }
		for(i=32;i<55;i++){ buf[321]=(byte)i; if(sha1_32(buf)==1988963019){break;} }
		for(i=119;i<128;i++){ buf[322]=(byte)i; if(sha1_32(buf)==1415352540){break;} }
		for(i=33;i<53;i++){ buf[323]=(byte)i; if(sha1_32(buf)==-2027855194){break;} }
		for(i=50;i<59;i++){ buf[324]=(byte)i; if(sha1_32(buf)==-299015464){break;} }
		for(i=-116;i<-95;i++){ buf[325]=(byte)i; if(sha1_32(buf)==847550070){break;} }
		for(i=-127;i<-119;i++){ buf[326]=(byte)i; if(sha1_32(buf)==83364883){break;} }
		for(i=-76;i<-59;i++){ buf[327]=(byte)i; if(sha1_32(buf)==-1964171461){break;} }
		for(i=-102;i<-80;i++){ buf[328]=(byte)i; if(sha1_32(buf)==-911440352){break;} }
		for(i=-26;i<-15;i++){ buf[329]=(byte)i; if(sha1_32(buf)==1393813317){break;} }
		for(i=49;i<73;i++){ buf[330]=(byte)i; if(sha1_32(buf)==470431550){break;} }
		for(i=-118;i<-88;i++){ buf[331]=(byte)i; if(sha1_32(buf)==761332674){break;} }
		for(i=113;i<128;i++){ buf[332]=(byte)i; if(sha1_32(buf)==105607315){break;} }
		for(i=-128;i<-118;i++){ buf[333]=(byte)i; if(sha1_32(buf)==768134207){break;} }
		for(i=-6;i<19;i++){ buf[334]=(byte)i; if(sha1_32(buf)==-1207036997){break;} }
		for(i=33;i<55;i++){ buf[335]=(byte)i; if(sha1_32(buf)==-1994418642){break;} }
		for(i=-57;i<-36;i++){ buf[336]=(byte)i; if(sha1_32(buf)==1341155958){break;} }
		for(i=56;i<63;i++){ buf[337]=(byte)i; if(sha1_32(buf)==191139178){break;} }
		for(i=99;i<123;i++){ buf[338]=(byte)i; if(sha1_32(buf)==1524082463){break;} }
		for(i=-29;i<-13;i++){ buf[339]=(byte)i; if(sha1_32(buf)==-1844893315){break;} }
		for(i=2;i<10;i++){ buf[340]=(byte)i; if(sha1_32(buf)==1806800623){break;} }
		for(i=0;i<11;i++){ buf[341]=(byte)i; if(sha1_32(buf)==127973333){break;} }
		for(i=10;i<20;i++){ buf[342]=(byte)i; if(sha1_32(buf)==-1499426095){break;} }
		for(i=-113;i<-100;i++){ buf[343]=(byte)i; if(sha1_32(buf)==282414246){break;} }
		for(i=-97;i<-81;i++){ buf[344]=(byte)i; if(sha1_32(buf)==-950248498){break;} }
		for(i=114;i<128;i++){ buf[345]=(byte)i; if(sha1_32(buf)==-871083184){break;} }
		for(i=48;i<65;i++){ buf[346]=(byte)i; if(sha1_32(buf)==100580923){break;} }
		for(i=83;i<89;i++){ buf[347]=(byte)i; if(sha1_32(buf)==1134372897){break;} }
		for(i=9;i<18;i++){ buf[348]=(byte)i; if(sha1_32(buf)==34118259){break;} }
		for(i=100;i<112;i++){ buf[349]=(byte)i; if(sha1_32(buf)==1010013487){break;} }
		for(i=-27;i<-3;i++){ buf[350]=(byte)i; if(sha1_32(buf)==1291334364){break;} }
		for(i=-15;i<1;i++){ buf[351]=(byte)i; if(sha1_32(buf)==1682392816){break;} }
		for(i=-60;i<-56;i++){ buf[352]=(byte)i; if(sha1_32(buf)==-741400425){break;} }
		for(i=102;i<121;i++){ buf[353]=(byte)i; if(sha1_32(buf)==-1393951230){break;} }
		for(i=-36;i<-23;i++){ buf[354]=(byte)i; if(sha1_32(buf)==-649468838){break;} }
		for(i=58;i<67;i++){ buf[355]=(byte)i; if(sha1_32(buf)==-227160403){break;} }
		for(i=-5;i<8;i++){ buf[356]=(byte)i; if(sha1_32(buf)==1129698424){break;} }
		for(i=-62;i<-55;i++){ buf[357]=(byte)i; if(sha1_32(buf)==-258236441){break;} }
		for(i=31;i<38;i++){ buf[358]=(byte)i; if(sha1_32(buf)==-1754050698){break;} }
		for(i=-37;i<-17;i++){ buf[359]=(byte)i; if(sha1_32(buf)==68875705){break;} }
		for(i=86;i<100;i++){ buf[360]=(byte)i; if(sha1_32(buf)==-1805052451){break;} }
		for(i=-105;i<-81;i++){ buf[361]=(byte)i; if(sha1_32(buf)==-1624044036){break;} }
		for(i=113;i<128;i++){ buf[362]=(byte)i; if(sha1_32(buf)==-262338191){break;} }
		for(i=38;i<52;i++){ buf[363]=(byte)i; if(sha1_32(buf)==1807576922){break;} }
		for(i=-1;i<22;i++){ buf[364]=(byte)i; if(sha1_32(buf)==1706968113){break;} }
		for(i=40;i<67;i++){ buf[365]=(byte)i; if(sha1_32(buf)==95912101){break;} }
		for(i=-58;i<-56;i++){ buf[366]=(byte)i; if(sha1_32(buf)==523833769){break;} }
		for(i=-100;i<-78;i++){ buf[367]=(byte)i; if(sha1_32(buf)==536576117){break;} }
		for(i=-92;i<-72;i++){ buf[368]=(byte)i; if(sha1_32(buf)==-276599138){break;} }
		for(i=-91;i<-64;i++){ buf[369]=(byte)i; if(sha1_32(buf)==-655104650){break;} }
		for(i=43;i<63;i++){ buf[370]=(byte)i; if(sha1_32(buf)==1936395719){break;} }
		for(i=-23;i<-16;i++){ buf[371]=(byte)i; if(sha1_32(buf)==-1112110445){break;} }
		for(i=-62;i<-57;i++){ buf[372]=(byte)i; if(sha1_32(buf)==-22440885){break;} }
		for(i=-128;i<-122;i++){ buf[373]=(byte)i; if(sha1_32(buf)==869914543){break;} }
		for(i=-11;i<-6;i++){ buf[374]=(byte)i; if(sha1_32(buf)==-967766705){break;} }
		for(i=-128;i<-113;i++){ buf[375]=(byte)i; if(sha1_32(buf)==-1194237976){break;} }
		for(i=-81;i<-73;i++){ buf[376]=(byte)i; if(sha1_32(buf)==-2037039809){break;} }
		for(i=7;i<27;i++){ buf[377]=(byte)i; if(sha1_32(buf)==1422871853){break;} }
		for(i=82;i<89;i++){ buf[378]=(byte)i; if(sha1_32(buf)==424645507){break;} }
		for(i=51;i<73;i++){ buf[379]=(byte)i; if(sha1_32(buf)==711965189){break;} }
		for(i=90;i<98;i++){ buf[380]=(byte)i; if(sha1_32(buf)==-1957584800){break;} }
		for(i=-7;i<1;i++){ buf[381]=(byte)i; if(sha1_32(buf)==1604419531){break;} }
		for(i=-27;i<-14;i++){ buf[382]=(byte)i; if(sha1_32(buf)==-928722434){break;} }
		for(i=-104;i<-94;i++){ buf[383]=(byte)i; if(sha1_32(buf)==512944383){break;} }
		for(i=-128;i<-113;i++){ buf[384]=(byte)i; if(sha1_32(buf)==-582437497){break;} }
		for(i=119;i<128;i++){ buf[385]=(byte)i; if(sha1_32(buf)==709039951){break;} }
		for(i=-45;i<-40;i++){ buf[386]=(byte)i; if(sha1_32(buf)==1959903276){break;} }
		for(i=-35;i<-22;i++){ buf[387]=(byte)i; if(sha1_32(buf)==1694513950){break;} }
		for(i=21;i<38;i++){ buf[388]=(byte)i; if(sha1_32(buf)==-538532761){break;} }
		for(i=-114;i<-99;i++){ buf[389]=(byte)i; if(sha1_32(buf)==-40883462){break;} }
		for(i=-30;i<-13;i++){ buf[390]=(byte)i; if(sha1_32(buf)==646144996){break;} }
		for(i=-122;i<-105;i++){ buf[391]=(byte)i; if(sha1_32(buf)==-1248786198){break;} }
		for(i=38;i<55;i++){ buf[392]=(byte)i; if(sha1_32(buf)==-1649445275){break;} }
		for(i=-28;i<-21;i++){ buf[393]=(byte)i; if(sha1_32(buf)==-1257776650){break;} }
		for(i=-116;i<-92;i++){ buf[394]=(byte)i; if(sha1_32(buf)==417301313){break;} }
		for(i=42;i<60;i++){ buf[395]=(byte)i; if(sha1_32(buf)==-1824943120){break;} }
		for(i=3;i<11;i++){ buf[396]=(byte)i; if(sha1_32(buf)==245960659){break;} }
		for(i=-5;i<9;i++){ buf[397]=(byte)i; if(sha1_32(buf)==-300928178){break;} }
		for(i=-111;i<-100;i++){ buf[398]=(byte)i; if(sha1_32(buf)==721050676){break;} }
		for(i=-110;i<-93;i++){ buf[399]=(byte)i; if(sha1_32(buf)==1572318472){break;} }
		for(i=12;i<28;i++){ buf[400]=(byte)i; if(sha1_32(buf)==2036263853){break;} }
		for(i=13;i<33;i++){ buf[401]=(byte)i; if(sha1_32(buf)==-2042872058){break;} }
		for(i=-91;i<-74;i++){ buf[402]=(byte)i; if(sha1_32(buf)==28834399){break;} }
		for(i=85;i<111;i++){ buf[403]=(byte)i; if(sha1_32(buf)==-1086752823){break;} }
		for(i=-87;i<-69;i++){ buf[404]=(byte)i; if(sha1_32(buf)==172350097){break;} }
		for(i=-70;i<-46;i++){ buf[405]=(byte)i; if(sha1_32(buf)==185417000){break;} }
		for(i=65;i<87;i++){ buf[406]=(byte)i; if(sha1_32(buf)==383966446){break;} }
		for(i=38;i<58;i++){ buf[407]=(byte)i; if(sha1_32(buf)==-331280141){break;} }
		for(i=28;i<53;i++){ buf[408]=(byte)i; if(sha1_32(buf)==277726646){break;} }
		for(i=-98;i<-75;i++){ buf[409]=(byte)i; if(sha1_32(buf)==866527176){break;} }
		for(i=88;i<107;i++){ buf[410]=(byte)i; if(sha1_32(buf)==-2014945313){break;} }
		for(i=74;i<86;i++){ buf[411]=(byte)i; if(sha1_32(buf)==1771619962){break;} }
		for(i=14;i<30;i++){ buf[412]=(byte)i; if(sha1_32(buf)==36489632){break;} }
		for(i=62;i<71;i++){ buf[413]=(byte)i; if(sha1_32(buf)==-346321940){break;} }
		for(i=12;i<28;i++){ buf[414]=(byte)i; if(sha1_32(buf)==966789755){break;} }
		for(i=38;i<63;i++){ buf[415]=(byte)i; if(sha1_32(buf)==1428927348){break;} }
		for(i=-94;i<-64;i++){ buf[416]=(byte)i; if(sha1_32(buf)==-36549437){break;} }
		for(i=-105;i<-100;i++){ buf[417]=(byte)i; if(sha1_32(buf)==-88713059){break;} }
		for(i=-84;i<-82;i++){ buf[418]=(byte)i; if(sha1_32(buf)==282482979){break;} }
		for(i=85;i<98;i++){ buf[419]=(byte)i; if(sha1_32(buf)==-1417299982){break;} }
		for(i=63;i<83;i++){ buf[420]=(byte)i; if(sha1_32(buf)==-1886210882){break;} }
		for(i=-110;i<-98;i++){ buf[421]=(byte)i; if(sha1_32(buf)==1545328124){break;} }
		for(i=18;i<26;i++){ buf[422]=(byte)i; if(sha1_32(buf)==-1417289637){break;} }
		for(i=-31;i<-16;i++){ buf[423]=(byte)i; if(sha1_32(buf)==-1543418078){break;} }
		for(i=-53;i<-35;i++){ buf[424]=(byte)i; if(sha1_32(buf)==345132394){break;} }
		for(i=-128;i<-112;i++){ buf[425]=(byte)i; if(sha1_32(buf)==1152380311){break;} }
		for(i=4;i<23;i++){ buf[426]=(byte)i; if(sha1_32(buf)==1119154607){break;} }
		for(i=37;i<46;i++){ buf[427]=(byte)i; if(sha1_32(buf)==-267197792){break;} }
		for(i=-97;i<-88;i++){ buf[428]=(byte)i; if(sha1_32(buf)==1798755926){break;} }
		for(i=114;i<120;i++){ buf[429]=(byte)i; if(sha1_32(buf)==1553912644){break;} }
		for(i=111;i<127;i++){ buf[430]=(byte)i; if(sha1_32(buf)==797703736){break;} }
		for(i=-30;i<-18;i++){ buf[431]=(byte)i; if(sha1_32(buf)==-991573457){break;} }
		for(i=-83;i<-77;i++){ buf[432]=(byte)i; if(sha1_32(buf)==642143603){break;} }
		for(i=-31;i<-30;i++){ buf[433]=(byte)i; if(sha1_32(buf)==1489965995){break;} }
		for(i=-128;i<-107;i++){ buf[434]=(byte)i; if(sha1_32(buf)==288119258){break;} }
		for(i=11;i<25;i++){ buf[435]=(byte)i; if(sha1_32(buf)==-720305110){break;} }
		for(i=-54;i<-38;i++){ buf[436]=(byte)i; if(sha1_32(buf)==1390996546){break;} }
		for(i=-121;i<-119;i++){ buf[437]=(byte)i; if(sha1_32(buf)==-738021997){break;} }
		for(i=35;i<54;i++){ buf[438]=(byte)i; if(sha1_32(buf)==-1333972271){break;} }
		for(i=-76;i<-69;i++){ buf[439]=(byte)i; if(sha1_32(buf)==1454527829){break;} }
		for(i=122;i<128;i++){ buf[440]=(byte)i; if(sha1_32(buf)==-1581898943){break;} }
		for(i=-66;i<-42;i++){ buf[441]=(byte)i; if(sha1_32(buf)==1216714208){break;} }
		for(i=-41;i<-21;i++){ buf[442]=(byte)i; if(sha1_32(buf)==291591544){break;} }
		for(i=15;i<32;i++){ buf[443]=(byte)i; if(sha1_32(buf)==-232514844){break;} }
		for(i=-34;i<-9;i++){ buf[444]=(byte)i; if(sha1_32(buf)==600875785){break;} }
		for(i=102;i<109;i++){ buf[445]=(byte)i; if(sha1_32(buf)==1692096457){break;} }
		for(i=-32;i<-22;i++){ buf[446]=(byte)i; if(sha1_32(buf)==-290824776){break;} }
		for(i=-5;i<18;i++){ buf[447]=(byte)i; if(sha1_32(buf)==1532325582){break;} }
		for(i=-128;i<-124;i++){ buf[448]=(byte)i; if(sha1_32(buf)==-1675934374){break;} }
		for(i=-31;i<-4;i++){ buf[449]=(byte)i; if(sha1_32(buf)==1526330761){break;} }
		for(i=32;i<46;i++){ buf[450]=(byte)i; if(sha1_32(buf)==1248569733){break;} }
		for(i=-56;i<-35;i++){ buf[451]=(byte)i; if(sha1_32(buf)==-2063707589){break;} }
		for(i=7;i<26;i++){ buf[452]=(byte)i; if(sha1_32(buf)==-437970310){break;} }
		for(i=72;i<87;i++){ buf[453]=(byte)i; if(sha1_32(buf)==-497624332){break;} }
		for(i=-105;i<-93;i++){ buf[454]=(byte)i; if(sha1_32(buf)==-609972561){break;} }
		for(i=-67;i<-40;i++){ buf[455]=(byte)i; if(sha1_32(buf)==-536669890){break;} }
		for(i=-72;i<-63;i++){ buf[456]=(byte)i; if(sha1_32(buf)==-438675132){break;} }
		for(i=-94;i<-76;i++){ buf[457]=(byte)i; if(sha1_32(buf)==221707749){break;} }
		for(i=-97;i<-89;i++){ buf[458]=(byte)i; if(sha1_32(buf)==-895002491){break;} }
		for(i=-5;i<6;i++){ buf[459]=(byte)i; if(sha1_32(buf)==1056091031){break;} }
		for(i=-128;i<-108;i++){ buf[460]=(byte)i; if(sha1_32(buf)==664763447){break;} }
		for(i=2;i<15;i++){ buf[461]=(byte)i; if(sha1_32(buf)==-1010141479){break;} }
		for(i=35;i<64;i++){ buf[462]=(byte)i; if(sha1_32(buf)==562375139){break;} }
		for(i=-111;i<-91;i++){ buf[463]=(byte)i; if(sha1_32(buf)==-1214903666){break;} }
		for(i=119;i<128;i++){ buf[464]=(byte)i; if(sha1_32(buf)==-1215328804){break;} }
		for(i=-128;i<-115;i++){ buf[465]=(byte)i; if(sha1_32(buf)==1731450257){break;} }
		for(i=86;i<112;i++){ buf[466]=(byte)i; if(sha1_32(buf)==-524392626){break;} }
		for(i=-18;i<-9;i++){ buf[467]=(byte)i; if(sha1_32(buf)==303925524){break;} }
		for(i=13;i<34;i++){ buf[468]=(byte)i; if(sha1_32(buf)==472519348){break;} }
		for(i=115;i<123;i++){ buf[469]=(byte)i; if(sha1_32(buf)==-588813421){break;} }
		for(i=-89;i<-79;i++){ buf[470]=(byte)i; if(sha1_32(buf)==-1024030226){break;} }
		for(i=-97;i<-90;i++){ buf[471]=(byte)i; if(sha1_32(buf)==-377874710){break;} }
		for(i=46;i<61;i++){ buf[472]=(byte)i; if(sha1_32(buf)==1959622641){break;} }
		for(i=40;i<65;i++){ buf[473]=(byte)i; if(sha1_32(buf)==1372572592){break;} }
		for(i=-113;i<-108;i++){ buf[474]=(byte)i; if(sha1_32(buf)==-455169440){break;} }
		for(i=87;i<111;i++){ buf[475]=(byte)i; if(sha1_32(buf)==-496462632){break;} }
		for(i=122;i<128;i++){ buf[476]=(byte)i; if(sha1_32(buf)==1918049159){break;} }
		for(i=-11;i<16;i++){ buf[477]=(byte)i; if(sha1_32(buf)==1918049159){break;} }
		for(i=35;i<60;i++){ buf[478]=(byte)i; if(sha1_32(buf)==-304928576){break;} }
		for(i=-3;i<15;i++){ buf[479]=(byte)i; if(sha1_32(buf)==-2064298717){break;} }
		for(i=40;i<54;i++){ buf[480]=(byte)i; if(sha1_32(buf)==1318125638){break;} }
		for(i=-77;i<-71;i++){ buf[481]=(byte)i; if(sha1_32(buf)==-1010969081){break;} }
		for(i=-109;i<-84;i++){ buf[482]=(byte)i; if(sha1_32(buf)==835483387){break;} }
		for(i=-104;i<-84;i++){ buf[483]=(byte)i; if(sha1_32(buf)==1160755492){break;} }
		for(i=27;i<38;i++){ buf[484]=(byte)i; if(sha1_32(buf)==-830795748){break;} }
		for(i=-128;i<-114;i++){ buf[485]=(byte)i; if(sha1_32(buf)==-1675640804){break;} }
		for(i=94;i<108;i++){ buf[486]=(byte)i; if(sha1_32(buf)==1966484194){break;} }
		for(i=-30;i<-14;i++){ buf[487]=(byte)i; if(sha1_32(buf)==-499342519){break;} }
		for(i=-54;i<-25;i++){ buf[488]=(byte)i; if(sha1_32(buf)==1541867864){break;} }
		for(i=79;i<89;i++){ buf[489]=(byte)i; if(sha1_32(buf)==-237811446){break;} }
		for(i=-59;i<-44;i++){ buf[490]=(byte)i; if(sha1_32(buf)==-978758857){break;} }
		for(i=-28;i<-5;i++){ buf[491]=(byte)i; if(sha1_32(buf)==-2047745831){break;} }
		for(i=37;i<41;i++){ buf[492]=(byte)i; if(sha1_32(buf)==512528468){break;} }
		for(i=90;i<97;i++){ buf[493]=(byte)i; if(sha1_32(buf)==-642175158){break;} }
		for(i=119;i<128;i++){ buf[494]=(byte)i; if(sha1_32(buf)==1387272319){break;} }
		for(i=0;i<22;i++){ buf[495]=(byte)i; if(sha1_32(buf)==-108983228){break;} }
		for(i=-77;i<-56;i++){ buf[496]=(byte)i; if(sha1_32(buf)==1404656622){break;} }
		for(i=100;i<107;i++){ buf[497]=(byte)i; if(sha1_32(buf)==1103258984){break;} }
		for(i=-44;i<-17;i++){ buf[498]=(byte)i; if(sha1_32(buf)==954564435){break;} }
		for(i=13;i<33;i++){ buf[499]=(byte)i; if(sha1_32(buf)==117162960){break;} }
		for(i=-88;i<-70;i++){ buf[500]=(byte)i; if(sha1_32(buf)==-1596034207){break;} }
		for(i=-30;i<-12;i++){ buf[501]=(byte)i; if(sha1_32(buf)==974802264){break;} }
		for(i=45;i<52;i++){ buf[502]=(byte)i; if(sha1_32(buf)==-393018124){break;} }
		for(i=-125;i<-106;i++){ buf[503]=(byte)i; if(sha1_32(buf)==-972702629){break;} }
		for(i=120;i<126;i++){ buf[504]=(byte)i; if(sha1_32(buf)==1458536056){break;} }
		for(i=-102;i<-71;i++){ buf[505]=(byte)i; if(sha1_32(buf)==824033309){break;} }
		for(i=-59;i<-41;i++){ buf[506]=(byte)i; if(sha1_32(buf)==548330968){break;} }
		for(i=23;i<42;i++){ buf[507]=(byte)i; if(sha1_32(buf)==532849814){break;} }
		for(i=-115;i<-111;i++){ buf[508]=(byte)i; if(sha1_32(buf)==1890484262){break;} }
		for(i=-91;i<-75;i++){ buf[509]=(byte)i; if(sha1_32(buf)==-700556254){break;} }
		for(i=-111;i<-89;i++){ buf[510]=(byte)i; if(sha1_32(buf)==636000699){break;} }
		for(i=-78;i<-71;i++){ buf[511]=(byte)i; if(sha1_32(buf)==2054765994){break;} }
		for(i=-48;i<-20;i++){ buf[512]=(byte)i; if(sha1_32(buf)==-1187013526){break;} }
		for(i=81;i<99;i++){ buf[513]=(byte)i; if(sha1_32(buf)==608036553){break;} }
		for(i=97;i<116;i++){ buf[514]=(byte)i; if(sha1_32(buf)==-595241701){break;} }
		for(i=-66;i<-53;i++){ buf[515]=(byte)i; if(sha1_32(buf)==-1663548112){break;} }
		for(i=-68;i<-60;i++){ buf[516]=(byte)i; if(sha1_32(buf)==-871207953){break;} }
		for(i=-69;i<-48;i++){ buf[517]=(byte)i; if(sha1_32(buf)==-754085845){break;} }
		for(i=120;i<128;i++){ buf[518]=(byte)i; if(sha1_32(buf)==-325302412){break;} }
		for(i=-100;i<-77;i++){ buf[519]=(byte)i; if(sha1_32(buf)==-822895295){break;} }
		for(i=-25;i<-5;i++){ buf[520]=(byte)i; if(sha1_32(buf)==-1146930980){break;} }
		for(i=-58;i<-40;i++){ buf[521]=(byte)i; if(sha1_32(buf)==-667081204){break;} }
		for(i=-50;i<-30;i++){ buf[522]=(byte)i; if(sha1_32(buf)==-2124701951){break;} }
		for(i=124;i<128;i++){ buf[523]=(byte)i; if(sha1_32(buf)==-1701266436){break;} }
		for(i=52;i<64;i++){ buf[524]=(byte)i; if(sha1_32(buf)==335516085){break;} }
		for(i=24;i<36;i++){ buf[525]=(byte)i; if(sha1_32(buf)==-67004454){break;} }
		for(i=84;i<97;i++){ buf[526]=(byte)i; if(sha1_32(buf)==151744907){break;} }
		for(i=33;i<55;i++){ buf[527]=(byte)i; if(sha1_32(buf)==-1396914194){break;} }
		for(i=-16;i<-8;i++){ buf[528]=(byte)i; if(sha1_32(buf)==-1890689907){break;} }
		for(i=31;i<39;i++){ buf[529]=(byte)i; if(sha1_32(buf)==1091020581){break;} }
		for(i=-98;i<-84;i++){ buf[530]=(byte)i; if(sha1_32(buf)==1618254748){break;} }
		for(i=96;i<114;i++){ buf[531]=(byte)i; if(sha1_32(buf)==1986814939){break;} }
		for(i=-90;i<-83;i++){ buf[532]=(byte)i; if(sha1_32(buf)==908143165){break;} }
		for(i=-59;i<-41;i++){ buf[533]=(byte)i; if(sha1_32(buf)==2086165157){break;} }
		for(i=94;i<117;i++){ buf[534]=(byte)i; if(sha1_32(buf)==-867596348){break;} }
		for(i=-70;i<-57;i++){ buf[535]=(byte)i; if(sha1_32(buf)==-1569136793){break;} }
		for(i=-39;i<-15;i++){ buf[536]=(byte)i; if(sha1_32(buf)==510912509){break;} }
		for(i=-26;i<-20;i++){ buf[537]=(byte)i; if(sha1_32(buf)==1256764550){break;} }
		for(i=91;i<107;i++){ buf[538]=(byte)i; if(sha1_32(buf)==1707705406){break;} }
		for(i=23;i<46;i++){ buf[539]=(byte)i; if(sha1_32(buf)==139615398){break;} }
		for(i=-15;i<8;i++){ buf[540]=(byte)i; if(sha1_32(buf)==-412909801){break;} }
		for(i=-47;i<-33;i++){ buf[541]=(byte)i; if(sha1_32(buf)==1899968208){break;} }
		for(i=99;i<125;i++){ buf[542]=(byte)i; if(sha1_32(buf)==636389612){break;} }
		for(i=97;i<115;i++){ buf[543]=(byte)i; if(sha1_32(buf)==112657629){break;} }
		for(i=29;i<44;i++){ buf[544]=(byte)i; if(sha1_32(buf)==780205925){break;} }
		for(i=61;i<78;i++){ buf[545]=(byte)i; if(sha1_32(buf)==-1856669430){break;} }
		for(i=-117;i<-91;i++){ buf[546]=(byte)i; if(sha1_32(buf)==-1582167824){break;} }
		for(i=-13;i<12;i++){ buf[547]=(byte)i; if(sha1_32(buf)==-1582167824){break;} }
		for(i=-86;i<-65;i++){ buf[548]=(byte)i; if(sha1_32(buf)==-1085916920){break;} }
		for(i=-45;i<-34;i++){ buf[549]=(byte)i; if(sha1_32(buf)==-1722760498){break;} }
		for(i=-8;i<16;i++){ buf[550]=(byte)i; if(sha1_32(buf)==47958703){break;} }
		for(i=0;i<30;i++){ buf[551]=(byte)i; if(sha1_32(buf)==849036220){break;} }
		for(i=-67;i<-55;i++){ buf[552]=(byte)i; if(sha1_32(buf)==679058809){break;} }
		for(i=-9;i<1;i++){ buf[553]=(byte)i; if(sha1_32(buf)==-1999151102){break;} }
		for(i=94;i<105;i++){ buf[554]=(byte)i; if(sha1_32(buf)==1583479148){break;} }
		for(i=100;i<114;i++){ buf[555]=(byte)i; if(sha1_32(buf)==-1657594526){break;} }
		for(i=18;i<28;i++){ buf[556]=(byte)i; if(sha1_32(buf)==1982397852){break;} }
		for(i=-41;i<-26;i++){ buf[557]=(byte)i; if(sha1_32(buf)==-281248860){break;} }
		for(i=87;i<102;i++){ buf[558]=(byte)i; if(sha1_32(buf)==548083046){break;} }
		for(i=79;i<85;i++){ buf[559]=(byte)i; if(sha1_32(buf)==-392154950){break;} }
		for(i=-15;i<6;i++){ buf[560]=(byte)i; if(sha1_32(buf)==-392154950){break;} }
		for(i=-15;i<14;i++){ buf[561]=(byte)i; if(sha1_32(buf)==-1694674611){break;} }
		for(i=61;i<75;i++){ buf[562]=(byte)i; if(sha1_32(buf)==-569839745){break;} }
		for(i=-114;i<-96;i++){ buf[563]=(byte)i; if(sha1_32(buf)==241684340){break;} }
		for(i=108;i<128;i++){ buf[564]=(byte)i; if(sha1_32(buf)==-1096190872){break;} }
		for(i=-77;i<-59;i++){ buf[565]=(byte)i; if(sha1_32(buf)==-1911209350){break;} }
		for(i=-67;i<-65;i++){ buf[566]=(byte)i; if(sha1_32(buf)==-2067679546){break;} }
		for(i=-24;i<-8;i++){ buf[567]=(byte)i; if(sha1_32(buf)==-1002590120){break;} }
		for(i=106;i<125;i++){ buf[568]=(byte)i; if(sha1_32(buf)==-706107049){break;} }
		for(i=-90;i<-69;i++){ buf[569]=(byte)i; if(sha1_32(buf)==615688272){break;} }
		for(i=75;i<101;i++){ buf[570]=(byte)i; if(sha1_32(buf)==652276842){break;} }
		for(i=66;i<86;i++){ buf[571]=(byte)i; if(sha1_32(buf)==-1157838724){break;} }
		for(i=-46;i<-30;i++){ buf[572]=(byte)i; if(sha1_32(buf)==460873729){break;} }
		for(i=-128;i<-114;i++){ buf[573]=(byte)i; if(sha1_32(buf)==-651214164){break;} }
		for(i=118;i<128;i++){ buf[574]=(byte)i; if(sha1_32(buf)==1243566832){break;} }
		for(i=31;i<47;i++){ buf[575]=(byte)i; if(sha1_32(buf)==-1050695102){break;} }
		for(i=103;i<114;i++){ buf[576]=(byte)i; if(sha1_32(buf)==-1962968092){break;} }
		for(i=-75;i<-69;i++){ buf[577]=(byte)i; if(sha1_32(buf)==356765510){break;} }
		for(i=-96;i<-75;i++){ buf[578]=(byte)i; if(sha1_32(buf)==-327585701){break;} }
		for(i=-6;i<10;i++){ buf[579]=(byte)i; if(sha1_32(buf)==-1444170528){break;} }
		for(i=-48;i<-33;i++){ buf[580]=(byte)i; if(sha1_32(buf)==1497762479){break;} }
		for(i=-70;i<-62;i++){ buf[581]=(byte)i; if(sha1_32(buf)==704550608){break;} }
		for(i=57;i<73;i++){ buf[582]=(byte)i; if(sha1_32(buf)==-1829827121){break;} }
		for(i=-50;i<-32;i++){ buf[583]=(byte)i; if(sha1_32(buf)==-775805321){break;} }
		for(i=43;i<61;i++){ buf[584]=(byte)i; if(sha1_32(buf)==1606214951){break;} }
		for(i=-100;i<-77;i++){ buf[585]=(byte)i; if(sha1_32(buf)==-67008318){break;} }
		for(i=-88;i<-76;i++){ buf[586]=(byte)i; if(sha1_32(buf)==-949710512){break;} }
		for(i=-66;i<-45;i++){ buf[587]=(byte)i; if(sha1_32(buf)==524859267){break;} }
		for(i=-125;i<-107;i++){ buf[588]=(byte)i; if(sha1_32(buf)==2116273772){break;} }
		for(i=94;i<115;i++){ buf[589]=(byte)i; if(sha1_32(buf)==2029871759){break;} }
		for(i=77;i<93;i++){ buf[590]=(byte)i; if(sha1_32(buf)==-1864644352){break;} }
		for(i=31;i<50;i++){ buf[591]=(byte)i; if(sha1_32(buf)==-656785592){break;} }
		for(i=-61;i<-38;i++){ buf[592]=(byte)i; if(sha1_32(buf)==-215823969){break;} }
		for(i=44;i<50;i++){ buf[593]=(byte)i; if(sha1_32(buf)==1546160241){break;} }
		for(i=-34;i<-22;i++){ buf[594]=(byte)i; if(sha1_32(buf)==2078083672){break;} }
		for(i=108;i<120;i++){ buf[595]=(byte)i; if(sha1_32(buf)==1101840387){break;} }
		for(i=-73;i<-48;i++){ buf[596]=(byte)i; if(sha1_32(buf)==-140493291){break;} }
		for(i=36;i<62;i++){ buf[597]=(byte)i; if(sha1_32(buf)==1552408162){break;} }
		for(i=-91;i<-74;i++){ buf[598]=(byte)i; if(sha1_32(buf)==1836529383){break;} }
		for(i=86;i<99;i++){ buf[599]=(byte)i; if(sha1_32(buf)==2039512395){break;} }
		for(i=92;i<112;i++){ buf[600]=(byte)i; if(sha1_32(buf)==-450757733){break;} }
		for(i=85;i<91;i++){ buf[601]=(byte)i; if(sha1_32(buf)==-304169613){break;} }
		for(i=6;i<24;i++){ buf[602]=(byte)i; if(sha1_32(buf)==-2090586221){break;} }
		for(i=91;i<101;i++){ buf[603]=(byte)i; if(sha1_32(buf)==-1619535788){break;} }
		for(i=69;i<91;i++){ buf[604]=(byte)i; if(sha1_32(buf)==-1481728003){break;} }
		for(i=-54;i<-25;i++){ buf[605]=(byte)i; if(sha1_32(buf)==-1425723394){break;} }
		for(i=-24;i<-20;i++){ buf[606]=(byte)i; if(sha1_32(buf)==-1851884044){break;} }
		for(i=-100;i<-84;i++){ buf[607]=(byte)i; if(sha1_32(buf)==-1387283619){break;} }
		for(i=85;i<108;i++){ buf[608]=(byte)i; if(sha1_32(buf)==477947875){break;} }
		for(i=109;i<128;i++){ buf[609]=(byte)i; if(sha1_32(buf)==1454812686){break;} }
		for(i=42;i<48;i++){ buf[610]=(byte)i; if(sha1_32(buf)==-1688278812){break;} }
		for(i=-110;i<-95;i++){ buf[611]=(byte)i; if(sha1_32(buf)==1963543289){break;} }
		for(i=-54;i<-38;i++){ buf[612]=(byte)i; if(sha1_32(buf)==-292631286){break;} }
		for(i=-14;i<12;i++){ buf[613]=(byte)i; if(sha1_32(buf)==-2043177942){break;} }
		for(i=-57;i<-46;i++){ buf[614]=(byte)i; if(sha1_32(buf)==-1387457061){break;} }
		for(i=107;i<122;i++){ buf[615]=(byte)i; if(sha1_32(buf)==1035519872){break;} }
		for(i=-66;i<-56;i++){ buf[616]=(byte)i; if(sha1_32(buf)==-305219494){break;} }
		for(i=17;i<25;i++){ buf[617]=(byte)i; if(sha1_32(buf)==1194901529){break;} }
		for(i=80;i<97;i++){ buf[618]=(byte)i; if(sha1_32(buf)==-203658335){break;} }
		for(i=6;i<22;i++){ buf[619]=(byte)i; if(sha1_32(buf)==633256701){break;} }
		for(i=-127;i<-113;i++){ buf[620]=(byte)i; if(sha1_32(buf)==1086057925){break;} }
		for(i=46;i<63;i++){ buf[621]=(byte)i; if(sha1_32(buf)==-633205784){break;} }
		for(i=-81;i<-66;i++){ buf[622]=(byte)i; if(sha1_32(buf)==-1190119909){break;} }
		for(i=78;i<94;i++){ buf[623]=(byte)i; if(sha1_32(buf)==-665432476){break;} }
		for(i=-8;i<10;i++){ buf[624]=(byte)i; if(sha1_32(buf)==1509208863){break;} }
		for(i=-117;i<-101;i++){ buf[625]=(byte)i; if(sha1_32(buf)==-928804565){break;} }
		for(i=51;i<62;i++){ buf[626]=(byte)i; if(sha1_32(buf)==1116154581){break;} }
		for(i=-32;i<-20;i++){ buf[627]=(byte)i; if(sha1_32(buf)==-132935936){break;} }
		for(i=-8;i<4;i++){ buf[628]=(byte)i; if(sha1_32(buf)==-1247345555){break;} }
		for(i=52;i<69;i++){ buf[629]=(byte)i; if(sha1_32(buf)==-1281027874){break;} }
		for(i=-120;i<-110;i++){ buf[630]=(byte)i; if(sha1_32(buf)==-1007571742){break;} }
		for(i=70;i<87;i++){ buf[631]=(byte)i; if(sha1_32(buf)==1138990466){break;} }
		for(i=-117;i<-104;i++){ buf[632]=(byte)i; if(sha1_32(buf)==1336449963){break;} }
		for(i=-43;i<-30;i++){ buf[633]=(byte)i; if(sha1_32(buf)==-163270694){break;} }
		for(i=-126;i<-111;i++){ buf[634]=(byte)i; if(sha1_32(buf)==-1600619389){break;} }
		for(i=47;i<49;i++){ buf[635]=(byte)i; if(sha1_32(buf)==-1954592708){break;} }
		for(i=34;i<51;i++){ buf[636]=(byte)i; if(sha1_32(buf)==-167891638){break;} }
		for(i=-94;i<-78;i++){ buf[637]=(byte)i; if(sha1_32(buf)==-1140603336){break;} }
		for(i=-82;i<-71;i++){ buf[638]=(byte)i; if(sha1_32(buf)==325843922){break;} }
		for(i=34;i<48;i++){ buf[639]=(byte)i; if(sha1_32(buf)==1635545137){break;} }
		for(i=53;i<71;i++){ buf[640]=(byte)i; if(sha1_32(buf)==-1125456444){break;} }
		for(i=-11;i<15;i++){ buf[641]=(byte)i; if(sha1_32(buf)==-950509100){break;} }
		for(i=-78;i<-64;i++){ buf[642]=(byte)i; if(sha1_32(buf)==1393128304){break;} }
		for(i=-39;i<-10;i++){ buf[643]=(byte)i; if(sha1_32(buf)==-1736127387){break;} }
		for(i=25;i<48;i++){ buf[644]=(byte)i; if(sha1_32(buf)==312812484){break;} }
		for(i=117;i<127;i++){ buf[645]=(byte)i; if(sha1_32(buf)==-1314125922){break;} }
		for(i=-120;i<-107;i++){ buf[646]=(byte)i; if(sha1_32(buf)==2008794936){break;} }
		for(i=85;i<94;i++){ buf[647]=(byte)i; if(sha1_32(buf)==1710380311){break;} }
		for(i=114;i<116;i++){ buf[648]=(byte)i; if(sha1_32(buf)==1270018158){break;} }
		for(i=58;i<68;i++){ buf[649]=(byte)i; if(sha1_32(buf)==-194112785){break;} }
		for(i=-80;i<-56;i++){ buf[650]=(byte)i; if(sha1_32(buf)==792822499){break;} }
		for(i=-108;i<-87;i++){ buf[651]=(byte)i; if(sha1_32(buf)==-645189945){break;} }
		for(i=-75;i<-68;i++){ buf[652]=(byte)i; if(sha1_32(buf)==-909088827){break;} }
		for(i=-98;i<-97;i++){ buf[653]=(byte)i; if(sha1_32(buf)==628111526){break;} }
		for(i=86;i<107;i++){ buf[654]=(byte)i; if(sha1_32(buf)==-801132418){break;} }
		for(i=17;i<39;i++){ buf[655]=(byte)i; if(sha1_32(buf)==-1032331848){break;} }
		for(i=65;i<73;i++){ buf[656]=(byte)i; if(sha1_32(buf)==-33491460){break;} }
		for(i=45;i<57;i++){ buf[657]=(byte)i; if(sha1_32(buf)==113713647){break;} }
		for(i=-121;i<-109;i++){ buf[658]=(byte)i; if(sha1_32(buf)==-1677932010){break;} }
		for(i=-66;i<-55;i++){ buf[659]=(byte)i; if(sha1_32(buf)==272406637){break;} }
		for(i=32;i<41;i++){ buf[660]=(byte)i; if(sha1_32(buf)==-404174670){break;} }
		for(i=-123;i<-115;i++){ buf[661]=(byte)i; if(sha1_32(buf)==-163760018){break;} }
		for(i=56;i<64;i++){ buf[662]=(byte)i; if(sha1_32(buf)==1602013420){break;} }
		for(i=-40;i<-16;i++){ buf[663]=(byte)i; if(sha1_32(buf)==1594900868){break;} }
		for(i=48;i<69;i++){ buf[664]=(byte)i; if(sha1_32(buf)==468726061){break;} }
		for(i=40;i<57;i++){ buf[665]=(byte)i; if(sha1_32(buf)==1901034877){break;} }
		for(i=-71;i<-45;i++){ buf[666]=(byte)i; if(sha1_32(buf)==-1866649333){break;} }
		for(i=-26;i<-7;i++){ buf[667]=(byte)i; if(sha1_32(buf)==948732905){break;} }
		for(i=17;i<41;i++){ buf[668]=(byte)i; if(sha1_32(buf)==-1437569555){break;} }
		for(i=109;i<115;i++){ buf[669]=(byte)i; if(sha1_32(buf)==-1816007960){break;} }
		for(i=-16;i<5;i++){ buf[670]=(byte)i; if(sha1_32(buf)==1853075154){break;} }
		for(i=-56;i<-32;i++){ buf[671]=(byte)i; if(sha1_32(buf)==-1540465802){break;} }
		for(i=-36;i<-20;i++){ buf[672]=(byte)i; if(sha1_32(buf)==-730096743){break;} }
		for(i=12;i<32;i++){ buf[673]=(byte)i; if(sha1_32(buf)==-1809278353){break;} }
		for(i=-12;i<-1;i++){ buf[674]=(byte)i; if(sha1_32(buf)==-801365478){break;} }
		for(i=112;i<128;i++){ buf[675]=(byte)i; if(sha1_32(buf)==680914222){break;} }
		for(i=-45;i<-31;i++){ buf[676]=(byte)i; if(sha1_32(buf)==-1920055741){break;} }
		for(i=-8;i<9;i++){ buf[677]=(byte)i; if(sha1_32(buf)==-944335857){break;} }
		for(i=121;i<125;i++){ buf[678]=(byte)i; if(sha1_32(buf)==-1800075076){break;} }
		for(i=-67;i<-56;i++){ buf[679]=(byte)i; if(sha1_32(buf)==-1881810489){break;} }
		for(i=-47;i<-44;i++){ buf[680]=(byte)i; if(sha1_32(buf)==-32493186){break;} }
		for(i=83;i<91;i++){ buf[681]=(byte)i; if(sha1_32(buf)==831941812){break;} }
		for(i=57;i<62;i++){ buf[682]=(byte)i; if(sha1_32(buf)==-1154738635){break;} }
		for(i=61;i<67;i++){ buf[683]=(byte)i; if(sha1_32(buf)==-669556255){break;} }
		for(i=25;i<51;i++){ buf[684]=(byte)i; if(sha1_32(buf)==-719530146){break;} }
		for(i=67;i<85;i++){ buf[685]=(byte)i; if(sha1_32(buf)==1394556854){break;} }
		for(i=113;i<128;i++){ buf[686]=(byte)i; if(sha1_32(buf)==1878544504){break;} }
		for(i=-44;i<-28;i++){ buf[687]=(byte)i; if(sha1_32(buf)==-622001013){break;} }
		for(i=-71;i<-55;i++){ buf[688]=(byte)i; if(sha1_32(buf)==1303287019){break;} }
		for(i=-17;i<-1;i++){ buf[689]=(byte)i; if(sha1_32(buf)==-160959802){break;} }
		for(i=-16;i<-1;i++){ buf[690]=(byte)i; if(sha1_32(buf)==-2050802056){break;} }
		for(i=92;i<106;i++){ buf[691]=(byte)i; if(sha1_32(buf)==-2128661008){break;} }
		for(i=-62;i<-42;i++){ buf[692]=(byte)i; if(sha1_32(buf)==1191290167){break;} }
		for(i=-14;i<11;i++){ buf[693]=(byte)i; if(sha1_32(buf)==544041859){break;} }
		for(i=-68;i<-65;i++){ buf[694]=(byte)i; if(sha1_32(buf)==1406757550){break;} }
		for(i=-100;i<-76;i++){ buf[695]=(byte)i; if(sha1_32(buf)==-797397624){break;} }
		for(i=-13;i<12;i++){ buf[696]=(byte)i; if(sha1_32(buf)==-797397624){break;} }
		for(i=104;i<123;i++){ buf[697]=(byte)i; if(sha1_32(buf)==421764726){break;} }
		for(i=107;i<119;i++){ buf[698]=(byte)i; if(sha1_32(buf)==444863842){break;} }
		for(i=87;i<91;i++){ buf[699]=(byte)i; if(sha1_32(buf)==417510926){break;} }
		for(i=2;i<26;i++){ buf[700]=(byte)i; if(sha1_32(buf)==-636788990){break;} }
		for(i=87;i<104;i++){ buf[701]=(byte)i; if(sha1_32(buf)==-371010553){break;} }
		for(i=-29;i<-15;i++){ buf[702]=(byte)i; if(sha1_32(buf)==-602003801){break;} }
		for(i=-14;i<7;i++){ buf[703]=(byte)i; if(sha1_32(buf)==1842407220){break;} }
		for(i=-117;i<-103;i++){ buf[704]=(byte)i; if(sha1_32(buf)==339651193){break;} }
		for(i=73;i<90;i++){ buf[705]=(byte)i; if(sha1_32(buf)==-1876693508){break;} }
		for(i=2;i<26;i++){ buf[706]=(byte)i; if(sha1_32(buf)==545888041){break;} }
		for(i=-55;i<-45;i++){ buf[707]=(byte)i; if(sha1_32(buf)==860017177){break;} }
		for(i=-75;i<-54;i++){ buf[708]=(byte)i; if(sha1_32(buf)==778348738){break;} }
		for(i=3;i<23;i++){ buf[709]=(byte)i; if(sha1_32(buf)==976752145){break;} }
		for(i=-55;i<-47;i++){ buf[710]=(byte)i; if(sha1_32(buf)==-1506232950){break;} }
		for(i=-121;i<-105;i++){ buf[711]=(byte)i; if(sha1_32(buf)==422780955){break;} }
		for(i=-19;i<-6;i++){ buf[712]=(byte)i; if(sha1_32(buf)==-587557694){break;} }
		for(i=-11;i<18;i++){ buf[713]=(byte)i; if(sha1_32(buf)==587117171){break;} }
		for(i=-36;i<-24;i++){ buf[714]=(byte)i; if(sha1_32(buf)==-107235444){break;} }
		for(i=-9;i<6;i++){ buf[715]=(byte)i; if(sha1_32(buf)==-107235444){break;} }
		for(i=-46;i<-35;i++){ buf[716]=(byte)i; if(sha1_32(buf)==1216686931){break;} }
		for(i=-52;i<-30;i++){ buf[717]=(byte)i; if(sha1_32(buf)==-92930638){break;} }
		for(i=-104;i<-91;i++){ buf[718]=(byte)i; if(sha1_32(buf)==1871474997){break;} }
		for(i=-39;i<-35;i++){ buf[719]=(byte)i; if(sha1_32(buf)==-862010827){break;} }
		for(i=109;i<121;i++){ buf[720]=(byte)i; if(sha1_32(buf)==1878801468){break;} }
		for(i=117;i<128;i++){ buf[721]=(byte)i; if(sha1_32(buf)==1868391826){break;} }
		for(i=-42;i<-39;i++){ buf[722]=(byte)i; if(sha1_32(buf)==-1716591246){break;} }
		for(i=52;i<66;i++){ buf[723]=(byte)i; if(sha1_32(buf)==1688032835){break;} }
		for(i=116;i<128;i++){ buf[724]=(byte)i; if(sha1_32(buf)==379512944){break;} }
		for(i=-92;i<-77;i++){ buf[725]=(byte)i; if(sha1_32(buf)==-1248465199){break;} }
		for(i=-54;i<-38;i++){ buf[726]=(byte)i; if(sha1_32(buf)==-455737315){break;} }
		for(i=9;i<31;i++){ buf[727]=(byte)i; if(sha1_32(buf)==-439837579){break;} }
		for(i=123;i<128;i++){ buf[728]=(byte)i; if(sha1_32(buf)==-1683228473){break;} }
		for(i=-11;i<-3;i++){ buf[729]=(byte)i; if(sha1_32(buf)==-810845358){break;} }
		for(i=-16;i<1;i++){ buf[730]=(byte)i; if(sha1_32(buf)==-681780768){break;} }
		for(i=122;i<128;i++){ buf[731]=(byte)i; if(sha1_32(buf)==-992108681){break;} }
		for(i=50;i<56;i++){ buf[732]=(byte)i; if(sha1_32(buf)==-1462701943){break;} }
		for(i=-43;i<-28;i++){ buf[733]=(byte)i; if(sha1_32(buf)==2099407736){break;} }
		for(i=69;i<92;i++){ buf[734]=(byte)i; if(sha1_32(buf)==-541224739){break;} }
		for(i=-4;i<12;i++){ buf[735]=(byte)i; if(sha1_32(buf)==1437306488){break;} }
		for(i=-128;i<-100;i++){ buf[736]=(byte)i; if(sha1_32(buf)==-1558988764){break;} }
		for(i=42;i<55;i++){ buf[737]=(byte)i; if(sha1_32(buf)==1696687691){break;} }
		for(i=112;i<128;i++){ buf[738]=(byte)i; if(sha1_32(buf)==450884772){break;} }
		for(i=50;i<73;i++){ buf[739]=(byte)i; if(sha1_32(buf)==1352130457){break;} }
		for(i=-45;i<-34;i++){ buf[740]=(byte)i; if(sha1_32(buf)==2049704919){break;} }
		for(i=-93;i<-80;i++){ buf[741]=(byte)i; if(sha1_32(buf)==-75256696){break;} }
		for(i=99;i<123;i++){ buf[742]=(byte)i; if(sha1_32(buf)==2126869422){break;} }
		for(i=-37;i<-20;i++){ buf[743]=(byte)i; if(sha1_32(buf)==-1570192578){break;} }
		for(i=-7;i<13;i++){ buf[744]=(byte)i; if(sha1_32(buf)==1891068508){break;} }
		for(i=20;i<34;i++){ buf[745]=(byte)i; if(sha1_32(buf)==933893335){break;} }
		for(i=-1;i<27;i++){ buf[746]=(byte)i; if(sha1_32(buf)==-950859105){break;} }
		for(i=61;i<82;i++){ buf[747]=(byte)i; if(sha1_32(buf)==-1924285277){break;} }
		for(i=-65;i<-55;i++){ buf[748]=(byte)i; if(sha1_32(buf)==534691081){break;} }
		for(i=-40;i<-24;i++){ buf[749]=(byte)i; if(sha1_32(buf)==-1206575347){break;} }
		for(i=-68;i<-51;i++){ buf[750]=(byte)i; if(sha1_32(buf)==1267077263){break;} }
		for(i=-73;i<-60;i++){ buf[751]=(byte)i; if(sha1_32(buf)==389529342){break;} }
		for(i=-2;i<0;i++){ buf[752]=(byte)i; if(sha1_32(buf)==858182702){break;} }
		for(i=42;i<67;i++){ buf[753]=(byte)i; if(sha1_32(buf)==575481665){break;} }
		for(i=35;i<55;i++){ buf[754]=(byte)i; if(sha1_32(buf)==1948425936){break;} }
		for(i=57;i<73;i++){ buf[755]=(byte)i; if(sha1_32(buf)==-408529700){break;} }
		for(i=-87;i<-57;i++){ buf[756]=(byte)i; if(sha1_32(buf)==-969786303){break;} }
		for(i=-85;i<-62;i++){ buf[757]=(byte)i; if(sha1_32(buf)==1719404461){break;} }
		for(i=-96;i<-71;i++){ buf[758]=(byte)i; if(sha1_32(buf)==671138815){break;} }
		for(i=40;i<42;i++){ buf[759]=(byte)i; if(sha1_32(buf)==-452272453){break;} }
		for(i=102;i<125;i++){ buf[760]=(byte)i; if(sha1_32(buf)==548312762){break;} }
		for(i=88;i<117;i++){ buf[761]=(byte)i; if(sha1_32(buf)==2124716431){break;} }
		for(i=89;i<102;i++){ buf[762]=(byte)i; if(sha1_32(buf)==-1748512102){break;} }
		for(i=82;i<99;i++){ buf[763]=(byte)i; if(sha1_32(buf)==853892369){break;} }
		for(i=-100;i<-75;i++){ buf[764]=(byte)i; if(sha1_32(buf)==-68074573){break;} }
		for(i=-1;i<17;i++){ buf[765]=(byte)i; if(sha1_32(buf)==1865398497){break;} }
		for(i=2;i<12;i++){ buf[766]=(byte)i; if(sha1_32(buf)==-783117268){break;} }
		for(i=24;i<28;i++){ buf[767]=(byte)i; if(sha1_32(buf)==317089180){break;} }
		for(i=31;i<51;i++){ buf[768]=(byte)i; if(sha1_32(buf)==1148042098){break;} }
		for(i=76;i<101;i++){ buf[769]=(byte)i; if(sha1_32(buf)==1578693428){break;} }
		for(i=105;i<128;i++){ buf[770]=(byte)i; if(sha1_32(buf)==1282366535){break;} }
		for(i=7;i<27;i++){ buf[771]=(byte)i; if(sha1_32(buf)==46621004){break;} }
		for(i=-46;i<-31;i++){ buf[772]=(byte)i; if(sha1_32(buf)==133969518){break;} }
		for(i=25;i<44;i++){ buf[773]=(byte)i; if(sha1_32(buf)==-808685113){break;} }
		for(i=76;i<85;i++){ buf[774]=(byte)i; if(sha1_32(buf)==-1440690429){break;} }
		for(i=-12;i<-1;i++){ buf[775]=(byte)i; if(sha1_32(buf)==-1867467733){break;} }
		for(i=33;i<36;i++){ buf[776]=(byte)i; if(sha1_32(buf)==21195382){break;} }
		for(i=18;i<35;i++){ buf[777]=(byte)i; if(sha1_32(buf)==-1083812623){break;} }
		for(i=-1;i<9;i++){ buf[778]=(byte)i; if(sha1_32(buf)==939920523){break;} }
		for(i=-38;i<-23;i++){ buf[779]=(byte)i; if(sha1_32(buf)==1481308768){break;} }
		for(i=38;i<46;i++){ buf[780]=(byte)i; if(sha1_32(buf)==90508112){break;} }
		for(i=-92;i<-73;i++){ buf[781]=(byte)i; if(sha1_32(buf)==357798031){break;} }
		for(i=-128;i<-114;i++){ buf[782]=(byte)i; if(sha1_32(buf)==1371394939){break;} }
		for(i=-50;i<-32;i++){ buf[783]=(byte)i; if(sha1_32(buf)==-4685131){break;} }
		for(i=62;i<71;i++){ buf[784]=(byte)i; if(sha1_32(buf)==-1556795649){break;} }
		for(i=18;i<34;i++){ buf[785]=(byte)i; if(sha1_32(buf)==-1628506744){break;} }
		for(i=55;i<68;i++){ buf[786]=(byte)i; if(sha1_32(buf)==-619865866){break;} }
		for(i=20;i<35;i++){ buf[787]=(byte)i; if(sha1_32(buf)==2066530309){break;} }
		for(i=58;i<68;i++){ buf[788]=(byte)i; if(sha1_32(buf)==-505114736){break;} }
		for(i=-14;i<4;i++){ buf[789]=(byte)i; if(sha1_32(buf)==-1281800521){break;} }
		for(i=82;i<90;i++){ buf[790]=(byte)i; if(sha1_32(buf)==-58164003){break;} }
		for(i=-64;i<-52;i++){ buf[791]=(byte)i; if(sha1_32(buf)==507345159){break;} }
		for(i=-64;i<-38;i++){ buf[792]=(byte)i; if(sha1_32(buf)==333230750){break;} }
		for(i=-89;i<-78;i++){ buf[793]=(byte)i; if(sha1_32(buf)==-1690927446){break;} }
		for(i=79;i<97;i++){ buf[794]=(byte)i; if(sha1_32(buf)==1205442032){break;} }
		for(i=-14;i<12;i++){ buf[795]=(byte)i; if(sha1_32(buf)==-1317821083){break;} }
		for(i=30;i<38;i++){ buf[796]=(byte)i; if(sha1_32(buf)==1712316951){break;} }
		for(i=-22;i<2;i++){ buf[797]=(byte)i; if(sha1_32(buf)==-2055107750){break;} }
		for(i=-49;i<-35;i++){ buf[798]=(byte)i; if(sha1_32(buf)==-629639611){break;} }
		for(i=25;i<42;i++){ buf[799]=(byte)i; if(sha1_32(buf)==1027840805){break;} }
		for(i=13;i<22;i++){ buf[800]=(byte)i; if(sha1_32(buf)==1580102253){break;} }
		for(i=119;i<128;i++){ buf[801]=(byte)i; if(sha1_32(buf)==-1835423078){break;} }
		for(i=-78;i<-59;i++){ buf[802]=(byte)i; if(sha1_32(buf)==-724764881){break;} }
		for(i=-112;i<-104;i++){ buf[803]=(byte)i; if(sha1_32(buf)==897449960){break;} }
		for(i=-110;i<-96;i++){ buf[804]=(byte)i; if(sha1_32(buf)==1920760600){break;} }
		for(i=-25;i<-20;i++){ buf[805]=(byte)i; if(sha1_32(buf)==1041666778){break;} }
		for(i=13;i<33;i++){ buf[806]=(byte)i; if(sha1_32(buf)==-1729533223){break;} }
		for(i=50;i<56;i++){ buf[807]=(byte)i; if(sha1_32(buf)==2097677048){break;} }
		for(i=-80;i<-67;i++){ buf[808]=(byte)i; if(sha1_32(buf)==957615013){break;} }
		for(i=96;i<113;i++){ buf[809]=(byte)i; if(sha1_32(buf)==-1008789680){break;} }
		for(i=27;i<43;i++){ buf[810]=(byte)i; if(sha1_32(buf)==-1033031972){break;} }
		for(i=88;i<105;i++){ buf[811]=(byte)i; if(sha1_32(buf)==160261869){break;} }
		for(i=109;i<128;i++){ buf[812]=(byte)i; if(sha1_32(buf)==-1723941108){break;} }
		for(i=-51;i<-44;i++){ buf[813]=(byte)i; if(sha1_32(buf)==2073184577){break;} }
		for(i=114;i<126;i++){ buf[814]=(byte)i; if(sha1_32(buf)==-1061165267){break;} }
		for(i=-118;i<-103;i++){ buf[815]=(byte)i; if(sha1_32(buf)==-250757004){break;} }
		for(i=47;i<51;i++){ buf[816]=(byte)i; if(sha1_32(buf)==545155741){break;} }
		for(i=8;i<25;i++){ buf[817]=(byte)i; if(sha1_32(buf)==-1426906108){break;} }
		for(i=25;i<40;i++){ buf[818]=(byte)i; if(sha1_32(buf)==-757454780){break;} }
		for(i=-20;i<-8;i++){ buf[819]=(byte)i; if(sha1_32(buf)==-1636557031){break;} }
		for(i=-59;i<-37;i++){ buf[820]=(byte)i; if(sha1_32(buf)==1732774099){break;} }
		for(i=-51;i<-29;i++){ buf[821]=(byte)i; if(sha1_32(buf)==1084809095){break;} }
		for(i=19;i<39;i++){ buf[822]=(byte)i; if(sha1_32(buf)==1453299119){break;} }
		for(i=-49;i<-28;i++){ buf[823]=(byte)i; if(sha1_32(buf)==1079382636){break;} }
		for(i=-113;i<-88;i++){ buf[824]=(byte)i; if(sha1_32(buf)==-1950976492){break;} }
		for(i=115;i<119;i++){ buf[825]=(byte)i; if(sha1_32(buf)==173971565){break;} }
		for(i=102;i<121;i++){ buf[826]=(byte)i; if(sha1_32(buf)==85094095){break;} }
		for(i=73;i<85;i++){ buf[827]=(byte)i; if(sha1_32(buf)==1252361366){break;} }
		for(i=63;i<68;i++){ buf[828]=(byte)i; if(sha1_32(buf)==-233256793){break;} }
		for(i=-72;i<-60;i++){ buf[829]=(byte)i; if(sha1_32(buf)==-230272578){break;} }
		for(i=-21;i<-1;i++){ buf[830]=(byte)i; if(sha1_32(buf)==158434563){break;} }
		for(i=37;i<38;i++){ buf[831]=(byte)i; if(sha1_32(buf)==1021138678){break;} }
		for(i=-118;i<-97;i++){ buf[832]=(byte)i; if(sha1_32(buf)==-1438945630){break;} }
		for(i=66;i<81;i++){ buf[833]=(byte)i; if(sha1_32(buf)==244882996){break;} }
		for(i=-39;i<-26;i++){ buf[834]=(byte)i; if(sha1_32(buf)==364904391){break;} }
		for(i=-65;i<-48;i++){ buf[835]=(byte)i; if(sha1_32(buf)==976700527){break;} }
		for(i=-109;i<-92;i++){ buf[836]=(byte)i; if(sha1_32(buf)==358097689){break;} }
		for(i=21;i<47;i++){ buf[837]=(byte)i; if(sha1_32(buf)==1415487546){break;} }
		for(i=26;i<33;i++){ buf[838]=(byte)i; if(sha1_32(buf)==833296499){break;} }
		for(i=-4;i<10;i++){ buf[839]=(byte)i; if(sha1_32(buf)==833296499){break;} }
		for(i=-25;i<-8;i++){ buf[840]=(byte)i; if(sha1_32(buf)==2019017365){break;} }
		for(i=92;i<107;i++){ buf[841]=(byte)i; if(sha1_32(buf)==-757568108){break;} }
		for(i=71;i<99;i++){ buf[842]=(byte)i; if(sha1_32(buf)==-1741140824){break;} }
		for(i=-45;i<-43;i++){ buf[843]=(byte)i; if(sha1_32(buf)==1521069573){break;} }
		for(i=-39;i<-13;i++){ buf[844]=(byte)i; if(sha1_32(buf)==55365875){break;} }
		for(i=55;i<68;i++){ buf[845]=(byte)i; if(sha1_32(buf)==905633234){break;} }
		for(i=-54;i<-33;i++){ buf[846]=(byte)i; if(sha1_32(buf)==-2054564449){break;} }
		for(i=-126;i<-115;i++){ buf[847]=(byte)i; if(sha1_32(buf)==-1821975964){break;} }
		for(i=4;i<11;i++){ buf[848]=(byte)i; if(sha1_32(buf)==-1514496941){break;} }
		for(i=-15;i<-5;i++){ buf[849]=(byte)i; if(sha1_32(buf)==481214928){break;} }
		for(i=-89;i<-67;i++){ buf[850]=(byte)i; if(sha1_32(buf)==1727681756){break;} }
		for(i=-25;i<-12;i++){ buf[851]=(byte)i; if(sha1_32(buf)==-1024285403){break;} }
		for(i=67;i<87;i++){ buf[852]=(byte)i; if(sha1_32(buf)==1610507910){break;} }
		for(i=-75;i<-53;i++){ buf[853]=(byte)i; if(sha1_32(buf)==-1073600793){break;} }
		for(i=-39;i<-23;i++){ buf[854]=(byte)i; if(sha1_32(buf)==-1140511664){break;} }
		for(i=-117;i<-100;i++){ buf[855]=(byte)i; if(sha1_32(buf)==-225017973){break;} }
		for(i=18;i<34;i++){ buf[856]=(byte)i; if(sha1_32(buf)==-699945539){break;} }
		for(i=98;i<111;i++){ buf[857]=(byte)i; if(sha1_32(buf)==745353204){break;} }
		for(i=-3;i<16;i++){ buf[858]=(byte)i; if(sha1_32(buf)==509663357){break;} }
		for(i=-120;i<-105;i++){ buf[859]=(byte)i; if(sha1_32(buf)==146154583){break;} }
		for(i=-61;i<-45;i++){ buf[860]=(byte)i; if(sha1_32(buf)==-1057289775){break;} }
		for(i=60;i<78;i++){ buf[861]=(byte)i; if(sha1_32(buf)==-344742737){break;} }
		for(i=73;i<83;i++){ buf[862]=(byte)i; if(sha1_32(buf)==846883344){break;} }
		for(i=112;i<128;i++){ buf[863]=(byte)i; if(sha1_32(buf)==-421333687){break;} }
		for(i=-2;i<11;i++){ buf[864]=(byte)i; if(sha1_32(buf)==-817210842){break;} }
		for(i=30;i<40;i++){ buf[865]=(byte)i; if(sha1_32(buf)==1221567926){break;} }
		for(i=-48;i<-22;i++){ buf[866]=(byte)i; if(sha1_32(buf)==905093458){break;} }
		for(i=-12;i<9;i++){ buf[867]=(byte)i; if(sha1_32(buf)==1638101114){break;} }
		for(i=-1;i<23;i++){ buf[868]=(byte)i; if(sha1_32(buf)==523222158){break;} }
		for(i=-128;i<-116;i++){ buf[869]=(byte)i; if(sha1_32(buf)==1221986727){break;} }
		for(i=118;i<128;i++){ buf[870]=(byte)i; if(sha1_32(buf)==1010011679){break;} }
		for(i=-45;i<-27;i++){ buf[871]=(byte)i; if(sha1_32(buf)==16254538){break;} }
		for(i=100;i<119;i++){ buf[872]=(byte)i; if(sha1_32(buf)==-544852604){break;} }
		for(i=-51;i<-31;i++){ buf[873]=(byte)i; if(sha1_32(buf)==1891003953){break;} }
		for(i=-96;i<-69;i++){ buf[874]=(byte)i; if(sha1_32(buf)==-193318350){break;} }
		for(i=126;i<128;i++){ buf[875]=(byte)i; if(sha1_32(buf)==-787814979){break;} }
		for(i=93;i<106;i++){ buf[876]=(byte)i; if(sha1_32(buf)==20602866){break;} }
		for(i=50;i<75;i++){ buf[877]=(byte)i; if(sha1_32(buf)==-1294821060){break;} }
		for(i=41;i<61;i++){ buf[878]=(byte)i; if(sha1_32(buf)==-1886781135){break;} }
		for(i=-5;i<16;i++){ buf[879]=(byte)i; if(sha1_32(buf)==-96354602){break;} }
		for(i=81;i<88;i++){ buf[880]=(byte)i; if(sha1_32(buf)==1330914843){break;} }
		for(i=-92;i<-78;i++){ buf[881]=(byte)i; if(sha1_32(buf)==-1747127262){break;} }
		for(i=16;i<37;i++){ buf[882]=(byte)i; if(sha1_32(buf)==1251158783){break;} }
		for(i=26;i<39;i++){ buf[883]=(byte)i; if(sha1_32(buf)==-10544793){break;} }
		for(i=-106;i<-87;i++){ buf[884]=(byte)i; if(sha1_32(buf)==1283385461){break;} }
		for(i=-69;i<-54;i++){ buf[885]=(byte)i; if(sha1_32(buf)==696607851){break;} }
		for(i=83;i<106;i++){ buf[886]=(byte)i; if(sha1_32(buf)==2013574920){break;} }
		for(i=-95;i<-83;i++){ buf[887]=(byte)i; if(sha1_32(buf)==451053379){break;} }
		for(i=-41;i<-18;i++){ buf[888]=(byte)i; if(sha1_32(buf)==-1767529540){break;} }
		for(i=80;i<89;i++){ buf[889]=(byte)i; if(sha1_32(buf)==1504563515){break;} }
		for(i=-81;i<-68;i++){ buf[890]=(byte)i; if(sha1_32(buf)==1221694679){break;} }
		for(i=71;i<98;i++){ buf[891]=(byte)i; if(sha1_32(buf)==-495150770){break;} }
		for(i=-83;i<-69;i++){ buf[892]=(byte)i; if(sha1_32(buf)==-1367379470){break;} }
		for(i=-4;i<0;i++){ buf[893]=(byte)i; if(sha1_32(buf)==1966453410){break;} }
		for(i=-85;i<-71;i++){ buf[894]=(byte)i; if(sha1_32(buf)==419484986){break;} }
		for(i=8;i<26;i++){ buf[895]=(byte)i; if(sha1_32(buf)==1955421507){break;} }
		for(i=-58;i<-32;i++){ buf[896]=(byte)i; if(sha1_32(buf)==185272386){break;} }
		for(i=13;i<41;i++){ buf[897]=(byte)i; if(sha1_32(buf)==-1032358740){break;} }
		for(i=90;i<102;i++){ buf[898]=(byte)i; if(sha1_32(buf)==-1726095603){break;} }
		for(i=-121;i<-105;i++){ buf[899]=(byte)i; if(sha1_32(buf)==-1309852247){break;} }
		for(i=19;i<39;i++){ buf[900]=(byte)i; if(sha1_32(buf)==-939117337){break;} }
		for(i=-16;i<-4;i++){ buf[901]=(byte)i; if(sha1_32(buf)==541817912){break;} }
		for(i=102;i<117;i++){ buf[902]=(byte)i; if(sha1_32(buf)==-463716455){break;} }
		for(i=44;i<69;i++){ buf[903]=(byte)i; if(sha1_32(buf)==-886607140){break;} }
		for(i=33;i<41;i++){ buf[904]=(byte)i; if(sha1_32(buf)==1335995488){break;} }
		for(i=-5;i<15;i++){ buf[905]=(byte)i; if(sha1_32(buf)==343814707){break;} }
		for(i=76;i<93;i++){ buf[906]=(byte)i; if(sha1_32(buf)==-1809129413){break;} }
		for(i=-88;i<-67;i++){ buf[907]=(byte)i; if(sha1_32(buf)==872867984){break;} }
		for(i=-34;i<-19;i++){ buf[908]=(byte)i; if(sha1_32(buf)==362233214){break;} }
		for(i=15;i<32;i++){ buf[909]=(byte)i; if(sha1_32(buf)==-633500805){break;} }
		for(i=88;i<109;i++){ buf[910]=(byte)i; if(sha1_32(buf)==-1511465705){break;} }
		for(i=110;i<125;i++){ buf[911]=(byte)i; if(sha1_32(buf)==-1019927301){break;} }
		for(i=-6;i<2;i++){ buf[912]=(byte)i; if(sha1_32(buf)==-1381188400){break;} }
		for(i=55;i<69;i++){ buf[913]=(byte)i; if(sha1_32(buf)==93970650){break;} }
		for(i=14;i<20;i++){ buf[914]=(byte)i; if(sha1_32(buf)==-1223902190){break;} }
		for(i=-15;i<2;i++){ buf[915]=(byte)i; if(sha1_32(buf)==-1223902190){break;} }
		for(i=52;i<66;i++){ buf[916]=(byte)i; if(sha1_32(buf)==727039978){break;} }
		for(i=-41;i<-11;i++){ buf[917]=(byte)i; if(sha1_32(buf)==-513359678){break;} }
		for(i=-55;i<-45;i++){ buf[918]=(byte)i; if(sha1_32(buf)==-274970711){break;} }
		for(i=52;i<69;i++){ buf[919]=(byte)i; if(sha1_32(buf)==847943510){break;} }
		for(i=-57;i<-42;i++){ buf[920]=(byte)i; if(sha1_32(buf)==-986335914){break;} }
		for(i=-58;i<-50;i++){ buf[921]=(byte)i; if(sha1_32(buf)==-1566718934){break;} }
		for(i=-128;i<-106;i++){ buf[922]=(byte)i; if(sha1_32(buf)==1522938303){break;} }
		for(i=85;i<94;i++){ buf[923]=(byte)i; if(sha1_32(buf)==433568398){break;} }
		for(i=-91;i<-64;i++){ buf[924]=(byte)i; if(sha1_32(buf)==-1537723376){break;} }
		for(i=42;i<55;i++){ buf[925]=(byte)i; if(sha1_32(buf)==-54186384){break;} }
		for(i=80;i<99;i++){ buf[926]=(byte)i; if(sha1_32(buf)==392535462){break;} }
		for(i=37;i<55;i++){ buf[927]=(byte)i; if(sha1_32(buf)==-1890692628){break;} }
		for(i=-43;i<-41;i++){ buf[928]=(byte)i; if(sha1_32(buf)==-1809964392){break;} }
		for(i=69;i<84;i++){ buf[929]=(byte)i; if(sha1_32(buf)==886616078){break;} }
		for(i=-46;i<-35;i++){ buf[930]=(byte)i; if(sha1_32(buf)==-738373528){break;} }
		for(i=-114;i<-89;i++){ buf[931]=(byte)i; if(sha1_32(buf)==-1732087340){break;} }
		for(i=104;i<111;i++){ buf[932]=(byte)i; if(sha1_32(buf)==1995430172){break;} }
		for(i=34;i<42;i++){ buf[933]=(byte)i; if(sha1_32(buf)==-767490659){break;} }
		for(i=64;i<76;i++){ buf[934]=(byte)i; if(sha1_32(buf)==-1482462546){break;} }
		for(i=26;i<49;i++){ buf[935]=(byte)i; if(sha1_32(buf)==-415832848){break;} }
		for(i=-64;i<-37;i++){ buf[936]=(byte)i; if(sha1_32(buf)==-1962574666){break;} }
		for(i=-62;i<-43;i++){ buf[937]=(byte)i; if(sha1_32(buf)==-460988867){break;} }
		for(i=37;i<52;i++){ buf[938]=(byte)i; if(sha1_32(buf)==508492684){break;} }
		for(i=-6;i<12;i++){ buf[939]=(byte)i; if(sha1_32(buf)==-200138122){break;} }
		for(i=-3;i<1;i++){ buf[940]=(byte)i; if(sha1_32(buf)==811337507){break;} }
		for(i=-25;i<1;i++){ buf[941]=(byte)i; if(sha1_32(buf)==-417319727){break;} }
		for(i=110;i<128;i++){ buf[942]=(byte)i; if(sha1_32(buf)==196697200){break;} }
		for(i=-69;i<-57;i++){ buf[943]=(byte)i; if(sha1_32(buf)==719195391){break;} }
		for(i=22;i<48;i++){ buf[944]=(byte)i; if(sha1_32(buf)==-936700912){break;} }
		for(i=108;i<120;i++){ buf[945]=(byte)i; if(sha1_32(buf)==-2009192318){break;} }
		for(i=-70;i<-58;i++){ buf[946]=(byte)i; if(sha1_32(buf)==-1565280353){break;} }
		for(i=-16;i<4;i++){ buf[947]=(byte)i; if(sha1_32(buf)==-955677593){break;} }
		for(i=7;i<23;i++){ buf[948]=(byte)i; if(sha1_32(buf)==-518142282){break;} }
		for(i=106;i<119;i++){ buf[949]=(byte)i; if(sha1_32(buf)==1649361326){break;} }
		for(i=-114;i<-101;i++){ buf[950]=(byte)i; if(sha1_32(buf)==-77804929){break;} }
		for(i=91;i<103;i++){ buf[951]=(byte)i; if(sha1_32(buf)==614374388){break;} }
		for(i=-54;i<-44;i++){ buf[952]=(byte)i; if(sha1_32(buf)==-1670583775){break;} }
		for(i=-4;i<10;i++){ buf[953]=(byte)i; if(sha1_32(buf)==545550103){break;} }
		for(i=-81;i<-72;i++){ buf[954]=(byte)i; if(sha1_32(buf)==-1384533131){break;} }
		for(i=-68;i<-55;i++){ buf[955]=(byte)i; if(sha1_32(buf)==-401380841){break;} }
		for(i=122;i<128;i++){ buf[956]=(byte)i; if(sha1_32(buf)==-1018473810){break;} }
		for(i=113;i<128;i++){ buf[957]=(byte)i; if(sha1_32(buf)==647426363){break;} }
		for(i=83;i<97;i++){ buf[958]=(byte)i; if(sha1_32(buf)==-773640006){break;} }
		for(i=-66;i<-51;i++){ buf[959]=(byte)i; if(sha1_32(buf)==1344237507){break;} }
		for(i=77;i<106;i++){ buf[960]=(byte)i; if(sha1_32(buf)==1592524558){break;} }
		for(i=9;i<18;i++){ buf[961]=(byte)i; if(sha1_32(buf)==-298491784){break;} }
		for(i=-106;i<-101;i++){ buf[962]=(byte)i; if(sha1_32(buf)==-1928751453){break;} }
		for(i=5;i<29;i++){ buf[963]=(byte)i; if(sha1_32(buf)==1314494478){break;} }
		for(i=-84;i<-70;i++){ buf[964]=(byte)i; if(sha1_32(buf)==1146166602){break;} }
		for(i=-8;i<9;i++){ buf[965]=(byte)i; if(sha1_32(buf)==-1048161331){break;} }
		for(i=83;i<97;i++){ buf[966]=(byte)i; if(sha1_32(buf)==1508878329){break;} }
		for(i=-33;i<-11;i++){ buf[967]=(byte)i; if(sha1_32(buf)==-202131609){break;} }
		for(i=77;i<80;i++){ buf[968]=(byte)i; if(sha1_32(buf)==749434011){break;} }
		for(i=-74;i<-66;i++){ buf[969]=(byte)i; if(sha1_32(buf)==43939922){break;} }
		for(i=4;i<10;i++){ buf[970]=(byte)i; if(sha1_32(buf)==-588979337){break;} }
		for(i=1;i<18;i++){ buf[971]=(byte)i; if(sha1_32(buf)==743029453){break;} }
		for(i=-6;i<12;i++){ buf[972]=(byte)i; if(sha1_32(buf)==834849755){break;} }
		for(i=29;i<51;i++){ buf[973]=(byte)i; if(sha1_32(buf)==-1994806952){break;} }
		for(i=82;i<90;i++){ buf[974]=(byte)i; if(sha1_32(buf)==-683825930){break;} }
		for(i=-24;i<-14;i++){ buf[975]=(byte)i; if(sha1_32(buf)==1711206627){break;} }
		for(i=-100;i<-81;i++){ buf[976]=(byte)i; if(sha1_32(buf)==-753017892){break;} }
		for(i=-109;i<-102;i++){ buf[977]=(byte)i; if(sha1_32(buf)==-2059196876){break;} }
		for(i=6;i<30;i++){ buf[978]=(byte)i; if(sha1_32(buf)==807835415){break;} }
		for(i=16;i<43;i++){ buf[979]=(byte)i; if(sha1_32(buf)==306542958){break;} }
		for(i=-26;i<-10;i++){ buf[980]=(byte)i; if(sha1_32(buf)==510991531){break;} }
		for(i=23;i<40;i++){ buf[981]=(byte)i; if(sha1_32(buf)==-2147129317){break;} }
		for(i=32;i<48;i++){ buf[982]=(byte)i; if(sha1_32(buf)==-745877467){break;} }
		for(i=-48;i<-27;i++){ buf[983]=(byte)i; if(sha1_32(buf)==-1025016168){break;} }
		for(i=-103;i<-92;i++){ buf[984]=(byte)i; if(sha1_32(buf)==-1133321488){break;} }
		for(i=-83;i<-69;i++){ buf[985]=(byte)i; if(sha1_32(buf)==-1810650066){break;} }
		for(i=-85;i<-75;i++){ buf[986]=(byte)i; if(sha1_32(buf)==288869509){break;} }
		for(i=-9;i<0;i++){ buf[987]=(byte)i; if(sha1_32(buf)==-708367292){break;} }
		for(i=21;i<29;i++){ buf[988]=(byte)i; if(sha1_32(buf)==874504766){break;} }
		for(i=27;i<51;i++){ buf[989]=(byte)i; if(sha1_32(buf)==157954252){break;} }
		for(i=6;i<33;i++){ buf[990]=(byte)i; if(sha1_32(buf)==-2083819046){break;} }
		for(i=-128;i<-113;i++){ buf[991]=(byte)i; if(sha1_32(buf)==1249339127){break;} }
		for(i=3;i<24;i++){ buf[992]=(byte)i; if(sha1_32(buf)==177462882){break;} }
		for(i=-42;i<-15;i++){ buf[993]=(byte)i; if(sha1_32(buf)==-1398490847){break;} }
		for(i=86;i<99;i++){ buf[994]=(byte)i; if(sha1_32(buf)==-1014743796){break;} }
		for(i=-83;i<-69;i++){ buf[995]=(byte)i; if(sha1_32(buf)==1523780317){break;} }
		for(i=-55;i<-41;i++){ buf[996]=(byte)i; if(sha1_32(buf)==1355953074){break;} }
		for(i=-62;i<-45;i++){ buf[997]=(byte)i; if(sha1_32(buf)==-818408425){break;} }
		for(i=-57;i<-46;i++){ buf[998]=(byte)i; if(sha1_32(buf)==1101039790){break;} }
		for(i=-41;i<-11;i++){ buf[999]=(byte)i; if(sha1_32(buf)==-1293666527){break;} }
		for(i=111;i<119;i++){ buf[1000]=(byte)i; if(sha1_32(buf)==-995230626){break;} }
		for(i=98;i<115;i++){ buf[1001]=(byte)i; if(sha1_32(buf)==1670534560){break;} }
		for(i=-82;i<-65;i++){ buf[1002]=(byte)i; if(sha1_32(buf)==-192767138){break;} }
		for(i=-128;i<-122;i++){ buf[1003]=(byte)i; if(sha1_32(buf)==757542906){break;} }
		for(i=85;i<101;i++){ buf[1004]=(byte)i; if(sha1_32(buf)==-196593418){break;} }
		for(i=38;i<60;i++){ buf[1005]=(byte)i; if(sha1_32(buf)==1233134947){break;} }
		for(i=-80;i<-57;i++){ buf[1006]=(byte)i; if(sha1_32(buf)==93306991){break;} }
		for(i=-103;i<-92;i++){ buf[1007]=(byte)i; if(sha1_32(buf)==710756502){break;} }
		for(i=38;i<58;i++){ buf[1008]=(byte)i; if(sha1_32(buf)==-1491991322){break;} }
		for(i=18;i<30;i++){ buf[1009]=(byte)i; if(sha1_32(buf)==-1817167288){break;} }
		for(i=54;i<60;i++){ buf[1010]=(byte)i; if(sha1_32(buf)==455957914){break;} }
		for(i=-105;i<-91;i++){ buf[1011]=(byte)i; if(sha1_32(buf)==1113904313){break;} }
		for(i=80;i<100;i++){ buf[1012]=(byte)i; if(sha1_32(buf)==-1297808963){break;} }
		for(i=-26;i<-12;i++){ buf[1013]=(byte)i; if(sha1_32(buf)==1176584629){break;} }
		for(i=82;i<94;i++){ buf[1014]=(byte)i; if(sha1_32(buf)==-1967917110){break;} }
		for(i=59;i<69;i++){ buf[1015]=(byte)i; if(sha1_32(buf)==415444210){break;} }
		for(i=-90;i<-70;i++){ buf[1016]=(byte)i; if(sha1_32(buf)==-4273542){break;} }
		for(i=113;i<128;i++){ buf[1017]=(byte)i; if(sha1_32(buf)==1020965991){break;} }
		for(i=36;i<59;i++){ buf[1018]=(byte)i; if(sha1_32(buf)==1117176287){break;} }
		for(i=65;i<80;i++){ buf[1019]=(byte)i; if(sha1_32(buf)==2050029629){break;} }
		for(i=0;i<22;i++){ buf[1020]=(byte)i; if(sha1_32(buf)==1627409506){break;} }
		for(i=84;i<98;i++){ buf[1021]=(byte)i; if(sha1_32(buf)==1103516389){break;} }
		for(i=-128;i<-115;i++){ buf[1022]=(byte)i; if(sha1_32(buf)==836264628){break;} }
		for(i=16;i<38;i++){ buf[1023]=(byte)i; if(sha1_32(buf)==1196860598){break;} }
		for(i=-128;i<-111;i++){ buf[1024]=(byte)i; if(sha1_32(buf)==970412859){break;} }
		for(i=94;i<108;i++){ buf[1025]=(byte)i; if(sha1_32(buf)==-988785895){break;} }
		for(i=-25;i<-10;i++){ buf[1026]=(byte)i; if(sha1_32(buf)==49185980){break;} }
		for(i=-116;i<-97;i++){ buf[1027]=(byte)i; if(sha1_32(buf)==-497590916){break;} }
		for(i=-5;i<16;i++){ buf[1028]=(byte)i; if(sha1_32(buf)==1713052423){break;} }
		for(i=14;i<31;i++){ buf[1029]=(byte)i; if(sha1_32(buf)==-1913420912){break;} }
		for(i=69;i<97;i++){ buf[1030]=(byte)i; if(sha1_32(buf)==812224215){break;} }
		for(i=58;i<65;i++){ buf[1031]=(byte)i; if(sha1_32(buf)==703059384){break;} }
		for(i=84;i<94;i++){ buf[1032]=(byte)i; if(sha1_32(buf)==1669140823){break;} }
		for(i=76;i<102;i++){ buf[1033]=(byte)i; if(sha1_32(buf)==38463924){break;} }
		for(i=-55;i<-47;i++){ buf[1034]=(byte)i; if(sha1_32(buf)==-1363954330){break;} }
		for(i=117;i<128;i++){ buf[1035]=(byte)i; if(sha1_32(buf)==-1994049343){break;} }
		for(i=-2;i<5;i++){ buf[1036]=(byte)i; if(sha1_32(buf)==-1291413778){break;} }
		for(i=-108;i<-96;i++){ buf[1037]=(byte)i; if(sha1_32(buf)==1860117261){break;} }
		for(i=-70;i<-48;i++){ buf[1038]=(byte)i; if(sha1_32(buf)==1735560155){break;} }
		for(i=-128;i<-112;i++){ buf[1039]=(byte)i; if(sha1_32(buf)==-382336003){break;} }
		for(i=97;i<120;i++){ buf[1040]=(byte)i; if(sha1_32(buf)==-1547381216){break;} }
		for(i=-98;i<-71;i++){ buf[1041]=(byte)i; if(sha1_32(buf)==1397700458){break;} }
		for(i=-116;i<-96;i++){ buf[1042]=(byte)i; if(sha1_32(buf)==-2008105777){break;} }
		for(i=70;i<92;i++){ buf[1043]=(byte)i; if(sha1_32(buf)==-703089820){break;} }
		for(i=-64;i<-42;i++){ buf[1044]=(byte)i; if(sha1_32(buf)==-932524375){break;} }
		for(i=-42;i<-18;i++){ buf[1045]=(byte)i; if(sha1_32(buf)==-439319902){break;} }
		for(i=-120;i<-94;i++){ buf[1046]=(byte)i; if(sha1_32(buf)==-1839544071){break;} }
		for(i=4;i<20;i++){ buf[1047]=(byte)i; if(sha1_32(buf)==2011774265){break;} }
		for(i=48;i<67;i++){ buf[1048]=(byte)i; if(sha1_32(buf)==295958299){break;} }
		for(i=-60;i<-42;i++){ buf[1049]=(byte)i; if(sha1_32(buf)==-1338823802){break;} }
		for(i=-94;i<-71;i++){ buf[1050]=(byte)i; if(sha1_32(buf)==-992679738){break;} }
		for(i=89;i<113;i++){ buf[1051]=(byte)i; if(sha1_32(buf)==-108870918){break;} }
		for(i=58;i<65;i++){ buf[1052]=(byte)i; if(sha1_32(buf)==-1311693250){break;} }
		for(i=76;i<86;i++){ buf[1053]=(byte)i; if(sha1_32(buf)==116200961){break;} }
		for(i=-21;i<-11;i++){ buf[1054]=(byte)i; if(sha1_32(buf)==-1657224813){break;} }
		for(i=-40;i<-23;i++){ buf[1055]=(byte)i; if(sha1_32(buf)==1232205810){break;} }
		for(i=49;i<52;i++){ buf[1056]=(byte)i; if(sha1_32(buf)==-608702167){break;} }
		for(i=32;i<52;i++){ buf[1057]=(byte)i; if(sha1_32(buf)==110695615){break;} }
		for(i=1;i<23;i++){ buf[1058]=(byte)i; if(sha1_32(buf)==1573934544){break;} }
		for(i=-91;i<-72;i++){ buf[1059]=(byte)i; if(sha1_32(buf)==-1348467237){break;} }
		for(i=-117;i<-110;i++){ buf[1060]=(byte)i; if(sha1_32(buf)==-1399370087){break;} }
		for(i=114;i<128;i++){ buf[1061]=(byte)i; if(sha1_32(buf)==-2020722585){break;} }
		for(i=-69;i<-58;i++){ buf[1062]=(byte)i; if(sha1_32(buf)==1974928893){break;} }
		for(i=32;i<37;i++){ buf[1063]=(byte)i; if(sha1_32(buf)==1320622044){break;} }
		for(i=-125;i<-108;i++){ buf[1064]=(byte)i; if(sha1_32(buf)==44916812){break;} }
		for(i=34;i<52;i++){ buf[1065]=(byte)i; if(sha1_32(buf)==-1543724580){break;} }
		for(i=11;i<32;i++){ buf[1066]=(byte)i; if(sha1_32(buf)==-1571918632){break;} }
		for(i=26;i<46;i++){ buf[1067]=(byte)i; if(sha1_32(buf)==-1046825604){break;} }
		for(i=-65;i<-61;i++){ buf[1068]=(byte)i; if(sha1_32(buf)==1031495507){break;} }
		for(i=-27;i<-8;i++){ buf[1069]=(byte)i; if(sha1_32(buf)==-674749506){break;} }
		for(i=66;i<84;i++){ buf[1070]=(byte)i; if(sha1_32(buf)==449433807){break;} }
		for(i=-30;i<-20;i++){ buf[1071]=(byte)i; if(sha1_32(buf)==2009801001){break;} }
		for(i=108;i<128;i++){ buf[1072]=(byte)i; if(sha1_32(buf)==1839461749){break;} }
		for(i=-37;i<-27;i++){ buf[1073]=(byte)i; if(sha1_32(buf)==-1889799959){break;} }
		for(i=16;i<37;i++){ buf[1074]=(byte)i; if(sha1_32(buf)==-283082170){break;} }
		for(i=56;i<64;i++){ buf[1075]=(byte)i; if(sha1_32(buf)==283450006){break;} }
		for(i=8;i<28;i++){ buf[1076]=(byte)i; if(sha1_32(buf)==812689687){break;} }
		for(i=-75;i<-57;i++){ buf[1077]=(byte)i; if(sha1_32(buf)==-2093089326){break;} }
		for(i=112;i<128;i++){ buf[1078]=(byte)i; if(sha1_32(buf)==2005566208){break;} }
		for(i=-119;i<-109;i++){ buf[1079]=(byte)i; if(sha1_32(buf)==1788789324){break;} }
		for(i=93;i<109;i++){ buf[1080]=(byte)i; if(sha1_32(buf)==-901755828){break;} }
		for(i=92;i<107;i++){ buf[1081]=(byte)i; if(sha1_32(buf)==-1881221778){break;} }
		for(i=-56;i<-34;i++){ buf[1082]=(byte)i; if(sha1_32(buf)==898234112){break;} }
		for(i=-70;i<-42;i++){ buf[1083]=(byte)i; if(sha1_32(buf)==1402271844){break;} }
		for(i=-128;i<-102;i++){ buf[1084]=(byte)i; if(sha1_32(buf)==-1111063204){break;} }
		for(i=27;i<39;i++){ buf[1085]=(byte)i; if(sha1_32(buf)==-205553018){break;} }
		for(i=-107;i<-87;i++){ buf[1086]=(byte)i; if(sha1_32(buf)==-891077799){break;} }
		for(i=90;i<96;i++){ buf[1087]=(byte)i; if(sha1_32(buf)==1800465303){break;} }
		for(i=-1;i<22;i++){ buf[1088]=(byte)i; if(sha1_32(buf)==-1684127620){break;} }
		for(i=76;i<94;i++){ buf[1089]=(byte)i; if(sha1_32(buf)==-169531934){break;} }
		for(i=-9;i<13;i++){ buf[1090]=(byte)i; if(sha1_32(buf)==-549975762){break;} }
		for(i=-95;i<-88;i++){ buf[1091]=(byte)i; if(sha1_32(buf)==-358455091){break;} }
		for(i=83;i<97;i++){ buf[1092]=(byte)i; if(sha1_32(buf)==-1578632464){break;} }
		for(i=-83;i<-70;i++){ buf[1093]=(byte)i; if(sha1_32(buf)==-1238590364){break;} }
		for(i=-65;i<-55;i++){ buf[1094]=(byte)i; if(sha1_32(buf)==-1791537753){break;} }
		for(i=-71;i<-47;i++){ buf[1095]=(byte)i; if(sha1_32(buf)==-1976173901){break;} }
		for(i=70;i<82;i++){ buf[1096]=(byte)i; if(sha1_32(buf)==-2109945887){break;} }
		for(i=-41;i<-22;i++){ buf[1097]=(byte)i; if(sha1_32(buf)==-2041562017){break;} }
		for(i=50;i<65;i++){ buf[1098]=(byte)i; if(sha1_32(buf)==-171556286){break;} }
		for(i=2;i<8;i++){ buf[1099]=(byte)i; if(sha1_32(buf)==-244064384){break;} }
		for(i=7;i<16;i++){ buf[1100]=(byte)i; if(sha1_32(buf)==-961330036){break;} }
		for(i=-36;i<-13;i++){ buf[1101]=(byte)i; if(sha1_32(buf)==-1038380431){break;} }
		for(i=42;i<61;i++){ buf[1102]=(byte)i; if(sha1_32(buf)==-125886729){break;} }
		for(i=-115;i<-97;i++){ buf[1103]=(byte)i; if(sha1_32(buf)==-1725885139){break;} }
		for(i=105;i<128;i++){ buf[1104]=(byte)i; if(sha1_32(buf)==201524680){break;} }
		for(i=-124;i<-103;i++){ buf[1105]=(byte)i; if(sha1_32(buf)==-1436319927){break;} }
		for(i=65;i<89;i++){ buf[1106]=(byte)i; if(sha1_32(buf)==279745523){break;} }
		for(i=-52;i<-40;i++){ buf[1107]=(byte)i; if(sha1_32(buf)==1837757125){break;} }
		for(i=7;i<25;i++){ buf[1108]=(byte)i; if(sha1_32(buf)==2038312100){break;} }
		for(i=-119;i<-90;i++){ buf[1109]=(byte)i; if(sha1_32(buf)==-1787168375){break;} }
		for(i=65;i<74;i++){ buf[1110]=(byte)i; if(sha1_32(buf)==-744732928){break;} }
		for(i=30;i<44;i++){ buf[1111]=(byte)i; if(sha1_32(buf)==1955740083){break;} }
		for(i=-51;i<-34;i++){ buf[1112]=(byte)i; if(sha1_32(buf)==1802283624){break;} }
		for(i=94;i<122;i++){ buf[1113]=(byte)i; if(sha1_32(buf)==518979499){break;} }
		for(i=105;i<109;i++){ buf[1114]=(byte)i; if(sha1_32(buf)==-2104730842){break;} }
		for(i=84;i<98;i++){ buf[1115]=(byte)i; if(sha1_32(buf)==-439628032){break;} }
		for(i=66;i<78;i++){ buf[1116]=(byte)i; if(sha1_32(buf)==616563130){break;} }
		for(i=-84;i<-66;i++){ buf[1117]=(byte)i; if(sha1_32(buf)==-1522022120){break;} }
		for(i=-98;i<-87;i++){ buf[1118]=(byte)i; if(sha1_32(buf)==1757786164){break;} }
		for(i=-105;i<-88;i++){ buf[1119]=(byte)i; if(sha1_32(buf)==-894638872){break;} }
		for(i=53;i<69;i++){ buf[1120]=(byte)i; if(sha1_32(buf)==219901631){break;} }
		for(i=-115;i<-113;i++){ buf[1121]=(byte)i; if(sha1_32(buf)==1757179382){break;} }
		for(i=-128;i<-101;i++){ buf[1122]=(byte)i; if(sha1_32(buf)==-398054991){break;} }
		for(i=84;i<96;i++){ buf[1123]=(byte)i; if(sha1_32(buf)==-466909271){break;} }
		for(i=-58;i<-44;i++){ buf[1124]=(byte)i; if(sha1_32(buf)==-1866641816){break;} }
		for(i=106;i<126;i++){ buf[1125]=(byte)i; if(sha1_32(buf)==70021837){break;} }
		for(i=-16;i<-8;i++){ buf[1126]=(byte)i; if(sha1_32(buf)==-389437152){break;} }
		for(i=80;i<111;i++){ buf[1127]=(byte)i; if(sha1_32(buf)==-1166392644){break;} }
		for(i=-23;i<0;i++){ buf[1128]=(byte)i; if(sha1_32(buf)==-1564228635){break;} }
		for(i=-116;i<-106;i++){ buf[1129]=(byte)i; if(sha1_32(buf)==5459449){break;} }
		for(i=-22;i<-15;i++){ buf[1130]=(byte)i; if(sha1_32(buf)==-1189018592){break;} }
		for(i=-35;i<-17;i++){ buf[1131]=(byte)i; if(sha1_32(buf)==906065457){break;} }
		for(i=-40;i<-21;i++){ buf[1132]=(byte)i; if(sha1_32(buf)==1813523338){break;} }
		for(i=20;i<40;i++){ buf[1133]=(byte)i; if(sha1_32(buf)==-1358696457){break;} }
		for(i=-128;i<-105;i++){ buf[1134]=(byte)i; if(sha1_32(buf)==-877838245){break;} }
		for(i=95;i<104;i++){ buf[1135]=(byte)i; if(sha1_32(buf)==570466664){break;} }
		for(i=-85;i<-63;i++){ buf[1136]=(byte)i; if(sha1_32(buf)==1969989315){break;} }
		for(i=-3;i<21;i++){ buf[1137]=(byte)i; if(sha1_32(buf)==-441531862){break;} }
		for(i=103;i<112;i++){ buf[1138]=(byte)i; if(sha1_32(buf)==65118899){break;} }
		for(i=-104;i<-102;i++){ buf[1139]=(byte)i; if(sha1_32(buf)==-1153221459){break;} }
		for(i=102;i<105;i++){ buf[1140]=(byte)i; if(sha1_32(buf)==1390285307){break;} }
		for(i=-28;i<-3;i++){ buf[1141]=(byte)i; if(sha1_32(buf)==546007566){break;} }
		for(i=-123;i<-101;i++){ buf[1142]=(byte)i; if(sha1_32(buf)==469083843){break;} }
		for(i=49;i<62;i++){ buf[1143]=(byte)i; if(sha1_32(buf)==-1027312939){break;} }
		for(i=-128;i<-108;i++){ buf[1144]=(byte)i; if(sha1_32(buf)==-1403948300){break;} }
		for(i=-79;i<-61;i++){ buf[1145]=(byte)i; if(sha1_32(buf)==-390421064){break;} }
		for(i=105;i<128;i++){ buf[1146]=(byte)i; if(sha1_32(buf)==-939983089){break;} }
		for(i=-76;i<-58;i++){ buf[1147]=(byte)i; if(sha1_32(buf)==-661035659){break;} }
		for(i=30;i<51;i++){ buf[1148]=(byte)i; if(sha1_32(buf)==-1222800926){break;} }
		for(i=-77;i<-51;i++){ buf[1149]=(byte)i; if(sha1_32(buf)==569923383){break;} }
		for(i=-108;i<-87;i++){ buf[1150]=(byte)i; if(sha1_32(buf)==-670779587){break;} }
		for(i=26;i<39;i++){ buf[1151]=(byte)i; if(sha1_32(buf)==-639456656){break;} }
		for(i=-112;i<-96;i++){ buf[1152]=(byte)i; if(sha1_32(buf)==-429028211){break;} }
		for(i=-44;i<-30;i++){ buf[1153]=(byte)i; if(sha1_32(buf)==-602311034){break;} }
		for(i=-69;i<-60;i++){ buf[1154]=(byte)i; if(sha1_32(buf)==1140379874){break;} }
		for(i=105;i<128;i++){ buf[1155]=(byte)i; if(sha1_32(buf)==657003169){break;} }
		for(i=60;i<78;i++){ buf[1156]=(byte)i; if(sha1_32(buf)==-1183997793){break;} }
		for(i=58;i<89;i++){ buf[1157]=(byte)i; if(sha1_32(buf)==1598425727){break;} }
		for(i=9;i<29;i++){ buf[1158]=(byte)i; if(sha1_32(buf)==-2014539231){break;} }
		for(i=38;i<42;i++){ buf[1159]=(byte)i; if(sha1_32(buf)==-179637556){break;} }
		for(i=63;i<89;i++){ buf[1160]=(byte)i; if(sha1_32(buf)==-1156659771){break;} }
		for(i=32;i<58;i++){ buf[1161]=(byte)i; if(sha1_32(buf)==-601874743){break;} }
		for(i=116;i<128;i++){ buf[1162]=(byte)i; if(sha1_32(buf)==-1326091490){break;} }
		for(i=32;i<57;i++){ buf[1163]=(byte)i; if(sha1_32(buf)==1868983259){break;} }
		for(i=-21;i<-8;i++){ buf[1164]=(byte)i; if(sha1_32(buf)==-107062146){break;} }
		for(i=-79;i<-54;i++){ buf[1165]=(byte)i; if(sha1_32(buf)==2062239280){break;} }
		for(i=50;i<63;i++){ buf[1166]=(byte)i; if(sha1_32(buf)==1060620234){break;} }
		for(i=63;i<80;i++){ buf[1167]=(byte)i; if(sha1_32(buf)==1612584154){break;} }
		for(i=60;i<68;i++){ buf[1168]=(byte)i; if(sha1_32(buf)==-323298159){break;} }
		for(i=35;i<60;i++){ buf[1169]=(byte)i; if(sha1_32(buf)==670482111){break;} }
		for(i=100;i<113;i++){ buf[1170]=(byte)i; if(sha1_32(buf)==1942890848){break;} }
		for(i=18;i<34;i++){ buf[1171]=(byte)i; if(sha1_32(buf)==-2044362921){break;} }
		for(i=-128;i<-113;i++){ buf[1172]=(byte)i; if(sha1_32(buf)==562724615){break;} }
		for(i=-68;i<-52;i++){ buf[1173]=(byte)i; if(sha1_32(buf)==1943441170){break;} }
		for(i=-13;i<2;i++){ buf[1174]=(byte)i; if(sha1_32(buf)==-107269074){break;} }
		for(i=50;i<66;i++){ buf[1175]=(byte)i; if(sha1_32(buf)==808766531){break;} }
		for(i=104;i<124;i++){ buf[1176]=(byte)i; if(sha1_32(buf)==-561813029){break;} }
		for(i=109;i<117;i++){ buf[1177]=(byte)i; if(sha1_32(buf)==1316277266){break;} }
		for(i=108;i<123;i++){ buf[1178]=(byte)i; if(sha1_32(buf)==-523151851){break;} }
		for(i=-32;i<-25;i++){ buf[1179]=(byte)i; if(sha1_32(buf)==-389972177){break;} }
		for(i=91;i<112;i++){ buf[1180]=(byte)i; if(sha1_32(buf)==1919014052){break;} }
		for(i=84;i<106;i++){ buf[1181]=(byte)i; if(sha1_32(buf)==-2060002125){break;} }
		for(i=4;i<23;i++){ buf[1182]=(byte)i; if(sha1_32(buf)==-1463531489){break;} }
		for(i=56;i<82;i++){ buf[1183]=(byte)i; if(sha1_32(buf)==695679081){break;} }
		for(i=-66;i<-40;i++){ buf[1184]=(byte)i; if(sha1_32(buf)==-1837104615){break;} }
		for(i=93;i<117;i++){ buf[1185]=(byte)i; if(sha1_32(buf)==1754627857){break;} }
		for(i=15;i<32;i++){ buf[1186]=(byte)i; if(sha1_32(buf)==300802371){break;} }
		for(i=-122;i<-108;i++){ buf[1187]=(byte)i; if(sha1_32(buf)==593524476){break;} }
		for(i=-128;i<-116;i++){ buf[1188]=(byte)i; if(sha1_32(buf)==-71723968){break;} }
		for(i=97;i<110;i++){ buf[1189]=(byte)i; if(sha1_32(buf)==-835871327){break;} }
		for(i=-94;i<-74;i++){ buf[1190]=(byte)i; if(sha1_32(buf)==1565640330){break;} }
		for(i=-24;i<0;i++){ buf[1191]=(byte)i; if(sha1_32(buf)==-1405076234){break;} }
		for(i=-107;i<-87;i++){ buf[1192]=(byte)i; if(sha1_32(buf)==-641259061){break;} }
		for(i=34;i<46;i++){ buf[1193]=(byte)i; if(sha1_32(buf)==556435672){break;} }
		for(i=-99;i<-81;i++){ buf[1194]=(byte)i; if(sha1_32(buf)==-263775978){break;} }
		for(i=-46;i<-30;i++){ buf[1195]=(byte)i; if(sha1_32(buf)==-474550716){break;} }
		for(i=-79;i<-69;i++){ buf[1196]=(byte)i; if(sha1_32(buf)==1435546741){break;} }
		for(i=-43;i<-28;i++){ buf[1197]=(byte)i; if(sha1_32(buf)==-2080794730){break;} }
		for(i=-70;i<-52;i++){ buf[1198]=(byte)i; if(sha1_32(buf)==541279995){break;} }
		for(i=-25;i<-17;i++){ buf[1199]=(byte)i; if(sha1_32(buf)==-504484273){break;} }
		for(i=13;i<39;i++){ buf[1200]=(byte)i; if(sha1_32(buf)==-558620334){break;} }
		for(i=-43;i<-25;i++){ buf[1201]=(byte)i; if(sha1_32(buf)==-2015387280){break;} }
		for(i=-71;i<-58;i++){ buf[1202]=(byte)i; if(sha1_32(buf)==-1614892490){break;} }
		for(i=-92;i<-62;i++){ buf[1203]=(byte)i; if(sha1_32(buf)==-698839438){break;} }
		for(i=-58;i<-48;i++){ buf[1204]=(byte)i; if(sha1_32(buf)==-275996677){break;} }
		for(i=-55;i<-31;i++){ buf[1205]=(byte)i; if(sha1_32(buf)==-239617282){break;} }
		for(i=-82;i<-76;i++){ buf[1206]=(byte)i; if(sha1_32(buf)==-2036341800){break;} }
		for(i=79;i<106;i++){ buf[1207]=(byte)i; if(sha1_32(buf)==-997782934){break;} }
		for(i=100;i<128;i++){ buf[1208]=(byte)i; if(sha1_32(buf)==23491480){break;} }
		for(i=-77;i<-57;i++){ buf[1209]=(byte)i; if(sha1_32(buf)==-1232972174){break;} }
		for(i=-124;i<-108;i++){ buf[1210]=(byte)i; if(sha1_32(buf)==-1028747130){break;} }
		for(i=-41;i<-32;i++){ buf[1211]=(byte)i; if(sha1_32(buf)==-584667883){break;} }
		for(i=11;i<27;i++){ buf[1212]=(byte)i; if(sha1_32(buf)==2101808769){break;} }
		for(i=16;i<33;i++){ buf[1213]=(byte)i; if(sha1_32(buf)==1536066696){break;} }
		for(i=-94;i<-79;i++){ buf[1214]=(byte)i; if(sha1_32(buf)==-1416509743){break;} }
		for(i=70;i<87;i++){ buf[1215]=(byte)i; if(sha1_32(buf)==1704035081){break;} }
		for(i=-126;i<-107;i++){ buf[1216]=(byte)i; if(sha1_32(buf)==-714290022){break;} }
		for(i=68;i<92;i++){ buf[1217]=(byte)i; if(sha1_32(buf)==-1391148783){break;} }
		for(i=-67;i<-63;i++){ buf[1218]=(byte)i; if(sha1_32(buf)==1777479649){break;} }
		for(i=-116;i<-93;i++){ buf[1219]=(byte)i; if(sha1_32(buf)==-1920006034){break;} }
		for(i=100;i<106;i++){ buf[1220]=(byte)i; if(sha1_32(buf)==-422556442){break;} }
		for(i=116;i<128;i++){ buf[1221]=(byte)i; if(sha1_32(buf)==68151794){break;} }
		for(i=-2;i<18;i++){ buf[1222]=(byte)i; if(sha1_32(buf)==-1053722180){break;} }
		for(i=108;i<124;i++){ buf[1223]=(byte)i; if(sha1_32(buf)==2053810830){break;} }
		for(i=59;i<64;i++){ buf[1224]=(byte)i; if(sha1_32(buf)==-840122968){break;} }
		for(i=-25;i<-3;i++){ buf[1225]=(byte)i; if(sha1_32(buf)==466570571){break;} }
		for(i=2;i<18;i++){ buf[1226]=(byte)i; if(sha1_32(buf)==1172285560){break;} }
		return buf;
	}
}
