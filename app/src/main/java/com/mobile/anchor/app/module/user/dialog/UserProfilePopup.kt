package com.mobile.anchor.app.module.user.dialog

import anchor.app.base.bean.LanguagesBean
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bigkoo.pickerview.view.TimePickerView
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.TimeUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import anchor.app.base.dialog.LanguageSelectDialogFragment
import anchor.app.base.utils.CameraUtil
import anchor.app.base.utils.SharePreUtil
import anchor.app.base.utils.TimeUtil
import anchor.app.base.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupUserProfileBinding
import com.mobile.anchor.app.module.user.editedInfo.EditInfoModel
import java.io.File
import java.util.Calendar
import java.util.Date

class UserProfilePopup2(private val activity: FragmentActivity, private val block: () -> Unit) :
    DialogFragment() {

    private val editViewModel =
        ViewModelProvider.AndroidViewModelFactory.getInstance(activity.application)
            .create(EditInfoModel::class.java)

    private val params = JsonObject()
    private var birthdayString = ""
    private var mOpenTimePicker: TimePickerView? = null
    private val mCameraUtil: CameraUtil by lazy { CameraUtil(activity) }
    private var languages = ArrayList<String>()
    private var languagesData = ArrayList<LanguagesBean>()

    private lateinit var binding: PopupUserProfileBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding =
            DataBindingUtil.inflate(inflater, R.layout.popup_user_profile, container, false)
        dialog?.setCanceledOnTouchOutside(true)
        val window = dialog?.window
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.decorView?.setPadding(0, 0, 0, 0)
        val wlp = window?.attributes
        wlp?.gravity = Gravity.BOTTOM
        wlp?.width = WindowManager.LayoutParams.MATCH_PARENT
        wlp?.height = WindowManager.LayoutParams.WRAP_CONTENT
        window?.attributes = wlp
        return binding.rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initPopupContent()
    }

    private fun initPopupContent() {
        editViewModel.queryUserInfo(activity).observe(this) {
            binding?.apply {
                Glide.with(activity).load(it.headFileName).apply(
                    RequestOptions.bitmapTransform(
                        CircleCrop()
                    )
                ).into(ivHeader)
                tvId.text = it.userCode
                etNickname.setText(it.nickName)
                tvAge.text = it.birthday
                tvCountry.text = it.country
                tvLanguage.text = it.languages?.map { it.languageName }?.joinToString()
                etSignature.setText(it.signature)

                if (TextUtils.equals(it.infoStatus, "1")) {
                    ivReview.visibility = View.VISIBLE
                    tvReview.visibility = View.VISIBLE
                    ivHeader.isEnabled = false
                    etNickname.isEnabled = false
                    tvAge.isEnabled = false
                    tvCountry.isEnabled = false
                    tvLanguage.isEnabled = false
                }
            }
        }
        editViewModel.query_user_language(activity).observe(this) {
            languagesData.clear()
            languagesData.addAll(it)
        }

        binding?.ivHeader?.setOnClickListener {
            mCameraUtil.getPic(activity as AppCompatActivity,
                callback = object : OnResultCallbackListener<LocalMedia> {
                    override fun onResult(result: java.util.ArrayList<LocalMedia>) {
                        if (result.isNotEmpty() && result.size > 0) {
                            val path = result[0].cutPath ?: result[0].realPath
                            Glide.with(activity).load(path).apply(
                                RequestOptions.bitmapTransform(
                                    CircleCrop()
                                )
                            ).into(binding.ivHeader)

                            editViewModel.uploadPicture(File(path))
                                .observe(this@UserProfilePopup2) {
                                    params.addProperty("headFileName", it)
                                }
                        }
                    }

                    override fun onCancel() {
                    }
                })
        }
        binding?.tvAge?.setOnClickListener {
            setOpenTime(binding) {
                binding.tvAge.text = it
                params.addProperty("birthday", it)
            }
        }

        //选择国家
        binding?.tvCountry?.setOnClickListener {
            showCountryPopup(activity) {
                binding.tvCountry.text = it.countryName
                params.addProperty("country", it.countryName)
            }
        }

        //选择语言
        binding?.tvLanguage?.setOnClickListener {
            val diagl = LanguageSelectDialogFragment(languagesData) {
                languages.clear()
                languagesData.forEach {
                    if (it.isSelected) {
                        languages.add(it.languageName)
                    }
                }
                params.add(
                    "languages",
                    Gson().toJsonTree(languagesData.filter { it.isSelected }
                        ?.map { it.languageCode })
                )

                if (languages.isNotEmpty()) {
                    binding.tvLanguage.text = TextUtils.join("、", languages)
                } else {
                    binding.tvLanguage.text = ""
                }
            }
            diagl.show(activity.supportFragmentManager, "LanguageSelectDialogFragment")
        }

        binding?.tvCancel?.setOnClickListener {
            dismiss()
        }

        binding?.tvSave?.setOnClickListener {
            //审核中
            if (binding?.ivReview?.visibility == View.VISIBLE) {
                dismiss()
                return@setOnClickListener
            }

            editViewModel.modifyBasicInfo(params.apply {
                addProperty("id", SharePreUtil.getUserId())
                addProperty("nickName", binding.etNickname.text.toString())
                addProperty("gender", "1")
                addProperty("signature", binding.etSignature.text.toString())
            }).observe(this) {
                if (it) {
                    ToastUtil.show("update successful")
                } else {
                    ToastUtil.show("update failure")
                }
                block.invoke()
                dismiss()
            }
        }
    }

    private fun setOpenTime(binding: PopupUserProfileBinding, block: (String) -> Unit) {
        val birth = if (birthdayString.isEmpty()) TimeUtils.getNowString() else birthdayString

        val date = Calendar.getInstance()
        date.time = TimeUtils.string2Date(birth, "yyyy-MM-dd")

        //时间选择器 ，自定义布局
        mOpenTimePicker = TimePickerBuilder(
            activity
        ) { date: Date?, v: View? ->
            date?.let {
                birthdayString = TimeUtil.yyyyMMdd(it.time)
            }

        }.setTextColorCenter(Color.BLACK) //设置选中项的颜色
            .setLineSpacingMultiplier(2.6f) //设置两横线之间的间隔倍数
            .setRangDate(Calendar.getInstance().apply {
                set(Calendar.YEAR, 1900)
            }, Calendar.getInstance()).setDate(date)
            .setLayoutRes(R.layout.device_layout_select_time) { v: View ->
                val tv_cancel = v.findViewById<TextView>(R.id.screen_cancle)
                val tv_sure = v.findViewById<TextView>(R.id.screen_sure)
                val screen_title = v.findViewById<TextView>(R.id.screen_title)
                screen_title.setText(StringUtils.getString(R.string.age_select))
                tv_sure.setOnClickListener { v1: View? ->
                    mOpenTimePicker?.let {
                        block.invoke(birthdayString)
                    }
                    mOpenTimePicker?.dismiss()
                }
                tv_cancel.setOnClickListener { v12: View? ->
                    mOpenTimePicker?.dismiss()
                }
            }.setTimeSelectChangeListener {
                birthdayString = TimeUtil.yyyyMMdd(it.time)
                block.invoke(birthdayString)
            }.setType(booleanArrayOf(true, true, true, false, false, false))
            .setDividerColor(Color.parseColor("#EBEBEB")).setBgColor(0)
            .setDecorView(binding.calendarContainer).build()
        mOpenTimePicker?.show()
    }
}

fun showUserProfilePopup(activity: FragmentActivity, block: () -> Unit) {
    UserProfilePopup2(activity, block).show(activity.supportFragmentManager, "")
}

