package com.mobile.anchor.app.module.user.setting.language;

import com.hjq.language.MultiLanguages;
import com.mobile.anchor.app.R;
import com.mobile.anchor.app.databinding.ActivityLanguageBinding;

import java.util.Locale;

import anchor.app.base.ext.AppKtKt;
import anchor.app.base.ui.BaseActivity;
import anchor.app.base.utils.ActivityUtil;
import anchor.app.base.utils.ToastUtil;
import anchor.app.base.utils.immersionbar.standard.ImmersionBar;
import com.mobile.anchor.app.main.MainActivity;
import com.mobile.anchor.app.module.user.setting.SettingModel;

/**
 * 设置页面
 */
public class LanguageActivity extends BaseActivity<SettingModel, ActivityLanguageBinding> {
    public static final String TAG = "SettingActivity";

    Locale appLanguage;
    Locale beforeAppLanguage;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_language;
    }

    @Override
    protected void initView() {
        bindingView.setActivity(this);
        bindingView.setLifecycleOwner(this);
        showContentView();

        // 获取当前的语种
        appLanguage = MultiLanguages.getAppLanguage();
        beforeAppLanguage = MultiLanguages.getAppLanguage();

        initSelectView();


        bindingView.RGLanguages.setOnCheckedChangeListener((radioGroup, checkedId) -> {
//                ARouter.getInstance().build(ArouterPath.PATH_USER_WALLETA_DESCRIBTION11).navigation();

            // 是否需要重启
            boolean restart = false;

            if (checkedId == R.id.id00) {
                // 跟随系统
                restart = MultiLanguages.clearAppLanguage(LanguageActivity.this);
            } else if (checkedId == R.id.id01) {

                // 简体中文
                appLanguage = new Locale("zh", "CN", "");
            } else if (checkedId == R.id.id02) {
                // 英语
                appLanguage = new Locale("en", "US", "");
            } else if (checkedId == R.id.id03) {
                //以色列
                appLanguage = new Locale("ar", "IL", "");
            } else if (checkedId == R.id.id04) {
                //西班牙
                appLanguage = new Locale("es", "ES", "");
            } else if (checkedId == R.id.id05) {
                //法国
                appLanguage = new Locale("fr", "FR", "");
            } else if (checkedId == R.id.id06) {
                //印度
                appLanguage = new Locale("hi", "IN", "");
            } else if (checkedId == R.id.id07) {
                //印度尼西亚共和国
                appLanguage = new Locale("in", "ID", "");
            } else if (checkedId == R.id.id08) {
                //葡萄牙共和国
                appLanguage = new Locale("pt", "PT");
            } else if (checkedId == R.id.id09) {
                //土耳其共和国
                appLanguage = new Locale("tr", "TR", "");
            } else if (checkedId == R.id.id10) {
                //巴基斯坦伊斯兰共和国
                appLanguage = new Locale("ur", "PK", "");
            } else if (checkedId == R.id.id11) {
                //越南
                appLanguage = new Locale("vi", "VN", "");
            } else {
                //  R.id.RBimg
                ToastUtil.show("Twitter");

            }
        });
    }

    private void initSelectView() {
        if (appLanguage.getLanguage().contains("zh")) {
            bindingView.RGLanguages.check(R.id.id01);

        } else if (appLanguage.getLanguage().contains("en")) {
            bindingView.RGLanguages.check(R.id.id02);

        } else if (appLanguage.getLanguage().contains("ar")) {
            bindingView.RGLanguages.check(R.id.id03);

        } else if (appLanguage.getLanguage().contains("es")) {
            bindingView.RGLanguages.check(R.id.id04);


        } else if (appLanguage.getLanguage().contains("fr")) {
            bindingView.RGLanguages.check(R.id.id05);

        } else if (appLanguage.getLanguage().contains("hi")) {
            bindingView.RGLanguages.check(R.id.id06);

        } else if (appLanguage.getLanguage().contains("in")) {
            bindingView.RGLanguages.check(R.id.id07);

        } else if (appLanguage.getLanguage().contains("pt")) {
            bindingView.RGLanguages.check(R.id.id08);

        } else if (appLanguage.getLanguage().contains("tr")) {
            bindingView.RGLanguages.check(R.id.id09);

        } else if (appLanguage.getLanguage().contains("ur")) {
            bindingView.RGLanguages.check(R.id.id10);

        } else if (appLanguage.getLanguage().contains("vi")) {
            bindingView.RGLanguages.check(R.id.id11);
        }

    }


    public void commit() {
        if (appLanguage.getLanguage().equals(beforeAppLanguage.getLanguage())) {
            finish();
        } else {
            boolean restart = MultiLanguages.setAppLanguage(LanguageActivity.this, appLanguage);
            if (restart) {
//            startActivity(new Intent(LanguageActivity.this, LanguageActivity.class));
//            overridePendingTransition(R.anim.activity_alpha_in, R.anim.activity_alpha_out);
                ActivityUtil.getInstance().finishAllActivity();
//                ARouter.getInstance().build(ArouterPath.PATH_APP_MAIN).navigation();
                AppKtKt.jump(this, MainActivity.class);
            }
        }
    }


    @Override
    protected void initImmersionBar() {
        ImmersionBar.with(this)
                .statusBarDarkFont(false, 0.2f).init();
    }
}
