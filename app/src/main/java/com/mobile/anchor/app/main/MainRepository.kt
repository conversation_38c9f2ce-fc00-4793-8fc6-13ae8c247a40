package com.mobile.anchor.app.main

import anchor.app.base.bean.LoginBean
import anchor.app.base.bean.WordEntity
import anchor.app.base.repository.IRepository
import anchor.app.base.retrofit.BaseResult
import com.mobile.anchor.app.module.workbench.api.WorkbenchApi
import io.reactivex.Flowable


class MainRepository : IRepository {
    /**
     * 查询孩子
     *
     * @return
     */
    fun queryChildInfo() {
        println("")
        //        return BaseApi.queryChildInfo();
    }


    fun anchorPayInfo(): Flowable<BaseResult<Any>> {
        return WorkbenchApi.anchorPayInfo()
    }

    fun getSensitiveWords(): Flowable<BaseResult<MutableList<WordEntity>>> =
        WorkbenchApi.getSensitiveWords()

    fun anchorAddVideo(anchorId: Long, url: String, channelId: String): Flowable<BaseResult<Any>> = WorkbenchApi.anchorAddVideo(anchorId,url, channelId)

    fun getAnchorDetail(): Flowable<BaseResult<LoginBean>> = WorkbenchApi.getAnchorDetail()
}
