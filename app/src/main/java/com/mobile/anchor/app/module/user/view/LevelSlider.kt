package com.mobile.anchor.app.module.user.view

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/11/23 17:08
 * @description :
 */
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import kotlin.math.abs

class LevelSlider @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var minValue = 10     // 起始值
    private var maxValue = 40     // 最大值
    private var step = 10          // 每个刻度的步长
    private var currentValue = 0 // 当前值

    private val linePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.GRAY
        strokeWidth = 8f
        strokeCap = Paint.Cap.ROUND // 保留圆角
    }

    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#9F2AF7")
        strokeWidth = 8f
        strokeCap = Paint.Cap.ROUND // 保留圆角
    }

    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#9F2AF7")
    }

    private val scaleTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.GRAY
        textSize = 28f
        textAlign = Paint.Align.CENTER
    }

    private var sliderWidth = 0f
    private var sliderHeight = 0f
    private val circleRadius = 14f // 滑块圆的半径
    private var activeCircleX = 0f
    private var tickPositions = listOf<Float>() // 每个刻度的位置
    private var onChangeListener: (Int, Int) -> Unit = { index, value -> }

    init {
        setWillNotDraw(false)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        sliderWidth = w.toFloat()
        sliderHeight = h.toFloat()
        calculateTickPositions()
        updateCirclePosition()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        // 动态计算高度，确保文字和滑块显示完整
        val textHeight = scaleTextPaint.fontMetrics.run { bottom - top } // 文字高度
        val desiredHeight = (circleRadius * 4 + textHeight + paddingTop + paddingBottom).toInt()

        // 根据宽度和高度模式重新计算尺寸
        val measuredWidth = MeasureSpec.getSize(widthMeasureSpec)
        val measuredHeight = resolveSize(desiredHeight, heightMeasureSpec)

        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    fun setRange(min: Int, max: Int, step: Int = this.step) {
        this.minValue = min
        this.maxValue = max
        this.step = step
        invalidate()
    }

    fun setCurrent(value: Int) {
        this.currentValue = value
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 动态调整起点和终点，避免文字被裁剪
        val startX = tickPositions.first()
        val endX = tickPositions.last()
        val centerY = circleRadius

        // 绘制背景线
        canvas.drawLine(startX, centerY, endX, centerY, linePaint)

        // 绘制已选中的进度条
        canvas.drawLine(startX, centerY, activeCircleX, centerY, progressPaint)

        // 绘制刻度值
        val textOffsetY = centerY + circleRadius * 4
        for (i in tickPositions.indices) {
            val x = tickPositions[i]
            val value = minValue + i * step
            canvas.drawText(
                value.toString(), x, textOffsetY, scaleTextPaint
            )
        }

        // 绘制滑块圆
        canvas.drawCircle(activeCircleX, centerY, circleRadius, circlePaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                val closestTick = findClosestTick(event.x)

                // 通过动画移动滑块
                animateSliderTo(closestTick)

                // 根据滑块位置计算当前值
                val index = tickPositions.indexOf(closestTick)
                currentValue = minValue + index * step
                onChangeListener.invoke(index, currentValue)
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    private fun calculateTickPositions() {
        val stepCount = (maxValue - minValue) / step
        val startX = paddingLeft.toFloat() + circleRadius
        val endX = sliderWidth - paddingRight.toFloat() - circleRadius
        val totalWidth = endX - startX

        tickPositions = List(stepCount + 1) { index ->
            startX + (index.toFloat() / stepCount) * totalWidth
        }
    }

    private fun findClosestTick(x: Float): Float {
        return tickPositions.minByOrNull { abs(it - x) } ?: activeCircleX
    }

    private fun updateCirclePosition() {
        val index = (currentValue - minValue) / step
        activeCircleX = tickPositions.getOrElse(index) { tickPositions.first() }
    }

    /**
     * 动画滑动到目标位置
     */
    private fun animateSliderTo(targetX: Float) {
        if (activeCircleX == targetX) return

        val animator = ObjectAnimator.ofFloat(activeCircleX, targetX).apply {
//            ValueAnimator.setDuration = 300
            interpolator = AccelerateDecelerateInterpolator()
            addUpdateListener { animation ->
                activeCircleX = animation.animatedValue as Float
                invalidate()
            }
        }
        animator.start()
    }

    fun setOnChangeListener(block: (Int, Int) -> Unit) {
        this.onChangeListener = block
    }
}
