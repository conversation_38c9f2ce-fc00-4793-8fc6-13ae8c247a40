package com.mobile.anchor.app.registration.states

import anchor.app.base.manager.UserInfoManager
import anchor.app.base.utils.Logger
import com.mobile.anchor.app.registration.RegistrationContext
import com.mobile.anchor.app.registration.RegistrationState

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/26 17:20
 * @description : 相册上传状态
 * 处理相册上传阶段的逻辑，上传完成后切换到人脸视频审核状态
 */
class AlbumUploadState : RegistrationState {
    override fun handle(context: RegistrationContext) {
        Logger.i("RegistrationState 昵称审核通过，进入相册审核阶段")

        // 检查是否已上传相册
        if ((UserInfoManager.user()?.isAlbumUpload == "1" && UserInfoManager.user()?.albumStatus == 1)
            || (UserInfoManager.user()?.isAlbumUpload == "1" && UserInfoManager.user()?.isUploadTestUrl == "0")) {
            context.setState(FaceVideoApprovalState())
            context.process()
        }
    }

    override fun isMissing(): Boolean = UserInfoManager.user()?.isAlbumUpload == "0"

    override fun isReviewing(): Boolean =
        UserInfoManager.user()?.isAlbumUpload == "1" && UserInfoManager.user()?.albumStatus == 0

    override fun isCompleted(): Boolean =
        UserInfoManager.user()?.isAlbumUpload == "1" && UserInfoManager.user()?.albumStatus == 1
}