package com.mobile.anchor.app.module.discover.bean;

import java.util.List;

/**
 * <AUTHOR>
 * @name ZileMobileApp
 * @class name：mikchat.app.discover.bean
 * @class describe
 * @time 2019/8/22 10:47
 * @class describe
 */
public class ContentRecommend {

    private List<ListBean> list;

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean {
        /**
         * albums : [{"albumId":39,"albumInfo":"小时候那些经典的日本动漫您还记得么？《铁臂阿童木》《樱桃小丸子》《聪明的一休》\u2026\u2026这里给您精选了经典动漫中原声带的儿歌，让您在给宝宝听歌的时候想起自己的童年时光。","albumName":"日语儿歌","audioCount":25,"fieldId":10,"id":9,"imageUrl":"http://static.duyaya.com/image/album/96eda7c46ba42b256f2c77f7a702ab72.jpg","orderNo":3,"stick":1},{"albumId":51,"albumInfo":"节气指二十四时节和气候，是中国古代订立的一种用来指导农事的补充历法，是中国劳动人民长期经验的积累和智慧的结晶。盛龙原创的二十四首节气儿歌，内容丰富，涵盖了气候特点、太阳高度、节气三候、民俗活动、饮食养生等。每一首儿歌都以一篇节气诗词开始，旋律有明显的民歌和戏曲特色，优美动听、朗朗上口。","albumName":"二十四节气儿歌","audioCount":24,"fieldId":10,"id":10,"imageUrl":"http://static.duyaya.com/image/album/e7251132432022c88a9185d8eb9bb5f5.jpg","orderNo":4,"stick":1},{"albumId":63,"albumInfo":"","albumName":"常爸国学儿歌-三字经","audioCount":15,"fieldId":10,"id":11,"imageUrl":"http://static.duyaya.com/image/album/43424a0f3f961e77295fb332854858f8.jpg","orderNo":5,"stick":1}]
         * id : 10
         * imageUrl :
         * name : 绘本专区
         * orderNo : 1
         * publish : 1
         */

        private int id;
        private String imageUrl;
        private String name;
        private int orderNo;
        private int publish;
        private List<Albums> albums;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(int orderNo) {
            this.orderNo = orderNo;
        }

        public int getPublish() {
            return publish;
        }

        public void setPublish(int publish) {
            this.publish = publish;
        }

        public List<Albums> getAlbums() {
            return albums;
        }

        public void setAlbums(List<Albums> albums) {
            this.albums = albums;
        }

    }
}
