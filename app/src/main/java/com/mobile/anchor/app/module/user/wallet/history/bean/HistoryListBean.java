package com.mobile.anchor.app.module.user.wallet.history.bean;

import java.util.List;

/**
 * 奖品Bean
 * Modified by syk on 2020/3/3
 */

public class HistoryListBean {

    private Integer current;
    private Boolean hitCount;
    private Integer pages;
    private Boolean searchCount;
    private Integer size;
    private Integer total;
    private List<RecordsDTO> records;

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(Boolean hitCount) {
        this.hitCount = hitCount;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Boolean searchCount) {
        this.searchCount = searchCount;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<RecordsDTO> getRecords() {
        return records;
    }

    public void setRecords(List<RecordsDTO> records) {
        this.records = records;
    }

    public static class RecordsDTO {
        private int afterNum;
        private int beforeNum;
        private int changeNum;
        private String changeType;
        private Long id;
        private Long relationId;
        private Long userId;

        private String createTime;
        private String message;
        private String newUserFlag;
        private int isIncreasing;

        public String getNewUserFlag() {
            return newUserFlag;
        }

        public void setNewUserFlag(String newUserFlag) {
            this.newUserFlag = newUserFlag;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        private String headFileName;

        public Integer getAfterNum() {
            return afterNum;
        }

        public void setAfterNum(Integer afterNum) {
            this.afterNum = afterNum;
        }

        public Integer getBeforeNum() {
            return beforeNum;
        }

        public void setBeforeNum(Integer beforeNum) {
            this.beforeNum = beforeNum;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setHeadFileName(String headFileName) {
            this.headFileName = headFileName;
        }

        public String getHeadFileName() {
            return headFileName;
        }

        public int getChangeNum() {
            return changeNum;
        }

        public void setChangeNum(int changeNum) {
            this.changeNum = changeNum;
        }

        public String getChangeType() {
            return changeType;
        }

        public void setChangeType(String changeType) {
            this.changeType = changeType;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getRelationId() {
            return relationId;
        }

        public void setRelationId(Long relationId) {
            this.relationId = relationId;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public int getIsIncreasing() {
            return isIncreasing;
        }

        public void setIsIncreasing(int isIncreasing) {
            this.isIncreasing = isIncreasing;
        }
    }
}
