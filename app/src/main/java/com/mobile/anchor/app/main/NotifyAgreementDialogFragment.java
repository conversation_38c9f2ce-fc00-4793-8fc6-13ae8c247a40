package com.mobile.anchor.app.main;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;

import com.mobile.anchor.app.R;
import com.mobile.anchor.app.databinding.AppFragmentNotifyAgreementBinding;

import org.jetbrains.annotations.NotNull;

import io.reactivex.annotations.Nullable;
import anchor.app.base.core.Constants;
import anchor.app.base.ext.AppKtKt;
import com.mobile.anchor.app.module.user.agreement.AgreementActivity;

/**
 * 用户隐私协议页面
 */
//@Route(path = ArouterPath.PATH_APP_NOTIFYAGREEMENT)
public class NotifyAgreementDialogFragment extends DialogFragment {

    public static final String TAG = NotifyAgreementDialogFragment.class.getSimpleName();

    private AppFragmentNotifyAgreementBinding bindingView;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        bindingView = DataBindingUtil.inflate(inflater, R.layout.app_fragment_notify_agreement, container, false);
        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        initViews();
        return bindingView.getRoot();
    }

    private void initViews() {
        try {
            bindingView.tvConfirm.setOnClickListener(v -> dismiss());

            String agreementInfo = "欢迎您使用MindMate！我们高度重视您的数据安全与个人隐私。我们将通过《MindMate用户协议及隐私政策》帮助您了解我们收集、使用、存储和共享个人信息的情况，特别是我们所采集的个人信息类型与用途的对应关系。此外，您还能了解到您所享有的相关权利及实现途径，以及我们为保护好您的个人信息所采用的安全技术。";
            SpannableStringBuilder spannable = new SpannableStringBuilder(agreementInfo);

            //这个一定要记得设置，不然点击不生效
            bindingView.tvContent.setMovementMethod(LinkMovementMethod.getInstance());
            spannable.setSpan(new TextClick(), 32, 46, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

            bindingView.tvContent.setText(spannable);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onResume() {
        super.onResume();
        Window window = getDialog().getWindow();
        window.setGravity(Gravity.CENTER);
    }

    private static class TextClick extends ClickableSpan {
        @Override
        public void onClick(@NotNull View widget) {
            // 跳转查看隐私协议
            Bundle bundle = new Bundle();
            bundle.putString("url", Constants.HTTP_URL.Useragreement);
            bundle.putString("title", widget.getContext().getResources().getString(R.string.User_agreement_title));
            bundle.putBoolean(Constants.KEY.TOOLBARDARKMODE, true);
            AppKtKt.jump(widget.getContext(), AgreementActivity.class, bundle);
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            ds.setColor(Color.BLUE);
        }
    }
}
