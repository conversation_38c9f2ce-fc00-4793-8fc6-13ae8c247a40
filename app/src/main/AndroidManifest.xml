<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <!-- 权限-->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />

    <application
        android:name="anchor.app.base.BaseApp"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning">
        <meta-data
            android:name="zileVersionName"
            android:value="${zileVersionName}" />

        <provider
            android:name=".module.update.UpdateFileProvider"
            android:authorities="${applicationId}.updatefileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/update_cache_path" />
        </provider>

        <activity
            android:name=".main.WelcomeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/LauncherTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".main.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">

        </activity>


        <!--        阻止 FCM 自动初始化。-->
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="true" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="true" />

        <meta-data
            android:name="com.google.firebase.analytics.collection.enable"
            android:value="true" />

        <!--        <service android:name="com.google.android.gms.measurement.AppMeasurementService"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="false"/>-->

        <!--        <receiver android:name="com.google.android.gms.measurement.AppMeasurementReceiver"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.google.android.gms.measurement.UPLOAD" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="mikchat" />

        <meta-data
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:value="com.google.firebase.iid.FirebaseInstanceIdReceiver" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />

        <!--        扩展 FirebaseMessagingService 的服务。除了接收通知外，如果您还希望在后台应用中进行消息处理，则必须添加此服务。
                    如需在前台应用中接收通知、接收数据载荷以及发送上行消息等，您必须添加此扩展服务。-->
        <!--        <service-->
        <!--            android:name="io.rong.push.platform.google.RongFirebaseMessagingService"-->
        <!--            android:stopWithTask="false"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.google.firebase.MESSAGING_EVENT"/>-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <service
            android:name=".main.DemoHmsMessageService"
            android:exported="false"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!--        rongyun 注册 PushMessageReceiver，用于接收推送相关的事件广播-->
        <receiver
            android:name="io.rong.push.notification.PushMessageReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="io.rong.push.intent.MESSAGE_ARRIVED" />
                <action android:name="io.rong.push.intent.MESSAGE_CLICKED" />
                <action android:name="io.rong.push.intent.THIRD_PARTY_PUSH_STATE" />
            </intent-filter>
        </receiver>


        <!--        Workbench-->
        <activity
            android:name=".module.workbench.connectrate.ConnectRateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Base.Theme.NoActionBar" />

        <activity
            android:name=".module.workbench.proxy.memberlist.MemberListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.workbench.proxy.tndagentlist.TndAgentlistDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.workbench.proxy.tndagentlist.TndAgentListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.workbench.notification.NotificationActivity"
            android:theme="@style/TransparentTheme" />

        <!--        User-->
        <activity
            android:name=".module.user.setting.SettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.language.LanguageActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.user.about.AboutActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.user.agreement.AgreementActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.user.feedback.FeedbackActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.user.setting.blacklist.BlackListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.editedInfo.BuildProfileActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.WalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.invitation.InvitationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.rank.RankActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.invitation.problem.DescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.WithdrawActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.BankBindActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.problem.DescriptionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.history.HistoryListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.follow.FansListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.grouplist.GroupListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.level.LevelActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Base.Theme.NoActionBar" />
        <activity
            android:name=".module.user.album.AlbumManageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.album.AlbumAdditionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.album.VideoPlaybackActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.gift.MyGiftActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.safe.AccountSafeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.safe.ValidateMethodActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.safe.ModifyPasswordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.safe.ValidateEmailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.setting.safe.ModifyEmailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.statement.IncomeStatementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.editedInfo.ProfileActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.invitation.InviteActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.task.RewardTaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.level.LevelActivity2"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.record.RecordVideoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.WalletActivity2"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.DiamondDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.user.wallet.ChooseCurrencyActivity"
            android:screenOrientation="portrait" />

        <!--        Discover-->
        <activity
            android:name=".module.discover.main.chat.videochat.CallVideoChatActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".module.discover.main.chat.videochat.result.VideoResultActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".module.discover.main.rank.RankListActivity"
            android:launchMode="singleTask"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".module.discover.main.center.PersonalCenterActivity"
            android:theme="@style/TransparentTheme" />

        <activity
            android:name=".module.discover.main.center.AlbumBigImagePreviewActivity"
            android:theme="@style/TransparentTheme" />
        <activity
            android:name=".module.discover.main.center.report.ReportActivity"
            android:theme="@style/TransparentTheme" />

        <!--        Login-->
        <activity
            android:name=".module.login.login.LoginWithPasswordActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.login.audit.AuditTipActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--        android:theme="@style/ActivityDialog"-->


        <activity
            android:name=".module.login.login.LoginSelectActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.login.register.ModifyInfoActivity"
            android:theme="@style/TransparentTheme" />

        <activity
            android:name=".module.login.login.LoginActivity"
            android:screenOrientation="portrait" />

        <!--        Moment-->
        <activity
            android:name="anchor.app.base.view.BigImagePreviewActivity"
            android:theme="@style/TransparentTheme" />
    </application>

</manifest>