<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <declare-styleable name="BGABanner" tools:ignore="ResourceName">
        <!-- 指示点容器背景 -->
        <attr name="banner_pointContainerBackground" format="reference|color"/>
        <!-- 指示点背景 -->
        <attr name="banner_pointDrawable" format="reference"/>
        <!-- 指示点容器左右内间距 -->
        <attr name="banner_pointContainerLeftRightPadding" format="dimension"/>
        <!-- 指示点上下外间距 -->
        <attr name="banner_pointTopBottomMargin" format="dimension"/>
        <!-- 指示点左右外间距 -->
        <attr name="banner_pointLeftRightMargin" format="dimension"/>
        <!-- 指示器的位置 -->
        <attr name="banner_indicatorGravity">
            <flag name="top" value="0x30"/>
            <flag name="bottom" value="0x50"/>
            <flag name="left" value="0x03"/>
            <flag name="right" value="0x05"/>
            <flag name="center_horizontal" value="0x01"/>
        </attr>
        <!-- 是否开启自动轮播 -->
        <attr name="banner_pointAutoPlayAble" format="boolean"/>
        <!-- 自动轮播的时间间隔 -->
        <attr name="banner_pointAutoPlayInterval" format="integer"/>
        <!-- 页码切换过程的时间长度 -->
        <attr name="banner_pageChangeDuration" format="integer"/>
        <!-- 页面切换的动画效果 -->
        <attr name="banner_transitionEffect" format="enum">
            <enum name="defaultEffect" value="0"/>
            <enum name="fade" value="1"/>
        </attr>
        <!-- 提示文案的文字颜色 -->
        <attr name="banner_tipTextColor" format="reference|color"/>
        <!-- 提示文案的文字大小 -->
        <attr name="banner_tipTextSize" format="dimension"/>
        <!-- 加载网络数据时覆盖在 BGABanner 最上层的占位图 -->
        <attr name="banner_placeholderDrawable" format="reference"/>
        <!-- 是否是数字指示器 -->
        <attr name="banner_isNumberIndicator" format="boolean"/>
        <!-- 数字指示器文字颜色 -->
        <attr name="banner_numberIndicatorTextColor" format="reference|color"/>
        <!-- 数字指示器文字大小 -->
        <attr name="banner_numberIndicatorTextSize" format="dimension"/>
        <!-- 数字指示器背景 -->
        <attr name="banner_numberIndicatorBackground" format="reference"/>
        <!-- 当只有一页数据时是否显示指示器，默认值为 false -->
        <attr name="banner_isNeedShowIndicatorOnOnlyOnePage" format="boolean"/>
        <!-- 自动轮播区域距离 BGABanner 底部的距离 -->
        <attr name="banner_contentBottomMargin" format="dimension"/>
        <!-- 宽高比例，如果大于 0，则会根据宽度来计算高度，否则使用 android:layout_height 指定的高度 -->
        <attr name="banner_aspectRatio" format="float"/>
        <!-- 占位图和资源图片缩放模式 -->
        <attr name="android:scaleType"/>
    </declare-styleable>

    <!-- MarqueeView的自定义属性 -->
    <declare-styleable name="MarqueeView">
        <!-- 文本颜色 -->
        <attr name="marqueeTextColor" format="color" />
        <!-- 文本大小 -->
        <attr name="marqueeTextSize" format="dimension" />
        <!-- 滚动间隔时间(毫秒) -->
        <attr name="marqueeInterval" format="integer" />
        <!-- 动画持续时间(毫秒) -->
        <attr name="marqueeDuration" format="integer" />
        <!-- 最大行数 -->
        <attr name="marqueeMaxLines" format="integer" />
        <!-- 是否自动滚动 -->
        <attr name="marqueeAutoScroll" format="boolean" />
        <!-- 文本对齐方式 -->
        <attr name="marqueeTextGravity">
            <flag name="top" value="0x30" />
            <flag name="bottom" value="0x50" />
            <flag name="left" value="0x03" />
            <flag name="right" value="0x05" />
            <flag name="center_vertical" value="0x10" />
            <flag name="center_horizontal" value="0x01" />
            <flag name="center" value="0x11" />
        </attr>
    </declare-styleable>
</resources>