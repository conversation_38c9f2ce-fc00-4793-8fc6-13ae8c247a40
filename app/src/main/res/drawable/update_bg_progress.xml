<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:id="@android:id/background"
        android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle">
            <!--android:tint="?attr/colorControlNormal">-->
            <size android:height="@dimen/dp_3" />
            <solid android:color="#EAEAEA" />
            <corners android:radius="@dimen/dp_3" />
        </shape>
    </item>
    <item
        android:id="@android:id/secondaryProgress"
        android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <shape android:shape="rectangle">
                <!--android:tint="?attr/colorControlActivated">-->
                <size android:height="@dimen/dp_3" />
                <solid android:color="#EAEAEA" />
                <corners android:radius="@dimen/dp_3" />
            </shape>
        </scale>
    </item>
    <item
        android:id="@android:id/progress"
        android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <shape android:shape="rectangle">
                <!--android:tint="?attr/colorControlActivated">-->
                <size android:height="@dimen/dp_3" />
                <solid android:color="#FF6619" />
                <corners android:radius="@dimen/dp_3" />
            </shape>
        </scale>
    </item>
</layer-list>