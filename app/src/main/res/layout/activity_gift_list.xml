<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.gift.MyGiftActivity" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="@string/my_gift"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/sp_18" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/user_shape_1e2132"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp_10"
            android:paddingVertical="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/all_gift_values"
                android:textColor="@color/colorWhite" />

            <TextView
                android:id="@+id/tv_total_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_5"
                android:text="0"
                android:drawableStart="@mipmap/user_ic_coin"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/sp_18" />
        </LinearLayout>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_weight="1"
            android:background="@drawable/user_shape_white_top8"
            android:paddingLeft="@dimen/dp_16"
            android:paddingRight="@dimen/dp_16">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/user_item_my_gift" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>
</layout>