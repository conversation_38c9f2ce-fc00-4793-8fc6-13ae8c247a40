<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="workbenchinfo"
            type="com.mobile.anchor.app.module.workbench.bean.ConnectRateListBean" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_marginTop="@dimen/dp_12"
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,workbenchinfo)}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/head"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:src="@mipmap/ic_pic_default_oval"
            />

        <TextView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/name"
            android:textStyle="bold"
            android:maxWidth="@dimen/dp_110"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="RiliRiliRiliRiliRiliRiliRili"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ff333333"
            android:textSize="16sp"
            />

        <ImageView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintLeft_toRightOf="@+id/name"
            app:layout_constraintTop_toTopOf="@+id/name"
            app:layout_constraintBottom_toBottomOf="@+id/name"
            android:id="@+id/state"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@mipmap/workbenc_home_fragment_bg_08"
            />



        <TextView
            app:layout_constraintTop_toBottomOf="@+id/state"
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/countryTV"
            android:paddingLeft="@dimen/dp_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2023-08-30 15:03:01 "
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_14"
            />
        <TextView
            android:layout_marginTop="@dimen/dp_8"
            app:layout_constraintTop_toBottomOf="@+id/state"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/countryTV"
            android:layout_marginStart="@dimen/dp_6"
            android:id="@+id/createTime"
            android:paddingLeft="@dimen/dp_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2023-08-30 15:03:01 "
            android:textColor="#333333"
            android:textSize="@dimen/sp_14"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>