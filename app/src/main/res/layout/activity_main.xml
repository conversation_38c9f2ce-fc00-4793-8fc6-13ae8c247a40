<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>

        <variable
            name="mainActivity"
            type="com.mobile.anchor.app.main.MainActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme">


<!--                <anchor.app.base.view.NoScrollViewPager-->
        <anchor.app.base.view.NoScrollViewPager
            android:id="@+id/viewPager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:bind_viewPager_fragmentManager="@{ mainActivity.getSupportFragmentManager()}"
            app:bind_viewPager_fragments="@{mainActivity.initFragments()}"
            app:bind_viewPager_offScreenPageLimit="@{ 3 }"
            app:layout_constraintBottom_toTopOf="@+id/navigation"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/colorTheme"
            app:layout_constraintBottom_toTopOf="@+id/navigation" />

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/navigation"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_58"
            android:background="@color/colorTheme"
            app:bind_onNavigationBottomSelectedChanged="@{ (menuItem) -> mainActivity.onBottomNavigationSelectChanged(menuItem) }"
            app:itemHorizontalTranslationEnabled="true"
            app:itemIconSize="@dimen/dp_30"
            app:itemTextColor="@drawable/selector_main_bottom_nav_button"
            app:labelVisibilityMode="labeled"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/viewPager"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>