<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.wallet.WalletActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorWhite"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_210"
            android:background="@mipmap/user_ic_wallte_bg"
            android:fitsSystemWindows="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:orientation="horizontal">
                <!--            user_ic_wallte_bg-->
                <FrameLayout
                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20"
                    app:bind_view_onClick="@{()->activity.onBackPressed()}">

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@mipmap/base_ic_back"
                        app:tint="@color/base_color_divider" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:singleLine="true"
                    android:text="@string/My_wallet"
                    android:textColor="#FFFFFF"
                    android:textSize="@dimen/sp_18"
                    android:textStyle="bold" />


                <ImageView
                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/dp_15"
                    android:scaleType="fitXY"
                    android:src="@mipmap/user_ic_wallte_01"
                    android:visibility="gone"
                    app:bind_view_onClick="@{ (v) -> activity.query() }" />
            </RelativeLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_56"
                android:text="@string/Balance"
                android:textColor="#ffffffff"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/currentDiamond"
                android:layout_width="134dp"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_86"
                android:text="100000"
                android:textColor="#ffffffff"
                android:textSize="38sp" />

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/abc_vector_test"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/RLTitle">


                <androidx.recyclerview.widget.RecyclerView

                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_4"
                    android:layout_marginEnd="@dimen/dp_4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/RLTitle" />
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

            <LinearLayout
                android:id="@+id/LLEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/img_err"
                    android:layout_width="@dimen/dp_270"
                    android:layout_height="@dimen/dp_270"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp_70"
                    android:src="@mipmap/base_ic_net_error" />

                <TextView
                    android:id="@+id/tvNetErrorTips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/img_err"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="@dimen/dp_30"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginEnd="@dimen/dp_30"
                    android:layout_marginBottom="@dimen/dp_30"
                    android:text="@string/base_empty_view_hint"
                    android:textColor="#B3B9BF"
                    android:textSize="@dimen/sp_14" />


            </LinearLayout>
        </LinearLayout>

        <Button
            android:id="@+id/btnSave"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_45"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_45"
            android:layout_marginBottom="@dimen/dp_20"
            android:background="@drawable/bg_button_them"
            android:text="@string/wallet_withdraw"
            android:textAllCaps="false"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_14"
            app:bind_view_onClick="@{ () -> activity.save() }" />

    </LinearLayout>
</layout>