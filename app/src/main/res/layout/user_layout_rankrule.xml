<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_40"
        android:background="@mipmap/user_activity_rank_bg_16"
        android:orientation="vertical">

        <ImageView
            android:layout_marginStart="@dimen/dp_55"
            android:layout_marginEnd="@dimen/dp_55"
            android:layout_marginTop="@dimen/dp_28"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            android:src="@mipmap/user_activity_rank_bg_15"
            />



        <LinearLayout
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="left"
            android:orientation="horizontal">
            <anchor.app.base.view.RoundImageView
                android:layout_marginTop="@dimen/dp_6"
                app:is_circle="true"
                android:layout_width="@dimen/dp_6"
                android:layout_height="@dimen/dp_6"
                android:src="#817DFF"
                />
            <TextView
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_8"
                android:includeFontPadding="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="When you receive a specified number of specific gifts each week, you will be eligible for the corresponding rewards; the top ten will receive corresponding rewards; no rewards will be given if you are not on the list;"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_14"
                />
        </LinearLayout>


        <LinearLayout
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="left"
            android:orientation="horizontal">
            <anchor.app.base.view.RoundImageView
                android:layout_marginTop="@dimen/dp_6"
                app:is_circle="true"
                android:layout_width="@dimen/dp_6"
                android:layout_height="@dimen/dp_6"
                android:src="#817DFF"
                />
            <TextView
                android:layout_marginEnd="@dimen/dp_16"

                android:layout_marginStart="@dimen/dp_8"
                android:includeFontPadding="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Rewards will be distributed every Monday based on your highest score, and rewards are not cumulative;"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_14"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="left"
            android:orientation="horizontal">
            <anchor.app.base.view.RoundImageView
                android:layout_marginTop="@dimen/dp_6"
                app:is_circle="true"
                android:layout_width="@dimen/dp_6"
                android:layout_height="@dimen/dp_6"
                android:src="#817DFF"
                />
            <TextView
                android:layout_marginEnd="@dimen/dp_16"

                android:layout_marginStart="@dimen/dp_8"
                android:includeFontPadding="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="This list only shows the top ten anchors who received gifts. The weekly star list will be settled at 24:00 every Sunday;"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_14"
                />
        </LinearLayout>


        <LinearLayout
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="left"
            android:orientation="horizontal">
            <anchor.app.base.view.RoundImageView
                android:layout_marginTop="@dimen/dp_6"
                app:is_circle="true"
                android:layout_width="@dimen/dp_6"
                android:layout_height="@dimen/dp_6"
                android:src="#817DFF"
                />
            <TextView
                android:layout_marginEnd="@dimen/dp_16"

                android:layout_marginStart="@dimen/dp_8"
                android:includeFontPadding="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="The activity ranking list is a weekly round. If the number of gifts is the same, the ranking list will be based on first come first served;"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_14"
                />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>