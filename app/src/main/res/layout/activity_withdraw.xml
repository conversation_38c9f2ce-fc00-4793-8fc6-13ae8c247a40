<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.wallet.WithdrawActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorWhite"
        android:orientation="vertical">

        <RelativeLayout
            android:fitsSystemWindows="true"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_210"
            android:background="@mipmap/user_ic_wallte_bg_03"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:orientation="horizontal">
                <!--            user_ic_wallte_bg-->
                <FrameLayout
                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20"
                    app:bind_view_onClick="@{()->activity.onBackPressed()}">

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@mipmap/base_ic_back"
                        android:tint="@color/base_color_divider" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:singleLine="true"
                    android:text="@string/My_wallet"
                    android:textColor="#FFFFFF"
                    android:textSize="@dimen/sp_18"
                    android:textStyle="bold" />


                <ImageView
                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/dp_15"
                    android:scaleType="fitXY"
                    android:src="@mipmap/user_ic_wallte_01"
                    android:visibility="gone" />
            </RelativeLayout>

            <TextView
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_56"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/Balance"
                android:textColor="#ffffffff"
                android:textSize="@dimen/sp_16"
                />

            <TextView
                android:id="@+id/currentDiamond"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_86"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100000"
                android:textColor="#ffffffff"
                android:textSize="38sp"
                />

        </RelativeLayout>





        <LinearLayout
            android:id="@+id/LL01"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dp_180"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:background="@mipmap/user_activity_wallte02_bg_01"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_258">

            <TextView
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Enter the number of diamonds"
                android:textColor="#ff333333"
                android:textSize="14sp"
                />


            <EditText
                android:digits="0123456789"
                android:layout_margin="@dimen/dp_16"
                android:padding="@dimen/dp_16"
                android:id="@+id/editAcount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="#00000000"
                android:paddingLeft="@dimen/dp_12"
                android:textColor="#000"
                android:imeOptions="actionNext"
                android:maxLength="8"
                android:singleLine="true"
                android:textColorHint="@color/login_text_tips_color"
                android:hint="Please enter the amount"
                android:textSize="@dimen/sp_20" />


            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_28"

                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/bottomPriceTV"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Minimum withdrawal 20000"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    />

                <ImageView
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:src="@mipmap/diamond"
                    />



            </LinearLayout>

            <Button
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="@dimen/dp_80"
                android:layout_marginTop="@dimen/dp_20"
                android:id="@+id/btnSave"
                android:layout_marginStart="@dimen/dp_45"
                android:layout_marginEnd="@dimen/dp_45"
                style="@style/PrimaryButton"
                android:text="@string/wallet_withdraw"
                app:bind_view_onClick="@{ () -> activity.save() }" />
        </LinearLayout>

        <ImageView
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_65"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_alignTop="@+id/LL01"
            android:layout_alignParentRight="true"
            android:id="@+id/close02"
            app:bind_view_onClick="@{ (v) -> activity.close() }"
            android:padding="@dimen/dp_6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/base_ic_close_04"
            />
    </RelativeLayout>



</layout>