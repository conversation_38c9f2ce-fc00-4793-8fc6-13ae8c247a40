<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>

        <variable
            name="viewModel"
            type="anchor.app.base.web.BaseWebViewFragment" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/dp_50"
        android:background="@color/colorTheme">

        <View
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56"
            android:layout_marginTop="25dp"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <anchor.app.base.view.tablayout.MTabLayout-->
        <!--            android:id="@+id/slidingTabLayout"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="@dimen/dp_40"-->
        <!--            android:layout_gravity="center_vertical"-->
        <!--            app:ddAnimatedIndicator="dachshund"-->
        <!--            app:ddIndicatorColor="@color/colorWhite"-->
        <!--            app:ddIndicatorHeight="@dimen/dp_4"-->
        <!--            android:layout_marginStart="@dimen/dp_5"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/title"-->
        <!--            app:layout_constraintStart_toStartOf="@+id/title"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/title"-->
        <!--            app:tabGravity="fill"-->
        <!--            app:tabMaxWidth="@dimen/dp_200"-->
        <!--            app:tabMinWidth="@dimen/dp_20"-->
        <!--            app:tabMode="scrollable"-->
        <!--            app:tabPaddingEnd="@dimen/dp_11"-->
        <!--            app:tabPaddingStart="@dimen/dp_11"-->
        <!--            app:tabRippleColor = "@android:color/transparent"-->
        <!--            app:tabSelectedTextColor="@color/colorWhite"-->
        <!--            app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"-->
        <!--            app:tabTextColor="#999999" />-->

        <anchor.app.base.view.SlidingTabLayout2
            android:id="@+id/slidingTabLayout"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/title"
            app:layout_constraintTop_toTopOf="@+id/title"
            app:tl_indicator_corner_radius="@dimen/dp_24"
            app:tl_indicator_end_color="#8531FF"
            app:tl_indicator_height="@dimen/dp_8"
            app:tl_indicator_margin_bottom="@dimen/dp_10"
            app:tl_indicator_margin_top="@dimen/dp_20"
            app:tl_indicator_start_color="#EC12E2"
            app:tl_indicator_style="NORMAL"
            app:tl_indicator_width_equal_title="true"
            app:tl_textBold="SELECT"
            app:tl_textSelectColor="@color/white"
            app:tl_textsize="@dimen/sp_18" />

        <ImageView
            android:id="@+id/selectCountry"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_10"
            android:src="@mipmap/discover_country_icon"
            app:layout_constraintBottom_toBottomOf="@+id/slidingTabLayout"
            app:layout_constraintRight_toRightOf="@+id/slidingTabLayout"
            app:layout_constraintTop_toTopOf="@+id/slidingTabLayout"

            />

        <!--            app:tabSelectedBold="true"-->


        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />
        <!--        <androidx.viewpager.widget.ViewPager-->
        <!--            android:id="@+id/viewPager"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="0dp"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/title"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"/>-->

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>