<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/user_shape_popup_white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingBottom="@dimen/dp_40">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:text="@string/reward_description"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_18"
                android:layout_marginTop="@dimen/dp_20"
                android:lineSpacingExtra="@dimen/dp_5"
                android:text="@string/reward_description_content"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@mipmap/base_ic_circle_close"
            android:backgroundTint="@color/white_60" />
    </LinearLayout>
</layout>