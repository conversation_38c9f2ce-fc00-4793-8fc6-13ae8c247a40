<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_16"
    android:gravity="center"
    tools:background="@color/background">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_5"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_diamond"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_20"
            android:background="@mipmap/user_diamond" />

        <TextView
            android:id="@+id/tv_diamond"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_3"
            android:text="+10"
            android:textColor="#9F2AF8"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:text="XXX"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2024-12-12"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_12"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progress_bar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_3"
            android:layout_marginTop="@dimen/dp_8"
            android:progress="50"
            android:progressDrawable="@drawable/user_progress_drawable_task"
            android:progressTint="@color/colorAccent"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/user_shape_9f2af8_50radius"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_3"
        android:text="@string/task_get"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />
</LinearLayout>