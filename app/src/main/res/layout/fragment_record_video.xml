<?xml version="1.0" encoding="utf-8"?>
<layout>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <TextureView
            android:id="@+id/textureView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/recorder_facing"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_35"
            android:layout_alignParentEnd="true"
            android:visibility="gone"
            android:paddingHorizontal="@dimen/dp_13"
            android:src="@mipmap/ic_camera_facing" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/record_layout"
            android:layout_below="@+id/recorder_facing">

            <com.mobile.anchor.app.module.user.record.view.TCConfigPlayerView
                android:id="@+id/playerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:lineSpacingExtra="@dimen/dp_5"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingBottom="@dimen/dp_50"
                android:text="@string/record_video_reminder"
                android:textColor="@color/color_F53D3D"
                android:textSize="@dimen/sp_12" />
        </FrameLayout>
        <!--录制-->
        <RelativeLayout
            android:id="@+id/record_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_155"
            android:layout_alignParentBottom="true"
            android:background="@color/color_cardBackground">

            <LinearLayout
                android:id="@+id/rl_record_tip"
                android:layout_width="@dimen/dp_180"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_10"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/iv_red_point"
                    android:layout_width="@dimen/dp_6"
                    android:layout_height="@dimen/dp_6"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@drawable/red_point" />

                <TextView
                    android:id="@+id/process_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0.0"
                    android:textColor="#fff"
                    android:textSize="@dimen/sp_12" />

                <ImageView
                    android:id="@+id/iv_line"
                    android:layout_width="@dimen/dp_1"
                    android:layout_height="@dimen/dp_8"
                    android:layout_marginHorizontal="@dimen/dp_5"
                    android:background="#9f9f9e" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="02:00"
                    android:textColor="#fff"
                    android:textSize="@dimen/sp_12" />
            </LinearLayout>

            <com.mobile.anchor.app.module.user.record.view.ComposeRecordBtn
                android:id="@+id/compose_record_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />

            <LinearLayout
                android:id="@+id/rl_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="@dimen/dp_50"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_record_again"
                    style="@style/PrimaryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_36"
                    android:background="@drawable/user_shape_9f2af8_50radius"
                    android:minWidth="@dimen/dp_60"
                    android:paddingHorizontal="@dimen/dp_10"
                    android:text="@string/cancel" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tv_upload"
                    style="@style/PrimaryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginEnd="@dimen/dp_36"
                    android:background="@drawable/user_shape_9f2af8_50radius"
                    android:minWidth="@dimen/dp_60"
                    android:paddingHorizontal="@dimen/dp_10"
                    android:text="@string/upload" />
            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>
</layout>