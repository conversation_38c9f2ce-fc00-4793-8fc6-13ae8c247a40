<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.grouplist.GroupListActivity"
            />
    </data>
    <RelativeLayout
        android:background="@mipmap/user_fans_bg_01"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:id="@+id/RLTitle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />



            </FrameLayout>


            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="Details"
                android:textColor="@color/public_text_color"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />
        </RelativeLayout>

        <!--            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout-->
        <!--                android:layout_below="@+id/RLTitle"-->
        <!--                android:id="@+id/refreshLayout"-->
        <!--                android:paddingLeft="@dimen/dp_20"-->
        <!--                android:paddingRight="@dimen/dp_20"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content">-->

        <com.scwang.smartrefresh.layout.SmartRefreshLayout

            android:layout_below="@+id/RLTitle"
            android:paddingLeft="@dimen/dp_20"
            android:paddingRight="@dimen/dp_20"
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/RLTitle"
                app:layout_constraintBottom_toBottomOf="parent"
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <!--            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>-->



    </RelativeLayout>

</layout>