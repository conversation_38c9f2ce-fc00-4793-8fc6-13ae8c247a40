<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="userinfo"
            type="com.mobile.anchor.app.module.user.bean.RankBean.RankConfigListDTO" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:paddingRight="@dimen/dp_16"
        android:paddingLeft="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">



        <LinearLayout
            android:id="@+id/LLCount"
            android:layout_marginStart="@dimen/dp_58"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:id="@+id/gift"
                />

            <TextView
                android:id="@+id/giftCount"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="x99"
                android:textColor="#fffc6399"
                android:textSize="@dimen/sp_14"
                />

        </LinearLayout>
        <LinearLayout
            android:layout_marginStart="@dimen/dp_110"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/LLCount"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:id="@+id/diamond"
                android:src="@mipmap/diamond"
                />

            <TextView
                android:id="@+id/diamondCount"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="x99"
                android:textColor="#ff219aff"
                android:textSize="@dimen/sp_14"
                />

        </LinearLayout>





    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>