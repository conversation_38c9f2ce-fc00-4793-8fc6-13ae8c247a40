<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_40"
        android:background="@drawable/base_shape_round_background"
        android:orientation="vertical">

        <TextView
            android:padding="@dimen/dp_22"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/Operation_Tips"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_18"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_color_divider" />
        <TextView
            android:id="@+id/content"
            android:padding="@dimen/dp_27"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Are you sure to remove that userfrom your blacklist?"
            android:textColor="#ff666666"
            android:textSize="@dimen/sp_14"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_color_divider" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textViewConcle"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_60"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/Cancel"
                android:textColor="#999999"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/textViewConfirm"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_60"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/Done"
                android:textColor="@color/colorPrimaryDark"
                android:textSize="@dimen/sp_16" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>