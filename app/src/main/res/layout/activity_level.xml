<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="user"
            type="anchor.app.base.bean.LoginBean" />

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.level.LevelActivity" />
    </data>
    <!--    android:fitsSystemWindows="true"-->
    <!---->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/user_level_bg_01"
        android:fitsSystemWindows="true">

        <RelativeLayout
            android:id="@+id/RL"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">
            <!--        ?attr/actionBarSize-->

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.finish()}">

                <ImageView
                    android:layout_width="@dimen/dp_9"
                    android:layout_height="@dimen/dp_15"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back"
                    app:tint="#FFFFFF" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="@string/My_Level"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />
        </RelativeLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/CL"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_82"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@mipmap/user_level_bg_02"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/RL">

            <!--        <anchor.app.base.view.RoundImageView-->
            <!--            app:bind_imageView_url="@{ user.getHeadFileName() }"-->
            <!--            app:avatar_url="@{user.getHeadFileName()}"-->
            <anchor.app.base.view.AvatarView
                android:id="@+id/head"
                android:layout_width="@dimen/dp_68"
                android:layout_height="@dimen/dp_68"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_16"
                app:is_circle="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/LL01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/head"
                app:layout_constraintLeft_toRightOf="@+id/head"
                app:layout_constraintTop_toTopOf="@+id/head">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/anchorName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@{user.getNick()}"
                        android:textColor="#FFFFFF"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold"
                        tools:text="LiLi" />


                </LinearLayout>

                <TextView
                    android:id="@+id/levelTips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:text="Noble not activated～"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_14" />


                <ImageView
                    android:id="@+id/level"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_22"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_8"
                    android:visibility="gone" />


            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/FL"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/topUp"
            app:layout_constraintTop_toBottomOf="@+id/CL" />

        <ImageView
            android:id="@+id/topUp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_36"
            android:src="@mipmap/user_level_bg_03"
            app:bind_view_onClick="@{() -> activity.topUp()}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>