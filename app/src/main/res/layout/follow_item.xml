<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_12"
    android:paddingTop="@dimen/dp_14"
    tools:ignore="ResourceName">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:src="@mipmap/ic_pic_default_oval"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/fl_online_status"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_2"
        android:layout_marginBottom="@dimen/dp_2"
        android:background="@drawable/shape_follow_online_dot_bg"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar">

        <View
            android:id="@+id/online_dot"
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_8"
            android:layout_gravity="center"
            android:background="@drawable/shape_online_dot" />

    </FrameLayout>


    <TextView
        android:id="@+id/tv_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="李生" />


    <TextView
        android:id="@+id/tv_age"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_age_bg"
        android:paddingHorizontal="@dimen/dp_4"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        tools:text="26" />

    <TextView
        android:id="@+id/tv_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:background="@drawable/shape_country_bg"
        android:paddingHorizontal="@dimen/dp_4"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toEndOf="@id/tv_age"
        tools:text="26" />

    <ImageView
        android:id="@+id/iv_send_msg"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:src="@mipmap/ic_anchor_message"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dp_13"
        android:background="#10F0F0F0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar" />
</androidx.constraintlayout.widget.ConstraintLayout>