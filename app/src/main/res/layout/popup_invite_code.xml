<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/user_shape_popup_white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingBottom="@dimen/dp_40">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_18"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/invite_code_explain"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_invite_code"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/user_shape_e6e6e6_8"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp_16"
                android:text="9d9d9f"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/sp_14"
                app:drawableEndCompat="@mipmap/user_ic_copy"
                app:drawableTint="@color/color_999999" />

            <TextView
                android:id="@+id/tv_invite"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:layout_marginHorizontal="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/user_shape_9f2af8_50radius"
                android:gravity="center"
                android:text="@string/invite"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@mipmap/base_ic_circle_close"
            android:backgroundTint="@color/white_60" />
    </LinearLayout>
</layout>