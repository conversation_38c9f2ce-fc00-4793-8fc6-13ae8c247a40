<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/base_shape_round_rect_background"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44">

            <TextView
                android:id="@+id/screen_cancle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:paddingLeft="@dimen/dp_40"
                android:text="@string/user_login_concel"
                android:textColor="@color/user_select_text_color"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/screen_sure"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:paddingRight="@dimen/dp_40"
                android:text="@string/user_login_confirm"
                android:textColor="@color/schedule_confirm_text_color"
                android:textSize="@dimen/sp_16" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/base_color_divider" />
        <!--此部分需要完整复制过去，删减或者更改ID会导致初始化找不到内容而报空-->
        <LinearLayout
            android:id="@+id/optionspicker"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="horizontal">

            <com.contrarywind.view.WheelView
                android:id="@+id/options1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.contrarywind.view.WheelView
                android:id="@+id/options2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.contrarywind.view.WheelView
                android:id="@+id/options3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>