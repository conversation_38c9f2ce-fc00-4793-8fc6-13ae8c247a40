<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_16"
        android:background="@color/background">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/enter_your_email"
            android:textColor="#9FA1A6"
            android:textSize="@dimen/sp_14" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_54"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_edit_info"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:hint="@string/please_enter_email"
            android:paddingHorizontal="@dimen/dp_24"
            android:textColor="@color/white"
            android:textColorHint="@color/color_999999"
            android:textSize="@dimen/sp_16" />

        <Button
            android:id="@+id/btn_code"
            style="@style/PrimaryButton"
            android:layout_marginTop="@dimen/dp_32"
            android:text="@string/get_verification_code" />
    </LinearLayout>
</layout>