<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName">

    <data>


        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.level.LevelActivity"
            />

        <variable
            name="viewModel"
            type="anchor.app.base.web.BaseWebViewFragment" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56"
            app:layout_constraintTop_toTopOf="parent"/>
        <anchor.app.base.view.tablayout.MTabLayout
            android:background="#00000000"
            android:id="@+id/slidingTabLayout"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="center_vertical"
            app:ddAnimatedIndicator="dachshund"
            app:ddIndicatorColor="#C7A993"
            app:ddIndicatorHeight="@dimen/dp_4"
            android:layout_marginStart="@dimen/dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toStartOf="@+id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title"
            app:tabGravity="fill"
            app:tabMaxWidth="@dimen/dp_200"
            app:tabMinWidth="@dimen/dp_20"
            app:tabMode="scrollable"
            app:tabPaddingEnd="@dimen/dp_11"
            app:tabPaddingStart="@dimen/dp_11"
            app:tabRippleColor = "@android:color/transparent"
            app:tabSelectedTextColor="#C7A993"
            app:tabTextAppearance="@style/TabLayoutTextStyle_15_bold"
            app:tabTextColor="#695C57" />


<!--            app:tabSelectedBold="true"-->


        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/title"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>



</layout>