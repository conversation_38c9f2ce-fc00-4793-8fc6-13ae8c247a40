<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <include layout="@layout/base_layout_title_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#1D2031" />

        <TextView
            android:id="@+id/tv_modify_pwd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/modify_login_password"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_16"
            app:drawableEndCompat="@mipmap/user_ic_right_white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#1D2031" />

        <TextView
            android:id="@+id/tv_modify_email"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:visibility="gone"
            android:drawableEnd="@mipmap/user_ic_right_white"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/modify_bind_email"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_16" />
    </LinearLayout>
</layout>