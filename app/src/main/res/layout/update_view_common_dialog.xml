<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/public_transparent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="@dimen/dp_285"
        android:layout_height="@dimen/dp_143"
        android:scaleType="fitXY"
        android:src="@mipmap/update_bg_title_dialog" />

    <LinearLayout
        android:layout_width="@dimen/dp_285"
        android:layout_height="wrap_content"
        android:background="@drawable/update_bg_bottom_dialog"
        android:orientation="vertical">

        <!--版本更新 - 标题-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingTop="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_12"
            android:text="Version updates"
            android:textColor="#FF781A"
            android:textSize="@dimen/sp_19" />

        <!--内容-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_29"
            android:paddingRight="@dimen/dp_29">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Update content:"
                android:textColor="#475460"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:id="@+id/tv_content_dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:lineSpacingExtra="@dimen/dp_5"
                android:text="1. Fix some issues \n2. Add several features"
                android:textColor="#666C72"
                android:textSize="@dimen/sp_13" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginTop="@dimen/dp_18"
            android:background="#EAEAEA" />

        <!--不再提醒、立刻更新-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btn_not_remind"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingTop="@dimen/dp_14"
                android:paddingBottom="@dimen/dp_16"
                android:text="Maybe later"
                android:textColor="#545C65"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:layout_width="@dimen/dp_1"
                android:layout_height="@dimen/dp_31"
                android:layout_gravity="center_vertical"
                android:background="#EAEAEA" />

            <TextView
                android:id="@+id/btn_update_now"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingTop="@dimen/dp_14"
                android:paddingBottom="@dimen/dp_16"
                android:text="Update now"
                android:textColor="#FF6619"
                android:textSize="@dimen/sp_16" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/btn_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_30"
        android:background="@mipmap/update_btn_close_dialog" />

</LinearLayout>