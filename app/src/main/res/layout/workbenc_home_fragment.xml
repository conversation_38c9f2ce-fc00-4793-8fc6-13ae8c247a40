<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>

        <variable
            name="viewModel"
            type="anchor.app.base.web.BaseWebViewFragment" />

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.workbench.home.WorkbenchFragment" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <TextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <RelativeLayout
            android:id="@+id/RLTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_24"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="Workbench"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14"
                android:src="@mipmap/workbenc_home_fragment_rank"
                android:visibility="gone"
                app:bind_view_onClick="@{ () -> activity.rank() }" />
        </RelativeLayout>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/RLTitle">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/dp_16"
                    android:paddingRight="@dimen/dp_16">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardViewTask"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible"
                        app:cardBackgroundColor="@color/color_cardBackground"
                        app:cardCornerRadius="@dimen/dp_10"
                        tools:visibility="visible">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/dp_20"
                            android:paddingVertical="@dimen/dp_15">

                            <TextView
                                android:id="@+id/tv_task"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawablePadding="@dimen/dp_5"
                                android:drawableTint="@color/color_F53D3D"
                                android:gravity="center_vertical"
                                android:text="@string/match_bonus_task_text"
                                android:textColor="@color/color_F53D3D"
                                android:textSize="@dimen/sp_16"
                                android:textStyle="bold"
                                app:drawableStartCompat="@mipmap/workbenc_tip" />

                            <TextView
                                android:id="@+id/tv_valid_match_calls"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_6"
                                android:text="@string/valid_match_calls_today"
                                android:textColor="@color/color_666666"
                                android:textSize="@dimen/sp_14" />
                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardViewWork"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/dp_10"
                        app:cardBackgroundColor="@color/color_cardBackground"
                        app:cardCornerRadius="@dimen/dp_10">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="@dimen/sp_16"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingBottom="@dimen/sp_16">

                            <TextView
                                android:id="@+id/TV01"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_6"
                                android:elevation="@dimen/dp_10"
                                android:text="@string/work_mode"
                                android:textColor="@color/colorWhite"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.kyleduo.switchbutton.SwitchButton
                                android:id="@+id/switch_01"
                                android:layout_width="@dimen/dp_45"
                                android:layout_height="@dimen/dp_25"
                                android:elevation="@dimen/dp_10"
                                app:kswAnimationDuration="100"
                                app:kswBackColor="#EAEAEA"
                                app:kswBackDrawable="@drawable/base_selector_bg_switch_button"
                                app:kswThumbColor="#ffffff"
                                app:kswTintColor="@color/colorAccent"
                                app:layout_constraintBottom_toBottomOf="@id/TV01"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/TV01" />

                            <TextView
                                android:id="@+id/TV02"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_16"
                                android:elevation="@dimen/dp_10"
                                android:text="@string/match_call"
                                android:textColor="@color/colorWhite"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintStart_toStartOf="@id/TV01"
                                app:layout_constraintTop_toBottomOf="@id/TV01"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.kyleduo.switchbutton.SwitchButton
                                android:id="@+id/switch_02"
                                android:layout_width="@dimen/dp_45"
                                android:layout_height="@dimen/dp_25"
                                android:elevation="@dimen/dp_10"
                                app:kswAnimationDuration="100"
                                app:kswBackColor="#EAEAEA"
                                app:kswBackDrawable="@drawable/base_selector_bg_switch_button"
                                app:kswThumbColor="#ffffff"
                                app:kswTintColor="@color/colorAccent"
                                app:layout_constraintBottom_toBottomOf="@id/TV02"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/TV02" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardViewInstruction"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone"
                        app:cardBackgroundColor="@color/color_cardBackground"
                        app:cardCornerRadius="@dimen/dp_10">

                        <TextView
                            android:id="@+id/tv_instruction"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_40"
                            android:drawablePadding="@dimen/dp_5"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/dp_20"
                            android:text="@string/instructions"
                            android:textColor="@color/color_666666"
                            android:textSize="@dimen/sp_12" />

                    </androidx.cardview.widget.CardView>

                    <!--                    Banner-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/CL002"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_110"
                        android:layout_marginTop="@dimen/dp_10"
                        android:background="@mipmap/workbenc_home_fragment_bg_11"
                        android:visibility="gone"
                        app:bind_view_onClick="@{ () -> activity.rank02() }" />

                    <com.mobile.anchor.app.util.MarqueeView
                        android:id="@+id/marqueeView"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_50"
                        android:layout_marginTop="@dimen/dp_10"
                        app:marqueeMaxLines="5"
                        app:marqueeTextSize="14sp"
                        app:marqueeInterval="2000"
                        android:minHeight="@dimen/dp_30"
                        android:padding="8dp"
                        tools:text="This is a marquee view, which can be used to display a list of messages in a scrolling manner." />

                    <!--                    Agent part-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/agent_part_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        tools:visibility="gone">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/ProxyCL01"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_141"
                            android:layout_marginTop="@dimen/dp_10"
                            android:elevation="@dimen/dp_10"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.cardview.widget.CardView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="@dimen/dp_3"
                                app:cardBackgroundColor="@color/color_cardBackground"
                                app:cardCornerRadius="@dimen/dp_10" />

                            <androidx.constraintlayout.widget.ConstraintLayout

                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_12"
                                android:layout_marginTop="@dimen/dp_12"
                                android:layout_marginEnd="@dimen/dp_12"
                                android:elevation="@dimen/dp_10"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <TextView
                                    android:id="@+id/Agent"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Agent Name : Bella"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_14"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />


                                <TextView
                                    android:id="@+id/Invite"

                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:drawableRight="@mipmap/workbenc_home_fragment_bg_10"
                                    android:drawablePadding="@dimen/dp_10"
                                    android:text="Invite Code：888888"
                                    android:textColor="@color/color_666666"
                                    android:textSize="@dimen/sp_12"
                                    app:bind_view_onClick="@{ () -> activity.copyID02() }"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/currentAmount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_38"
                                    android:text="$1000.00"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_28"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_88"
                                    android:text="Current Earnings ( USD )"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="@dimen/sp_12"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <LinearLayout
                            android:id="@+id/ProxyLL01"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="2"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ProxyCL01">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/todayDiamond"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                                        android:drawablePadding="@dimen/dp_5"
                                        android:text="99"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_28" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_14"
                                        android:text="Today Earnings (Diamonds)"
                                        android:textColor="@color/color_C8C8C8"
                                        android:textSize="@dimen/sp_10" />
                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/yesterdayDiamond"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                                        android:drawablePadding="@dimen/dp_5"
                                        android:text="99"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_28" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_14"
                                        android:text="Yesterday Earnings (Diamonds)"
                                        android:textColor="@color/color_C8C8C8"
                                        android:textSize="@dimen/sp_10" />
                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ProxyLL02"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="2"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ProxyLL01">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/weekDiamond"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                                        android:drawablePadding="@dimen/dp_5"
                                        android:text="99"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_28" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_14"
                                        android:text="Week Earnings (Diamonds)"
                                        android:textColor="@color/color_C8C8C8"
                                        android:textSize="@dimen/sp_10" />
                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/monthDiamond"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                                        android:drawablePadding="@dimen/dp_5"
                                        android:text="99"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_28" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_14"
                                        android:text="Month Earnings (Diamonds)"
                                        android:textColor="@color/color_C8C8C8"
                                        android:textSize="@dimen/sp_10" />
                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ProxyLL03"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="2"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ProxyLL02">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/totalDiamond"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                                        android:drawablePadding="@dimen/dp_5"
                                        android:text="99"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_28" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_14"
                                        android:text="Total Earnings (Diamonds)"
                                        android:textColor="@color/color_C8C8C8"
                                        android:textSize="@dimen/sp_10" />

                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_106"
                                android:layout_marginTop="@dimen/dp_10"
                                android:layout_weight="1"
                                app:bind_view_onClick="@{ () -> activity.agencySkip() }">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10"
                                    tools:layout_editor_absoluteX="4dp"
                                    tools:layout_editor_absoluteY="3dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:gravity="center"
                                    android:orientation="vertical"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/agencySkip"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="2nd Agent Info >>"
                                        android:textColor="#ff0491ff"
                                        android:textSize="@dimen/sp_16"
                                        android:textStyle="bold" />

                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ProxyLL04"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="2"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ProxyLL03">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/ProxyLL05"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_141"
                                android:layout_marginTop="@dimen/dp_10"
                                android:elevation="@dimen/dp_10"
                                app:bind_view_onClick="@{ () -> activity.agencySkip02() }"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="@dimen/dp_3"
                                    app:cardBackgroundColor="@color/color_cardBackground"
                                    app:cardCornerRadius="@dimen/dp_10" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:text="Member list >>"
                                    android:textColor="#ffffb909"
                                    android:textSize="@dimen/sp_16"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!--                    Working time card-->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_3"
                        android:layout_marginTop="@dimen/dp_10"
                        app:cardBackgroundColor="@color/color_cardBackground"
                        app:cardCornerRadius="@dimen/dp_10">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_141"
                            android:padding="@dimen/dp_16">

                            <TextView
                                android:id="@+id/TV03"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:elevation="@dimen/dp_10"
                                android:text="Working time"
                                android:textColor="@color/color_C8C8C8"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/time01"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_20"
                                android:layout_marginEnd="@dimen/dp_60"
                                android:elevation="@dimen/dp_10"
                                android:textColor="@color/colorWhite"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintHorizontal_chainStyle="spread"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toLeftOf="@+id/time02"
                                app:layout_constraintTop_toBottomOf="@+id/TV03"
                                tools:text="01:30:05" />

                            <TextView
                                android:id="@+id/time02"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_20"
                                android:elevation="@dimen/dp_10"
                                android:textColor="@color/colorWhite"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintLeft_toRightOf="@+id/time01"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/TV03"
                                tools:text="01:30:05" />

                            <TextView
                                android:id="@+id/TV04"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Today"
                                android:textColor="@color/color_999999"
                                android:textSize="@dimen/sp_14"
                                app:layout_constraintLeft_toLeftOf="@+id/time01"
                                app:layout_constraintRight_toRightOf="@+id/time01"
                                app:layout_constraintTop_toBottomOf="@+id/time01" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Week"
                                android:textColor="@color/color_999999"
                                android:textSize="@dimen/sp_14"
                                app:layout_constraintLeft_toLeftOf="@+id/time02"
                                app:layout_constraintRight_toRightOf="@+id/time02"
                                app:layout_constraintTop_toBottomOf="@+id/time02" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.cardview.widget.CardView>

                    <!--                    Average call duration & Connect rate-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_131"
                        android:layout_marginTop="@dimen/dp_10"
                        android:baselineAligned="false"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/TV05"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:elevation="@dimen/dp_10"
                                    android:text="Average call duration"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="@dimen/sp_16" />

                                <TextView
                                    android:id="@+id/time03"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_16"
                                    android:elevation="@dimen/dp_10"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_16"
                                    tools:text="01:30:05" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:elevation="@dimen/dp_10"
                                    android:text="Week"
                                    android:textColor="@color/color_999999"
                                    android:textSize="@dimen/sp_14" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_131"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/TV06"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Connect rate"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="@dimen/sp_16" />

                                <TextView
                                    android:id="@+id/time04"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_16"
                                    android:elevation="@dimen/dp_10"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_16"
                                    tools:text="Today：100%" />

                                <TextView
                                    android:id="@+id/time05"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:elevation="@dimen/dp_10"
                                    android:text="Week：100%"
                                    android:textColor="@color/color_999999"
                                    android:textSize="@dimen/sp_16" />

                                <ImageView
                                    android:id="@+id/connectRateQuery"
                                    android:layout_width="@dimen/dp_20"
                                    android:layout_height="@dimen/dp_20"
                                    android:layout_marginTop="@dimen/dp_6"
                                    android:layout_marginEnd="@dimen/dp_6"
                                    android:elevation="@dimen/dp_10"
                                    android:src="@mipmap/workbenc_home_fragment_bg_06"
                                    android:visibility="gone"
                                    app:bind_view_onClick="@{ () -> activity.connectRateQuery() }"
                                    app:layout_constraintRight_toRightOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />


                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <!--                    Match Call Completion Rate & Video Call Completion Rate-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:baselineAligned="false"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:paddingVertical="@dimen/dp_10">

                                <TextView
                                    android:id="@+id/tv_match_call_completion_rate_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:padding="@dimen/dp_10"
                                    android:text="@string/match_call_completion_rate"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="@dimen/sp_14"
                                    app:drawableEndCompat="@mipmap/workbenc_home_fragment_bg_06" />

                                <TextView
                                    android:id="@+id/tv_match_call_completion_rate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_16"
                                    tools:text="Today: 40%" />

                                <TextView
                                    android:id="@+id/tv_match_call_completion_rate_total"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:elevation="@dimen/dp_10"
                                    android:text="Total: 39%"
                                    android:textColor="@color/color_999999"
                                    android:textSize="@dimen/sp_14" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:paddingVertical="@dimen/dp_10">

                                <TextView
                                    android:id="@+id/tv_video_call_completion_rate_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:padding="@dimen/dp_10"
                                    android:text="@string/video_call_completion_rate"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="@dimen/sp_14"
                                    app:drawableEndCompat="@mipmap/workbenc_home_fragment_bg_06" />

                                <TextView
                                    android:id="@+id/tv_video_call_completion_rate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:textColor="@color/colorWhite"
                                    android:textSize="@dimen/sp_16"
                                    tools:text="@string/today_rate" />

                                <TextView
                                    android:id="@+id/tv_video_call_completion_rate_total"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:elevation="@dimen/dp_10"
                                    android:text="@string/total_rate"
                                    android:textColor="@color/color_999999"
                                    android:textSize="@dimen/sp_16" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <!--                    Notification & Level-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_106"
                        android:layout_marginTop="@dimen/dp_10"
                        android:baselineAligned="false"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10"
                            tools:layout_editor_absoluteX="4dp"
                            tools:layout_editor_absoluteY="3dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:bind_view_onClick="@{ () -> activity.notification() }">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/img01"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">

                                    <ImageView
                                        android:id="@+id/notification"
                                        android:layout_width="@dimen/dp_44"
                                        android:layout_height="@dimen/dp_44"
                                        android:src="@mipmap/workbenc_home_fragment_bg_02"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />

                                    <TextView
                                        android:id="@+id/badge"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginStart="@dimen/dp_40"
                                        android:background="@drawable/base_shape_oval_primary"
                                        android:gravity="center"
                                        android:includeFontPadding="false"
                                        android:minWidth="@dimen/dp_12"
                                        android:paddingHorizontal="@dimen/dp_3"
                                        android:textColor="@color/colorWhite"
                                        android:textSize="@dimen/sp_10"
                                        android:visibility="gone"
                                        app:layout_constraintLeft_toLeftOf="@+id/notification"
                                        app:layout_constraintRight_toRightOf="@+id/notification"
                                        app:layout_constraintTop_toTopOf="@+id/notification"
                                        tools:text="1" />
                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:text="Notify"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="14sp" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10"
                            tools:layout_editor_absoluteX="4dp"
                            tools:layout_editor_absoluteY="3dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:bind_view_onClick="@{ () -> activity.dumpWeb(0) }"
                                app:layout_constraintLeft_toRightOf="@+id/CL05"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/CL03">

                                <ImageView
                                    android:id="@+id/img02"
                                    android:layout_width="@dimen/dp_44"
                                    android:layout_height="@dimen/dp_44"
                                    android:src="@mipmap/workbenc_home_fragment_bg_03" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:text="Level"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="14sp" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <!--                    Working standard & Beginner tutorial-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_106"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10"
                            tools:visibility="visible">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:bind_view_onClick="@{ () -> activity.dumpWeb(1) }">

                                <ImageView
                                    android:layout_width="@dimen/dp_44"
                                    android:layout_height="@dimen/dp_44"
                                    android:src="@mipmap/workbenc_home_fragment_bg_04" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:text="Working standard"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="14sp" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_106"
                            android:layout_margin="@dimen/dp_3"
                            android:layout_weight="1"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10"
                            tools:visibility="visible">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:bind_view_onClick="@{ () -> activity.dumpWeb(2) }">

                                <ImageView
                                    android:layout_width="@dimen/dp_44"
                                    android:layout_height="@dimen/dp_44"
                                    android:src="@mipmap/workbenc_home_fragment_bg_05" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_5"
                                    android:text="Beginner Tutorial"
                                    android:textColor="@color/color_C8C8C8"
                                    android:textSize="14sp" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <!--                    Contact us-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_74"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="@dimen/dp_3"
                            android:visibility="visible"
                            app:cardBackgroundColor="@color/color_cardBackground"
                            app:cardCornerRadius="@dimen/dp_10" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:elevation="@dimen/dp_10"
                            android:orientation="horizontal"
                            android:visibility="visible"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/TV07"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/dp_16"
                                android:text="If you have any questions, please "
                                android:textColor="@color/color_C8C8C8"
                                android:textSize="@dimen/sp_14" />

                            <TextView
                                android:id="@+id/TV08"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingTop="@dimen/dp_16"
                                android:paddingBottom="@dimen/dp_16"
                                android:text="contact us"
                                android:textColor="#02a7f0"
                                android:textSize="@dimen/sp_14"
                                app:bind_view_onClick="@{ () -> activity.feedback() }" />

                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </RelativeLayout>

</layout>