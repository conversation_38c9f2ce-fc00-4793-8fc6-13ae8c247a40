<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.login.register.ModifyInfoActivity" />
    </data>



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorWhite"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:textColor="@color/public_text_color"
                android:textSize="@dimen/sp_18" />
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
        <TextView
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/Please_fill_in_your_information"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_20"
            />

        <TextView
            android:visibility="gone"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_11"
            android:layout_width="@dimen/dp_254"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="@string/Real_information_can_enchance_ones_charm_and_match"
            android:textColor="#ff999999"
            android:textSize="@dimen/sp_14"
            />


        <ImageView
            android:scaleType="centerCrop"
            android:id="@+id/imageViewHead"
            android:layout_width="@dimen/dp_90"
            android:layout_height="@dimen/dp_90"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:src="@mipmap/user_ic_head_boy"
            app:bind_view_onClick="@{ () -> activity.showSelectAlert() }"
            />

        <ImageView
            android:visibility="gone"
            android:id="@+id/imageViewHeadBG"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />

        <EditText
            android:layout_marginTop="@dimen/dp_12"
            android:paddingLeft="@dimen/dp_22"
            android:layout_marginEnd="@dimen/dp_27"
            android:layout_marginStart="@dimen/dp_27"
            android:id="@+id/editTextName"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_gravity="center_vertical"
            android:background="@drawable/login_register_bg_01"
            android:hint="@string/Username"
            android:maxLength="20"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:layout_marginTop="@dimen/dp_14"
            android:paddingLeft="@dimen/dp_22"
            android:layout_marginEnd="@dimen/dp_27"
            android:layout_marginStart="@dimen/dp_27"
            android:id="@+id/editPhone"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_gravity="center_vertical"
            android:background="@drawable/login_register_bg_01"
            android:hint="@string/b66"
            android:maxLength="11"
            android:inputType="number"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="@dimen/sp_14" />

        <EditText
            android:layout_marginTop="@dimen/dp_14"
            android:paddingLeft="@dimen/dp_22"
            android:layout_marginEnd="@dimen/dp_27"
            android:layout_marginStart="@dimen/dp_27"
            android:id="@+id/editEmail"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_gravity="center_vertical"
            android:background="@drawable/login_register_bg_01"
            android:hint="@string/b67"
            android:maxLength="30"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="@dimen/sp_14" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_12"
            android:orientation="horizontal">

            <TextView
                app:bind_view_onClick="@{ (v) -> activity.checkbox(v,0) }"
                android:id="@+id/checkbox01"
                android:layout_width="@dimen/dp_152"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp_52"
                android:background="@drawable/login_register_state_selected_01"
                android:layout_marginEnd="@dimen/dp_17"
                android:textColor="#ff666666"
                android:textSize="18sp"
                android:text="@string/Male"
                android:paddingLeft="@dimen/dp_72"
                android:paddingTop="@dimen/dp_14"
                />

            <TextView
                app:bind_view_onClick="@{ (v) -> activity.checkbox(v,1) }"
                android:id="@+id/checkbox02"
                android:layout_width="@dimen/dp_152"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp_52"
                android:background="@drawable/login_register_state_selected_02"
                android:textColor="#ff666666"
                android:textSize="18sp"
                android:text="@string/Female"
                android:paddingLeft="@dimen/dp_72"
                android:paddingTop="@dimen/dp_14"
                />


        </LinearLayout>

        <TextView
            android:layout_marginTop="@dimen/dp_27"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/Gender_cannot_be_modified_after_selection"
            android:textColor="#ff999999"
            android:textSize="@dimen/sp_14"
            android:layout_gravity="center_horizontal"
            />
        <TextView
            android:visibility="gone"
            android:id="@+id/title01"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_27"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Upload cover image（0/1）"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_18"
            />
        <androidx.recyclerview.widget.RecyclerView
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_10"
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_120"
            android:layout_marginStart="@dimen/dp_27"
            android:layout_marginEnd="@dimen/dp_27"
            android:overScrollMode="never"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />
        <TextView
            android:visibility="gone"
            android:id="@+id/title02"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_27"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Upload live video（0/1）"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_18"
            />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:visibility="gone"
                    android:layout_marginStart="@dimen/dp_27"
                    android:layout_marginEnd="@dimen/dp_27"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    >


                    <anchor.app.base.view.RoundImageView
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:corner_radius="6dp"
                        android:scaleType="centerCrop"
                        app:bind_view_onClick="@{ () -> activity.uploadVideo() }"
                        android:layout_marginTop="@dimen/dp_10"
                        android:id="@+id/imgVideo"
                        android:layout_width="@dimen/dp_109"
                        android:layout_height="@dimen/dp_109"
                        android:background="@mipmap/base_publish_add"
                        android:layout_margin="4dp"

                        />

                    <ImageView
                        android:visibility="gone"
                        app:bind_view_onClick="@{ () -> activity.ivDelete() }"
                        android:layout_toRightOf="@+id/imgVideo"
                        android:id="@+id/ivDelete"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/base_publish_delete"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:visibility="gone"
                    android:layout_marginTop="@dimen/dp_13"
                    android:layout_marginEnd="@dimen/dp_27"
                    android:layout_marginStart="@dimen/dp_27"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">



                    <TextView
                        android:drawablePadding="@dimen/sp_6"
                        android:drawableLeft="@mipmap/login_activity_modify_info_bg_01"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="The best time is between 5s and 15s！"
                        android:textColor="#ff999999"
                        android:textSize="@dimen/sp_14"
                        />



                </LinearLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <Button
                    android:layout_marginTop="@dimen/dp_25"
                    android:id="@+id/btnSave"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="@dimen/dp_45"
                    android:layout_marginEnd="@dimen/dp_45"
                    android:layout_marginBottom="@dimen/dp_36"
                    android:background="@drawable/login_register_state_selected_03"
                    android:text="@string/Done"
                    android:textAllCaps="false"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/sp_14"
                    app:bind_view_onClick="@{ () -> activity.save() }" />
                <!--            app:bind_view_clickable="@={activity.viewModel.saveClickable}"-->
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>


    </LinearLayout>

</layout>