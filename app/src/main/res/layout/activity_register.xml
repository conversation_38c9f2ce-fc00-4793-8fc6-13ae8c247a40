<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.login.login.LoginViewModel" />

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.login.register.RegisterActivity" />

    </data>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#FF101321">

        <LinearLayout
            android:id="@+id/email_login_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_20"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <FrameLayout
                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="@dimen/dp_12"
                    android:paddingRight="@dimen/dp_12"
                    app:bind_view_onClick="@{ (v) -> activity.finish() }">

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_gravity="center"
                        android:src="@mipmap/base_ic_back" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:singleLine="true"
                    android:text="@string/b100"
                    android:textColor="#FFD8D7DC"
                    android:textSize="@dimen/sp_18"
                    android:textStyle="bold" />
            </RelativeLayout>


            <TextView
                android:id="@+id/tg01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/register"
                android:textColor="@color/colorWhite"
                android:textSize="24sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/LL01"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tg01">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_24">

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/ic_login_phone" />

                    <EditText
                        android:id="@+id/account"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:gravity="left|center_vertical"
                        android:hint="@string/b111"
                        android:imeOptions="actionNext"
                        android:inputType="textEmailAddress"
                        android:maxLength="30"
                        android:paddingStart="@dimen/dp_26"
                        android:singleLine="true"
                        android:textColor="#999999"
                        android:textColorHint="@color/login_text_tips_color"
                        android:textSize="@dimen/sp_14" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical"
                        android:visibility="gone">

                        <!--                    <ImageView-->
                        <!--                        android:id="@+id/close01"-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:padding="@dimen/dp_6"-->
                        <!--                        android:src="@mipmap/base_ic_close_04"-->
                        <!--                        android:visibility="gone"-->
                        <!--                        app:bind_view_onClick="@{ (v) -> activity.close(1) }"-->
                        <!--                        tools:visibility="visible" />-->
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_alignParentBottom="true"
                        android:background="#FF1D2031" />


                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_24">

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/ic_register_verification_code" />

                    <EditText
                        android:id="@+id/verCode"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:gravity="left|center_vertical"
                        android:hint="@string/b104"
                        android:imeOptions="actionNext"
                        android:inputType="textVisiblePassword"
                        android:maxLength="20"
                        android:paddingStart="@dimen/dp_26"
                        android:singleLine="true"
                        android:textColor="#999999"
                        android:textColorHint="@color/login_text_tips_color"
                        android:textSize="@dimen/sp_14" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/tvGetCheckCode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:bind_view_onClick="@{()->activity.onGetCheckCode()}"
                            android:text="@string/login_send_captcha"
                            android:textColor="#ff9ea3d8"
                            android:textSize="@dimen/sp_14" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_alignParentBottom="true"
                        android:background="#FF1D2031" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_24">

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/ic_login_pass" />

                    <EditText
                        android:id="@+id/password"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:gravity="left|center_vertical"
                        android:hint="@string/b103"
                        android:imeOptions="actionNext"
                        android:inputType="textPassword"
                        android:maxLength="20"
                        android:paddingStart="@dimen/dp_26"
                        android:singleLine="true"
                        android:textColor="#999999"
                        android:textColorHint="@color/login_text_tips_color"
                        android:textSize="@dimen/sp_14" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical">

                        <!--                    <ImageView-->
                        <!--                        android:id="@+id/close03"-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:padding="@dimen/dp_6"-->

                        <!--                        android:src="@mipmap/base_ic_close_04"-->
                        <!--                        android:visibility="gone"-->
                        <!--                        app:bind_view_onClick="@{ (v) -> activity.close(3) }"-->
                        <!--                        tools:visibility="visible" />-->

                        <ImageView
                            android:id="@+id/show2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp_6"
                            android:src="@mipmap/login_activity_signup_bg_04"
                            android:visibility="gone"
                            app:bind_view_onClick="@{ (v) -> activity.show(2) }"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_alignParentBottom="true"
                        android:background="#FF1D2031" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_24">
                    <!--                android:inputType="textPassword"-->
                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/ic_register_invitation_code" />

                    <EditText
                        android:id="@+id/code"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:gravity="left|center_vertical"
                        android:hint="@string/b105"
                        android:imeOptions="actionNext"
                        android:maxLength="6"
                        android:paddingStart="@dimen/dp_26"
                        android:singleLine="true"
                        android:textColor="#999999"
                        android:textColorHint="@color/login_text_tips_color"
                        android:textSize="@dimen/sp_14" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/dp_28"
                        android:gravity="center_vertical">

                        <!--                    <ImageView-->
                        <!--                        android:id="@+id/close04"-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:padding="@dimen/dp_6"-->

                        <!--                        android:src="@mipmap/base_ic_close_04"-->
                        <!--                        android:visibility="gone"-->
                        <!--                        app:bind_view_onClick="@{ (v) -> activity.close(4) }"-->
                        <!--                        tools:visibility="visible" />-->
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_alignParentBottom="true"
                        android:background="#FF1D2031" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/submit"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_33"
                    android:layout_marginTop="@dimen/dp_32"
                    android:layout_marginEnd="@dimen/dp_33"
                    android:background="@drawable/bg_button_them"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    android:text="@string/register"
                    android:textColor="#FFFFFF"
                    android:textSize="@dimen/sp_14"
                    app:bind_view_onClick="@{()->activity.submit()}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent" />

            </LinearLayout>


            <TextView
                android:id="@+id/nation"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_52"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@drawable/login_bg_01"
                android:gravity="left|center_vertical"
                android:hint="@string/b101"
                android:imeOptions="actionNext"
                android:maxLength="20"
                android:paddingLeft="@dimen/dp_12"
                android:singleLine="true"
                android:textColor="#999999"
                android:textColorHint="@color/login_text_tips_color"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:bind_view_onClick="@{ (v) -> activity.select() }"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/select"
                android:layout_width="@dimen/dp_38"
                android:layout_height="@dimen/dp_38"
                android:padding="@dimen/dp_10"
                android:src="@mipmap/login_activity_signup_bg_01"
                android:visibility="gone"
                app:bind_view_onClick="@{ (v) -> activity.select() }"
                app:layout_constraintBottom_toBottomOf="@+id/nation"
                app:layout_constraintRight_toRightOf="@+id/nation"
                app:layout_constraintTop_toTopOf="@+id/nation" />


            <androidx.cardview.widget.CardView
                android:id="@+id/cd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_11"
                android:layout_marginEnd="@dimen/dp_11"
                android:visibility="gone"
                app:cardElevation="6dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/nation">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"

                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(1) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_05" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Brasil(Brazil)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"

                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(2) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_01" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="@string/a029"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(3) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_06" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Filipinas(Philippines)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(4) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_09" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="المغرب(Morocco)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(5) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_14" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Hindi(हिंदी)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(6) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_16" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Colombia(Colombia)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />
                        <!--                    哥伦比亚-->

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(7) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_17" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Peru(Perú)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(8) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/base_ic_country_18" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Venezuela(Venezuela)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />
                        <!--                    委内瑞拉-->
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_10"
                        app:bind_view_onClick="@{ (v) -> activity.country(9) }">

                        <ImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_16"
                            android:src="@mipmap/ic_launcher" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_12"
                            android:text="Other(Other Country)"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_16" />
                    </LinearLayout>


                </LinearLayout>


            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_32"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llBottomTop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center">

                    <View
                        android:layout_width="@dimen/dp_60"
                        android:layout_height="@dimen/dp_1"
                        android:background="#FF5A5D7B" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_8"
                        android:text="@string/register_other"
                        android:textColor="#ff5a5d7b"
                        android:textSize="@dimen/sp_12" />

                    <View
                        android:layout_width="@dimen/dp_60"
                        android:layout_height="@dimen/dp_1"
                        android:background="#FF5A5D7B" />
                </LinearLayout>

                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/llGoogleLogin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_32"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_32"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:background="@drawable/shape_translucent_white_button"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:src="@mipmap/ic_login_google" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/google_login"
                        android:textColor="@color/colorWhite"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>


                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/llFacebookLogin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_32"
                    android:layout_marginEnd="@dimen/dp_32"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:background="@drawable/shape_translucent_white_button"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:layout_marginEnd="@dimen/dp_10"
                        android:src="@mipmap/ic_login_facebook" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/facebook_login"
                        android:textColor="@color/colorWhite"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_login_agreement_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp_50"
                    android:layout_marginBottom="@dimen/dp_20"
                    android:gravity="center_vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent">

                    <ImageView
                        android:id="@+id/checkbox"
                        android:layout_width="@dimen/dp_26"
                        android:layout_height="@dimen/dp_26"
                        android:padding="@dimen/dp_6"
                        android:src="@drawable/selector_checkbox"
                        app:bind_view_onClick="@{ (v) -> activity.checkbox(v) }" />

                    <TextView
                        android:id="@+id/tvCheckboxText1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/a003"
                        android:textColor="#999999"
                        android:textSize="@dimen/sp_13" />

                    <TextView
                        android:id="@+id/tv_login_user_agreement_id"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="@string/User_agreement"
                        android:textColor="@color/colorAccent"
                        android:textSize="@dimen/sp_13"
                        app:bind_view_onClick="@{ () -> activity.treaty(1) }" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/a005"
                        android:textColor="#999999"
                        android:textSize="@dimen/sp_13" />

                    <TextView
                        android:id="@+id/tv_login_private_private_id"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="@string/Privacy_policy"
                        android:textColor="@color/colorAccent"
                        android:textSize="@dimen/sp_13"
                        app:bind_view_onClick="@{ () -> activity.treaty(2) }" />
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>
    </ScrollView>
</layout>