<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120"
        android:layout_margin="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_10">

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="@dimen/dp_100"
            android:layout_height="@dimen/dp_100"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/base_publish_add" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@mipmap/base_publish_delete"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="@id/iv_image"
            app:layout_constraintTop_toTopOf="@id/iv_image" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>