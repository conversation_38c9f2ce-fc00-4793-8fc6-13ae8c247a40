<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_30"
        android:background="@drawable/user_shape_white_top16">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/cancel"
            android:textColor="@color/color_BFBFBF"
            android:textSize="@dimen/sp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/my_person_data"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_55"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:text="@string/save"
            android:textColor="@color/color_F84139"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_cancel" />

        <RelativeLayout
            android:id="@+id/avatar_container"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_cancel">

            <ImageView
                android:id="@+id/iv_header"
                android:layout_width="@dimen/dp_80"
                android:layout_height="@dimen/dp_80"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <ImageView
                android:id="@+id/iv_capture"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_centerInParent="true"
                android:scaleType="centerCrop"
                android:src="@mipmap/user_ic_camera" />

            <anchor.app.base.view.RoundImageView
                android:id="@+id/iv_review"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal"
                android:scaleType="centerCrop"
                android:src="#50000000"
                android:visibility="gone"
                app:is_circle="true"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_review"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_6"
                android:text="@string/in_review"
                android:textColor="#ffffb909"
                android:textSize="@dimen/sp_17"
                android:visibility="gone"
                tools:visibility="visible" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_id_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:gravity="center_vertical"
            android:text="ID"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avatar_container" />

        <TextView
            android:id="@+id/tv_id"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_52"
            android:background="@null"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_id_text"
            app:layout_constraintStart_toEndOf="@id/tv_id_text"
            app:layout_constraintTop_toTopOf="@id/tv_id_text"
            tools:text="ID" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_id_text" />

        <TextView
            android:id="@+id/tv_name_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/nickname"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_id_text" />

        <EditText
            android:id="@+id/et_nickname"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@null"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_name_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_name_text"
            app:layout_constraintTop_toTopOf="@id/tv_name_text"
            tools:text="苏沐橙" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_name_text" />

        <TextView
            android:id="@+id/tv_age_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/age"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name_text" />

        <TextView
            android:id="@+id/tv_age"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_age_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_age_text"
            app:layout_constraintTop_toTopOf="@id/tv_age_text"
            tools:text="18" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_16"
            android:src="@mipmap/user_ic_arrow_right_gray"
            app:layout_constraintBottom_toBottomOf="@id/tv_age_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_age_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_age_text" />

        <TextView
            android:id="@+id/tv_language_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/language"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_age_text" />

        <TextView
            android:id="@+id/tv_language"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/tv_language_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_language_text"
            app:layout_constraintTop_toTopOf="@id/tv_language_text"
            tools:text="汉语、英语" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_16"
            android:src="@mipmap/user_ic_arrow_right_gray"
            app:layout_constraintBottom_toBottomOf="@id/tv_language_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_language_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/tv_language_text" />

        <TextView
            android:id="@+id/tv_country_text"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/country"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_language" />

        <TextView
            android:id="@+id/tv_country"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_52"
            android:gravity="center_vertical"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_country_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_country_text"
            app:layout_constraintTop_toTopOf="@id/tv_country_text"
            tools:text="中国" />

        <ImageView
            android:layout_width="@dimen/dp_7"
            android:layout_height="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_16"
            android:src="@mipmap/user_ic_arrow_right_gray"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_country_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_country_text" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="@color/color_EEEEEE"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_country_text" />

        <TextView
            android:id="@+id/tv_signature"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_16"
            android:gravity="center_vertical"
            android:text="@string/signature"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_language" />

        <EditText
            android:id="@+id/et_signature"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_5"
            android:background="@drawable/base_shape_round_f5f5f5"
            android:gravity="top"
            android:minHeight="@dimen/dp_100"
            android:padding="@dimen/dp_10"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            android:hint="@string/please_enter_signature"
            android:textColorHint="@color/color_666666"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_signature"/>

        <LinearLayout
            android:id="@+id/calendar_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>