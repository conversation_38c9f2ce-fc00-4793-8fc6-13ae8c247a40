<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_76"
        android:layout_marginEnd="@dimen/dp_20"
        android:background="@drawable/base_shape_round_background"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_21"
            android:text="个人隐私保护指引"
            android:textColor="#2B2D2E"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="@dimen/dp_225"
            android:layout_height="@dimen/dp_217"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_30"
            android:lineSpacingExtra="@dimen/dp_5"
            android:textColor="#2B2D2E"
            android:textSize="@dimen/sp_13" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30"
            android:gravity="center"
            android:paddingTop="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_15"
            android:text="我知道了"
            android:textColor="#FFD100"
            android:textSize="16sp" />
    </LinearLayout>
</layout>