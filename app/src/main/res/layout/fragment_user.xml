<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        tools:ignore="MissingDefaultResource">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@mipmap/bg_mine" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="?actionBarSize"
            android:gravity="center"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/sp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_20"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_person"
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:id="@+id/avatar_container"
                        android:layout_width="@dimen/dp_70"
                        android:layout_height="@dimen/dp_70"
                        android:layout_marginStart="@dimen/dp_16"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_avatar"
                            android:layout_width="match_parent"
                            android:src="@mipmap/user_ic_default"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop" />

                        <anchor.app.base.view.RoundImageView
                            android:id="@+id/iv_review"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal"
                            android:scaleType="centerCrop"
                            android:visibility="gone"
                            app:is_circle="true"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tv_review"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="@dimen/dp_6"
                            android:text="@string/in_review"
                            android:textColor="#ffffb909"
                            android:textSize="@dimen/sp_10"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tv_online_status"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_28"
                            android:layout_alignParentBottom="true"
                            android:background="@mipmap/user_ic_online_status_half_circle"
                            android:gravity="center"
                            android:paddingBottom="@dimen/dp_3"
                            android:textColor="#21C76E"
                            android:textSize="@dimen/sp_12" />

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_15"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#FFFFFF"
                        android:textSize="@dimen/sp_18"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@+id/avatar_container"
                        app:layout_constraintTop_toTopOf="@id/avatar_container"
                        app:layout_constraintWidth_default="wrap"
                        tools:text="张三111" />

                    <TextView
                        android:id="@+id/tv_age"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/user_shape_age_label"
                        android:paddingHorizontal="@dimen/dp_8"
                        android:paddingVertical="@dimen/dp_2"
                        android:text="25"
                        android:textColor="#7E6B95"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintStart_toStartOf="@id/tv_name"
                        app:layout_constraintTop_toBottomOf="@id/tv_name" />

                    <TextView
                        android:id="@+id/tv_country"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_4"
                        android:background="@drawable/user_shape_age_label"
                        android:paddingHorizontal="@dimen/dp_8"
                        android:paddingVertical="@dimen/dp_2"
                        android:text="Indonesia"
                        android:textColor="#7E6B95"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/tv_age"
                        app:layout_constraintStart_toEndOf="@id/tv_age"
                        app:layout_constraintTop_toTopOf="@id/tv_age" />

                    <TextView
                        android:id="@+id/tv_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@mipmap/user_ic_copy"
                        android:drawablePadding="@dimen/dp_3"
                        android:text="ID：22222"
                        android:textColor="#7E6B95"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintStart_toStartOf="@id/tv_age"
                        app:layout_constraintTop_toBottomOf="@id/tv_age" />

                    <LinearLayout
                        android:id="@+id/llBackIcon"
                        android:layout_width="@dimen/dp_48"
                        android:layout_height="@dimen/dp_45"
                        android:layout_gravity="center"
                        android:gravity="center"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/ivBackIcon"
                            android:layout_width="@dimen/dp_10"
                            android:layout_height="@dimen/dp_13"
                            android:src="@mipmap/user_ic_right_white" />
                    </LinearLayout>

                    <com.donkingliang.labels.LabelsView
                        android:id="@+id/labels"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_2"
                        android:saveEnabled="false"
                        app:labelBackground="@drawable/user_shape_1b1d39_50radius"
                        app:labelTextColor="@color/color_8F94C7"
                        app:labelTextPaddingBottom="@dimen/dp_3"
                        app:labelTextPaddingLeft="@dimen/dp_8"
                        app:labelTextPaddingRight="@dimen/dp_8"
                        app:labelTextPaddingTop="@dimen/dp_3"
                        app:labelTextSize="@dimen/sp_12"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/tv_id"
                        app:layout_constraintTop_toBottomOf="@id/tv_id"
                        app:layout_scrollFlags="scroll"
                        app:lineMargin="@dimen/dp_6"
                        app:maxSelect="0"
                        app:selectType="NONE"
                        app:wordMargin="@dimen/dp_6" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/ll_coin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_40"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintHorizontal_chainStyle="spread"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/cl_person">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/ll_menu_album"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_80"
                        android:layout_marginEnd="@dimen/dp_5"
                        android:layout_weight="1"
                        android:background="@drawable/user_shape_201831"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="@dimen/dp_58"
                            android:layout_height="match_parent"
                            android:background="@mipmap/user_album_bg"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_album"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:layout_marginStart="@dimen/dp_10"
                            android:src="@mipmap/user_ic_mine_album"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_album"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:layout_marginTop="@dimen/dp_6"
                            android:text="@string/mine_menu_album"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_16"
                            app:layout_constraintStart_toEndOf="@id/iv_album"
                            app:layout_constraintTop_toTopOf="@id/iv_album" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_5"
                            android:text="@string/album_explain"
                            android:textColor="@color/white_40"
                            android:textSize="@dimen/sp_12"
                            app:layout_constraintStart_toStartOf="@id/tv_album"
                            app:layout_constraintTop_toBottomOf="@id/tv_album" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/ll_menu_level"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_80"
                        android:layout_marginStart="@dimen/dp_5"
                        android:layout_weight="1"
                        android:background="@drawable/user_shape_201831"
                        android:gravity="center"
                        android:orientation="vertical">


                        <ImageView
                            android:layout_width="@dimen/dp_58"
                            android:layout_height="match_parent"
                            android:background="@mipmap/user_album_bg"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_level"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:layout_marginStart="@dimen/dp_10"
                            android:src="@mipmap/user_ic_mine_level"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_level"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:layout_marginTop="@dimen/dp_6"
                            android:text="@string/mine_menu_level"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_16"
                            app:layout_constraintStart_toEndOf="@id/iv_level"
                            app:layout_constraintTop_toTopOf="@id/iv_level" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_5"
                            android:text="@string/level_explain"
                            android:textColor="@color/white_40"
                            android:textSize="@dimen/sp_12"
                            app:layout_constraintStart_toStartOf="@id/tv_level"
                            app:layout_constraintTop_toBottomOf="@id/tv_level" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@drawable/user_shape_201831"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_coin">

                    <anchor.app.base.view.CustomMineTextView
                        android:id="@+id/ll_wallet"
                        style="@style/mine"
                        android:gravity="center_vertical"
                        app:ctv_left="@string/mine_menu_wallet"
                        app:ctv_left_icon="@mipmap/user_ic_mine_wallet" />

                    <anchor.app.base.view.CustomMineTextView
                        android:id="@+id/ll_tasks"
                        style="@style/mine"
                        android:gravity="center_vertical"
                        app:ctv_left="@string/mine_menu_tasks"
                        app:ctv_left_icon="@mipmap/user_ic_mine_tasks" />

                    <anchor.app.base.view.CustomMineTextView
                        android:id="@+id/ll_share_friend"
                        style="@style/mine"
                        android:gravity="center_vertical"
                        app:ctv_left="@string/mine_menu_share"
                        android:visibility="gone"
                        app:ctv_left_icon="@mipmap/user_ic_mine_share" />

                    <anchor.app.base.view.CustomMineTextView
                        android:id="@+id/ll_setting"
                        style="@style/mine"
                        android:gravity="center_vertical"
                        app:ctv_left="@string/Settings"
                        app:ctv_left_icon="@mipmap/user_ic_mine_setting" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>