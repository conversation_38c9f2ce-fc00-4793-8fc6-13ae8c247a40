<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.login.login.LoginViewModel" />

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.login.login.LoginWithPasswordActivity" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/email_login_form"
        android:background="#FF101321"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rlTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_80"
            android:orientation="horizontal"
            android:paddingTop="@dimen/dp_20"
            app:layout_constraintTop_toTopOf="parent">
            <!--            user_ic_wallte_bg-->
            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_close_white" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text=""
                android:textColor="#FFFFFF"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/RLeditAcount"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rlTitle">

            <ImageView
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_centerVertical="true"
                android:src="@mipmap/ic_login_phone" />

            <EditText
                android:id="@+id/editAcount"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_52"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="@null"
                android:gravity="left|center_vertical"
                android:hint="@string/b102"
                android:inputType="textEmailAddress"
                android:imeOptions="actionNext"
                android:maxLength="30"
                android:paddingStart="@dimen/dp_26"
                android:singleLine="true"
                android:textColor="#999999"
                android:textColorHint="@color/login_text_tips_color"
                android:textSize="@dimen/sp_14" />

            <!--            <ImageView-->
            <!--                android:id="@+id/close01"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_alignParentRight="true"-->
            <!--                android:layout_centerVertical="true"-->
            <!--                android:padding="@dimen/dp_6"-->
            <!--                android:src="@mipmap/base_ic_close_04"-->
            <!--                android:visibility="gone"-->
            <!--                app:bind_view_onClick="@{ (v) -> activity.close(1) }"-->
            <!--                tools:visibility="visible" />-->

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_alignParentBottom="true"
                android:background="#FF1D2031" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rlPass"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/RLeditAcount">

            <ImageView
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_centerVertical="true"
                android:src="@mipmap/ic_login_pass" />

            <EditText
                android:id="@+id/editPassword"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_52"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="@null"
                android:gravity="left|center_vertical"
                android:hint="@string/b103"
                android:imeOptions="actionNext"
                android:inputType="textPassword"
                android:maxLength="12"
                android:paddingStart="@dimen/dp_26"
                android:singleLine="true"
                android:textColor="#999999"
                android:textColorHint="@color/login_text_tips_color"
                android:textSize="@dimen/sp_14" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical">

                <!--                <ImageView-->
                <!--                    android:id="@+id/close02"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:padding="@dimen/dp_6"-->
                <!--                    android:src="@mipmap/base_ic_close_04"-->
                <!--                    android:visibility="gone"-->
                <!--                    app:bind_view_onClick="@{ (v) -> activity.close(2) }"-->
                <!--                    tools:visibility="visible" />-->

                <ImageView
                    android:id="@+id/show1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp_6"
                    android:src="@mipmap/login_activity_signup_bg_04"
                    android:visibility="gone"
                    app:bind_view_onClick="@{ (v) -> activity.show(1) }"
                    tools:visibility="visible" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_alignParentBottom="true"
                android:background="#FF1D2031" />
        </RelativeLayout>

        <TextView
            android:id="@+id/login"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_33"
            android:layout_marginEnd="@dimen/dp_33"
            android:background="@drawable/bg_button_them"
            android:gravity="center"
            android:stateListAnimator="@null"
            android:text="@string/b74"
            android:layout_marginTop="@dimen/dp_44"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/sp_18"
            app:bind_view_onClick="@{()->activity.login()}"
            app:layout_constraintTop_toBottomOf="@+id/rlPass" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>