<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="@dimen/dp_16">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/enter_your_email"
            android:textColor="#9FA1A6"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_email"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_54"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_edit_info"
            android:enabled="false"
            android:gravity="center_vertical"
            android:hint="@string/please_enter_email"
            android:paddingHorizontal="@dimen/dp_24"
            android:textColor="#4B4D5B"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_28"
            android:text="@string/enter_your_verification_code"
            android:textColor="#9FA1A6"
            android:textSize="@dimen/sp_14" />

        <anchor.app.base.view.CaptchaView
            android:id="@+id/captchaView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_13"
            app:captcha_box_background="@color/color_cardBackground"
            app:captcha_inputType="number"
            app:captcha_number="6"
            app:captcha_spacing="@dimen/dp_10"
            app:captcha_text_color="@color/colorAccent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btn_next"
            style="@style/PrimaryButton"
            android:layout_marginTop="@dimen/dp_32"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="@string/login_next" />

        <TextView
            android:id="@+id/tv_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="00:00"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="@string/resend_email_code_hint"
            android:textColor="#9FA1A6"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>
</layout>