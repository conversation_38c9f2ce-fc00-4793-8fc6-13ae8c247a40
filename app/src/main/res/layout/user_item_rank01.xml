<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="userinfo"
            type="com.mobile.anchor.app.module.user.bean.RankBean.RankListDTO" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:paddingRight="@dimen/dp_16"
        android:paddingLeft="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_width="@dimen/dp_22"
            android:layout_height="@dimen/dp_22"
            android:id="@+id/nu"
            android:background="@drawable/f5f5f5_round_orange_bg"
            />

        <ImageView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintLeft_toRightOf="@+id/nu"
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,userinfo)}"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/head"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:src="@mipmap/user_ic_default"
            />

        <LinearLayout
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="@+id/head"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">


            <TextView
                android:id="@+id/consumptionType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Top-Up"
                android:textStyle="bold"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                />

            <TextView
                android:id="@+id/id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID:78855888"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                />

        </LinearLayout>

        <LinearLayout
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/head"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:id="@+id/gift"
                tools:background="@color/colorBlack"
                />

            <TextView
                android:id="@+id/count"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="x99"
                android:textColor="#fffc6399"
                android:textSize="@dimen/sp_14"
                />

        </LinearLayout>





    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>