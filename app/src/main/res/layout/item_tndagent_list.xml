<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="workbenchinfo"
            type="com.mobile.anchor.app.module.workbench.bean.TndAgentListBean" />
<!--            type="mikchat.app.workbench.bean.MemberListBean" />-->

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>

        <androidx.constraintlayout.widget.ConstraintLayout
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,workbenchinfo)}"

            android:elevation="@dimen/dp_10"
            android:layout_marginTop="@dimen/dp_10"
            android:id="@+id/ProxyCL01"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_141"
            >
            <androidx.cardview.widget.CardView
                android:layout_margin="@dimen/dp_3"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorWhite"
                app:cardCornerRadius="@dimen/dp_10"
                />
            <androidx.constraintlayout.widget.ConstraintLayout

                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:elevation="@dimen/dp_10"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <TextView
                    android:id="@+id/Agent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Agent Name : Bella"
                    android:textColor="#ff666666"
                    android:textSize="@dimen/sp_14"
                    />

                <TextView
                    android:id="@+id/Invite"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Invite Code：888888"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_12"
                    />

                <LinearLayout
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginTop="@dimen/dp_38"

                    app:layout_constraintLeft_toLeftOf="parent"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="3"
                    >



                    <LinearLayout
                        android:gravity="center"

                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/currentAmount"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$1000.00"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_22"
                            android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"

                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Current Earnings"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" ( Diamonds )"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                    </LinearLayout>

                    <LinearLayout
                        android:gravity="center"

                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/weekAmount"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$1000.00"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_22"
                            android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"

                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Week Earnings"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" ( Diamonds )"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                    </LinearLayout>

                    <LinearLayout
                        android:gravity="center"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/monthAmount"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$1000.00"
                            android:textColor="#ff333333"
                            android:textSize="@dimen/sp_22"
                            android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"


                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Month Earnings"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" ( Diamonds )"
                            android:textColor="#ff999999"
                            android:textSize="@dimen/sp_12"
                            />
                    </LinearLayout>

                </LinearLayout>



            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

</layout>