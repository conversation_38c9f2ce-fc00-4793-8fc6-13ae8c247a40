<?xml version="1.0" encoding="utf-8"?>
<layout>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:visibility="gone"
            android:layout_height="@dimen/dp_35"
            android:paddingHorizontal="@dimen/dp_13"
            android:src="@mipmap/ic_white_cancel"
            tools:ignore="ContentDescription" />
    </RelativeLayout>
</layout>