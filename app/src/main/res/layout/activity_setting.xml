<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.setting.SettingActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <include layout="@layout/base_layout_title_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            android:visibility="gone"
            app:bind_view_onClick="@{ () -> activity.goAccountSafe() }">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:text="@string/account_safe"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:visibility="gone"
            android:background="#1D2031" />

        <RelativeLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.goBindBank() }">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/bank_bind"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.getLanguage() }">
            <!--            android:visibility="gone"-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/Language"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:id="@+id/img01"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_7"
                android:layout_toStartOf="@+id/img01"
                android:text=""
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_16" />
            <!--            English-->

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.getBlackList() }">
            <!--            android:visibility="gone"-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/Blacklist"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.onAgreementClick() }">
            <!--            android:visibility="gone"-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/Privacy_policy"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.onAgreementClick2() }">
            <!--            android:visibility="gone"-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/User_agreement"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />
        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.onAboutClick() }">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/About_us"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.clearCache() }">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/Clear_Cache"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView

                android:id="@+id/img02"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />

            <TextView
                android:id="@+id/appCacheSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_7"
                android:layout_toStartOf="@+id/img02"
                android:text="100.00M"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_16" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:id="@+id/relativeBaseUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_20"
            app:bind_view_onClick="@{ () -> activity.changeBaseUrl() }">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="@string/Version"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:id="@+id/img03"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right_white" />

            <TextView
                android:id="@+id/tvVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_7"
                android:layout_toStartOf="@+id/img03"
                android:gravity="end"
                android:text="v1.0.0"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_16" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1D2031" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorWhite"
            android:padding="@dimen/dp_20"
            android:visibility="gone"
            app:bind_view_onClick="@{ () -> activity.changeBaseUrl() }">
            <!--            android:visibility="gone"-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_gravity="center_vertical"
                android:text="000"
                android:textColor="@color/user_text_userinfo"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/user_ic_right" />
        </RelativeLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btnLogOut"
            style="@style/PrimaryButton"
            android:layout_above="@+id/temp01"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="@string/Log_out"
            app:bind_view_onClick="@{()->activity.logOut()}" />
    </LinearLayout>
</layout>