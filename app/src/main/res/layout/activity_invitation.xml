<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.invitation.InvitationActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:background="@color/color_FF4B20"
                android:orientation="horizontal">
                <!--            user_ic_wallte_bg-->
                <FrameLayout
                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20"
                    app:bind_view_onClick="@{()->activity.onBackPressed()}">

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@mipmap/base_ic_back"
                        app:tint="@color/base_color_divider" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:singleLine="true"
                    android:text="Invite friend rewards"
                    android:textColor="#FFFFFF"
                    android:textSize="@dimen/sp_18"
                    android:textStyle="bold" />


                <ImageView
                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/dp_15"
                    android:scaleType="fitXY"
                    android:src="@mipmap/user_ic_wallte_01"
                    android:visibility="gone"
                    app:bind_view_onClick="@{ (v) -> activity.query() }" />
            </RelativeLayout>


            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:layout_below="@+id/bar"
                android:background="#FB6324"
                android:scrollbars="none">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">


                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/user_activity_invitation_bg_21"

                        />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_76"
                        android:layout_marginTop="@dimen/dp_67"
                        android:src="@mipmap/user_activity_invitation_bg_20"

                        />


                    <com.sunfusheng.marqueeview.MarqueeView
                        android:id="@+id/marqueeView"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_150"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_marginBottom="@dimen/dp_16"
                        android:background="@mipmap/user_activity_invitation_bg_22"
                        app:mvAnimDuration="1000"
                        app:mvDirection="bottom_to_top"
                        app:mvGravity="center"
                        app:mvInterval="3000"
                        app:mvSingleLine="true"
                        app:mvTextColor="@color/colorWhite"
                        app:mvTextSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_400"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_312"
                            android:layout_margin="@dimen/dp_16"
                            android:background="@mipmap/user_activity_invitation_bg_09">

                            <ImageView
                                android:layout_width="@dimen/dp_38"
                                android:layout_height="@dimen/dp_38"

                                android:layout_marginStart="@dimen/dp_16"
                                android:padding="@dimen/dp_10"
                                android:src="@mipmap/user_activity_invitation_bg_05"
                                android:visibility="gone"
                                app:bind_view_onClick="@{ (v) -> activity.query() }"
                                app:layout_constraintBottom_toBottomOf="@+id/ruleTotle"
                                app:layout_constraintLeft_toRightOf="@+id/ruleTotle"
                                app:layout_constraintTop_toTopOf="@+id/ruleTotle" />

                            <LinearLayout
                                android:id="@+id/ruleTotle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_14"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:layout_width="@dimen/dp_48"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/user_activity_invitation_bg_03" />

                                <TextView
                                    android:layout_width="wrap_content"

                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:text="Reward rules"
                                    android:textColor="#ff755210"
                                    android:textSize="@dimen/sp_18" />


                                <ImageView
                                    android:layout_width="@dimen/dp_48"

                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:src="@mipmap/user_activity_invitation_bg_03" />

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/doubleTip"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_36"
                                android:orientation="horizontal"
                                android:weightSum="2"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/ruleTotle">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/dp_128"
                                    android:layout_marginStart="@dimen/dp_12"
                                    android:layout_marginEnd="@dimen/dp_6"
                                    android:layout_weight="1"
                                    android:background="@mipmap/user_activity_invitation_bg_06"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_5"
                                        android:text="Recharge rewards"
                                        android:textColor="#ffffffff"
                                        android:textSize="@dimen/sp_12" />

                                    <TextView
                                        android:id="@+id/rewardsRecharge"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_20"
                                        android:text="Up to 15%"
                                        android:textColor="#ffb45f30"
                                        android:textSize="@dimen/sp_14" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_16"
                                        android:gravity="center"
                                        android:text="Diamond rebate ratio for each recharge amount of invited friends"
                                        android:textColor="#ffcb8d66"
                                        android:textSize="@dimen/sp_8" />


                                </LinearLayout>

                                <LinearLayout

                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/dp_128"
                                    android:layout_marginStart="@dimen/dp_6"
                                    android:layout_marginEnd="@dimen/dp_12"
                                    android:layout_weight="1"
                                    android:background="@mipmap/user_activity_invitation_bg_07"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_5"
                                        android:text="Gift rewards"
                                        android:textColor="#ffffffff"
                                        android:textSize="@dimen/sp_12" />

                                    <TextView
                                        android:id="@+id/rewardsGift"
                                        android:layout_width="wrap_content"

                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp_20"
                                        android:text="Up to 20%"
                                        android:textColor="#FF3461"
                                        android:textSize="@dimen/sp_14" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"

                                        android:layout_marginTop="@dimen/dp_16"
                                        android:gravity="center"
                                        android:text="Diamond rebate ratio for each recharge amount of invited friends"
                                        android:textColor="#FF829D"
                                        android:textSize="@dimen/sp_8" />


                                </LinearLayout>


                            </LinearLayout>

                            <ImageView
                                android:id="@+id/imgInvite"

                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_80"
                                android:layout_marginTop="@dimen/dp_20"
                                android:src="@mipmap/user_activity_invitation_bg_08"
                                app:bind_view_onClick="@{ (v) -> activity.imgInvite() }"
                                app:layout_constraintTop_toBottomOf="@+id/doubleTip"

                                />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="@dimen/dp_10"
                                android:text="Invite friends now"
                                android:textColor="#ffffffff"
                                android:textSize="@dimen/dp_20"
                                app:layout_constraintBottom_toBottomOf="@+id/imgInvite"
                                app:layout_constraintLeft_toLeftOf="@+id/imgInvite"
                                app:layout_constraintRight_toRightOf="@+id/imgInvite"
                                app:layout_constraintTop_toTopOf="@+id/imgInvite" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp_16"
                            android:background="@drawable/base_rectangle_ffffff_r8_cccccc_w1">

                            <LinearLayout
                                android:id="@+id/invitationTotle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_14"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:layout_width="@dimen/dp_48"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/user_activity_invitation_bg_03" />

                                <TextView
                                    android:layout_width="wrap_content"

                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:text="My invitation"
                                    android:textColor="#ff755210"
                                    android:textSize="@dimen/sp_18" />


                                <ImageView
                                    android:layout_width="@dimen/dp_48"

                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:src="@mipmap/user_activity_invitation_bg_03" />

                            </LinearLayout>


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_26"
                                android:orientation="vertical"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/invitationTotle">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:weightSum="2">

                                    <LinearLayout

                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/dp_12"
                                        android:layout_marginEnd="@dimen/dp_6"
                                        android:layout_weight="1"
                                        android:background="@mipmap/user_activity_invitation_bg_11"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/day"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="#ff926018"
                                            android:textSize="22sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_4"
                                            android:text="Invitation for the day"
                                            android:textColor="#ffe3a64f"
                                            android:textSize="12sp" />


                                    </LinearLayout>

                                    <LinearLayout

                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/dp_6"
                                        android:layout_marginEnd="@dimen/dp_12"
                                        android:layout_weight="1"
                                        android:background="@mipmap/user_activity_invitation_bg_11"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/accumulated"

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="#ff926018"
                                            android:textSize="22sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_4"
                                            android:text="Accumulated invitations"
                                            android:textColor="#ffe3a64f"
                                            android:textSize="12sp" />


                                    </LinearLayout>


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp_10"
                                    android:orientation="horizontal"
                                    android:weightSum="2">

                                    <LinearLayout

                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/dp_12"
                                        android:layout_marginEnd="@dimen/dp_6"
                                        android:layout_weight="1"
                                        android:background="@mipmap/user_activity_invitation_bg_12"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/dayreward"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:drawableRight="@mipmap/user_ic_wallte_02"
                                            android:text="10"
                                            android:textColor="#5785FD"
                                            android:textSize="22sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_4"
                                            android:text="Today's reward"
                                            android:textColor="#80A1F3"
                                            android:textSize="12sp" />


                                    </LinearLayout>

                                    <LinearLayout

                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/dp_6"
                                        android:layout_marginEnd="@dimen/dp_12"
                                        android:layout_weight="1"
                                        android:background="@mipmap/user_activity_invitation_bg_12"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/accumulatedreward"
                                            android:layout_width="wrap_content"

                                            android:layout_height="wrap_content"
                                            android:drawableRight="@mipmap/user_ic_wallte_02"
                                            android:text="10"
                                            android:textColor="#5785FD"
                                            android:textSize="22sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_4"
                                            android:text="Accumulated reward"
                                            android:textColor="#80A1F3"
                                            android:textSize="12sp" />


                                    </LinearLayout>


                                </LinearLayout>

                                <androidx.recyclerview.widget.RecyclerView

                                    android:id="@+id/recyclerView"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_190"
                                    android:layout_marginStart="@dimen/dp_4"
                                    android:layout_marginEnd="@dimen/dp_4"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/RLTitle" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_1"
                                    android:background="#EEEEEE" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_12"
                                    android:layout_marginTop="@dimen/dp_24"
                                    android:gravity="left"
                                    android:text="Invitation rules"
                                    android:textColor="#ff333333"
                                    android:textSize="@dimen/sp_14"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"

                                    android:layout_height="wrap_content"

                                    android:layout_marginStart="@dimen/dp_12"
                                    android:layout_marginTop="@dimen/dp_12"
                                    android:layout_marginBottom="@dimen/dp_24"
                                    android:text="1.The invitee must be a new account + new device. If you do not receive rewards after the invitation, the invitee may not be a new user or new device；
\n2.Friends bound to the invitation code are limited to chat interaction consumption or diamond generation, real-time invitation bonus payment, and included in my income；
\n3. If the invitee has registered, it will not become my invited user；
\n4. For users who invite friends illegally, the platform will block their accounts and deduct all rewards. Please abide by the rules of the platform；
\n5. Time limit: Friend rewards can last up to 180 days."
                                    android:textColor="#ff999999"
                                    android:textSize="@dimen/sp_12" />

                            </LinearLayout>


                        </androidx.constraintlayout.widget.ConstraintLayout>


                        <LinearLayout
                            android:id="@+id/LLEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/img_err"
                                android:layout_width="@dimen/dp_270"
                                android:layout_height="@dimen/dp_270"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="@dimen/dp_70"
                                android:src="@mipmap/base_ic_net_error" />

                            <TextView
                                android:id="@+id/tvNetErrorTips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/img_err"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="@dimen/dp_30"
                                android:layout_marginTop="@dimen/dp_30"
                                android:layout_marginEnd="@dimen/dp_30"
                                android:layout_marginBottom="@dimen/dp_30"
                                android:text="@string/base_empty_view_hint"
                                android:textColor="#B3B9BF"
                                android:textSize="@dimen/sp_14" />


                        </LinearLayout>
                    </LinearLayout>
                </RelativeLayout>
            </androidx.core.widget.NestedScrollView>

        </RelativeLayout>


    </LinearLayout>
</layout>