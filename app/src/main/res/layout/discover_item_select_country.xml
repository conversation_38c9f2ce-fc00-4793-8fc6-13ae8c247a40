<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <!--        <import type="java.lang.String" />-->

        <variable
            name="courseinfo"
            type="com.mobile.anchor.app.module.discover.dialog.selectcountry.bean.CountryBean" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <!--    android:text="@={courseinfo.title}"-->
    <LinearLayout
        android:id="@+id/LL01"
        android:layout_width="@dimen/dp_109"
        android:layout_height="@dimen/dp_73"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:background="@drawable/discover_selector_bg_01"
        android:gravity="center_horizontal"
        android:orientation="vertical"

        app:bind_view_onClick="@{(v)->presenter.onItemClick(v,courseinfo)}">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_18"
            android:src="@mipmap/busy" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:text="America"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</layout>