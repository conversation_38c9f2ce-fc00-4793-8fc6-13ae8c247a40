<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:orientation="vertical"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:background="@color/colorTheme"
        android:layout_height="match_parent">

        <include layout="@layout/base_layout_title_bar"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#1D2031" />

        <TextView
            android:id="@+id/tv_validate_pwd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/old_password_validate"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_16"
            app:drawableEndCompat="@mipmap/user_ic_right_white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#1D2031" />

        <TextView
            android:id="@+id/tv_validate_email"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:drawableEnd="@mipmap/user_ic_right_white"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/email_validate"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_16" />
    </LinearLayout>
</layout>