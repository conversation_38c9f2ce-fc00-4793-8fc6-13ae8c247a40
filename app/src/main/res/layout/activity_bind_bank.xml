<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.wallet.BankBindActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.wallet.WalletModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/RL01"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:orientation="horizontal">
                <!--            user_ic_wallte_bg-->
                <FrameLayout
                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="@dimen/dp_12"
                    android:paddingEnd="@dimen/dp_12"
                    app:bind_view_onClick="@{()->activity.onBackPressed()}">

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@mipmap/base_ic_back" />

                </FrameLayout>

                <TextView
                    android:layout_width="90dp"
                    android:layout_height="25dp"
                    android:layout_centerInParent="true"
                    android:text="@string/bank_bind"
                    android:textColor="#ffd8d7dc"
                    android:textSize="18sp" />

            </RelativeLayout>


        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:background="#FF1D2031" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <LinearLayout
                    android:id="@+id/flCountry"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="@dimen/dp_90"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_country"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/country"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:drawableEnd="@mipmap/user_ic_withdraw02_bg_01"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_10"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14"
                        app:bind_view_onClick="@{ () -> activity.country() }" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/flWallet"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_wallet"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/wallet"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:drawableEnd="@mipmap/user_ic_withdraw02_bg_01"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_wallet_hint"
                        android:paddingStart="@dimen/dp_10"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14"
                        app:bind_view_onClick="@{ () -> activity.wallet() }" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/flTargetOrg"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_targetorg"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/targetOrg"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:drawableEnd="@mipmap/user_ic_withdraw02_bg_01"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_targetorg_hint"
                        android:paddingStart="@dimen/dp_10"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14"
                        app:bind_view_onClick="@{ () -> activity.targetOrg() }"
                        />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flAccountNo"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:text="@string/bank_accountno"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />

                    <EditText
                        android:id="@+id/accountNo"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_accountno_hint"
                        android:imeOptions="actionNext"
                        android:maxLength="20"
                        android:inputType="number"
                        android:background="@null"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flAddress"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_address"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/address"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_address_hint"
                        android:imeOptions="actionNext"
                        android:maxLength="50"
                        android:background="@null"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flBankCode"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_bankcode"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/bankCode"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_bankcode_hint"
                        android:imeOptions="actionNext"
                        android:maxLength="20"
                        android:background="@null"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flEmail"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_email"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/email"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_email_hint"
                        android:imeOptions="actionNext"
                        android:background="@null"
                        android:maxLength="20"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flFullName"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_fullname"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/fullName"

                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_fullname_hint"
                        android:imeOptions="actionDone"
                        android:background="@null"
                        android:maxLength="20"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/flPayeePhone"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="@dimen/dp_95"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/dp_16"
                        android:autoSizeMinTextSize="@dimen/sp_12"
                        android:gravity="center_vertical|start"
                        android:text="@string/bank_payeephone"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/sp_14" />


                    <EditText
                        android:id="@+id/payeePhone"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:hint="@string/bank_payeephone_hint"
                        android:imeOptions="actionDone"
                        android:maxLength="20"
                        android:background="@null"
                        android:paddingStart="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/colorWhite"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/dp_16"
                        android:background="#FF1D2031" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp_10"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/feedback"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:text="@string/feedback_hint"
                        android:textColor="#999999"
                        android:textSize="@dimen/sp_11" />

                </LinearLayout>


                <Button
                    android:id="@+id/btnSave"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_44"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_40"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:layout_marginBottom="@dimen/dp_90"
                    android:background="@drawable/bg_button_them"
                    android:text="@string/bank_save"
                    android:textAllCaps="false"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/sp_16"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:bind_view_onClick="@{ () -> activity.save() }"
                    tools:visibility="visible" />
            </LinearLayout>

        </ScrollView>


    </LinearLayout>


</layout>