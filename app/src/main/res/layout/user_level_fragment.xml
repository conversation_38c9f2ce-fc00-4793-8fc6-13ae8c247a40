<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName">

    <data>

        <variable
            name="viewModel"
            type="anchor.app.base.web.BaseWebViewFragment" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/dp_50">

        <ImageView
            android:src="@mipmap/user_level_ic_bg_04"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_160"
            android:id="@+id/levelBG"
            />


        <me.zhouzhuo.zzhorizontalprogressbar.ZzHorizontalProgressBar
            app:layout_constraintBottom_toBottomOf="@+id/levelBG"
            android:layout_marginBottom="@dimen/dp_25"
            android:layout_marginStart="@dimen/dp_110"
            android:layout_marginEnd="@dimen/dp_110"
            app:layout_constraintLeft_toLeftOf="parent"
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_4"
            app:zpb_bg_color="#3E394A"
            app:zpb_max="100"
            app:zpb_padding="0dp"
            app:zpb_pb_color="#C7A993"
            app:zpb_progress="30" />

        <LinearLayout
            app:layout_constraintLeft_toLeftOf="@+id/levelBG"
            app:layout_constraintRight_toRightOf="@+id/levelBG"
            app:layout_constraintBottom_toTopOf="@+id/progressBar"
            android:layout_marginBottom="@dimen/dp_4"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/nowProgress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10000"
                android:textColor="#ffc7a993"
                android:textSize="12sp"
                />
            <TextView
                android:id="@+id/allProgress"

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="/30000"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                />
        </LinearLayout>


        <TextView
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintTop_toBottomOf="@+id/levelBG"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Have Privileges"
            android:textColor="#ffc7a993"
            android:textSize="18sp"
            android:id="@+id/title"
            />

        <ImageView
            android:layout_marginEnd="@dimen/dp_12"
            app:layout_constraintRight_toLeftOf="@+id/title"
            app:layout_constraintTop_toTopOf="@+id/title"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            android:src="@mipmap/user_level_bg_04"
            android:layout_width="@dimen/dp_68"
            android:layout_height="@dimen/dp_7"
            android:id="@+id/view01"
            />

        <ImageView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintLeft_toRightOf="@+id/title"
            app:layout_constraintTop_toTopOf="@+id/title"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            android:src="@mipmap/user_level_bg_05"
            android:layout_width="@dimen/dp_68"
            android:layout_height="@dimen/dp_7"
            android:id="@+id/view02"
            />

        <LinearLayout
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_22"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:weightSum="2"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_130">
                <RelativeLayout
                    android:background="#000"
                    android:layout_marginEnd="@dimen/dp_9"
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                </RelativeLayout>
                <RelativeLayout
                    android:background="#000"
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                </RelativeLayout>

            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="@dimen/dp_9"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_130">

                <RelativeLayout
                    android:background="#000"
                    android:layout_marginEnd="@dimen/dp_9"
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                </RelativeLayout>
                <RelativeLayout
                    android:background="#000"
                    android:layout_weight="1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>



</layout>