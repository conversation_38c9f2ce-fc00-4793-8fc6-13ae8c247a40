<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.wallet.problem.DescriptionActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorWhite"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:background="@color/colorWhite"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.finish()}">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="FAQ"
                android:textColor="@color/public_text_color"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/public_edittext_color" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_margin="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/a0014"
                android:textColor="#ff333333"
                android:textStyle="bold"
                android:textSize="@dimen/dp_16"
                />

            <TextView
                android:layout_marginTop="@dimen/dp_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/FAQ"
                android:textColor="#ff333333"
                android:textSize="@dimen/dp_16"
                />


            <TextView
                android:visibility="gone"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_width="343dp"
                android:layout_height="48dp"
                android:text="2 . Google need to verily your account , if purchase faled , you will get the refund"
                android:textColor="#ff333333"
                android:textSize="@dimen/dp_16"
                />

        </LinearLayout>


    </LinearLayout>
</layout>