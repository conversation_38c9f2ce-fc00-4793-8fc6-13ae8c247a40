<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.follow.FansListActivity"
            />
    </data>
    <LinearLayout
        android:background="@color/colorTheme"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include layout="@layout/base_layout_title_bar"/>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout

            android:layout_below="@+id/RLTitle"
            android:paddingLeft="@dimen/dp_20"
            android:paddingRight="@dimen/dp_20"
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/RLTitle"
                app:layout_constraintBottom_toBottomOf="parent"
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>

</layout>