<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <import type="android.view.View" />

        <variable
            name="userinfo"
            type="com.mobile.anchor.app.module.user.wallet.history.bean.HistoryListBean" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_16">

        <ImageView
            android:id="@+id/head"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:src="@mipmap/user_ic_default"
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,userinfo)}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/consumptionType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:text="Top-Up"
            android:textColor="#ff333333"
            android:textSize="16sp"
            app:layout_constraintLeft_toRightOf="@+id/head"
            app:layout_constraintTop_toTopOf="@+id/head" />


        <ImageView
            android:id="@+id/newP"
            android:layout_width="@dimen/dp_47"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:src="@mipmap/user_item_historylist_01"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/consumptionType"
            app:layout_constraintLeft_toRightOf="@+id/consumptionType"
            app:layout_constraintTop_toTopOf="@+id/consumptionType"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/user_ic_wallte_02"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:src="@mipmap/user_ic_wallte_02"
            app:layout_constraintBottom_toBottomOf="@+id/consumptionType"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/consumptionType" />

        <TextView
            android:id="@+id/changeNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_6"
            android:text="-99999"
            android:textColor="#fffb4240"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/consumptionType"
            app:layout_constraintRight_toLeftOf="@+id/user_ic_wallte_02"
            app:layout_constraintTop_toTopOf="@+id/consumptionType" />


        <TextView
            android:id="@+id/beforeNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="99999"
            android:textColor="#ccc"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@+id/time"
            app:layout_constraintRight_toRightOf="@+id/user_ic_wallte_02"
            app:layout_constraintTop_toTopOf="@+id/time" />

        <TextView
            android:id="@+id/beforeNumTV"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_6"
            android:text="Balance:"
            android:textColor="#ccc"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@+id/time"
            app:layout_constraintRight_toLeftOf="@+id/beforeNum"
            app:layout_constraintTop_toTopOf="@+id/time" />


        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:text="2023-05-23 19:30:30"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_12"
            android:background="#F7F7F7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/time" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/time" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>