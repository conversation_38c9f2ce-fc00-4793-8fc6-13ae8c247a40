<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp_5"
    android:background="@drawable/shape_card_background"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingVertical="@dimen/dp_20">

    <TextView
        android:id="@+id/tv_period"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="2024.09.17-2024.09.19"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="-10000coins"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@id/tv_period"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_period" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:text="2024-11-7 12:24:30"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="@id/tv_period"
        app:layout_constraintTop_toBottomOf="@id/tv_period" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Sitting"
        android:textColor="#35B244"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/tv_date"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_date" />

</androidx.constraintlayout.widget.ConstraintLayout>