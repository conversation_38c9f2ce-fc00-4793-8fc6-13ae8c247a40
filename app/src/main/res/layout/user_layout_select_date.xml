<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/base_shape_round_background"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:background="@color/colorWhite">

        <TextView
            android:id="@+id/screen_cancle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_40"
            android:text="@string/user_login_concel"
            android:textColor="@color/user_select_text_color"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/screen_sure"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingRight="@dimen/dp_40"
            android:text="@string/user_login_confirm"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/sp_16" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/base_color_divider" />

    <!--此部分需要完整复制过去，删减或者更改ID会导致初始化找不到内容而报空-->
    <LinearLayout
        android:id="@+id/timepicker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:orientation="horizontal">

        <com.contrarywind.view.WheelView
            android:id="@+id/year"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.contrarywind.view.WheelView

            android:id="@+id/month"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/day"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/hour"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/min"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />

        <com.contrarywind.view.WheelView
            android:id="@+id/second"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.1" />
    </LinearLayout>
</LinearLayout>