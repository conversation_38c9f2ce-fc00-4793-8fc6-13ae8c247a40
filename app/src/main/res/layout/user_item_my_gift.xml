<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="item"
            type="com.mobile.anchor.app.module.user.bean.GiftBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_333333"
            android:text='@string/received_gift_from_x'
            android:textSize="@dimen/sp_16"
            tools:text="收到xxx的礼物"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@mipmap/user_ic_coin"
            android:drawablePadding="@dimen/dp_5"
            android:text='@{"+"+item.giftPrice.toString}'
            android:textColor="@color/color_21c76e"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            tools:text="10" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@{item.createTime}"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_13"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="2024-08-09 13:34" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@color/color_F6F6F7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_time" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>