<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.editedInfo.BuildProfileActivity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical"
        android:scrollbars="none">

        <RelativeLayout
            android:id="@+id/rl_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_12"
                android:paddingRight="@dimen/dp_12"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />


            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="@string/edit_profile"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/rl_title_bar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:orientation="vertical"
                android:paddingBottom="@dimen/dp_90">

                <RelativeLayout
                    android:layout_width="@dimen/dp_80"
                    android:layout_height="@dimen/dp_80"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_16">

                    <ImageView
                        android:id="@+id/imageViewHead"
                        android:layout_width="@dimen/dp_80"
                        android:layout_height="@dimen/dp_80"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/user_ic_default"
                        app:bind_view_onClick="@{ () -> activity.showSelectAlert() }"
                        tools:ignore="DuplicateIds" />

                    <ImageView
                        android:id="@+id/imageViewHeadBG"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone" />

                    <anchor.app.base.view.RoundImageView
                        android:id="@+id/imageInReview"
                        android:layout_width="@dimen/dp_120"
                        android:layout_height="@dimen/dp_120"
                        android:layout_gravity="center_horizontal"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/user_ic_default"
                        android:visibility="gone"
                        app:is_circle="true"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvInReview"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="@dimen/dp_6"
                        android:text="@string/in_review"
                        android:textColor="#ffffb909"
                        android:textSize="@dimen/sp_14"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <ImageView
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        android:src="@mipmap/user_edit_profile_camera" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:paddingHorizontal="@dimen/dp_24">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/recommended_image_video"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12"
                        android:visibility="gone" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/photo_up_conut_min"
                        android:textColor="#ff6d7096"
                        android:textSize="@dimen/sp_12"
                        android:visibility="gone" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:overScrollMode="never"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="3"
                    tools:itemCount="10"
                    tools:listitem="@layout/user_item_album_upload" />

                <LinearLayout
                    android:id="@+id/rlEditTextName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_24">

                    <TextView
                        android:id="@+id/tv_nickname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:text="@string/Username"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12" />

                    <EditText
                        android:id="@+id/editTextName"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_54"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/dp_7"
                        android:background="@drawable/bg_edit_info"
                        android:gravity="center_vertical"
                        android:hint="@string/nickname_hint"
                        android:imeOptions="actionNext"
                        android:maxLength="10"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textColorHint="@color/color_999999"
                        android:textSize="@dimen/sp_14" />
                    <!--            android:hint="@string/user_hint_name"-->
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_24">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/date_of_birth"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12" />

                    <TextView
                        android:id="@+id/tvBirthday"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_54"
                        android:layout_marginTop="@dimen/dp_7"
                        android:background="@drawable/bg_edit_info"
                        android:gravity="center_vertical"
                        android:hint="@string/birthday_hint"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:textColor="@color/white"
                        android:textColorHint="@color/color_999999"
                        android:textSize="@dimen/sp_14"
                        app:drawableEndCompat="@mipmap/user_ic_right" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rlAgencyCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp_24">

                    <TextView
                        android:id="@+id/tv_agency_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:text="@string/agency_code"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12" />

                    <EditText
                        android:id="@+id/editAgencyCode"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_54"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/dp_7"
                        android:background="@drawable/bg_edit_info"
                        android:gravity="center_vertical"
                        android:hint="@string/agency_code_hint"
                        android:imeOptions="actionNext"
                        android:inputType="number"
                        android:maxLength="10"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textColorHint="@color/color_999999"
                        android:textSize="@dimen/sp_14" />
                    <!--            android:hint="@string/user_hint_name"-->
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_agency_code_help"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_20"
                    android:text="@string/agency_code_help"
                    android:textColor="#9fa1a6"
                    android:textSize="@dimen/sp_14"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_16"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/Language"
                        android:textColor="@color/user_text_userinfo"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:id="@+id/tvLanguage"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_44"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_toLeftOf="@+id/imgNation"
                        android:background="@drawable/bg_edit_info"
                        android:drawableEnd="@mipmap/user_ic_right"
                        android:gravity="center_vertical"
                        android:hint="@string/language_hint"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:textColor="#999999"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_16"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label"
                        android:textColor="@color/user_text_userinfo"
                        android:textSize="@dimen/sp_14" />

                    <EditText
                        android:id="@+id/tvLabel"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_108"
                        android:layout_marginTop="@dimen/dp_12"
                        android:background="@drawable/bg_edit_info"
                        android:gravity="start"
                        android:hint="@string/label_hint"
                        android:padding="@dimen/dp_16"
                        android:textColor="#999999"
                        android:textColorHint="#FF585A64"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:paddingHorizontal="@dimen/dp_24"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/vidoe_up"
                        android:textColor="@color/colorWhite"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/video_up_count_min"
                        android:textColor="#ff6d7096"
                        android:textSize="@dimen/sp_12" />
                </LinearLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewVideo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:overScrollMode="never"
                    android:visibility="gone"
                    tools:itemCount="1"
                    tools:listitem="@layout/base_item_dynamic_add_video" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/btnSave"
            style="@style/PrimaryButton"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_45"
            android:layout_marginBottom="@dimen/dp_16"
            android:text="@string/edit_profile_next"
            app:bind_view_onClick="@{ () -> activity.save(true) }" />
    </RelativeLayout>
</layout>