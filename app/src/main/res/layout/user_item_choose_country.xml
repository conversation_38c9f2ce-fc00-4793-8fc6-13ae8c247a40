<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    tools:background="@color/background"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tag"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:background="#201831"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_16"
        android:text="A"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_14" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_16"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_flag"
            android:layout_width="@dimen/dp_35"
            android:layout_height="@dimen/dp_25" />

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginHorizontal="@dimen/dp_10"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            tools:text="中国"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>

</LinearLayout>