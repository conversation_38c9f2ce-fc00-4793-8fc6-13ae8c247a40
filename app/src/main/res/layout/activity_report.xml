<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:paddingStart="@dimen/dp_20"
                android:paddingEnd="@dimen/dp_20">

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />

            </FrameLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/b89"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvPublish"
                android:layout_width="@dimen/dp_56"
                android:layout_height="@dimen/dp_34"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@mipmap/discover_ic_publish_02"
                android:gravity="center"
                android:textColor="#ffffff"
                android:textSize="@dimen/sp_12" />
        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="@dimen/dp_16"
            android:paddingRight="@dimen/dp_16">

            <TextView
                android:id="@+id/ds"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/b87"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_120"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/base_rectangle_ff1e2132_r19"
                android:gravity="top"
                android:hint="@string/b46"
                android:lineSpacingExtra="@dimen/dp_3"
                android:paddingLeft="@dimen/dp_20"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20"
                android:textColor="@color/white"
                android:textColorHighlight="@color/color_666666"
                android:textCursorDrawable="@drawable/base_rectangle_ffcc45"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ds" />

            <TextView
                android:id="@+id/tvNum"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:gravity="bottom"
                android:text="0/1000"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et" />

            <TextView
                android:id="@+id/ds2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Upload photos（0/2）"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvNum" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_15"
                android:overScrollMode="never"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ds2" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/b86"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/recyclerView" />

            <View
                android:id="@+id/vPlace"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et"
                app:layout_constraintVertical_bias="0.0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>