<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_122"
            android:layout_marginEnd="@dimen/dp_45"
            android:background="@drawable/base_shape_rect_round_background"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_65"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_30"
                android:text="请先开启设备，帮设备配置wifi网络信息。设备屏幕将会出现可扫描的二维码。"
                android:textColor="#666C72"
                android:textSize="@dimen/sp_13" />


            <TextView
                android:id="@+id/btn_confirm"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_16"
                android:gravity="center"
                android:background="@drawable/shape_yellow_round_button"
                android:text="我知道了"
                android:textAllCaps="false"
                android:textColor="@color/colorBlack"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <ImageView
            android:layout_width="@dimen/dp_285"
            android:layout_height="@dimen/dp_131"
            android:layout_gravity="center_horizontal"
            android:src="@mipmap/login_dialog_qrcode" />
    </FrameLayout>
</layout>