<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.setting.blacklist.BlackListActivity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="horizontal">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />
            </FrameLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="@string/Blacklist"
                android:textColor="@color/colorWhite"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />
        </RelativeLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_4"
                android:paddingLeft="@dimen/dp_15"
                android:paddingRight="@dimen/dp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>
</layout>