<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="fragment"
            type="com.mobile.anchor.app.module.discover.main.homelist.HomeListFragment"
            />
    </data>
<!--    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout-->
<!--        android:id="@+id/refreshLayout"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent">-->
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginEnd="@dimen/dp_12"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="2"
                    tools:listitem="@layout/discover_item_home_new"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
            </RelativeLayout>
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>

    <!--    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>-->

</layout>