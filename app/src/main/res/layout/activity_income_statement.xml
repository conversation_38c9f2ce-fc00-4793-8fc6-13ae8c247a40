<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.mobile.anchor.app.module.user.bean.IncomeStatementBean" />
    </data>

    <LinearLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorTheme"
        android:orientation="vertical">

        <include layout="@layout/base_layout_title_bar" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabIndicator="@color/colorAccent"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/colorAccent"
            app:tabTextAppearance="@style/TabFont"
            app:tabTextColor="@color/color_666666" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/user_shape_white_top8">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp_8">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp_5"
                            android:layout_weight="1"
                            android:background="@drawable/user_shape_feeded"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingVertical="@dimen/dp_16">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text='@{bean.totalIncome+""}'
                                android:textColor="@color/color_F84139"
                                android:textSize="@dimen/sp_25"
                                tools:text="10000" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_5"
                                android:drawablePadding="@dimen/dp_3"
                                android:text="@string/income_statement_coin_total"
                                android:textColor="@color/color_666666"
                                android:gravity="center"
                                android:textSize="@dimen/sp_14"
                                app:drawableStartCompat="@mipmap/user_ic_coin" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp_5"
                            android:layout_weight="1"
                            android:background="@drawable/user_shape_feeded"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingVertical="@dimen/dp_16">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text='@{bean.totalGiftIncome+""}'
                                android:textColor="@color/color_F84139"
                                android:textSize="@dimen/sp_25"
                                tools:text="10000" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_5"
                                android:drawablePadding="@dimen/dp_3"
                                android:gravity="center"
                                android:text="@string/income_statement_gift_total"
                                android:textColor="@color/color_666666"
                                android:textSize="@dimen/sp_14"
                                app:drawableStartCompat="@mipmap/user_ic_gift" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp_5"
                            android:layout_weight="1"
                            android:background="@drawable/user_shape_feeded"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingVertical="@dimen/dp_16">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text='@{(bean.realCallRate==""||bean.realCallRate==null?"0":bean.realCallRate)+"%"}'
                                android:textColor="@color/color_F84139"
                                android:textSize="@dimen/sp_25"
                                tools:text="800%" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_5"
                                android:drawablePadding="@dimen/dp_3"
                                android:gravity="center"
                                android:text="@string/income_statement_call_rate"
                                android:textColor="@color/color_666666"
                                android:textSize="@dimen/sp_14"
                                app:drawableStartCompat="@mipmap/user_ic_wallte_problem" />
                        </LinearLayout>
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp_16"
                        android:text="@string/income_statement_detail"
                        android:textColor="@color/color_333333"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <com.scwang.smartrefresh.layout.SmartRefreshLayout
                    android:id="@+id/refreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:paddingHorizontal="@dimen/dp_16"
                    app:srlEnableNestedScrolling="true">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:itemCount="10"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/user_item_income_statement" />

                </com.scwang.smartrefresh.layout.SmartRefreshLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>