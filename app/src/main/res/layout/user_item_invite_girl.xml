<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_12"
    tools:background="@color/color_1E2132">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/dp_44"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/status"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:background="@mipmap/user_ic_user_status_online"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar" />

    <TextView
        android:id="@+id/tv_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:text="XXX"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <TextView
        android:id="@+id/tv_last_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:text="2024-12-12"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname"
        app:layout_constraintStart_toStartOf="@id/tv_nickname" />
</androidx.constraintlayout.widget.ConstraintLayout>