<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="item"
            type="com.mobile.anchor.app.module.user.bean.IncomeStatementBean" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{item.statDay}"
            android:layout_gravity="end"
            android:textStyle="bold"
            android:textColor="@color/color_666666"
            android:textSize="@dimen/sp_15"
            tools:text="2023-02-02" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/income_statement_label_coin_total"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_3"
                android:text='@{item.totalIncome+""}'
                android:textColor="@color/color_333333"
                android:textSize="@dimen/sp_16"
                app:drawableEndCompat="@mipmap/user_ic_coin"
                tools:text="9" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/color_FAFAFA" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/income_statement_label_call_duration"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_3"
                android:text="@{item.videoTimeShow}"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/sp_16"
                tools:text="1h20m23S" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/color_FAFAFA" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/income_statement_label_gift_num"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_3"
                android:text='@{item.totalGiftNum+""}'
                android:textColor="@color/color_333333"
                android:textSize="@dimen/sp_16"
                tools:text="9" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/color_FAFAFA" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/income_statement_label_gift_value"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/dp_3"
                android:text='@{"$"+item.totalGiftIncome}'
                android:textColor="@color/color_333333"
                android:textSize="@dimen/sp_16"
                tools:text="$9" />
        </LinearLayout>

    </LinearLayout>
</layout>