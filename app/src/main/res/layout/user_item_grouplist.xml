<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="userinfo"
            type="com.mobile.anchor.app.module.user.bean.GroupListBean" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        app:bind_view_onClick="@{(v)->presenter.onItemClick(v,userinfo)}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/head"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:src="@mipmap/user_ic_default"
            />

        <TextView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/name"
            android:textStyle="bold"
            android:maxWidth="@dimen/dp_110"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="RiliRiliRiliRiliRiliRiliRili"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ff333333"
            android:textSize="16sp"
            />


        <ImageView
            app:layout_constraintTop_toTopOf="@+id/name"
            app:layout_constraintLeft_toRightOf="@+id/name"
            app:layout_constraintBottom_toBottomOf="@+id/name"
            android:layout_marginStart="@dimen/dp_3"
            android:id="@+id/level"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_30"
            tools:src="@mipmap/user_ic_level01"
            />


        <anchor.app.base.view.RoundImageView
            android:scaleType="centerCrop"
            app:is_circle="true"
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/country"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            tools:src="@mipmap/area_simp"
            />


        <TextView
            app:layout_constraintTop_toTopOf="@+id/country"
            app:layout_constraintBottom_toBottomOf="@+id/country"
            app:layout_constraintLeft_toRightOf="@+id/country"
            android:id="@+id/countryTV"
            android:paddingLeft="@dimen/dp_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="UK"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_14"
            />
        <View
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/country"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_65"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#F7F7F7"
            />

        <Button
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dp_12"
            android:id="@+id/btnSave"
            android:layout_width="@dimen/dp_89"
            android:layout_height="@dimen/dp_32"
            android:layout_gravity="bottom"
            android:background="@drawable/user_state_selected_03"
            tools:text="DONE"
            android:textAllCaps="false"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/sp_14"
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,userinfo)}"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>