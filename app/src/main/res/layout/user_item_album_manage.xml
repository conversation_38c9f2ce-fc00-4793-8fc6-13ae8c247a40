<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        android:layout_margin="@dimen/dp_5"
        android:clipChildren="false"
        android:clipToPadding="false"
        tools:layout_width="@dimen/dp_108">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/color_cardBackground"
            app:cardCornerRadius="@dimen/dp_12">

            <ImageView
                android:id="@+id/iv_album"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_140"
                android:scaleType="centerCrop" />
        </androidx.cardview.widget.CardView>

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/dp_5"
            android:button="@drawable/user_checkbox"
            android:translationZ="@dimen/dp_10"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_video"
            android:layout_width="@dimen/dp_12"
            android:layout_height="@dimen/dp_12"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="@dimen/dp_4"
            android:elevation="@dimen/dp_10"
            android:src="@mipmap/user_album_video" />

        <TextView
            android:id="@+id/tv_refused"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#80000000"
            android:gravity="center"
            android:text="@string/album_review_refuse"
            android:textColor="@color/color_F84139"
            android:textSize="@dimen/sp_20" />

        <TextView
            android:id="@+id/tv_reviewing"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_20"
            android:background="@drawable/user_shape_album_review_label"
            android:elevation="@dimen/dp_10"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_10"
            android:text="@string/album_reviewing"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12" />

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_5"
            android:layout_alignParentEnd="true"
            android:elevation="@dimen/dp_10"
            android:src="@mipmap/user_album_del"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>
</layout>