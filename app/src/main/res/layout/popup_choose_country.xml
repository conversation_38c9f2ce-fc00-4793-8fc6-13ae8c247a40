<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/user_shape_100821_top8"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:paddingHorizontal="@dimen/dp_12">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/cancel"
                android:textColor="#bfbfbf"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/choose_country"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_18" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/dp_12"
                android:layout_height="@dimen/dp_12"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/user_ic_close_black"
                android:backgroundTint="@color/white" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_500"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/user_item_choose_country" />

            <anchor.app.base.view.SideBar
                android:id="@+id/sideBar"
                android:layout_width="30dp"
                android:layout_height="@dimen/dp_500"
                android:layout_alignParentEnd="true" />
        </RelativeLayout>
    </LinearLayout>
</layout>