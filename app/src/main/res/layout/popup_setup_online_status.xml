<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/user_shape_white_top8"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_75"
            android:paddingHorizontal="@dimen/dp_12">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/cancel"
                android:textColor="#bfbfbf"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/setup_online_status"
                android:textColor="@color/colorBlack"
                android:textSize="@dimen/sp_18" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/confirm"
                android:textColor="#F84139" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_1"
            android:background="@color/color_EEEEEE" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_58"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_invisibility"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/setup_online_status_invisibility"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/sp_16" />

            <ImageView
                android:id="@+id/iv_invisibility_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_5"
                android:src="@mipmap/user_activity_invitation_bg_05" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_1"
            android:background="@color/color_EEEEEE" />

        <TextView
            android:id="@+id/tv_online"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_58"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:text="@string/setup_online_status_normal"
            android:textColor="@color/color_666666"
            android:textSize="@dimen/sp_16" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_1"
            android:background="@color/color_EEEEEE" />

        <TextView
            android:id="@+id/tv_busy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_58"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:text="@string/setup_online_status_busy"
            android:textColor="@color/color_666666"
            android:textSize="@dimen/sp_16" />
    </LinearLayout>
</layout>