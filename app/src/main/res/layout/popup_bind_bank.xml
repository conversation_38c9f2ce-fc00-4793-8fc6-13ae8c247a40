<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/user_shape_popup_white"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:paddingTop="@dimen/dp_24"
            android:paddingBottom="@dimen/dp_32">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/bind_account"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/real_name"
                android:textColor="#100821"
                android:textSize="@dimen/sp_14" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_real_name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_4"
                android:background="@drawable/user_shape_e6e6e6_8"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/bank"
                android:textColor="#100821"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tv_bank"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_4"
                android:background="@drawable/user_shape_e6e6e6_8"
                android:drawableEnd="@mipmap/user_ic_inverted_triangle"
                android:drawableTint="@color/color_999999"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/bank_account"
                android:textColor="#100821"
                android:textSize="@dimen/sp_14" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_bank_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_4"
                android:background="@drawable/user_shape_e6e6e6_8"
                android:paddingHorizontal="@dimen/dp_16"
                android:singleLine="true" />

            <Button
                android:id="@+id/btn_submit"
                style="@style/PrimaryButton"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_24"
                android:text="@string/bind_bank_submit" />
        </LinearLayout>
    </LinearLayout>
</layout>