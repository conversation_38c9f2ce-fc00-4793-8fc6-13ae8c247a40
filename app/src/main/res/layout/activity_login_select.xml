<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.login.login.LoginViewModel" />

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.login.login.LoginSelectActivity" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/email_login_form"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@mipmap/login_bg" />

        <LinearLayout
            android:id="@+id/LL00"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_110"
            android:animateLayoutChanges="true"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/ll_login_agreement_id"
            app:layout_constraintLeft_toLeftOf="parent">

            <TextView
                android:id="@+id/tvSignIn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:layout_marginBottom="@dimen/dp_12"
                android:background="@drawable/login_shape_button"
                android:gravity="center"
                android:padding="@dimen/dp_10"
                android:text="@string/login_sign_in"
                android:textColor="#ffffffff"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvRegister"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@drawable/login_shape_white_button"
                android:gravity="center"
                android:padding="@dimen/dp_10"
                android:text="@string/new_girl"
                android:textAllCaps="false"
                android:textColor="@color/colorAccent"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/LL01"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_70"
            android:animateLayoutChanges="true"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/ll_login_agreement_id"
            app:layout_constraintLeft_toLeftOf="parent">

            <LinearLayout
                android:id="@+id/llGoogleLogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:layout_marginBottom="@dimen/dp_12"
                android:background="@drawable/login_shape_button"
                android:gravity="center">

                <ImageView
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@mipmap/ic_login_google" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/google_login"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFacebookLogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:layout_marginBottom="@dimen/dp_12"
                android:background="@drawable/shape_translucent_white_button"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@mipmap/ic_login_facebook" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/facebook_login"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llEmailLogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:background="@drawable/login_shape_button"
                android:gravity="center">

                <ImageView
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@mipmap/ic_login_email" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/email_login"
                    android:textColor="@color/colorWhite"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_50"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_20"
                android:text="@string/back"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_login_agreement_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_19"
            android:gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <ImageView
                android:id="@+id/checkbox"
                android:layout_width="@dimen/dp_26"
                android:layout_height="@dimen/dp_26"
                android:padding="@dimen/dp_6"
                android:src="@drawable/selector_checkbox"
                app:bind_view_onClick="@{ (v) -> activity.checkbox(v) }" />

            <TextView
                android:id="@+id/tvCheckboxText1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/a003"
                android:textColor="#999999"
                android:textSize="@dimen/sp_13" />

            <TextView
                android:id="@+id/tv_login_user_agreement_id"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="@string/User_agreement"
                android:textColor="@color/color_F84139"
                android:textSize="@dimen/sp_13"
                app:bind_view_onClick="@{ () -> activity.treaty(1) }" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/a005"
                android:textColor="#999999"
                android:textSize="@dimen/sp_13" />

            <TextView
                android:id="@+id/tv_login_private_private_id"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="@string/Privacy_policy"
                android:textColor="@color/color_F84139"
                android:textSize="@dimen/sp_13"
                app:bind_view_onClick="@{ () -> activity.treaty(2) }" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>