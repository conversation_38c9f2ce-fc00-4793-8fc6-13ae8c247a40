<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.user.rank.RankActivity" />

        <variable
            name="viewModel"
            type="com.mobile.anchor.app.module.user.setting.SettingModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorWhite"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#6A50DE"
                android:scrollbars="none">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/user_activity_rank_bg_01" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_46"
                        android:layout_marginTop="@dimen/dp_220"
                        android:src="@mipmap/user_activity_rank_bg_02"

                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_34"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_270"

                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_marginBottom="@dimen/dp_16"
                        android:background="@mipmap/user_activity_rank_bg_03"
                        android:gravity="center"
                        android:text="Send weekly star gifts to win the competition!"
                        android:textColor="#ffffffff" />
                    <!--                    <com.sunfusheng.marqueeview.MarqueeView-->
                    <!--                        android:background="@mipmap/user_activity_rank_bg_03"-->
                    <!--                        android:id="@+id/marqueeView"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="@dimen/dp_34"-->
                    <!--                        android:layout_marginStart="@dimen/dp_16"-->
                    <!--                        android:layout_marginTop="@dimen/dp_270"-->
                    <!--                        android:layout_marginEnd="@dimen/dp_16"-->
                    <!--                        android:layout_marginBottom="@dimen/dp_16"-->
                    <!--                        app:mvAnimDuration="1000"-->
                    <!--                        app:mvDirection="bottom_to_top"-->
                    <!--                        app:mvGravity="center"-->
                    <!--                        app:mvInterval="3000"-->
                    <!--                        app:mvSingleLine="true"-->
                    <!--                        app:mvTextColor="@color/colorWhite"-->
                    <!--                        app:mvTextSize="14sp" />-->

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_400"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp_16"
                            android:background="@mipmap/user_activity_rank_bg_04"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_54"
                                android:layout_margin="@dimen/dp_16"
                                android:background="@mipmap/user_activity_rank_bg_05"
                                android:orientation="horizontal"
                                android:weightSum="2">

                                <RelativeLayout
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/dp_36"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="@dimen/dp_36"
                                    android:layout_weight="1"
                                    app:bind_view_onClick="@{()->activity.rank01(0)}">

                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/rank01bg"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:src="@color/colorWhite"

                                        app:corner_radius="@dimen/dp_36" />

                                    <TextView
                                        android:id="@+id/rank01"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:text="This week"
                                        android:textColor="#3082FD"
                                        android:textSize="@dimen/sp_16" />

                                </RelativeLayout>

                                <RelativeLayout
                                    android:layout_width="0dp"

                                    android:layout_height="@dimen/dp_36"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="@dimen/dp_36"
                                    android:layout_weight="1"
                                    app:bind_view_onClick="@{()->activity.rank01(1)}">

                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/rank02bg"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:src="@color/colorWhite"
                                        android:visibility="gone"
                                        app:corner_radius="@dimen/dp_36"
                                        tools:visibility="visible" />

                                    <TextView
                                        android:id="@+id/rank02"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:text="Last week"
                                        android:textColor="#3082FD"
                                        android:textSize="@dimen/sp_16" />

                                </RelativeLayout>

                            </LinearLayout>


                            <TextView
                                android:id="@+id/data"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_24"
                                android:layout_marginStart="@dimen/dp_16"
                                android:layout_marginEnd="@dimen/dp_16"
                                android:background="@mipmap/user_activity_rank_bg_06"
                                android:gravity="center"
                                android:textColor="#ffae7c40"
                                android:textSize="@dimen/sp_12"
                                tools:text="2024-06-01 ~ 2024-06-07" />


                            <LinearLayout
                                android:id="@+id/LL01"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/RLTitle"
                                android:layout_marginStart="@dimen/dp_16"
                                android:layout_marginTop="@dimen/dp_22"
                                android:layout_marginEnd="@dimen/dp_16"
                                android:gravity="bottom"
                                android:orientation="horizontal"
                                android:weightSum="3">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/dp_190"
                                    android:layout_weight="1">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/dp_167"
                                        android:layout_marginTop="@dimen/dp_22"
                                        android:src="@mipmap/user_activity_rank_bg_07"

                                        app:layout_constraintTop_toTopOf="@+id/head" />

                                    <LinearLayout
                                        android:id="@+id/LLName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="vertical"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/head">

                                        <TextView
                                            android:id="@+id/name"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:ellipsize="end"
                                            android:maxWidth="@dimen/dp_74"
                                            android:maxLines="1"
                                            android:textColor="#ff333333"
                                            android:textSize="@dimen/sp_16"
                                            tools:text="Candiicaaaaaaa" />

                                        <TextView
                                            android:id="@+id/id"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textColor="#ff999999"
                                            android:textSize="@dimen/sp_12"
                                            tools:text="ID:78888888" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/LLCout"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/LLName">


                                        <anchor.app.base.view.RoundImageView
                                            android:id="@+id/gift"
                                            android:layout_width="@dimen/dp_20"
                                            android:layout_height="@dimen/dp_20"
                                            android:scaleType="centerCrop"
                                            android:src="@mipmap/user_activity_rank_bg_08" />

                                        <TextView
                                            android:id="@+id/giftCount"

                                            android:layout_width="wrap_content"

                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/dp_6"
                                            android:textColor="#FC6399"
                                            android:textSize="@dimen/sp_14"
                                            tools:text="5525" />


                                    </LinearLayout>


                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/headbg"
                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:padding="@dimen/dp_20"
                                        app:is_circle="true"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:src="@color/colorBlack"

                                        />

                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/head"
                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:src="@mipmap/user_activity_rank_bg_10"
                                        app:is_circle="true"
                                        app:layout_constraintBottom_toBottomOf="@+id/headbg"
                                        app:layout_constraintLeft_toLeftOf="@+id/headbg"
                                        app:layout_constraintRight_toRightOf="@+id/headbg"
                                        app:layout_constraintTop_toTopOf="@+id/headbg"

                                        />


                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="0dp"

                                    android:layout_height="@dimen/dp_198"
                                    android:layout_marginStart="@dimen/dp_9"
                                    android:layout_weight="1">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/dp_167"
                                        android:layout_marginTop="@dimen/dp_34"
                                        android:src="@mipmap/user_activity_rank_bg_08"

                                        app:layout_constraintTop_toTopOf="@+id/head02" />

                                    <LinearLayout
                                        android:id="@+id/LLName02"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="vertical"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/head02">

                                        <TextView
                                            android:id="@+id/name02"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:ellipsize="end"
                                            android:maxWidth="@dimen/dp_74"
                                            android:maxLines="1"
                                            android:textColor="#ff333333"
                                            android:textSize="@dimen/sp_16"
                                            tools:text="Candiicaaaaaaa" />


                                        <TextView
                                            android:id="@+id/id02"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textColor="#ff999999"
                                            android:textSize="@dimen/sp_12"
                                            tools:text="ID:78888888" />

                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/LLCout02"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/LLName02">


                                        <anchor.app.base.view.RoundImageView
                                            android:id="@+id/gift02"
                                            android:layout_width="@dimen/dp_20"
                                            android:layout_height="@dimen/dp_20"
                                            android:scaleType="centerCrop"

                                            />

                                        <TextView
                                            android:id="@+id/giftCount02"
                                            android:layout_width="wrap_content"

                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/dp_6"
                                            android:textColor="#FC6399"
                                            android:textSize="@dimen/sp_14"
                                            tools:text="5525" />


                                    </LinearLayout>


                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/headbg02"
                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:padding="@dimen/dp_17"
                                        app:is_circle="true"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"

                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:src="@color/colorBlack" />

                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/head02"
                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:src="@mipmap/user_activity_rank_bg_11"

                                        app:is_circle="true"
                                        app:layout_constraintBottom_toBottomOf="@+id/headbg02"
                                        app:layout_constraintLeft_toLeftOf="@+id/headbg02"
                                        app:layout_constraintRight_toRightOf="@+id/headbg02"
                                        app:layout_constraintTop_toTopOf="@+id/headbg02" />


                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="0dp"
                                    android:layout_height="@dimen/dp_190"

                                    android:layout_marginStart="@dimen/dp_9"
                                    android:layout_weight="1">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="@dimen/dp_167"
                                        android:layout_marginTop="@dimen/dp_22"
                                        android:src="@mipmap/user_activity_rank_bg_09"

                                        app:layout_constraintTop_toTopOf="@+id/head03" />

                                    <LinearLayout
                                        android:id="@+id/LLName03"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="vertical"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/head03">

                                        <TextView
                                            android:id="@+id/name03"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:ellipsize="end"
                                            android:maxWidth="@dimen/dp_74"
                                            android:maxLines="1"
                                            android:textColor="#ff333333"
                                            android:textSize="@dimen/sp_16"
                                            tools:text="Candiicaaaaaaa" />


                                        <TextView
                                            android:id="@+id/id03"

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textColor="#ff999999"
                                            android:textSize="@dimen/sp_12"
                                            tools:text="ID:78888888" />

                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/LLCout03"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"
                                        app:layout_constraintTop_toBottomOf="@+id/LLName03">


                                        <anchor.app.base.view.RoundImageView
                                            android:id="@+id/gift03"
                                            android:layout_width="@dimen/dp_20"
                                            android:layout_height="@dimen/dp_20"
                                            android:scaleType="centerCrop"

                                            />

                                        <TextView
                                            android:id="@+id/giftCount03"

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/dp_6"
                                            android:textColor="#FC6399"
                                            android:textSize="@dimen/sp_14"
                                            tools:text="5525" />


                                    </LinearLayout>


                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/headbg03"
                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:padding="@dimen/dp_20"
                                        app:is_circle="true"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintRight_toRightOf="parent"

                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:src="@color/colorBlack" />

                                    <anchor.app.base.view.RoundImageView
                                        android:id="@+id/head03"

                                        android:layout_width="@dimen/dp_90"
                                        android:layout_height="@dimen/dp_90"
                                        android:src="@mipmap/user_activity_rank_bg_12"
                                        app:is_circle="true"
                                        app:layout_constraintBottom_toBottomOf="@+id/headbg03"
                                        app:layout_constraintLeft_toLeftOf="@+id/headbg03"
                                        app:layout_constraintRight_toRightOf="@+id/headbg03"
                                        app:layout_constraintTop_toTopOf="@+id/headbg03" />


                                </androidx.constraintlayout.widget.ConstraintLayout>


                            </LinearLayout>


                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/LL01"
                                android:layout_marginStart="@dimen/dp_4"
                                android:layout_marginTop="@dimen/dp_30"
                                android:layout_marginEnd="@dimen/dp_4"
                                android:maxHeight="@dimen/dp_240" />


                        </LinearLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp_16"
                            android:background="@drawable/base_rectangle_ffffff_r8_cccccc_w1">

                            <TextView
                                android:id="@+id/title"
                                android:layout_width="@dimen/dp_200"
                                android:layout_height="@dimen/dp_36"
                                android:background="@mipmap/user_activity_rank_bg_13"
                                android:gravity="center"
                                android:text="Weekly Reward"
                                android:textColor="#ffffffff"
                                android:textSize="@dimen/sp_16"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <LinearLayout
                                android:id="@+id/tab02"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_34"

                                android:layout_marginTop="@dimen/dp_18"
                                android:orientation="vertical"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/title">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal"
                                    android:weightSum="2">

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_weight="1"
                                        android:orientation="vertical"
                                        app:bind_view_onClick="@{()->activity.rank02(0)}">

                                        <TextView
                                            android:id="@+id/rank03"

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerInParent="true"
                                            android:layout_gravity="center_horizontal"
                                            android:text="This week"
                                            android:textColor="#3082FD"
                                            android:textSize="@dimen/sp_16" />

                                        <ImageView
                                            android:id="@+id/rank03bg"
                                            android:layout_width="@dimen/dp_24"
                                            android:layout_height="@dimen/dp_4"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="@dimen/dp_2"
                                            android:src="@mipmap/user_activity_rank_bg_14"
                                            app:corner_radius="@dimen/dp_36" />


                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"

                                        android:layout_height="match_parent"

                                        android:layout_gravity="center_vertical"
                                        android:layout_weight="1"
                                        android:orientation="vertical"
                                        app:bind_view_onClick="@{()->activity.rank02(1)}">


                                        <TextView
                                            android:id="@+id/rank04"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerInParent="true"
                                            android:layout_gravity="center_horizontal"
                                            android:text="Last week"
                                            android:textColor="#3082FD"
                                            android:textSize="@dimen/sp_16" />


                                        <ImageView
                                            android:id="@+id/rank04bg"
                                            android:layout_width="@dimen/dp_24"
                                            android:layout_height="@dimen/dp_4"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="@dimen/dp_2"
                                            android:src="@mipmap/user_activity_rank_bg_14"
                                            app:corner_radius="@dimen/dp_36" />

                                    </LinearLayout>

                                </LinearLayout>


                            </LinearLayout>


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_26"
                                android:orientation="vertical"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/tab02">

                                <androidx.recyclerview.widget.RecyclerView

                                    android:id="@+id/recyclerView01"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_4"
                                    android:layout_marginEnd="@dimen/dp_4"
                                    android:maxHeight="@dimen/dp_190"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/RLTitle" />


                                <!--                        <View-->
                                <!--                            android:layout_width="match_parent"-->
                                <!--                            android:layout_height="@dimen/dp_1"-->
                                <!--                            android:background="#EEEEEE"-->
                                <!--                            />-->
                                <!--                        <TextView-->
                                <!--                            android:layout_marginTop="@dimen/dp_24"-->
                                <!--                            android:layout_marginStart="@dimen/dp_12"-->
                                <!--                            android:layout_width="match_parent"-->
                                <!--                            android:layout_height="wrap_content"-->
                                <!--                            android:gravity="left"-->
                                <!--                            android:text="Invitation rules"-->
                                <!--                            android:textColor="#ff333333"-->
                                <!--                            android:textSize="@dimen/sp_14"-->
                                <!--                            android:textStyle="bold"-->
                                <!--                            />-->
                                <!--                        <TextView-->
                                <!--                            android:layout_marginTop="@dimen/dp_12"-->

                                <!--                            android:layout_marginStart="@dimen/dp_12"-->

                                <!--                            android:layout_width="wrap_content"-->
                                <!--                            android:layout_height="wrap_content"-->
                                <!--                            android:text="1.The invitee must be a new account + new device. If you do not receive rewards after the invitation, the invitee may not be a new user or new device；-->
                                <!--\n2.Friends bound to the invitation code are limited to chat interaction consumption or diamond generation, real-time invitation bonus payment, and included in my income；-->
                                <!--\n3. If the invitee has registered, it will not become my invited user；-->
                                <!--\n4. For users who invite friends illegally, the platform will block their accounts and deduct all rewards. Please abide by the rules of the platform；-->
                                <!--\n5. Time limit: Friend rewards can last up to 180 days."-->
                                <!--                            android:textColor="#ff999999"-->
                                <!--                            android:textSize="@dimen/sp_12"-->
                                <!--                            android:layout_marginBottom="@dimen/dp_24"-->
                                <!--                            />-->

                            </LinearLayout>


                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_60" />


                        <LinearLayout
                            android:id="@+id/LLEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/img_err"
                                android:layout_width="@dimen/dp_270"
                                android:layout_height="@dimen/dp_270"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="@dimen/dp_70"
                                android:src="@mipmap/base_ic_net_error" />

                            <TextView
                                android:id="@+id/tvNetErrorTips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/img_err"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="@dimen/dp_30"
                                android:layout_marginTop="@dimen/dp_30"
                                android:layout_marginEnd="@dimen/dp_30"
                                android:layout_marginBottom="@dimen/dp_30"
                                android:text="@string/base_empty_view_hint"
                                android:textColor="#B3B9BF"
                                android:textSize="@dimen/sp_14" />


                        </LinearLayout>
                    </LinearLayout>
                </RelativeLayout>
            </androidx.core.widget.NestedScrollView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/CLSelf"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/colorWhite"
                android:paddingLeft="@dimen/dp_16"
                android:paddingTop="@dimen/dp_8"
                android:paddingRight="@dimen/dp_16"
                android:paddingBottom="@dimen/dp_8">

                <TextView
                    android:id="@+id/nu"
                    android:layout_width="@dimen/dp_22"
                    android:layout_height="@dimen/dp_22"
                    android:background="@drawable/f5f5f5_round_orange_bg"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/headSelf"
                    android:layout_width="@dimen/dp_50"
                    android:layout_height="@dimen/dp_50"
                    android:layout_marginStart="@dimen/dp_12"
                    android:src="@mipmap/user_ic_default"
                    app:layout_constraintLeft_toRightOf="@+id/nu"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@+id/headSelf"
                    app:layout_constraintLeft_toRightOf="@+id/headSelf"
                    app:layout_constraintTop_toTopOf="@+id/headSelf">


                    <TextView
                        android:id="@+id/consumptionType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#ff333333"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold"
                        tools:text="Top-Up" />

                    <TextView
                        android:id="@+id/idSelf"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#ff666666"
                        android:textSize="@dimen/sp_12"
                        tools:text="ID:78855888" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="@+id/headSelf"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/headSelf">

                    <ImageView
                        android:id="@+id/giftSelf"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_30"
                        android:src="@mipmap/diamond" />

                    <TextView
                        android:id="@+id/giftSelfCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/dp_4"
                        android:textColor="#fffc6399"
                        android:textSize="@dimen/sp_14"
                        tools:text="x99" />

                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:id="@+id/bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_90"
                android:orientation="horizontal">
                <!--            user_ic_wallte_bg-->
                <FrameLayout

                    android:id="@+id/relativeBack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20"
                    app:bind_view_onClick="@{()->activity.onBackPressed()}">

                    <ImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:layout_gravity="center"
                        android:scaleType="fitXY"
                        android:src="@mipmap/base_ic_back"
                        android:tint="@color/base_color_divider" />

                </FrameLayout>

                <TextView

                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:singleLine="true"
                    android:text="Rank"
                    android:textColor="#FFFFFF"
                    android:textSize="@dimen/sp_18"
                    android:textStyle="bold" />


                <ImageView

                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/dp_15"
                    android:scaleType="fitXY"
                    android:src="@mipmap/user_ic_wallte_01"
                    app:bind_view_onClick="@{ (v) -> activity.query() }" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>
</layout>