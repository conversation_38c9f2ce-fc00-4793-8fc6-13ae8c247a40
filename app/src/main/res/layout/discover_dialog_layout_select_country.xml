<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.mobile.anchor.app.module.discover.dialog.selectcountry.DiscoverSelectCountryDialogFragment" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_102"
        android:background="@drawable/base_dialog_bg_01"
        android:backgroundTint="@color/background"
        android:minHeight="@dimen/dp_200">

        <RelativeLayout
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_58"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/close"
                android:layout_width="@dimen/dp_46"
                android:layout_height="@dimen/dp_46"
                android:layout_centerVertical="true"
                android:padding="@dimen/dp_15"
                android:src="@mipmap/discover_country_close"
                app:bind_view_onClick="@{()->dialog.close()}" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/a0019"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_18" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14"
            android:minHeight="@dimen/dp_200"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintTop_toBottomOf="@+id/title"
            app:spanCount="3"
            tools:listitem="@layout/discover_item_select_country" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>