<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/textViewMsgCount"
        android:layout_width="@dimen/dp_19"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_5"
        android:background="@drawable/app_notify_circle_bg"
        android:gravity="center"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/sp_10"
        android:visibility="visible" />
    <TextView
        android:id="@+id/textViewPoint"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_5"
        android:background="@drawable/app_notify_point_circle_bg"
        android:gravity="center"
        android:textColor="@color/colorWhite"
        android:textSize="@dimen/sp_10"
        android:visibility="visible" />
</RelativeLayout>