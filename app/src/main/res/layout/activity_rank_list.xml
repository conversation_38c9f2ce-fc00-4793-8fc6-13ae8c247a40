<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource,ResourceName">

    <data>

        <variable
            name="activity"
            type="com.mobile.anchor.app.module.discover.main.rank.RankListActivity"
            />
    </data>
    <RelativeLayout
        android:background="@mipmap/discover_rank_bg_00"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:id="@+id/RLTitle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/relativeBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/dp_20"
                android:paddingRight="@dimen/dp_20"
                app:bind_view_onClick="@{()->activity.onBackPressed()}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@mipmap/base_ic_back" />



            </FrameLayout>


            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:singleLine="true"
                android:text="Hot Girls"
                android:textColor="@color/public_text_color"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />


            <ImageView
                android:tint="@color/colorBlack"
                app:bind_view_onClick="@{ (v) -> activity.query() }"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:padding="@dimen/dp_15"
                android:layout_width="@dimen/dp_54"
                android:layout_height="@dimen/dp_54"
                android:scaleType="fitXY"
                android:src="@mipmap/discover_ic_query" />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/LL01"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_below="@+id/RLTitle"
            android:orientation="horizontal"
            android:weightSum="1"
            android:gravity="bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/dp_102"
                android:layout_height="@dimen/dp_190">
                <ImageView
                    android:layout_marginTop="@dimen/dp_22"
                    app:layout_constraintTop_toTopOf="@+id/head"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_167"

                    android:src="@mipmap/discover_rank_bg_02"
                    />

                <LinearLayout
                    android:id="@+id/LLName"
                    app:layout_constraintTop_toBottomOf="@+id/head"
                    android:layout_marginTop="@dimen/dp_35"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/name"
                        android:maxWidth="@dimen/dp_74"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Candiicaaaaaaa"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#ff333333"
                        android:textSize="@dimen/sp_16"
                        />
                    <anchor.app.base.view.RoundImageView
                        android:id="@+id/countryICON"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:src="@mipmap/base_ic_country_01"
                        app:is_circle="true"
                        android:scaleType="centerCrop"
                        />


                </LinearLayout>
                <LinearLayout
                    android:id="@+id/LLCout"
                    app:layout_constraintTop_toBottomOf="@+id/LLName"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">


                    <anchor.app.base.view.RoundImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:src="@mipmap/discover_rank_bg_04"
                        android:scaleType="centerCrop"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="5525"
                        android:textColor="#ffff6f88"
                        android:textSize="@dimen/sp_12"
                        />


                </LinearLayout>
                
                <ImageView
                    android:visibility="gone"

                    app:bind_view_onClick="@{()->activity.toVideo(1)}"
                    android:layout_marginTop="@dimen/dp_11"
                    app:layout_constraintTop_toBottomOf="@+id/LLCout"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_width="@dimen/dp_70"
                    android:layout_height="@dimen/dp_24"
                    android:src="@mipmap/discover_rank_bg_05"
                    />



                <anchor.app.base.view.RoundImageView
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:id="@+id/headbg"
                    android:layout_width="@dimen/dp_48"
                    android:layout_height="@dimen/dp_48"
                    app:is_circle="true"
                    android:src="@color/colorWhite"
                    />
                <anchor.app.base.view.RoundImageView
                    app:layout_constraintLeft_toLeftOf="@+id/headbg"
                    app:layout_constraintRight_toRightOf="@+id/headbg"
                    app:layout_constraintTop_toTopOf="@+id/headbg"
                    app:layout_constraintBottom_toBottomOf="@+id/headbg"
                    android:id="@+id/head"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"
                    app:is_circle="true"
                    android:src="@color/colorBlack"
                    />



            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_weight="1"
                android:layout_width="@dimen/dp_102"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_marginTop="@dimen/dp_34"
                    app:layout_constraintTop_toTopOf="@+id/head02"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_191"

                    android:src="@mipmap/discover_rank_bg_01"
                    />

                <LinearLayout
                    android:id="@+id/LLName02"
                    app:layout_constraintTop_toBottomOf="@+id/head02"
                    android:layout_marginTop="@dimen/dp_47"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/name02"
                        android:maxWidth="@dimen/dp_74"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Candiicaaaaaaa"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#ff333333"
                        android:textSize="@dimen/sp_16"
                        />
                    <anchor.app.base.view.RoundImageView
                        android:id="@+id/countryICON02"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:src="@mipmap/base_ic_country_01"
                        app:is_circle="true"
                        android:scaleType="centerCrop"
                        />


                </LinearLayout>
                <LinearLayout
                    android:id="@+id/LLCout02"
                    app:layout_constraintTop_toBottomOf="@+id/LLName02"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">


                    <anchor.app.base.view.RoundImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:src="@mipmap/discover_rank_bg_04"
                        android:scaleType="centerCrop"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="5525"
                        android:textColor="#ffff6f88"
                        android:textSize="@dimen/sp_12"
                        />


                </LinearLayout>

                <ImageView
                    app:bind_view_onClick="@{()->activity.toVideo(0)}"
                    android:visibility="gone"

                    android:layout_marginTop="@dimen/dp_11"
                    app:layout_constraintTop_toBottomOf="@+id/LLCout02"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_width="@dimen/dp_70"
                    android:layout_height="@dimen/dp_24"
                    android:src="@mipmap/discover_rank_bg_06"
                    />



                <anchor.app.base.view.RoundImageView
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:id="@+id/headbg02"
                    android:layout_width="@dimen/dp_67"
                    android:layout_height="@dimen/dp_67"
                    app:is_circle="true"
                    android:src="@color/colorWhite"
                    />

                <anchor.app.base.view.RoundImageView
                    android:id="@+id/head02"
                    android:layout_width="@dimen/dp_62"
                    android:layout_height="@dimen/dp_62"
                    android:src="@color/colorBlack"
                    app:is_circle="true"
                    app:layout_constraintBottom_toBottomOf="@+id/headbg02"
                    app:layout_constraintLeft_toLeftOf="@+id/headbg02"
                    app:layout_constraintRight_toRightOf="@+id/headbg02"
                    app:layout_constraintTop_toTopOf="@+id/headbg02" />


            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/dp_102"
                android:layout_height="@dimen/dp_190">
                <ImageView
                    android:layout_marginTop="@dimen/dp_22"
                    app:layout_constraintTop_toTopOf="@+id/head03"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_167"

                    android:src="@mipmap/discover_rank_bg_03"
                    />

                <LinearLayout
                    android:id="@+id/LLName03"
                    app:layout_constraintTop_toBottomOf="@+id/head03"
                    android:layout_marginTop="@dimen/dp_35"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/name03"
                        android:maxWidth="@dimen/dp_74"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Candiicaaaaaaa"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#ff333333"
                        android:textSize="@dimen/sp_16"
                        />
                    <anchor.app.base.view.RoundImageView
                        android:id="@+id/countryICON03"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"
                        android:src="@mipmap/base_ic_country_01"
                        app:is_circle="true"
                        android:scaleType="centerCrop"
                        />


                </LinearLayout>
                <LinearLayout
                    android:id="@+id/LLCout03"
                    app:layout_constraintTop_toBottomOf="@+id/LLName03"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">


                    <anchor.app.base.view.RoundImageView
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:src="@mipmap/discover_rank_bg_04"
                        android:scaleType="centerCrop"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="5525"
                        android:textColor="#ffff6f88"
                        android:textSize="@dimen/sp_12"
                        />


                </LinearLayout>

                <ImageView
                    app:bind_view_onClick="@{()->activity.toVideo(2)}"
                    android:visibility="gone"

                    android:layout_marginTop="@dimen/dp_11"
                    app:layout_constraintTop_toBottomOf="@+id/LLCout03"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_width="@dimen/dp_70"
                    android:layout_height="@dimen/dp_24"
                    android:src="@mipmap/discover_rank_bg_07"
                    />



                <anchor.app.base.view.RoundImageView
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:id="@+id/headbg03"
                    android:layout_width="@dimen/dp_48"
                    android:layout_height="@dimen/dp_48"
                    app:is_circle="true"
                    android:src="@color/colorWhite"
                    />
                <anchor.app.base.view.RoundImageView
                    app:layout_constraintLeft_toLeftOf="@+id/headbg03"
                    app:layout_constraintRight_toRightOf="@+id/headbg03"
                    app:layout_constraintTop_toTopOf="@+id/headbg03"
                    app:layout_constraintBottom_toBottomOf="@+id/headbg03"
                    android:id="@+id/head03"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"
                    app:is_circle="true"
                    android:src="@color/colorBlack"
                    />



            </androidx.constraintlayout.widget.ConstraintLayout>






        </LinearLayout>





                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_marginTop="@dimen/dp_30"
                        android:layout_below="@+id/LL01"
                        android:id="@+id/recyclerView"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_marginEnd="@dimen/dp_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>




    </RelativeLayout>

</layout>