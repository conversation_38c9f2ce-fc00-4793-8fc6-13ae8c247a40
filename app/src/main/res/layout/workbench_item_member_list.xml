<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource,ResourceName">
    <data>
        <import type="android.view.View"/>
        <variable
            name="userinfo"
            type="com.mobile.anchor.app.module.workbench.bean.MemberListBean" />

        <variable
            name="presenter"
            type="anchor.app.base.adapter.ItemClickPresenter" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_156">


        <androidx.cardview.widget.CardView
            android:layout_margin="@dimen/dp_3"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/colorWhite"
            app:cardCornerRadius="@dimen/dp_10"
            />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginTop="@dimen/dp_15"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:elevation="@dimen/dp_10"

        android:paddingRight="@dimen/dp_16"
        android:paddingLeft="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_156">
        <ImageView
            app:layout_constraintLeft_toLeftOf="parent"
            app:bind_view_onClick="@{(v)->presenter.onItemClick(v,userinfo)}"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/head"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:src="@mipmap/ic_pic_default_oval"
            />

        <TextView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Liiiiiiiiiiiiiiiii..."
            android:textColor="#ff333333"
            android:textSize="16sp"
            />






        <TextView
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2023-05-23 19:30:30"
            android:textColor="#ff999999"
            android:textSize="14sp"

            />





        <TextView
            android:layout_marginStart="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="@+id/head"
            app:layout_constraintLeft_toRightOf="@+id/head"
            android:id="@+id/idNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ID:76665888"
            android:textColor="#ff999999"
            android:textSize="14sp"
            />




        <LinearLayout
            android:layout_marginTop="@dimen/dp_18"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@+id/head"
            android:orientation="horizontal"
            android:weightSum="3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/data01"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2239"
                    android:textStyle="bold"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_16"
                    android:drawablePadding="@dimen/dp_4"
                    android:drawableRight="@mipmap/workbenc_home_fragment_bg_09"
                    />

                <TextView
                    android:layout_gravity="center_horizontal"

                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Current Earnings(Diamonds)"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_10"
                    />

            </LinearLayout>
            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content">


                <TextView
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="As of yesterday"
                    android:layout_gravity="center_horizontal"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_10"
                    />

                <TextView
                    android:id="@+id/data02"

                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="205.5"
                    android:textStyle="bold"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_16"
                    />

                <TextView
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Online Times (Hours)"
                    android:layout_gravity="center_horizontal"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_10"
                    />

            </LinearLayout>
            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content">


                <TextView
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="As of yesterday"
                    android:layout_gravity="center_horizontal"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_10"
                    />

                <TextView
                    android:id="@+id/data03"

                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80%"
                    android:textStyle="bold"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_16"
                    />

                <TextView
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Call Reply Rate"
                    android:textColor="#ff999999"
                    android:textSize="@dimen/sp_10"
                    />

            </LinearLayout>



        </LinearLayout>




    </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>