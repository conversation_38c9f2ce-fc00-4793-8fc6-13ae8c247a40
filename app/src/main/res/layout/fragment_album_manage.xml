<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="@dimen/dp_10"
            android:paddingRight="@dimen/dp_10">

            <anchor.app.base.view.refreshlayout.XRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                tools:listitem="@layout/user_item_album_manage" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <TextView
            android:id="@+id/tv_addition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_40"
            android:background="@mipmap/user_album_addition" />

        <TextView
            android:id="@+id/tv_submit"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_40"
            android:text="@string/album_submit"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:background="#0f1220"
            android:gravity="center_vertical|end"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp_10"
                android:background="@drawable/user_shape_1e2132"
                android:paddingHorizontal="@dimen/dp_20"
                android:paddingVertical="@dimen/dp_5"
                android:text="@string/base_delete"
                android:textColor="@color/colorAccent" />
        </LinearLayout>
    </RelativeLayout>
</layout>