<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@color/black" />

    <RelativeLayout
        android:id="@+id/rl_controllLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/play_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerInParent="true"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="15dp"
            android:background="@mipmap/play_start" />


        <RelativeLayout
            android:id="@+id/rl_time_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="22dp">

            <TextView
                android:id="@+id/progress_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:text="0:00"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <SeekBar
                android:id="@+id/seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/all_time"
                android:layout_toEndOf="@+id/progress_time"
                android:max="100"
                android:maxHeight="1.0dip"
                android:minHeight="1.0dip"
                android:progressDrawable="@drawable/play_seekbar_progress"
                android:thumb="@drawable/white_point" />

            <TextView
                android:id="@+id/all_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="15dp"
                android:text="0:00"
                android:textColor="@color/white"
                android:textSize="13sp" />
        </RelativeLayout>

    </RelativeLayout>
</RelativeLayout>