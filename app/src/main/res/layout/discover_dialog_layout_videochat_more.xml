<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="dialog"
            type="com.mobile.anchor.app.module.discover.main.chat.videochat.dialog.MoreDialogFragment" />

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_marginTop="@dimen/dp_102"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/base_dialog_bg_01"
        android:paddingTop="@dimen/dp_19"
        android:paddingLeft="@dimen/dp_16"
        android:paddingRight="@dimen/dp_16"
        >

        <LinearLayout
            android:visibility="gone"
            android:id="@+id/LL01"
            android:gravity="center_vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56">
            <ImageView
                android:layout_width="@dimen/dp_22"
                android:layout_height="@dimen/dp_22"
                android:src="@mipmap/discover_ic_video_03"
                />


            <TextView
                android:text="@string/Camera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                android:layout_marginStart="@dimen/dp_16"
                />
            <View
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                />


            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch_01"
                android:layout_width="@dimen/dp_54"
                android:layout_height="@dimen/dp_32"
                app:kswAnimationDuration="100"
                app:kswBackColor="#EAEAEA"
                app:kswBackDrawable="@drawable/base_selector_bg_switch_button"
                app:kswThumbColor="#ffffff"
                app:kswTintColor="#FFD100" />
        </LinearLayout>



        <LinearLayout
            android:id="@+id/LL02"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@+id/LL01"
            app:layout_constraintLeft_toLeftOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56">
            <ImageView
                android:layout_width="@dimen/dp_22"
                android:layout_height="@dimen/dp_22"
                android:src="@mipmap/discover_ic_video_02"
                />

            <TextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/Mic"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                android:layout_marginStart="@dimen/dp_16"
                />

            <View
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                />
            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch_02"
                android:layout_width="@dimen/dp_54"
                android:layout_height="@dimen/dp_32"
                app:kswAnimationDuration="100"
                app:kswBackColor="#EAEAEA"
                app:kswBackDrawable="@drawable/base_selector_bg_switch_button"
                app:kswThumbColor="#ffffff"
                app:kswTintColor="#FFD100" />
        </LinearLayout>



        <LinearLayout
            android:id="@+id/LL03"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@+id/LL02"
            app:layout_constraintLeft_toLeftOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56">

            <ImageView
                android:layout_width="@dimen/dp_22"
                android:layout_height="@dimen/dp_22"
                android:src="@mipmap/discover_ic_video_08"
                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/Report"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                android:layout_marginStart="@dimen/dp_16"
                />

        </LinearLayout>




        <LinearLayout
            android:id="@+id/LL04"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/LL03"
            app:layout_constraintLeft_toLeftOf="parent"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_5"
                android:background="#F0F1F5"
                />

            <TextView
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_50"
                android:text="@string/Cancel"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                android:layout_marginStart="@dimen/dp_16"
                />

        </LinearLayout>



<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_centerInParent="true"-->
<!--            android:text="Country"-->
<!--            android:textColor="#ff333333"-->
<!--            android:textSize="@dimen/sp_18" />-->
    </LinearLayout>

</layout>