/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

apply plugin: 'kotlin-allopen'
apply plugin: 'com.google.gms.google-services'  // Google Services plugin
apply plugin: 'com.google.firebase.crashlytics'  // Add the Crashlytics Gradle plugin

allOpen {
    // allows mocking for classes w/o directly opening them for release builds
    annotation 'com.android.example.github.testing.OpenClass'
}

android {
    namespace 'com.mobile.anchor.app'
    compileSdkVersion build_versions.target_sdk
    defaultConfig {
        applicationId "com.mobile.anchor.app" //
        minSdkVersion build_versions.min_sdk
        targetSdkVersion build_versions.target_sdk
        versionCode build_versions.versionCode
        versionName build_versions.versionName
        manifestPlaceholders = [zileVersionName: build_versions.zileVersionName]
        multiDexEnabled true

        // keep specific classes using proguard syntax
        multiDexKeepProguard file('../multiDexKeep.pro')

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "arm64-v8a", "armeabi-v7a"
        }

        flavorDimensions "default"
    }
    dataBinding {
        enabled = true
    }

    buildFeatures {
        viewBinding true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        androidTest.java.srcDirs += "src/lottie-common/java"
        test.java.srcDirs += "src/lottie-common/java"
    }

    signingConfigs {
        release {
            keyAlias 'anchor'
            keyPassword 'anchor@2025'
            storeFile file('../anchor.jks')
            storePassword 'anchor@2025'
        }
    }
    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
            shrinkResources false
            versionNameSuffix = ".test"
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            manifestPlaceholders = [zileVersionName: build_versions.zileVersionName]
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            manifestPlaceholders = [zileVersionName: build_versions.zileVersionName]
        }
        uat.initWith(release)
        uat {
            matchingFallbacks = ['release']
            versionNameSuffix = ".uat"
        }

        applicationVariants.all { variant ->
            variant.outputs.each { output ->
                def vn
                if (variant.getFlavorName() != null && variant.getFlavorName() != "") {
                    vn = variant.name;
                } else {
                    if (variant.getBuildType().name == "release") {
                        vn = "Release"
                    } else {
                        vn = "Debug"
                    }
                }
                def taskName = "process${vn}MainManifest"
                try {
                    println("=============== taskName ${taskName} ===============")
                    project.getTasks().getByName(taskName)
                } catch (Exception e) {
                    return
                }
                ///你的自定义名字
                project.getTasks().getByName(taskName).doFirst {
                    //def method = it.getClass().getMethods()
                    it.getManifests().getFiles().each {
                        if (it.exists() && it.canRead()) {
                            def manifestFile = it
                            def exportedTag = "android:exported"
                            def nameTag = "android:name"
                            ///这里第二个参数是 false ，所以 namespace 是展开的，所以下面不能用 androidSpace，而是用 nameTag
                            def xml = new XmlParser(false, false).parse(manifestFile)
                            if (xml.application != null && xml.application.size() > 0) {
                                def nodes = xml.application[0].'*'.findAll {
                                    //挑选要修改的节点，没有指定的 exported 的才需要增加
                                    //如果 exportedTag 拿不到可以尝试 it.attribute(androidSpace.exported)
                                    (it.name() == 'activity' || it.name() == 'receiver' || it.name() == 'service') && it.attribute(exportedTag) == null

                                }
                                if (nodes.application != null && nodes.application.size() > 0) {
                                    nodes.each {
                                        def t = it
                                        it.each {
                                            if (it.name() == "intent-filter") {
                                                println("$manifestFile \n .....................${t.attributes().get(nameTag)}......................")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            variant.outputs.all { output ->
                if (variant.name.startsWith("outerRing")) {
                    def aaptParams = variant.androidResources.aaptAdditionalParameters
                    aaptParams.add("-0")
                    aaptParams.add("so")
                }

                def type = variant.buildType.name
                String releaseVersion = "unknown"
                if (null != type) {
                    if ("debug" == type) {
                        releaseVersion = build_versions.zileVersionName
                    } else if ("uat" == type) {
                        releaseVersion = build_versions.zileVersionName
                    } else if ("release" == type) {
                        releaseVersion = build_versions.zileVersionName
                    }
                }
                String time = new Date().format("yyMMdd")
                def releaseApkName = "anchor_${variant.productFlavors[0].name}_${type}_${releaseVersion}_${time}.apk"
                outputFileName = releaseApkName
            }
        }
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/license.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt', 'META-INF/notice.txt']
        }
    }

    productFlavors {
        official {}
    }

    lint {
        abortOnError false
        lintConfig file('lint.xml')
        disable 'Instantiatable'
    }

    productFlavors.configureEach { flavor -> flavor.manifestPlaceholders = [ANCHOR_CHANNEL: ("official" == name ? "official" : name)] }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api project(":base")
    api project(":imkit")
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'

//    // firebase-messaging
//    implementation 'com.google.firebase:firebase-messaging:22.0.0'
//    // Google Play Service
//    implementation 'com.google.android.gms:play-services-gcm:17.0.0'

    kapt deps.room.compiler
    kapt deps.lifecycle.compiler
    kapt "com.android.databinding:compiler:${versions.android_gradle_plugin}"
    implementation("com.github.donkingliang:LabelsView:1.6.1")
    implementation("com.tencent.liteavsdk:LiteAVSDK_Player:8.5.10033")
    implementation("androidx.media2:media2-exoplayer:1.3.0")
}

// we need all open to run tests which  a we enable only for debug builds.
project.tasks.whenTaskAdded {
    if (it.name == "testReleaseUnitTest") {
        it.enabled = false
    }
}
